# LeanLogic QA migration from 2.X to version 3.0

## Dump and restore database dumps
### Dump database
1. Heroku
    1. See Heroku documentation about `pg:pull`
2. <PERSON> (VPN)
    4. Start up the GF Release VM
    5. Connect via VPN with PulseSecure
    6. Open PuTTY and connect to `GFMS Prod` (LLQA2). The user and password can be found in the Dashlane secure note
    7. Dump database with `pg_dump -U llqa -x -O --schema=public -F t --file ./llqa2gfprod-`date +'%F'`.dump.tar llqa`
    8. Open WinSCP and connect to `GFMS Prod` (LLQA2)
    9. Download the created `llqa2_dump.tar` file

### Restore database
1. Heroku
    1. See Heroku documentation about `pg:push`
2. <PERSON> (VPN)
    4. Start up the GF Release VM
    5. Connect via VPN with PulseSecure
    6. Open PuTTY and connect to `GFMS LLQA3`. The user and password can be found in the Dashlane secure note
    7. Run `sudo su` to enter root
    8. Stop the backend container with `docker container stop llqa3-backend` in order to remove all connections to postgres
    9. Drop old database with `docker exec -i llqa3-postgres dropdb -U postgres llqa`
    10. Create new database with `docker exec -i llqa3-postgres createdb -U postgres llqa`
    11. Restore with `docker exec -i llqa3-postgres  pg_restore -U postgres -x -O --schema=public -d llqa < llqa3_dump_2022_01_23.tar`
    12. Restart docker container with `docker container start llqa3-backend`


## Migrate on AWS
1. Execute step [Dump database](#dump-database)
2. For connecting the first time with SSH to the AWS server, create a key.pem file with the private key as the content. The private key can be found in the dashlane LLQA secure note
3. Connect with `ssh -i key.pem <EMAIL>`. Note that the aws domain name may be different since it changes if the server has been stopped previously
4. Fetch and pull changes from master (llqa2-monolith) (Current user is @ninodinatale, if it fails, log into with another accoutn)
5. Secure copy the previously downloaded LLQA2 database dump to this remote with `scp -i /path/to/private/key.pem /path/to/local/llqa2_dump.tar  <EMAIL>:/home/<USER>/` on your local machine
6. On the AWS server, run:
    - `rvm use 2.6.3`
    - `dropdb llqamigration`
    - `createdb llqamigration`
    - `pg_restore -x -O --schema=public -d llqamigration /home/<USER>/<llqa2_dump.tar>`
    - `cd ~/llqa2-monolith/LlqaMigration_3_0`
7. Start migration depending on LeanLogic, GF or Osterwalder:
   1. For GF: `nohup mvn spring-boot:run -Dagentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8000 -Dspring.datasource.url=********************************************** -Ddatabase.name=llqamigration -Dspring.datasource.username=llqa -Dspring.datasource.password=postgres -Dpre.migrate.scripts=preMigrateScripts.json -Dpost.migrate.scripts=postMigrateScripts.json > migration.log &`
   2. For LeanLogic: `mvn spring-boot:run -Dagentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8000 -Dspring.datasource.url=********************************************** -Ddatabase.name=llqamigration -Dspring.datasource.username=llqa -Dspring.datasource.password=postgres -Dpre.migrate.scripts=preMigrateScriptsHpTest.json -Dpost.migrate.scripts=postMigrateScripts.json`
   2. For Osterwalder: `mvn spring-boot:run -Dagentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8000 -Dspring.datasource.url=********************************************** -Ddatabase.name=llqamigration -Dspring.datasource.username=postgres -Dspring.datasource.password=admin -Dpre.migrate.scripts=preMigrateScriptsOsterwalder.json -Dpost.migrate.scripts=postMigrateScriptsOsterwalder.json`
8. After the successful migration, run `pg_dump -x -O --schema=public -F t -f /path/to/llqa3_dump.tar llqamigration`
9. Download the new LLQA3 dump with SCP from your host machine: `scp -i /path/to/private/key.pem <EMAIL>:/home/<USER>/llqa3_dump.tar /path/to/remote/llqa3_dump.tar`
10. Execute step [Restore database](#restore-database)


### migrate data (local)
##### Prerequisites
    
*   Be sure your JAVA_HOME variable is set to java 1.8: ``java -version``. If not set, execute ``export JAVA_HOME="/usr/libexec/java_home -v 1.8" ``
*   Change working directory to ``/llqa2-monolith``
*   Make sure to use Ruby version ``2.6.3`` since it's been tested. Install different Ruby versions with Ruby version manager (rvm): ``rvm install 2.6.3`` and then ``rvm use 2.6.3``.
*   Install ruby for example with rvm (See How to on Google) 
*   Make sure following gems are installed locally **with the matching version**: (``gem list --local`` to list locally installed gems)
    
        activemodel (5.2.3)
        activerecord (5.2.3)
        activesupport (5.2.3)
        arel (9.0.0)
        bigdecimal (default: 1.2.8)
        bundler (default: 1.16.6)
        bundler-unload (1.0.2)
        concurrent-ruby (1.1.5)
        did_you_mean (1.0.0)
        executable-hooks (1.6.0)
        gem-wrappers (1.4.0)
        i18n (1.6.0)
        io-console (default: 0.4.5)
        json (default: 1.8.3.1)
        minitest (5.8.5)
        net-telnet (0.1.1)
        pg (1.1.4)
        power_assert (0.2.6)
        psych (default: 2.1.0.1)
        rake (10.4.2)
        rdoc (default: 4.2.1)
        rubygems-bundler (1.4.5)
        rvm (1.11.3.9)
        test-unit (3.1.5)
        thread_safe (0.3.6)
        tzinfo (1.2.5)
        
    If you want to have a clean gem installation, run ``gem uninstall -aIx`` to uninstall all gems and then run ``gem install activerecord && gem install pg`` to install all required gems. If the migration fails, make sure to use the same version as stated above.

