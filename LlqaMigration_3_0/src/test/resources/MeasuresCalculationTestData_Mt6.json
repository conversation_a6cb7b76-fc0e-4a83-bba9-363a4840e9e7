{"id": 112, "yaml": {"optional": 0, "mx_xsize": 3, "mx_ysize": 1, "t1_targetv": 30, "t1_targetu": "mm", "t1_tholdu": "µm", "t1_tholdv": 3, "mx_formula": "max(row1)", "mx_ctop1": "1. <PERSON><PERSON><PERSON> bei 0°", "mx_ctop2": "2. <PERSON><PERSON><PERSON> bei 120°", "mx_ctop3": "3. <PERSON><PERSON><PERSON> bei 240°", "mx_fplc11": "Inhalt Zelle 11", "mx_fplc12": "Inhalt Zelle 12", "mx_fplc13": "Inhalt Zelle 13", "mx_rleft1": "Zeile 1 rechts", "mx_usedfields": ["a1", "b1", "c1", "h5"], "mx_fplc89": "Inhalt Zelle 89", "mx_cbottom9": "Ueberschrift Spalte 9"}}