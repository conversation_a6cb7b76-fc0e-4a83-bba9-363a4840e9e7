package ch.diso.llqa.db.migration.processor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Assert;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * abxtract base class to get json test data
 *
 * <AUTHOR>
 */
public abstract class JsonProcessorTest {

    /**
     * get json test datas
     *
     * @param jsonFileName the filename from the json test data in the resource directory
     * @return the json test data
     */
    protected JsonNode getTestDatas(String jsonFileName) {
        JsonNode testData = null;
        try {
            Resource testDataFile = new ClassPathResource(jsonFileName);
            InputStream targetStream = new FileInputStream(testDataFile.getFile());
            testData = new ObjectMapper().readTree(targetStream);
        } catch (IOException e) {
            Assert.fail("COULD NOT PARSE TEST DATA >" + jsonFileName + "<");
        }
        return testData;
    }
}
