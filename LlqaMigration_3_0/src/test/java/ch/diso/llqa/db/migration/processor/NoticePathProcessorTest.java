package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.NoticePathDto;
import ch.diso.llqa.db.migration.dto.NoticePathElementDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import static ch.diso.llqa.db.migration.dto.NoticePathDto.Type.CHECK_STEP;
import static ch.diso.llqa.db.migration.dto.NoticePathElementDto.Type.*;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

public class NoticePathProcessorTest extends JsonProcessorTest {
    private static final String POSITIVE_TEST_TEST_DATA = "NoticePathTestData_01.json";
    private static final String NEGATIVE_TEST_INVALID_TYPE_TEST_DATA = "NoticePathTestData_02.json";
    private static final String NEGATIVE_TEST_INVALID_PATH_TYPE_TEST_DATA = "NoticePathTestData_03.json";

    @Test
    public void negativeTestInvalidType() {
        // given
        JsonNode testData = super.getTestDatas(NEGATIVE_TEST_INVALID_TYPE_TEST_DATA);

        // when
        try {
            NoticePathProcessor testee = new NoticePathProcessor();
            testee.process(testData);

            // then
            fail("WE TEST WITH AN INVALID TYPE AND SHOULD HAVE RECEIVED AN EXCEPTION");
        } catch (Exception e) {
        }
    }

    @Test
    public void negativeTestInvalidPathType() {
        // given
        JsonNode testData = super.getTestDatas(NEGATIVE_TEST_INVALID_PATH_TYPE_TEST_DATA);

        // when
        try {
            NoticePathProcessor testee = new NoticePathProcessor();
            testee.process(testData);

            // then
            fail("WE TEST WITH AN INVALID PATH TYPE AND SHOULD HAVE RECEIVED AN EXCEPTION");
        } catch (Exception e) {
        }
    }

    @Test
    public void positiveTest() {
        // given
        JsonNode testData = super.getTestDatas(POSITIVE_TEST_TEST_DATA);
        NoticePathProcessor testee = new NoticePathProcessor();

        // when
        NoticePathDto noticePath = testee.process(testData);

        // then
        assertThat("NO NOTICE PATH RECEIVED", noticePath, notNullValue());

        assertThat("NO PRIMARY KEY SET", noticePath.getPk(), notNullValue());

        assertThat("NO TYPE SET", noticePath.getType(), notNullValue());
        assertThat("TYPE NOT AS EXPECTED", noticePath.getType(), is(CHECK_STEP));

        assertThat("NO CHECK ID SET", noticePath.getUrlCheckId(), notNullValue());
        assertThat("URL CHECK ID AS EXPECTED", noticePath.getUrlCheckId(), is(104l));

        assertThat("NO PATH ELEMENTS RECEIVED", noticePath.getNoticePathElementDtos(), notNullValue());
        assertThat("NUMBER OF PATH ELEMENTS NOT AS EXPECTED", noticePath.getNoticePathElementDtos().size(), is(5));

        NoticePathElementDto pathElt_0 = noticePath.getNoticePathElementDtos().get(0);
        assertThat("MODEL FOR ELEMENT >0< IS NOT AS EXPECTED", pathElt_0.getType(), is(MODEL));
        assertThat("CHILD ID FOR ELEMENT >0< IS NOT AS EXPECTED", pathElt_0.getChildId(), is(61l));
        assertThat("SEQ NUMBER FOR ELEMENT >0< IS NOT AS EXPECTED", pathElt_0.getSeq(), is(1));

        NoticePathElementDto pathElt_1 = noticePath.getNoticePathElementDtos().get(1);
        assertThat("MODEL FOR ELEMENT >1< IS NOT AS EXPECTED", pathElt_1.getType(), is(UNIT));
        assertThat("CHILD ID FOR ELEMENT >1< IS NOT AS EXPECTED", pathElt_1.getChildId(), is(103l));
        assertThat("SEQ NUMBER FOR ELEMENT >1< IS NOT AS EXPECTED", pathElt_1.getSeq(), is(2));

        NoticePathElementDto pathElt_2 = noticePath.getNoticePathElementDtos().get(2);
        assertThat("MODEL FOR ELEMENT >2< IS NOT AS EXPECTED", pathElt_2.getType(), is(CHECK));
        assertThat("CHILD ID FOR ELEMENT >2< IS NOT AS EXPECTED", pathElt_2.getChildId(), is(104l));
        assertThat("SEQ NUMBER FOR ELEMENT >2< IS NOT AS EXPECTED", pathElt_2.getSeq(), is(3));

        NoticePathElementDto pathElt_3 = noticePath.getNoticePathElementDtos().get(3);
        assertThat("MODEL FOR ELEMENT >3< IS NOT AS EXPECTED", pathElt_3.getType(), is(PROCEDURE));
        assertThat("CHILD ID FOR ELEMENT >3< IS NOT AS EXPECTED", pathElt_3.getChildId(), is(28l));
        assertThat("SEQ NUMBER FOR ELEMENT >3< IS NOT AS EXPECTED", pathElt_3.getSeq(), is(4));

        NoticePathElementDto pathElt_4 = noticePath.getNoticePathElementDtos().get(4);
        assertThat("MODEL FOR ELEMENT >4< IS NOT AS EXPECTED", pathElt_4.getType(), is(STEP));
        assertThat("CHILD ID FOR ELEMENT >4< IS NOT AS EXPECTED", pathElt_4.getChildId(), is(277l));
        assertThat("SEQ NUMBER FOR ELEMENT >4< IS NOT AS EXPECTED", pathElt_4.getSeq(), is(5));
    }
}