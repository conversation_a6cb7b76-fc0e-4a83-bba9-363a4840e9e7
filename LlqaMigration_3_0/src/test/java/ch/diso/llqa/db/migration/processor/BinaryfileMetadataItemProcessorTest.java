package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.BinaryfilesMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import java.util.List;

import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;

public class BinaryfileMetadataItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_01 = "BinaryfilesMetadataTestData_01.json";
    private static final long ID = 1l;
    private static final String TYPE_WIDTH = "width";
    private static final String TYPE_HEIGHT = "height";
    private static final String WIDTH = "900";
    private static final String HEIGHT = "600";
    private static final Long pk = null;

    @Test
    public void generateDto() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_FILE_01);

        // when
        BinaryfileMetadataItemProcessor testee = new BinaryfileMetadataItemProcessor();
        List<BinaryfilesMetadataDto> result = testee.process(testData);

        // then
        assertThat(result, is(notNullValue()));
        assertThat(result, hasSize(2));
        doAssert(result.get(0), TYPE_WIDTH, WIDTH);
        doAssert(result.get(1), TYPE_HEIGHT, HEIGHT);
    }



    private void doAssert(BinaryfilesMetadataDto dto, String type, String value) {
        assertThat(dto.getBinaryfileId(), is(ID));
        assertThat(dto.getType(), is(type));
        assertThat(dto.getValue(), is(value));
        assertThat(dto.getPk(), is(nullValue()));
    }


}