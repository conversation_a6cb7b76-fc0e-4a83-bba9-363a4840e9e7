package ch.diso.llqa.db.migration.processor;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.number.OrderingComparison.greaterThan;
import static org.junit.Assert.assertThat;

import ch.diso.llqa.db.migration.dto.ProcedureProcessingTimeDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

public class ProcedureProcessingTimeProcessorTest extends JsonProcessorTest {
   private static final String POSITIVE_TEST_TEST_DATA = "ProcedureProcessingTimeTestData_01.json";

   @Test
   public void positiveTest() {
      // given
      JsonNode testData = super.getTestDatas(POSITIVE_TEST_TEST_DATA);
      ProcedureProcessingTimeProcessor testee = new ProcedureProcessingTimeProcessor();

      // when
      ProcedureProcessingTimeDto processingTime = testee.process(testData);

      // then
      assertThat("NO PROCESSINT TIME OBJECT RECEIVED", processingTime, notNullValue());
      assertThat("NO PROCESSING TIME RECEIVED", processingTime.getProcessingTime(), greaterThan(0l));
      assertThat("PROCESSING TIME RECEIVED NOT AS EXPECTED", processingTime.getProcessingTime(), equalTo(456840l));
   }
}