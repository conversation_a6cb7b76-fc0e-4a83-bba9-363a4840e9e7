package ch.diso.llqa.db.migration.rowmapper;

import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnHeadersDto;
import ch.diso.llqa.db.migration.rowMapper.ConfigColumnsRowMapper;
import ch.diso.llqa.db.migration.step.StepConfigColumnHeaders;
import org.junit.Test;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created by <PERSON><PERSON> B<PERSON>ka on 28.02.18.
 */
public class ConfigColumnHeadersRowMapperTest {

    private final long ID = 1;
    private final String COLUMN_1 = "Nummer Verkaufsposition";
    private final String COLUMN_2 = "Bezeichnung Verkaufsposition";
    private final String COLUMN_3 = "Reihenfolge";
    private final String COLUMN_4 = "Merkmalgruppe";
    private final String COLUMN_5 = null;
    private final String COLUMN_6 = null;

    @Test
    public void testMapRow() throws SQLException {

        //given
        ConfigColumnsRowMapper<ConfigColumnHeadersDto> rowMapper = new ConfigColumnsRowMapper<>();
        rowMapper.setType(ConfigColumnHeadersDto.class);
        rowMapper.setColumnNames(StepConfigColumnHeaders.CONFIG_TABLE_COLUMN_HEADER_NAMES);

        ResultSet resultSet = createResultSet();
        int line = 1;

        ConfigColumnHeadersDto dto1 = new ConfigColumnHeadersDto();
        dto1.setConfigId(ID);
        dto1.setContent(COLUMN_1);
        dto1.setSeqnum(1);

        ConfigColumnHeadersDto dto2 = new ConfigColumnHeadersDto();
        dto2.setConfigId(ID);
        dto2.setContent(COLUMN_2);
        dto2.setSeqnum(2);

        ConfigColumnHeadersDto dto3 = new ConfigColumnHeadersDto();
        dto3.setConfigId(ID);
        dto3.setContent(COLUMN_3);
        dto3.setSeqnum(3);

        ConfigColumnHeadersDto dto4 = new ConfigColumnHeadersDto();
        dto4.setConfigId(ID);
        dto4.setContent(COLUMN_4);
        dto4.setSeqnum(4);

        //when
        List<? extends ConfigColumnHeadersDto>  dtos = rowMapper.mapRow(resultSet, line);

        //then
        assertNotNull("ROWMAPPER RETURNED A NULL OBJECT", dtos);
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 4, dtos.size());

        assertTrue("HEADER_1 OBJECT NOT EQUALS", dtos.stream()
                .anyMatch(actualDto1-> actualDto1.getContent() == dto1.getContent() &&
                                        actualDto1.getSeqnum() == dto1.getSeqnum() &&
                                        actualDto1.getConfigId() == dto1.getConfigId() ));

        assertTrue("HEADER_2 OBJECT NOT EQUALS", dtos.stream()
                .anyMatch(actualDto2-> actualDto2.getContent() == dto2.getContent() &&
                                        actualDto2.getSeqnum() == dto2.getSeqnum() &&
                                        actualDto2.getConfigId() == dto2.getConfigId() ));

        assertTrue("HEADER_3 OBJECT NOT EQUALS", dtos.stream()
                .anyMatch(actualDto3-> actualDto3.getContent() == dto3.getContent() &&
                                        actualDto3.getSeqnum() == dto3.getSeqnum() &&
                                        actualDto3.getConfigId() == dto3.getConfigId() ));

        assertTrue("HEADER_4 OBJECT NOT EQUALS", dtos.stream()
                .anyMatch(actualDto4-> actualDto4.getContent() == dto4.getContent() &&
                                        actualDto4.getSeqnum() == dto4.getSeqnum() &&
                                        actualDto4.getConfigId() == dto4.getConfigId() ));
    }

    private ResultSet createResultSet() throws SQLException{

        ResultSet rs = mock(ResultSet.class);
        when(rs.getLong("id")).thenReturn(ID);
        when(rs.getString("colheader_1")).thenReturn(COLUMN_1);
        when(rs.getString("colheader_2")).thenReturn(COLUMN_2);
        when(rs.getString("colheader_3")).thenReturn(COLUMN_3);
        when(rs.getString("colheader_4")).thenReturn(COLUMN_4);
        when(rs.getString("colheader_5")).thenReturn(COLUMN_5);
        when(rs.getString("colheader_6")).thenReturn(COLUMN_6);
        return  rs;
    }
}
