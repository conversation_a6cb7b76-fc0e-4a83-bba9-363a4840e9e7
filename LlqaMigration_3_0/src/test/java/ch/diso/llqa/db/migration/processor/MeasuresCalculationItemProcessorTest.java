package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.measures.calculation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.*;
import static org.junit.Assert.assertThat;

public class MeasuresCalculationItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_MT6 = "MeasuresCalculationTestData_Mt6.json";

    @Test
    public void processMeasureType6() {
        // given
        JsonNode jsonNode = super.getTestDatas(TEST_DATA_FILE_MT6);

        // when
        MeasuresCalculationItemProcessor testee = new MeasuresCalculationItemProcessor();
        List<MeasureTypeContainerDto> result = testee.process(jsonNode);
        List<MeasureTypeDto> measureTypeDtos = result.stream()
                .flatMap(container -> container.getMeasureTypes().stream()).collect(Collectors.toList());

        // then
        assertThat(result, hasSize(1));
        assertThat(measureTypeDtos, hasSize(2));
        assertMeasureType1(measureTypeDtos.get(0), MeasureType.MT1);
        assertMeasureType6(measureTypeDtos.get(1), MeasureType.MT6);
    }

    private void assertMeasureType1(MeasureTypeDto measureTypeDto, MeasureType measureType) {
        assertThat(measureTypeDto.getMeasureType(), is(measureType));
        assertThat(measureTypeDto.getMeasureid(), is(112L));
        assertThat(measureTypeDto.getOptional(), is(false));
        assertThat(measureTypeDto.getInternal(), is(false));
        assertThat(measureTypeDto.getTargetValue(), is(30.0));
        assertThat(measureTypeDto.getTargetUnit(), is("mm"));
        assertThat(measureTypeDto.getThresholdValue(), is(3.0));
        assertThat(measureTypeDto.getThresholdUnit(), is("µm"));
    }

    private void assertMeasureType6(MeasureTypeDto measureTypeDto, MeasureType measureType) {
        assertMeasureType1(measureTypeDto, MeasureType.MT6);
        MatrixDto matrix = measureTypeDto.getMatrix();
        assertThat(matrix, notNullValue());
        assertThat(matrix.getXSize(), is(3));
        assertThat(matrix.getYSize(), is(1));
        assertThat(matrix.getFormula(), is("max(row1)"));
        Map<Integer, MatrixColumnHeaderDto> columnHeaderMap = matrix.getColumnHeaderDtos();
        assertThat(columnHeaderMap.size(), is(3));
        assertColHeaderDto(columnHeaderMap.get(1), 1, "1. Messung bei 0°");
        assertColHeaderDto(columnHeaderMap.get(2), 2, "2. Messung bei 120°");
        assertColHeaderDto(columnHeaderMap.get(3), 3, "3. Messung bei 240°");
        Map<Integer, MatrixRowHeaderDto> rowHeaderMap = matrix.getRowHeaderDtos();
        assertThat(rowHeaderMap.size(), is(1));
        assertRowHeaderDto(rowHeaderMap.get(1), 1, "Zeile 1 rechts");
        assertCellDtos(Lists.newArrayList(matrix.getCellDtos()));
    }

    private void assertColHeaderDto(MatrixColumnHeaderDto dto, int index, String valueTop) {
        assertThat(dto.getColNumber(), is(index));
        assertThat(dto.getValueTop(), is(valueTop));
    }

    private void assertRowHeaderDto(MatrixRowHeaderDto dto, int index, String value) {
        assertThat(dto.getRowNumber(), is(index));
        assertThat(dto.getValueLeft(), is(value));
    }

    private void assertCellDtos(List<MatrixCellDto> dtos) {
        sortCellDtos(dtos);
        assertCelDto(dtos.get(0), 1, 1, "Inhalt Zelle 11");
        assertCelDto(dtos.get(1), 1, 2, "Inhalt Zelle 12");
        assertCelDto(dtos.get(2), 1, 3, "Inhalt Zelle 13");
    }



    private void assertCelDto(MatrixCellDto dto, int rowIndex, int colIndex, String value) {
        assertThat(dto.getRowHeaderDto().getRowNumber(), is(rowIndex));
        assertThat(dto.getColumnHeaderDto().getColNumber(), is(colIndex));
        assertThat(dto.getCellHeader(), is(value));
    }

    private void sortCellDtos(List<MatrixCellDto> dtos) {
        dtos.sort((o1, o2) -> {
            int o1ColRow = o1.getRowHeaderDto().getRowNumber() + o1.getColumnHeaderDto().getColNumber();
            int o2ColRow = o2.getRowHeaderDto().getRowNumber() + o2.getColumnHeaderDto().getColNumber();
            return o1ColRow < o2ColRow ? -1 : 1;
        });
    }

}