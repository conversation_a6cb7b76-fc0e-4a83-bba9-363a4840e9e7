package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.NoticeTimelineDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import java.util.List;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.junit.Assert.assertThat;

public class NoticeTimelineProcessorTest extends JsonProcessorTest {
    private static final String POSITIVE_TEST_TEST_DATA = "NoticeNoticeTimelineTestData_01.json";

    @Test
    public void positiveTest() {
        // given
        JsonNode testData = super.getTestDatas(POSITIVE_TEST_TEST_DATA);
        NoticeTimelineProcessor testee = new NoticeTimelineProcessor();

        // when
        List<NoticeTimelineDto> timeline = testee.process(testData);

        // then
        assertThat("NO TIMELINES RECEIVED", timeline, notNullValue());
        assertThat("NUMBER OF TIMELINE ELEMENTS NOT AS EXPECTED", timeline.size(), is(3));

        List<Long> noticeIds = timeline.stream()
                .map(NoticeTimelineDto::getNoticeId)
                .distinct()
                .collect(Collectors.toList());
        assertThat("NUMBER OF DISTINCT NOTICE ID REFERENCES NOT AS EXPECTED", noticeIds.size(), is(1));
        assertThat("NUMBER OF DISTINCT NOTICE ID REFERENCES NOT AS EXPECTED", noticeIds.get(0).longValue(), is(1234L));

        NoticeTimelineDto timelineElt_01 = timeline.get(0);
        assertThat("STATE FOR ELEMENT >0< NOT AS EXPECTED", timelineElt_01.getStatus(), is(1L));
        assertThat("USER ID FOR ELEMENT >0< NOT AS EXPECTED", timelineElt_01.getUserId(), is(11L));
        assertThat("COMMENT FOR ELEMENT >0< NOT AS EXPECTED", timelineElt_01.getComment(), nullValue());
        assertThat("TIMESTAMP FOR ELEMENT >0< NOT AS EXPECTED", timelineElt_01.getTimestamp(), notNullValue());

        NoticeTimelineDto timelineElt_02 = timeline.get(1);
        assertThat("STATE FOR ELEMENT >1< NOT AS EXPECTED", timelineElt_02.getStatus(), is(2L));
        assertThat("USER ID FOR ELEMENT >1< NOT AS EXPECTED", timelineElt_02.getUserId(), is(11L));
        assertThat("COMMENT FOR ELEMENT >1< NOT AS EXPECTED", timelineElt_02.getComment(), is("Anleitung ist ergänzt"));
        assertThat("TIMESTAMP FOR ELEMENT >1< NOT AS EXPECTED", timelineElt_02.getTimestamp(), notNullValue());

        NoticeTimelineDto timelineElt_03 = timeline.get(2);
        assertThat("STATE FOR ELEMENT >2< NOT AS EXPECTED", timelineElt_03.getStatus(), is(5L));
        assertThat("USER ID FOR ELEMENT >2< NOT AS EXPECTED", timelineElt_03.getUserId(), is(11L));
        assertThat("COMMENT FOR ELEMENT >2< NOT AS EXPECTED", timelineElt_03.getComment(), is(""));
        assertThat("TIMESTAMP FOR ELEMENT >2< NOT AS EXPECTED", timelineElt_03.getTimestamp(), notNullValue());
    }
}