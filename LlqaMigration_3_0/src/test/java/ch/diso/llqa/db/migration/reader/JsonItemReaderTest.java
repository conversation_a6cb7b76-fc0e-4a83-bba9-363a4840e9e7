package ch.diso.llqa.db.migration.reader;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.common.JsonCreater;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class JsonItemReaderTest {

    private JsonItemReaderConfiguration configuration;

    @InjectMocks
    private JsonItemReader testee;

    @Mock
    private JsonCreater jsonCreater;

    @Before
    public void setup() {
        configuration = new JsonItemReaderConfiguration("databaseName", "table", "column", ColumnType.OBJECT, 1, 1, "password");
        testee.configure(configuration);
    }

    @Test
    public void jsonCreaterReturnsNull() throws IOException, InterruptedException {
        // given
        when(jsonCreater.getJson(configuration)).thenReturn(null);

        // when
        JsonNode result = testee.read();

        // then
        assertThat(result, nullValue());
    }

    @Test
    public void jsonCreaterReturnsNotNull() throws IOException, InterruptedException {
        // given
        String jsonTree = "[{\n" +
                "  \"id\": 1,\n" +
                "  \"yaml\": {\n" +
                "    \"width\": 900,\n" +
                "    \"height\": 600\n" +
                "  }\n" +
                "}]";

        when(jsonCreater.getJson(configuration)).thenReturn(jsonTree);

        // when
        JsonNode result = testee.read();

        // then
        assertThat(result, notNullValue());
    }
}