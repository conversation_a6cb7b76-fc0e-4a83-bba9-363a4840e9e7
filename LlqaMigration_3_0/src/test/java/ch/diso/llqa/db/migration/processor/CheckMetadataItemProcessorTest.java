package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

public class CheckMetadataItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_POSITIVE_TEST = "CheckMetadataTestData_01.json";
    private static final String TEST_DATA_FILE_NEGARIVE_TEST_01 = "CheckMetadataTestData_02.json";
    private static final String TEST_DATA_FILE_NEGARIVE_TEST_02 = "CheckMetadataTestData_03.json";
    private static final String TEST_DATA_FILE_NEGARIVE_TEST_03 = "CheckMetadataTestData_04.json";
    private static final String TEST_DATA_FILE_NEGARIVE_TEST_04 = "CheckMetadataTestData_05.json";

    @Test
    public void generateDtoCheckPositive() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_FILE_POSITIVE_TEST);

        // when
        CheckMetadataItemProcessor testee = new CheckMetadataItemProcessor();
        LlqaDto dto = testee.process(testData);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dto, notNullValue());
        assertThat("PRIMARY KEY NOT AS EXPECTED", dto.getPk(), is(new Long(1l)));
    }

    @Test
    public void generateDtoCheckNegative01() {
        this.generateDtoCheckNegative(TEST_DATA_FILE_NEGARIVE_TEST_01);
    }

    @Test
    public void generateDtoCheckNegative02() {
        this.generateDtoCheckNegative(TEST_DATA_FILE_NEGARIVE_TEST_02);
    }

    @Test
    public void generateDtoCheckNegative03() {
        this.generateDtoCheckNegative(TEST_DATA_FILE_NEGARIVE_TEST_03);
    }

    @Test
    public void generateDtoCheckNegative04() {
        this.generateDtoCheckNegative(TEST_DATA_FILE_NEGARIVE_TEST_04);
    }

    private void generateDtoCheckNegative(final String checkDataFileName) {
        // given
        JsonNode testData = super.getTestDatas(checkDataFileName);

        // when
        CheckMetadataItemProcessor testee = new CheckMetadataItemProcessor();
        LlqaDto dto = testee.process(testData);

        // then
        assertThat("PROCESSOR RETURNED NOT A NULL OBJECT", dto, nullValue());
    }
}