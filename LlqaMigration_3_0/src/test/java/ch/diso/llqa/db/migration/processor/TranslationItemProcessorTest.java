package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.TranslationDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class TranslationItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_01 = "TranslationItemProcessorTestData_01.json";

    private static final String EN_LANGUAGE_KEY = "en";
    private static final String DE_LANGUAGE_KEY = "de";
    private static final String FR_LANGUAGE_KEY = "fr";
    private static final String IT_LANGUAGE_KEY = "it";
    private static final String ES_LANGUAGE_KEY = "es";

    private static final String EN_TRANSLATION_CONTENT_HELLO_WORLD = "hello world.";
    private static final String DE_TRANSLATION_CONTENT_HELLO_WORLD = "hallo welt.";
    private static final String FR_TRANSLATION_CONTENT_HELLO_WORLD = "bonjour le mode.";
    private static final String IT_TRANSLATION_CONTENT_HELLO_WORLD = "ciao mondo.";
    private static final String ES_TRANSLATION_CONTENT_HELLO_WORLD = "hola mundo.";

    private static final String EXPECTED_JSON = "{" +
            "\"" + EN_LANGUAGE_KEY + "\":\"" + EN_TRANSLATION_CONTENT_HELLO_WORLD + "\"," +
            "\"" + DE_LANGUAGE_KEY + "\":\"" + DE_TRANSLATION_CONTENT_HELLO_WORLD + "\"," +
            "\"" + FR_LANGUAGE_KEY + "\":\"" + FR_TRANSLATION_CONTENT_HELLO_WORLD + "\"," +
            "\"" + IT_LANGUAGE_KEY + "\":\"" + IT_TRANSLATION_CONTENT_HELLO_WORLD + "\"," +
            "\"" + ES_LANGUAGE_KEY + "\":\"" + ES_TRANSLATION_CONTENT_HELLO_WORLD + "\"" +
            "}";

    @InjectMocks
    private TranslationItemProcessor testee;

    @Test
    public void generateDto() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_FILE_01);

        // when
        TranslationDto translationDto = this.testee.process(testData);

        // then
        assertThat("NO TRANSLATION CONTAINER RECEIVED ==> NULL VALUE RETURNED", translationDto, notNullValue());
        assertThat("NO TRANSLATIONS RECEIVED ==> NULL VALUE RETURNED", translationDto.getPk(), is(1L));
        assertThat("NUMBER OF TRANSLATIONS NOT AS EXPECTED", translationDto.getContent(), is(EXPECTED_JSON));
    }
}