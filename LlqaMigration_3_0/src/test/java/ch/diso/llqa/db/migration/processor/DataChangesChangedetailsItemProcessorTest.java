package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.DataChangesChangedetailsDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.Matchers.*;
import static org.junit.Assert.assertThat;

public class DataChangesChangedetailsItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_01 = "DataChangesChangedetailsTestData_01.json";
    private static final String TEST_DATA_02 = "DataChangesChangedetailsTestData_02.json";
    private static final String TEST_DATA_03 = "DataChangesChangedetailsTestData_03.json";
    private static final String TEST_DATA_04 = "DataChangesChangedetailsTestData_04.json";
    private static final String TEST_DATA_05 = "DataChangesChangedetailsTestData_05.json";
    private static final String TEST_DATA_06 = "DataChangesChangedetailsTestData_06.json";
    private static final String TEST_DATA_07 = "DataChangesChangedetailsTestData_07.json";
    private static final String TEST_DATA_08 = "DataChangesChangedetailsTestData_08.json";
    private static final String TEST_DATA_09 = "DataChangesChangedetailsTestData_09.json";
    private static final String TEST_DATA_10 = "DataChangesChangedetailsTestData_10.json";

    private DataChangesChangedetailsItemProcessor testee;

    @Before
    public void setup() {
        testee = new DataChangesChangedetailsItemProcessor();
    }

    @Test
    public void processFieldWithOlddataAndNewDataNotJson() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_06);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertThat(result.getField(), is("tooltype_id"));
        assertThat(result.getOldData(), is("{\"value\":\"20\"}"));
        assertThat(result.getNewData(), is("{\"value\":\"30\"}"));
    }

    @Test
    public void processFieldWithOlddataJsonAndNewDataNotJson() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_07);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertThat(result.getField(), is("tooltype_id"));
        assertThat(result.getOldData(), is("[{\"type\":\"CO\",\"code\":\"1.28\",\"res\":\"omit\",\"trigger\":\"Select\"}]"));
        assertThat(result.getNewData(), is("{\"value\":\"30\"}"));
    }

    @Test
    public void processProcRealId() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_08);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertNewData(result, "{\"proc_realid\":61}");
    }


    @Test
    public void processType() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_09);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertNewData(result, "{\"type\":\"disabled\"}");

    }

    @Test
    public void processOldcodeNewcode() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_10);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertNewData(result,"{\"oldcode\":\"0000\",\"newcode\":\"0003\"}");

    }

    @Test
    public void processInitdata() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_01);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertNewData(result, "{\"code\":\"0001\",\"devicetype_id\":1,\"title\":{\"de\":\"MILL S/X BG1\"}");
    }

    @Test
    public void processTgt() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_02);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertThat(result, notNullValue());
        assertThat(result.getPk(), notNullValue());
        assertThat(result.getNewData(), is("{\"code\":\"0001\",\"devicetype_id\":1,\"title\":{\"de\":\"MILL S/X BG1\"}"));
        assertThat(result.getField(), nullValue());
        assertThat(result.getOldData(), nullValue());
        assertThat(result.getTgt(), is("0001"));
    }

    @Test
    public void processOlddataNewdataField() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_03);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertThat(result, notNullValue());
        assertThat(result.getPk(), notNullValue());
        assertThat(result.getField(), is("flowcontrol"));
        assertThat(result.getOldData(), is("[]"));
        assertThat(result.getNewData(), is("{\"code\":\"0001\",\"devicetype_id\":1,\"title\":{\"de\":\"MILL S/X BG1\"}"));
        assertThat(result.getTgt(), nullValue());
    }

    @Test
    public void processVersion() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_04);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertNewData(result, "{\"version\":1}");
    }

    @Test
    public void processChanges() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_05);

        // when
        DataChangesChangedetailsDto result = testee.process(testData);

        // then
        assertNewData(result, "{\"changes\":\"[1,3,2]\"}");

    }

    private void assertNewData(DataChangesChangedetailsDto result, String expected) {
        assertThat(result, notNullValue());
        assertThat(result.getPk(), notNullValue());
        assertThat(result.getNewData(), is(expected));
        assertThat(result.getField(), nullValue());
        assertThat(result.getOldData(), nullValue());
        assertThat(result.getTgt(), nullValue());
    }
}