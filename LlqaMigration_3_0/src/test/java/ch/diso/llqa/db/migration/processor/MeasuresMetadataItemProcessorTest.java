package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.MeasureMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MeasuresMetadataItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_01 = "MeasuresMetadataTestData_01.json";

    private static final String TEST_DATA_FILE_02 = "MeasuresMetadataTestData_02.json";

    @Mock
    JdbcTemplate jdbcTemplate;

    @InjectMocks
    private MeasuresMetadataItemProcessor testee;

    @Test
    public void process() {
        // given
        JsonNode jsonNode = super.getTestDatas(TEST_DATA_FILE_01);
        when(jdbcTemplate.queryForList(ArgumentMatchers.any(String.class))).thenReturn(Lists.newArrayList(Maps.newHashMap()));

        // when
        List<MeasureMetadataDto> result = testee.process(jsonNode);

        // then
        assertThat(result.get(0).getNumberDecimals(), is(2));
    }

    @Test
    public void processDefault() {
        // given
        JsonNode jsonNode = super.getTestDatas(TEST_DATA_FILE_02);
        when(jdbcTemplate.queryForList(ArgumentMatchers.any(String.class))).thenReturn(Lists.newArrayList(Maps.newHashMap()));

        // when
        List<MeasureMetadataDto> result = testee.process(jsonNode);

        // then
        assertThat(result.get(0).getNumberDecimals(), is(0));
    }

    @Test
    public void processMissingMeasureType() {
        // given
        JsonNode jsonNode = super.getTestDatas(TEST_DATA_FILE_02);
        when(jdbcTemplate.queryForList(ArgumentMatchers.any(String.class))).thenReturn(Lists.newArrayList());

        // when
        List<MeasureMetadataDto> result = testee.process(jsonNode);

        // then
        assertThat(result, hasSize(0));
    }
}