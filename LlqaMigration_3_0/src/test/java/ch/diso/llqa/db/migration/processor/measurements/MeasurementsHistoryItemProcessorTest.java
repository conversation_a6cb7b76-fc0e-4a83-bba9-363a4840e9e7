package ch.diso.llqa.db.migration.processor.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsHistoriesDto;
import ch.diso.llqa.db.migration.processor.JsonProcessorTest;
import ch.diso.llqa.db.migration.processor.measurements.MeasurementsHistoriesItemProcessor;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static junit.framework.TestCase.assertTrue;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertEquals;

public class MeasurementsHistoryItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_01 = "MeasurementsHistoriesTestData_01.json";
    private static final String TEST_DATA_FILE_02 = "MeasurementsHistoriesTestData_02.json";


    @Test
    public void processTests() throws ParseException {

        // given
        JsonNode testFile1Data = super.getTestDatas(TEST_DATA_FILE_01);
        JsonNode testFile2Data = super.getTestDatas(TEST_DATA_FILE_02);

        // when
        MeasurementsHistoriesItemProcessor measurementsHistoriesItemProcessor = new MeasurementsHistoriesItemProcessor();
        List<MeasurementsHistoriesDto> dtoList = measurementsHistoriesItemProcessor.process(testFile1Data);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dtoList, notNullValue());
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 1, dtoList.size());

        doAssert(dtoList, createSingleShouldDtosTest());

        //when
        measurementsHistoriesItemProcessor = new MeasurementsHistoriesItemProcessor();
        dtoList = measurementsHistoriesItemProcessor.process(testFile2Data);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dtoList, notNullValue());
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 2, dtoList.size());

        doAssert(dtoList, createListShouldDtosTest());
    }

    private void doAssert(List<MeasurementsHistoriesDto> actualDtos, List<MeasurementsHistoriesDto> shouldDtos) {

        Function<MeasurementsHistoriesDto,List<Object>> assertKeys = ak -> Arrays.asList(ak.getComment(),
                                                                                        ak.getMeasurementId(),
                                                                                        ak.getStatus(),
                                                                                        ak.getSavedBy(),
                                                                                        ak.getValue(),
                                                                                        ak.getMeasurementErrorCategoryId(),
                                                                                        ak.getUpdatedAt(),
                                                                                        ak.getUpdatedBy());

        assertTrue("OBJECT CONTENT NOT EQUALS",actualDtos.stream()
                .map(assertKeys)
                .collect(Collectors.toList())
                .equals(shouldDtos.stream()
                        .map(assertKeys)
                        .collect(Collectors.toList())));
    }

    private List<MeasurementsHistoriesDto> createSingleShouldDtosTest() throws ParseException {

        MeasurementsHistoriesDto dto = new MeasurementsHistoriesDto();
        dto.setMeasurementId(7384L);
        dto.setValue("2.320");
        dto.setComment("Bei der Nacharbeit wurden die Lagerflansche nicht geöffnet und die vorherige Dokumentation wurde nicht beigelegt. Folglich sind die Masse unbekannt. .");
        dto.setStatus(1L);
        dto.setSavedBy(17L);
        dto.setUpdatedAt(convertToTimestamp("2018-02-12T11:24:55.935Z"));
        dto.setUpdatedBy("17");
        dto.setMeasurementErrorCategoryId(1);
        return new ArrayList(){{ add(dto); }};
    }


    private List<MeasurementsHistoriesDto> createListShouldDtosTest() throws ParseException {

        MeasurementsHistoriesDto dto = new MeasurementsHistoriesDto();
        dto.setMeasurementId(9819L);
        dto.setValue("16H4-0002");
        dto.setComment("");
        dto.setStatus(1L);
        dto.setSavedBy(33L);
        dto.setUpdatedAt(convertToTimestamp("2018-01-25T09:19:34.459Z"));
        dto.setUpdatedBy("33");
        dto.setMeasurementErrorCategoryId(1);

        MeasurementsHistoriesDto dto2 = new MeasurementsHistoriesDto();
        dto2.setMeasurementId(9819L);
        dto2.setValue("16H4-000213H9-0001");
        dto2.setComment("");
        dto2.setStatus(1L);
        dto2.setSavedBy(33L);
        dto2.setUpdatedAt(convertToTimestamp("2018-01-26T13:37:22.673Z"));
        dto2.setUpdatedBy("33");
        dto2.setMeasurementErrorCategoryId(1);

        return new ArrayList(){{ add(dto); add(dto2); }};
    }

    private Timestamp convertToTimestamp (String dateTime) throws ParseException {

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        Date parsedTimeStamp = df.parse(dateTime);
        return new Timestamp(parsedTimeStamp.getTime());
    }
}