package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.WorkflowRuleDto;
import ch.diso.llqa.db.migration.processor.flowcontrol.WorkflowRuleWorkflowGroupLoader.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MeasureFlowcontrolItemProcessorTest extends FlowcontrolItemProcessorTest {

    private static final String TEST_DATA_FILE = "MeasureFlowcontrolTestData_01.json";
    private static final String SELECT_REAL_ID_SQL = String.format("SELECT p2.real_id " +
            "FROM measures m2 INNER JOIN STEPS s2 ON m2.step_id = s2.id" +
            "                 INNER JOIN procedures p2 ON s2.procedure_id = p2.id " +
            "WHERE m2.id = %d", CHILD_PK);
    private static final int NUMBER_OF_WORKFLOW_RULES = 7;

    @Override
    protected void initWorkflowRuleGroupLoaderMock() {
        when(applicationContext.getBean(TYPE_PROCEDURES)).thenReturn(new WorkflowRuleProcedureLoader());
        when(applicationContext.getBean(TYPE_CONFIG_ENTRY)).thenReturn(new WorkflowRuleConfigEntryLoader());
        when(applicationContext.getBean(TYPE_DEVICE_TYPE)).thenReturn(new WorkflowRuleDeviceTypeLoader());
        when(applicationContext.getBean(TYPE_MODEL)).thenReturn(new WorkflowRuleModelLoader());
        when(applicationContext.getBean(TYPE_CHECK_TYPE)).thenReturn(new WorkflowRuleCheckTypeLoader());
        when(applicationContext.getBean(TYPE_MEASURES)).thenReturn(new WorkflowRuleMeasureLoader());
        when(applicationContext.getBean(TYPE_STEPS)).thenReturn(new WorkflowRuleStepLoader());
    }

    @Override
    protected void initWorkflowRulesLoaderMock() {
        this.initWorkflowRulesLoaderMock(String.format(WorkflowRuleProcedureLoader.SQL_SELECT_WF_GRP_ELT_FORMAT, PROCEDURE_CODE, this.getSelectRealIdSql()), WorkflowRuleProcedureLoader.WF_ELT_GRP_ID_COL_NAME, new Long(PROCEDURE_GROUP_PK));
        this.initWorkflowRulesLoaderMock(String.format(WorkflowRuleModelLoader.SQL_SELECT_WF_GRP_ELT_FORMAT, MODEL_CODE), WorkflowRuleModelLoader.WF_ELT_GRP_ID_COL_NAME, new Long(MODEL_GROUP_PK));
        this.initWorkflowRulesLoaderMock(String.format(WorkflowRuleDeviceTypeLoader.SQL_SELECT_WF_GRP_ELT_FORMAT, DEVICE_TYPE_CODE), WorkflowRuleDeviceTypeLoader.WF_ELT_GRP_ID_COL_NAME, new Long(DEVICE_TYPE_GROUP_PK));
        this.initWorkflowRulesLoaderMock(String.format(WorkflowRuleCheckTypeLoader.SQL_SELECT_WF_GRP_ELT_FORMAT, CHECK_TYPE_CODE), WorkflowRuleCheckTypeLoader.WF_ELT_GRP_ID_COL_NAME, new Long(CHECK_TYPE_GROUP_PK));
        this.initWorkflowRulesLoaderMock(String.format(WorkflowRuleConfigEntryLoader.SQL_SELECT_WF_GRP_ELT_FORMAT, CONFIG_ENTRY_CODE), WorkflowRuleConfigEntryLoader.WF_ELT_GRP_ID_COL_NAME, new Long(CONFIG_ENTRY_GROUP_PK));
        this.initWorkflowRulesLoaderMock(String.format(WorkflowRuleStepLoader.SQL_SELECT_WF_GRP_ELT_FORMAT, STEP_CODE, this.getSelectRealIdSql()), WorkflowRuleStepLoader.WF_ELT_GRP_ID_COL_NAME, new Long(STEP_GROUP_PK));
        this.initWorkflowRulesLoaderMock(String.format(WorkflowRuleMeasureLoader.SQL_SELECT_WF_GRP_ELT_FORMAT, MEASURE_CODE, this.getSelectRealIdSql()), WorkflowRuleMeasureLoader.WF_ELT_GRP_ID_COL_NAME, new Long(MEASURE_GROUP_PK));
    }

    @Override
    protected String getTestDataFile() {
        return TEST_DATA_FILE;
    }

    @Override
    protected int getNumberOfExpectedWorkflowRules() {
        return NUMBER_OF_WORKFLOW_RULES;
    }

    @Override
    protected void testWorkflowRuleContent(List<WorkflowRuleDto> workflowRules) {
        WorkflowRuleDto rule_01 = workflowRules.get(0);
        WorkflowRuleDto rule_02 = workflowRules.get(1);
        WorkflowRuleDto rule_03 = workflowRules.get(2);
        WorkflowRuleDto rule_04 = workflowRules.get(3);
        WorkflowRuleDto rule_05 = workflowRules.get(4);
        WorkflowRuleDto rule_06 = workflowRules.get(5);
        WorkflowRuleDto rule_07 = workflowRules.get(6);

        assertThat("TRIGGER FOR RULE 1 NOT AS EXPECTED", rule_01.getTrigger(), is("Select"));
        assertThat("TRIGGER FOR RULE 2 NOT AS EXPECTED", rule_02.getTrigger(), is("Omit"));
        assertThat("TRIGGER FOR RULE 3 NOT AS EXPECTED", rule_03.getTrigger(), is("Yes"));
        assertThat("TRIGGER FOR RULE 4 NOT AS EXPECTED", rule_04.getTrigger(), is("InQueue"));
        assertThat("TRIGGER FOR RULE 5 NOT AS EXPECTED", rule_05.getTrigger(), is("Pass"));
        assertThat("TRIGGER FOR RULE 6 NOT AS EXPECTED", rule_06.getTrigger(), is("Fail"));
        assertThat("TRIGGER FOR RULE 7 NOT AS EXPECTED", rule_07.getTrigger(), is("Skip"));

        assertThat("RESULT FOR RULE 1 NOT AS EXPECTED", rule_01.getResult(), is("omit"));
        assertThat("RESULT FOR RULE 2 NOT AS EXPECTED", rule_02.getResult(), is("skip"));
        assertThat("RESULT FOR RULE 3 NOT AS EXPECTED", rule_03.getResult(), is("omit"));
        assertThat("RESULT FOR RULE 4 NOT AS EXPECTED", rule_04.getResult(), is("skip"));
        assertThat("RESULT FOR RULE 5 NOT AS EXPECTED", rule_05.getResult(), is("omit"));
        assertThat("RESULT FOR RULE 6 NOT AS EXPECTED", rule_06.getResult(), is("skip"));
        assertThat("RESULT FOR RULE 7 NOT AS EXPECTED", rule_07.getResult(), is("omit"));

        assertThat("INVERSE FOR RULE 1 NOT AS EXPECTED", rule_01.isInverse(), is(false));
        assertThat("INVERSE FOR RULE 2 NOT AS EXPECTED", rule_02.isInverse(), is(false));
        assertThat("INVERSE FOR RULE 3 NOT AS EXPECTED", rule_03.isInverse(), is(false));
        assertThat("INVERSE FOR RULE 4 NOT AS EXPECTED", rule_04.isInverse(), is(false));
        assertThat("INVERSE FOR RULE 5 NOT AS EXPECTED", rule_05.isInverse(), is(true));
        assertThat("INVERSE FOR RULE 6 NOT AS EXPECTED", rule_06.isInverse(), is(false));
        assertThat("INVERSE FOR RULE 7 NOT AS EXPECTED", rule_07.isInverse(), is(false));
    }

    @Override
    protected String getTablename() {
        return "measures";
    }

    @Override
    protected String getSelectRealIdSql() {
        return SELECT_REAL_ID_SQL;
    }
}