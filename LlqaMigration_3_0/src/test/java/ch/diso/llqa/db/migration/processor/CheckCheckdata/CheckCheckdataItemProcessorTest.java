package ch.diso.llqa.db.migration.processor.CheckCheckdata;

import ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto;
import ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckContainerDto;
import ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckDto;
import ch.diso.llqa.db.migration.dto.LlqaDto;
import ch.diso.llqa.db.migration.processor.JsonProcessorTest;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto.RegistrationMode.MAY_PARTITION;
import static ch.diso.llqa.db.migration.processor.CheckCheckdata.CheckCheckdataItemProcessor.*;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CheckCheckdataItemProcessorTest extends JsonProcessorTest {
    private static AtomicLong WORKFLOW_ELEMENT_CHECK_ID_GENERATOR = new AtomicLong(0L);

    private static final String YAML_NODE_NAME = "yaml";
    private static final String ASSIGNEES_NODE_NAME = "assignees";

    private static final String TEST_DATA_POSITIVE = "CheckCheckdataTestData_01.json";

    @Mock
    private JdbcTemplate jdbcTemplate;
    @Mock
    private CheckCheckdataAssigneeItemProcessor checkCheckdataAssigneeItemProcessor;
    @InjectMocks
    private CheckCheckdataItemProcessor testee;

    @Before
    public void init() {
        when(jdbcTemplate.queryForList(SELECT_SEQUENCE_WORKFLOW_ELEMENT_CHECK_PK)).thenAnswer(CheckCheckdataItemProcessorTest::generateWorkflowEltCheckPkRow);
    }

    public static List<Map<String, Object>> generateWorkflowEltCheckPkRow(InvocationOnMock invocationOnMock) {
        return Collections.singletonList(ImmutableMap.of(WORKFLOW_ELEMENT_CHECK_PK_ALIAS, WORKFLOW_ELEMENT_CHECK_ID_GENERATOR.addAndGet(1)));
    }

    private List<CheckDataAssigneeDto> generateAssigneeMockData() {
        List<CheckDataAssigneeDto> mockedData = new ArrayList<>();
        final long checkId = 1;
        final long assigneeId = 1234L;
        mockedData.add(generateCheckDataAssigneeDto(assigneeId, checkId, 8L, null, MAY_PARTITION));
        return mockedData;
    }

    private CheckDataAssigneeDto generateCheckDataAssigneeDto(long pk, long checkId, Long userId, Long groupId, CheckDataAssigneeDto.RegistrationMode registrationMode) {
        CheckDataAssigneeDto dto = new CheckDataAssigneeDto();
        dto.setPk(pk);
        dto.setCheckId(checkId);
        dto.setUserId(userId);
        dto.setUserGroupId(groupId);
        dto.setRegistrationMode(registrationMode);
        return dto;
    }

    @Test
    public void testPositive() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_POSITIVE);

        JsonNode yamlNode = testData.get(YAML_NODE_NAME);
        JsonNode assigneesNode = this.testee.generateAssigneesNode(1L, yamlNode.get(ASSIGNEES_NODE_NAME));
        when(checkCheckdataAssigneeItemProcessor.process(assigneesNode)).thenReturn(generateAssigneeMockData());

        // when
        WorkflowElementCheckContainerDto checkData = this.testee.process(testData);

        // then
        assertThat("CHECK DATA COULD NOT BE PARSED", checkData, notNullValue());
        assertThat("ASSIGNEES MOCK DATA NOT INITIALIZED", checkData.getAssignees(), notNullValue());
        assertThat("ASSIGNEES MOCK DATA NOT AS EXPECTED", checkData.getAssignees().size(), is(1));
        assertThat("NO WORKFLOW ELEMENTS RETURNED", checkData.getWorkflowElementCheckDtos(), notNullValue());
        assertThat("CLOSED BY NOT AS EXPECTED", checkData.getClosedBy(), is(11L));
        assertThat("THE NUMBER OF WORKFLOW ELEMENT CHECKS IS NOT AS EXPECTED", checkData.getWorkflowElementCheckDtos().size(), is(7));
        final long numWfEltEmptyPk = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getPk() == null)
                .count();
        assertThat("THERE ARE SOME WORKFLOW ELEMENT CHECKS WITH AN INVALID PRIMARY KEY", numWfEltEmptyPk, is(0L));
        final long numDistinctWfEltPk = checkData.getWorkflowElementCheckDtos().stream()
                .mapToLong(LlqaDto::getPk)
                .distinct()
                .count();
        assertThat("THE NUMBER OF DISTINCT WORKFLOW ELEMENT PRIMARY KEYS IS NOT AS EXPECTED", numDistinctWfEltPk, is(7L));
        final long numWfEltEmptyWorkflowEltState = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getState() == null)
                .count();
        assertThat("THERE ARE SOME WORKFLOW ELEMENT CHECKS HAVE NO INITIALIZED STATE", numWfEltEmptyWorkflowEltState, is(0L));
        final long numCommitedByWorkflowElts = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getCommitedBy() != null)
                .count();
        assertThat("THE NUMBER OF WORKFLOW ELEMENT CHECKS THAT HAVE A COMMITTED BY IS NOT AS EXPECTED", numCommitedByWorkflowElts, is(1L));
        OptionalLong stepCommitedBy_3091 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.STEP.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(3091L).equals(wfElt.getChildId()))
                .mapToLong(WorkflowElementCheckDto::getCommitedBy)
                .findFirst();
        assertThat("THE STEP WITH ID >3091< HAS NO >COMMITTED BY<", stepCommitedBy_3091.isPresent(), is(true));
        assertThat("THE STEP WITH ID >3091< COMMITTED BY NOT AS EXPECTED", stepCommitedBy_3091.getAsLong(), is(36L));
        final long numLockedWorkflowElts = checkData.getWorkflowElementCheckDtos().stream()
                .filter(WorkflowElementCheckDto::isLocked)
                .count();
        assertThat("THE NUMBER OF WORKFLOW ELEMENT CHECKS THAT ARE LOCKED IS NOT AS EXPECTED", numLockedWorkflowElts, is(2L));
        Optional<Boolean> stepLocked_3092 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.STEP.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(3092L).equals(wfElt.getChildId()))
                .map(WorkflowElementCheckDto::isLocked)
                .findFirst();
        Optional<Boolean> measureLocked_10662 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.MEASURE.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(10662L).equals(wfElt.getChildId()))
                .map(WorkflowElementCheckDto::isLocked)
                .findFirst();
        assertThat("THE STEP LOCKED FLAG WITH ID >3092< IS NOT AS EXPECTED", stepLocked_3092.get(), is(Boolean.TRUE));
        assertThat("THE MEASURE LOCKED FLAG WITH ID >10662< IS NOT AS EXPECTED", measureLocked_10662.get(), is(Boolean.TRUE));

        OptionalInt procedureState_977 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.PROCEDURE.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(977L).equals(wfElt.getChildId()))
                .mapToInt(WorkflowElementCheckDto::getState)
                .findFirst();
        OptionalInt stepState_3091 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.STEP.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(3091L).equals(wfElt.getChildId()))
                .mapToInt(WorkflowElementCheckDto::getState)
                .findFirst();
        OptionalInt measureState_10662 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.MEASURE.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(10662L).equals(wfElt.getChildId()))
                .mapToInt(WorkflowElementCheckDto::getState)
                .findFirst();
        OptionalInt measureState_10663 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.MEASURE.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(10663L).equals(wfElt.getChildId()))
                .mapToInt(WorkflowElementCheckDto::getState)
                .findFirst();
        OptionalInt stepState_3092 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.STEP.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(3092L).equals(wfElt.getChildId()))
                .mapToInt(WorkflowElementCheckDto::getState)
                .findFirst();
        OptionalInt measureState_10664 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.MEASURE.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(10664L).equals(wfElt.getChildId()))
                .mapToInt(WorkflowElementCheckDto::getState)
                .findFirst();
        OptionalInt measureState_10665 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.MEASURE.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(10665L).equals(wfElt.getChildId()))
                .mapToInt(WorkflowElementCheckDto::getState)
                .findFirst();
        assertThat("THE PROCEDURE STATE WITH ID >977< IS NOT AS EXPECTED", procedureState_977.getAsInt(), is(13));
        assertThat("THE STEP STATE WITH ID >3091< IS NOT AS EXPECTED", stepState_3091.getAsInt(), is(10));
        assertThat("THE MEASURE STATE WITH ID >10662< IS NOT AS EXPECTED", measureState_10662.getAsInt(), is(10));
        assertThat("THE MEASURE STATE WITH ID >10663< IS NOT AS EXPECTED", measureState_10663.getAsInt(), is(10));
        assertThat("THE STEP STATE WITH ID >3092< IS NOT AS EXPECTED", stepState_3092.getAsInt(), is(13));
        assertThat("THE MEASURE STATE WITH ID >10664< IS NOT AS EXPECTED", measureState_10664.getAsInt(), is(13));
        assertThat("THE MEASURE STATE WITH ID >10665< IS NOT AS EXPECTED", measureState_10665.getAsInt(), is(0));

        final long numRemainingTimeWorkflowElts = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getRemainingTime() != null)
                .count();
        assertThat("THE NUMBER OF WORKFLOW ELEMENT THAT HAVE A ATTRIBUTE >REMAINING TIME< IS NOT AS EXPECTED", numRemainingTimeWorkflowElts, is(1L));
        OptionalLong remainingTime_977 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> WorkflowElementCheckDto.WorkflowElementType.PROCEDURE.equals(wfElt.getWorkflowElementType()))
                .filter(wfElt -> new Long(977L).equals(wfElt.getChildId()))
                .mapToLong(WorkflowElementCheckDto::getRemainingTime)
                .findFirst();
        assertThat("THE PROCEDURE WITH ID >977< HAS A REMAINING TIME NOT AS EXPECTED", remainingTime_977.getAsLong(), is(8973L));

        OptionalLong measureMeasurementId_10662 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getMeasurementId() != null)
                .filter(wfElt -> new Long(10662L).equals(wfElt.getChildId()))
                .mapToLong(WorkflowElementCheckDto::getMeasurementId)
                .findFirst();
        OptionalLong measureMeasurementId_10663 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getMeasurementId() != null)
                .filter(wfElt -> new Long(10663L).equals(wfElt.getChildId()))
                .mapToLong(WorkflowElementCheckDto::getMeasurementId)
                .findFirst();
        OptionalLong measureMeasurementId_10664 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getMeasurementId() != null)
                .filter(wfElt -> new Long(10664L).equals(wfElt.getChildId()))
                .mapToLong(WorkflowElementCheckDto::getMeasurementId)
                .findFirst();
        OptionalLong measureMeasurementId_10665 = checkData.getWorkflowElementCheckDtos().stream()
                .filter(wfElt -> wfElt.getMeasurementId() != null)
                .filter(wfElt -> new Long(10665L).equals(wfElt.getChildId()))
                .mapToLong(WorkflowElementCheckDto::getMeasurementId)
                .findFirst();
        assertThat("THE MEASUREMENT ID FOR THE MEASURE WITH ID >10662< IS NOT AS EXPECTED", measureMeasurementId_10662.getAsLong(), is(16446l));
        assertThat("THE MEASUREMENT ID FOR THE MEASURE WITH ID >10663< IS NOT AS EXPECTED", measureMeasurementId_10663.getAsLong(), is(16447l));
        assertThat("THE MEASUREMENT ID FOR THE MEASURE WITH ID >10664< IS NOT AS EXPECTED", measureMeasurementId_10664.getAsLong(), is(16448l));
        assertThat("THE MEASUREMENT ID FOR THE MEASURE WITH ID >10665< SHOULD NOT BE AVAILABLE", measureMeasurementId_10665.isPresent(), is(false));
    }
}