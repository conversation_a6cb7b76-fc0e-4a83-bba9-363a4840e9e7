package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.TagValuesValuesDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.assertThat;

public class TagValuesItemProcessorTest extends JsonProcessorTest{

    private static final String TEST_DATA_FILE_01 = "SettingsSubValuesProcessorTestData_01.json";

    @Test
    public void process() {
        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_FILE_01);

        // when
        TagValuesItemProcessor testee = new TagValuesItemProcessor();
        List<TagValuesValuesDto> result = testee.process(testData);

        // then
        assertThat(result, hasSize(2));
        assertThat(result.get(0).getTagValue(), is("{\"de\":\"MAC\",\"fr\":\"MAC\"}"));
        assertThat(result.get(1).getTagValue(), is("{\"de\":\"StepTec\",\"fr\":\"StepTec\"}"));
    }
}