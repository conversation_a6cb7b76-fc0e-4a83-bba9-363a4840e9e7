package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.WorkflowRuleDto;
import ch.diso.llqa.db.migration.processor.flowcontrol.FlowcontrolItemProcessor;
import ch.diso.llqa.db.migration.processor.flowcontrol.WorkflowRuleWorkflowGroupLoader.WorkflowRuleWorkflowGroupLoader;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.collection.IsCollectionWithSize.hasSize;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

public abstract class FlowcontrolItemProcessorTest extends JsonProcessorTest {

    protected static final long CHILD_PK = 1l;
    protected static final long WORKFLOW_ELT_PK = 1l;

    protected static String TYPE_PROCEDURES = "P";
    protected static String TYPE_STEPS = "S";
    protected static String TYPE_MEASURES = "M";
    protected static String TYPE_MODEL = "MD";
    protected static String TYPE_DEVICE_TYPE = "DT";
    protected static String TYPE_CHECK_TYPE = "CT";
    protected static String TYPE_CONFIG_ENTRY = "CO";

    protected static final long PROCEDURE_GROUP_PK = 1l;
    protected static final long MODEL_GROUP_PK = 1l;
    protected static final long DEVICE_TYPE_GROUP_PK = 1l;
    protected static final long CHECK_TYPE_GROUP_PK = 1l;
    protected static final long CONFIG_ENTRY_GROUP_PK = 1l;
    protected static final long STEP_GROUP_PK = 1l;
    protected static final long MEASURE_GROUP_PK = 1l;

    protected static String PROCEDURE_CODE = "1";
    protected static String STEP_CODE = "1";
    protected static String MEASURE_CODE = "1.1";
    protected static String MODEL_CODE = "MD01";
    protected static String DEVICE_TYPE_CODE = "DT01";
    protected static String CHECK_TYPE_CODE = "CT01";
    protected static String CONFIG_ENTRY_CODE = "1.1";

    @Mock
    protected JdbcTemplate jdbcTemplate;
    @Mock
    protected ApplicationContext applicationContext;
    @InjectMocks
    protected FlowcontrolItemProcessor testee;

    @Before
    public void init() {
        this.testee.setChildTableName(this.getTablename());
        this.initSelectWorkflowElementMock();
        this.initWorkflowRuleGroupLoaderMock();
        this.initWorkflowRulesLoaderMock();
    }

    /**
     * initialze the mock for the DB select of the workflow elements
     */
    protected void initSelectWorkflowElementMock() {
        String selectWorkflowElementSql = this.testee.generateSelectWorkflowElementSql(CHILD_PK);
        Map<String, Object> row = new HashMap<>();
        row.put(FlowcontrolItemProcessor.SQL_WF_ELT_ID, new Long(WORKFLOW_ELT_PK));
        List<Map<String, Object>> rows = Arrays.asList(row);
        when(jdbcTemplate.queryForList(selectWorkflowElementSql)).thenReturn(rows);
    }

    /**
     * initialize the workflow rule group loader mock
     */
    protected abstract void initWorkflowRuleGroupLoaderMock();

    /**
     * initialize the mock for the DB selects of implementations from {@link WorkflowRuleWorkflowGroupLoader}
     */
    protected abstract void initWorkflowRulesLoaderMock();


    /**
     * initialize the select query to load the destination of the workflow rules
     *
     * @param sql          sql
     * @param grpIdColName the column name from the selected primary key
     * @param mockedPk     the mocked primary key
     */
    protected void initWorkflowRulesLoaderMock(String sql, String grpIdColName, Long mockedPk) {
        when(jdbcTemplate.queryForList(sql)).thenReturn(this.generateWorkflowGroupMockResult(grpIdColName, mockedPk));
    }

    /**
     * get the mock result for a select query of a workflow group
     *
     * @param grpColIdName the column name from the id
     * @param groupPk      the value from the returend primary key
     * @return the result that can be mocked
     */
    protected List<Map<String, Object>> generateWorkflowGroupMockResult(String grpColIdName, Long groupPk) {
        Map<String, Object> producerRow = new HashMap<>();
        producerRow.put(grpColIdName, groupPk);
        return Arrays.asList(producerRow);
    }

    @Test
    public void generateDto() {
        // given
        JsonNode testData = super.getTestDatas(this.getTestDataFile());
        this.testee.setSelectRealIdSqlFormat(this.getSelectRealIdSql());

        // when
        List<WorkflowRuleDto> workflowRules = this.testee.process(testData);

        // then
        assertThat("NO WORKFLOW RULES RECEIVED ==> NULL VALUE RETURNED", workflowRules, notNullValue());
        assertThat("NUMBER OF WORKFLOW RULES NOT AS EXPECTED", workflowRules, hasSize(this.getNumberOfExpectedWorkflowRules()));

        Set<Long> rulePks = workflowRules.stream()
                .map(rule -> rule.getPk())
                .collect(Collectors.toSet());
        assertThat("RULES DO NOT BELONG TO THE SAME WORKFLOW ELEMENT. NUMBER OF WORKFLOW ELEMENTS NOT AS EXPECTED", rulePks.size(), is(1));

        // test virtual in the inherited class
        this.testWorkflowRuleContent(workflowRules);
    }

    /**
     * get the filename for the test data
     *
     * @return the filename for the test data
     */
    protected abstract String getTestDataFile();

    /**
     * get the number of expected workflow rules
     *
     * @return the number of expected workflow rules
     */
    protected abstract int getNumberOfExpectedWorkflowRules();

    /**
     * test the content of the workflow rules array
     */
    protected abstract void testWorkflowRuleContent(List<WorkflowRuleDto> workflowRules);

    /**
     * @return
     */
    protected abstract String getTablename();

    protected abstract String getSelectRealIdSql();
}