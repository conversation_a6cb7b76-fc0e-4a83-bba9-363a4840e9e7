package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.ModelsMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ch.diso.llqa.db.migration.processor.ModelsMetadataItemProcessor.COLUMN_ID;
import static ch.diso.llqa.db.migration.processor.ModelsMetadataItemProcessor.SQL_SELECT;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModelsMetadataItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_01 = "ModelsMetadataTestData_01.json";

    private static final String TEST_DATA_FILE_02 = "ModelsMetadataTestData_02.json";

    @InjectMocks
    private ModelsMetadataItemProcessor testee;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Test
    public void process() {
        // given
        JsonNode jsonNode = super.getTestDatas(TEST_DATA_FILE_01);
        when(jdbcTemplate.queryForList(String.format(SQL_SELECT, 7, 43))).thenReturn(createQueryResult(1L));
        when(jdbcTemplate.queryForList(String.format(SQL_SELECT, 2, 43))).thenReturn(createQueryResult(2L));
        when(jdbcTemplate.queryForList(String.format(SQL_SELECT, 1, 43))).thenReturn(createQueryResult(3L));
        when(jdbcTemplate.queryForList(String.format(SQL_SELECT, 3, 43))).thenReturn(createQueryResult(4L));

        // when
        List<ModelsMetadataDto> result = testee.process(jsonNode);

        // then
        assertThat(result, hasSize(5));
        result.sort((o1, o2) -> o1.getActiveProcedureId() < o2.getActiveProcedureId() ? -1 : 1);
        assertDto(result.get(0), 1L, 1L, 8L, null);
        assertDto(result.get(1), 1L, 10L, 12L, null);
        assertDto(result.get(2), 2L, 1L, 9L, null);
        assertDto(result.get(3), 3L, 1L, 9L, 4L);
        assertDto(result.get(4), 4L, 1L, 9L, null);

    }

    @Test
    public void processWithNoActiveProcedure() {
        // given
        JsonNode jsonNode = super.getTestDatas(TEST_DATA_FILE_02);
        when(jdbcTemplate.queryForList(String.format(SQL_SELECT, 7, 43))).thenReturn(Lists.newArrayList(createQueryResult(0L)));

        // when
        List<ModelsMetadataDto> result = testee.process(jsonNode);

        // then
        assertThat(result, hasSize(0));
    }

    private List<Map<String, Object>> createQueryResult(long activeProcedureId) {
        return Arrays.asList(ImmutableMap.of(COLUMN_ID, activeProcedureId));
    }

    private void assertDto(ModelsMetadataDto dto, Long... values) {
        assertThat(dto.getActiveProcedureId(), is(values[0]));
        assertThat(dto.getCheckTypeId(), is(values[1]));
        assertThat(dto.getGroupId(), is(values[2]));
        assertThat(dto.getUserId(), is(values[3]));
    }
}