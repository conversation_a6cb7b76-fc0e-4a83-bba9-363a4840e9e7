package ch.diso.llqa.db.migration.rowmapper;

import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnContentsDto;
import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnHeadersDto;
import ch.diso.llqa.db.migration.rowMapper.ConfigColumnsRowMapper;
import ch.diso.llqa.db.migration.step.StepConfigColumnContents;
import org.junit.Test;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ConfigColumnContensRowMapperTest {

    private final long ID = 1;

    private final String COLUMN_1 = null;
    private final String COLUMN_2 = "Spezial Geometrie";
    private final String COLUMN_3 = "0081";
    private final String COLUMN_4 = "Sonder";
    private final String COLUMN_5 = null;
    private final String COLUMN_6 = null;

    @Test
    public void testMapRow() throws SQLException {

        //given
        ConfigColumnsRowMapper<ConfigColumnContentsDto> rowMapper = new ConfigColumnsRowMapper<>();
        rowMapper.setType(ConfigColumnContentsDto.class);
        rowMapper.setColumnNames(StepConfigColumnContents.CONFIG_TABLE_COLUMN_NAMES);

        ResultSet resultSet = createResultSet();
        int line = 1;

        ConfigColumnContentsDto dto2 = new ConfigColumnContentsDto();
        dto2.setConfigId(ID);
        dto2.setConfigColHeaderId(ID);
        dto2.setContent(COLUMN_2);
        dto2.setSeqnum(2);

        ConfigColumnContentsDto dto3 = new ConfigColumnContentsDto();
        dto3.setConfigId(ID);
        dto3.setConfigColHeaderId(ID);
        dto3.setContent(COLUMN_3);
        dto3.setSeqnum(3);

        ConfigColumnContentsDto dto4 = new ConfigColumnContentsDto();
        dto4.setConfigId(ID);
        dto4.setConfigColHeaderId(ID);
        dto4.setContent(COLUMN_4);
        dto4.setSeqnum(4);

        //when
        List<? extends ConfigColumnHeadersDto>  dtos = rowMapper.mapRow(resultSet, line);

        //then
        assertNotNull("ROWMAPPER RETURNED A NULL OBJECT", dtos);
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 6, dtos.size());

        assertTrue("CONTENT_2 OBJECT NOT EQUALS", dtos.stream()
                .map(c -> (ConfigColumnContentsDto) c)
                .anyMatch(actualDto2-> actualDto2.getContent() == dto2.getContent() &&
                        actualDto2.getSeqnum() == dto2.getSeqnum() &&
                        actualDto2.getConfigId() == dto2.getConfigId() &&
                        actualDto2.getConfigColHeaderId() == dto2.getConfigColHeaderId()));

        assertTrue("CONTENT_3 OBJECT NOT EQUALS", dtos.stream()
                .map(c -> (ConfigColumnContentsDto) c)
                .anyMatch(actualDto3-> actualDto3.getContent() == dto3.getContent() &&
                        actualDto3.getSeqnum() == dto3.getSeqnum() &&
                        actualDto3.getConfigId() == dto3.getConfigId() &&
                        actualDto3.getConfigColHeaderId() == dto3.getConfigColHeaderId()));

        assertTrue("CONTENT_4 OBJECT NOT EQUALS", dtos.stream()
                .map(c -> (ConfigColumnContentsDto) c)
                .anyMatch(actualDto4-> actualDto4.getContent() == dto4.getContent() &&
                        actualDto4.getSeqnum() == dto4.getSeqnum() &&
                        actualDto4.getConfigId() == dto4.getConfigId() &&
                        actualDto4.getConfigColHeaderId() == dto4.getConfigColHeaderId()));
    }

    private ResultSet createResultSet() throws SQLException{

        ResultSet rs = mock(ResultSet.class);
        when(rs.getLong("id")).thenReturn(ID);
        when(rs.getLong("configtable_id")).thenReturn(ID);
        when(rs.getString("col1")).thenReturn(COLUMN_1);
        when(rs.getString("col2")).thenReturn(COLUMN_2);
        when(rs.getString("col3")).thenReturn(COLUMN_3);
        when(rs.getString("col4")).thenReturn(COLUMN_4);
        when(rs.getString("col5")).thenReturn(COLUMN_5);
        when(rs.getString("col6")).thenReturn(COLUMN_6);
        return  rs;
    }
}
