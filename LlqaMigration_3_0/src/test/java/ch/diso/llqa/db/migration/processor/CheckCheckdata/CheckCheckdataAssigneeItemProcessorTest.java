package ch.diso.llqa.db.migration.processor.CheckCheckdata;

import ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto;
import ch.diso.llqa.db.migration.processor.JsonProcessorTest;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto.RegistrationMode.*;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

@RunWith(MockitoJUnitRunner.class)
public class CheckCheckdataAssigneeItemProcessorTest extends JsonProcessorTest {

    private static String ASSIGNEES_TEST_DATA_POSITIVE = "CheckCheckdataAssigneesTestData_01.json";
    private static String ASSIGNEES_TEST_DATA_NEGATIVE_WRONG_TYPE = "CheckCheckdataAssigneesTestData_02.json";
    private static String ASSIGNEES_TEST_DATA_NEGATIVE_INVALID_REGISTRATION_MODE = "CheckCheckdataAssigneesTestData_03.json";
    private static String ASSIGNEES_TEST_DATA_NEGATIVE_INVALID_SUBOF_IDX = "CheckCheckdataAssigneesTestData_04.json";

    @InjectMocks
    private CheckCheckdataAssigneeItemProcessor testee;

    @Before
    public void init() {
    }

    @Test
    public void parseAssigneesPositive() {
        // given
        final long checkId = 1l;
        JsonNode testData = super.getTestDatas(ASSIGNEES_TEST_DATA_POSITIVE);

        // when
        List<CheckDataAssigneeDto> assignees = this.testee.process(testData);

        // then
        assertThat("ASSIGNEES COULD NOT BE PARSED", assignees, notNullValue());
        assertThat("NUMBER OF RECEIVED ASSIGNEES NOT AS EXPECTED", assignees.size(), is(8));

        Optional<CheckDataAssigneeDto> assignee_null_15_2 = assignees.stream()
                .filter(assignee -> assignee.getCheckId() == checkId)
                .filter(assignee -> assignee.getUserId() == null)
                .filter(assignee -> new Long(15).equals(assignee.getUserGroupId()))
                .filter(assignee -> MAY_PARTITION.equals(assignee.getRegistrationMode()))
                .findAny();
        Optional<CheckDataAssigneeDto> assignee_null_14_2 = assignees.stream()
                .filter(assignee -> assignee.getCheckId() == checkId)
                .filter(assignee -> assignee.getUserId() == null)
                .filter(assignee -> new Long(14).equals(assignee.getUserGroupId()))
                .filter(assignee -> MAY_PARTITION.equals(assignee.getRegistrationMode()))
                .findAny();
        Optional<CheckDataAssigneeDto> assignee_9_null_3 = assignees.stream()
                .filter(assignee -> assignee.getCheckId() == checkId)
                .filter(assignee -> new Long(9).equals(assignee.getUserId()))
                .filter(assignee -> assignee.getUserGroupId() == null)
                .filter(assignee -> MAY_PARTITION.equals(assignee.getRegistrationMode()))
                .findAny();
        Optional<CheckDataAssigneeDto> assignee_null_8_3 = assignees.stream()
                .filter(assignee -> assignee.getCheckId() == checkId)
                .filter(assignee -> assignee.getUserId() == null)
                .filter(assignee -> new Long(8).equals(assignee.getUserGroupId()))
                .filter(assignee -> MUST_PARTITION.equals(assignee.getRegistrationMode()))
                .findAny();
        Optional<CheckDataAssigneeDto> assignee_null_10_2 = assignees.stream()
                .filter(assignee -> assignee.getCheckId() == checkId)
                .filter(assignee -> assignee.getUserId() == null)
                .filter(assignee -> new Long(10).equals(assignee.getUserGroupId()))
                .filter(assignee -> MAY_PARTITION.equals(assignee.getRegistrationMode()))
                .findAny();
        Optional<CheckDataAssigneeDto> assignee_null_8_2 = assignees.stream()
                .filter(assignee -> assignee.getCheckId() == checkId)
                .filter(assignee -> assignee.getUserId() == null)
                .filter(assignee -> new Long(8).equals(assignee.getUserGroupId()))
                .filter(assignee -> MAY_PARTITION.equals(assignee.getRegistrationMode()))
                .findAny();
        Optional<CheckDataAssigneeDto> assignee_31_null_4 = assignees.stream()
                .filter(assignee -> assignee.getCheckId() == checkId)
                .filter(assignee -> new Long(31).equals(assignee.getUserId()))
                .filter(assignee -> assignee.getUserGroupId() == null)
                .filter(assignee -> APPENDED.equals(assignee.getRegistrationMode()))
                .findAny();

        assertThat("NO ASSIGNEE RETURNED FOR GROUP >15< AND REGISTRATION MODE >MAY PARTITION<", assignee_null_15_2.isPresent(), is(true));
        assertThat("NO ASSIGNEE RETURNED FOR GROUP >14< AND REGISTRATION MODE >MAY PARTITION<", assignee_null_14_2.isPresent(), is(true));
        assertThat("NO ASSIGNEE RETURNED FOR USER >9< AND REGISTRATION MODE >MAY PARTITION<", assignee_9_null_3.isPresent(), is(true));
        assertThat("NO ASSIGNEE RETURNED FOR GROUP >10< AND REGISTRATION MODE >MUST PARTITION<", assignee_null_8_3.isPresent(), is(true));
        assertThat("NO ASSIGNEE RETURNED FOR GROUP >8< AND REGISTRATION MODE >MAY PARTITION<", assignee_null_10_2.isPresent(), is(true));
        assertThat("NO ASSIGNEE RETURNED FOR GROUP >8< AND REGISTRATION MODE >MAY PARTITION<", assignee_null_8_2.isPresent(), is(true));
        assertThat("NO ASSIGNEE RETURNED FOR USER >31< AND REGISTRATION MODE >APPENDED<", assignee_31_null_4.isPresent(), is(true));

        CheckDataAssigneeDto assignee_null_15_2Dto = assignee_null_15_2.get();
        CheckDataAssigneeDto assignee_null_14_2Dto = assignee_null_14_2.get();
        CheckDataAssigneeDto assignee_9_null_3Dto = assignee_9_null_3.get();
        CheckDataAssigneeDto assignee_null_8_3Dto = assignee_null_8_3.get();
        CheckDataAssigneeDto assignee_null_10_2Dto = assignee_null_10_2.get();
        CheckDataAssigneeDto assignee_null_8_2Dto = assignee_null_8_2.get();
        CheckDataAssigneeDto assignee_31_null_4Dto = assignee_31_null_4.get();

        assertThat("NO PRIMARIY KEY SET FOR ASSIGNEE GROUP >15< AND REGISTRATION MODE >MAY PARTITION<", assignee_null_15_2Dto.getPk(), notNullValue());
        assertThat("NO PRIMARIY KEY SET FOR ASSIGNEE GROUP >14< AND REGISTRATION MODE >MAY PARTITION<", assignee_null_14_2Dto.getPk(), notNullValue());
        assertThat("NO PRIMARIY KEY SET FOR ASSIGNEE USER >9< AND REGISTRATION MODE >MAY PARTITION<", assignee_9_null_3Dto.getPk(), notNullValue());
        assertThat("NO PRIMARIY KEY SET FOR ASSIGNEE GROUP >10< AND REGISTRATION MODE >MUST PARTITION<", assignee_null_8_3Dto.getPk(), notNullValue());
        assertThat("NO PRIMARIY KEY SET FOR ASSIGNEE GROUP >8< AND REGISTRATION MODE >MAY PARTITION< ", assignee_null_10_2Dto.getPk(), notNullValue());
        assertThat("NO PRIMARIY KEY SET FOR ASSIGNEE GROUP >8< AND REGISTRATION MODE >MAY PARTITION<", assignee_null_8_2Dto.getPk(), notNullValue());
        assertThat("NO PRIMARIY KEY SET FOR ASSIGNEE USER >31< AND REGISTRATION MODE >APPENDED<", assignee_31_null_4Dto.getPk(), notNullValue());

        //assertThat("ASSIGNEE FOR GROUP >15< AND REGISTRATION MODE >MAY PARTITION< HAS NOT THE EXCEPTED NUMBER OF BLOCKS", assignee_null_15_2Dto.getCheckDataAssigneeBlockDtoList().size(), is(2));
        //assertThat("ASSIGNEE FOR GROUP >14< AND REGISTRATION MODE >MAY PARTITION< HAS NOT THE EXCEPTED NUMBER OF BLOCKS", assignee_null_14_2Dto.getCheckDataAssigneeBlockDtoList().size(), is(1));
        //assertThat("ASSIGNEE FOR USER >9< AND REGISTRATION MODE >MAY PARTITION< HAS NOT THE EXCEPTED NUMBER OF BLOCKS", assignee_9_null_3Dto.getCheckDataAssigneeBlockDtoList().size(), is(1));
        //assertThat("ASSIGNEE FOR GROUP >10< AND REGISTRATION MODE >MUST PARTITION< HAS NOT THE EXCEPTED NUMBER OF BLOCKS", assignee_null_8_3Dto.getCheckDataAssigneeBlockDtoList().size(), is(1));
        //assertThat("ASSIGNEE FOR GROUP >8< AND REGISTRATION MODE >MAY PARTITION< HAS NOT THE EXCEPTED NUMBER OF BLOCKS", assignee_null_10_2Dto.getCheckDataAssigneeBlockDtoList().size(), is(1));
        //assertThat("ASSIGNEE FOR GROUP >8< AND REGISTRATION MODE >MAY PARTITION< HAS NOT THE EXCEPTED NUMBER OF BLOCKS", assignee_null_8_2Dto.getCheckDataAssigneeBlockDtoList().size(), is(1));
        //assertThat("ASSIGNEE FOR USER >31< AND REGISTRATION MODE >APPENDED< HAS NOT THE EXCEPTED NUMBER OF BLOCKS", assignee_31_null_4Dto.getCheckDataAssigneeBlockDtoList().size(), is(1));
//
        //CheckDataAssigneeBlockDto assigneeBlock_00 = assignee_null_15_2Dto.getCheckDataAssigneeBlockDtoList().get(0);
        //CheckDataAssigneeBlockDto assigneeBlock_01 = assignee_null_15_2Dto.getCheckDataAssigneeBlockDtoList().get(1);
        //CheckDataAssigneeBlockDto assigneeBlock_02 = assignee_null_14_2Dto.getCheckDataAssigneeBlockDtoList().get(0);
        //CheckDataAssigneeBlockDto assigneeBlock_03 = assignee_9_null_3Dto.getCheckDataAssigneeBlockDtoList().get(0);
        //CheckDataAssigneeBlockDto assigneeBlock_04 = assignee_null_8_3Dto.getCheckDataAssigneeBlockDtoList().get(0);
        //CheckDataAssigneeBlockDto assigneeBlock_05 = assignee_null_10_2Dto.getCheckDataAssigneeBlockDtoList().get(0);
        //CheckDataAssigneeBlockDto assigneeBlock_06 = assignee_null_8_2Dto.getCheckDataAssigneeBlockDtoList().get(0);
        //CheckDataAssigneeBlockDto assigneeBlock_07 = assignee_31_null_4Dto.getCheckDataAssigneeBlockDtoList().get(0);
//
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK GROUP >15<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_00.isRegistered(), is(false));
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK GROUP >15<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_01.isRegistered(), is(false));
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK GROUP >14<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_02.isRegistered(), is(false));
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK USER >9<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_03.isRegistered(), is(false));
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK GROUP >8<, REGISTRATION MODE >MUST PARTITION< NOT AS EXPECTED", assigneeBlock_04.isRegistered(), is(false));
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK GROUP >10<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_05.isRegistered(), is(false));
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK GROUP >8<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_06.isRegistered(), is(false));
        //assertThat("SELF ASSIGN FOR ASSIGNEE BLOCK USER >31<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_07.isRegistered(), is(true));
//
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK GROUP >15<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_00.getAssigneeBlockId(), nullValue());
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK GROUP >15<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_01.getAssigneeBlockId(), is(assigneeBlock_06.getPk()));
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK GROUP >14<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_02.getAssigneeBlockId(), nullValue());
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK USER >9<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_03.getAssigneeBlockId(), nullValue());
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK GROUP >8<, REGISTRATION MODE >MUST PARTITION< NOT AS EXPECTED", assigneeBlock_04.getAssigneeBlockId(), nullValue());
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK GROUP >10<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_05.getAssigneeBlockId(), is(assigneeBlock_04.getPk()));
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK GROUP >8<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_06.getAssigneeBlockId(), nullValue());
        //assertThat("REFERENCE BLOCK ID ASSIGNEE BLOCK USER >31<, REGISTRATION MODE >MAY PARTITION< NOT AS EXPECTED", assigneeBlock_07.getAssigneeBlockId(), is(assigneeBlock_01.getPk()));
    }

    @Test
    public void parseAssigneesNegativeWrongType() {
        // given
        JsonNode testData = super.getTestDatas(ASSIGNEES_TEST_DATA_NEGATIVE_WRONG_TYPE);

        try {
            // when
            this.testee.process(testData);

            // then
            fail("NO EXCEPTION HAS BEEN THROWN. TEST DATA ARE CORRUPT. PARSING SHOULD THROW AN EXCEPTION.");
        } catch (RuntimeException ex) {
        }
    }

    @Test
    public void parseAssigneesNegativeInvalidRegMode() {
        // given
        JsonNode testData = super.getTestDatas(ASSIGNEES_TEST_DATA_NEGATIVE_INVALID_REGISTRATION_MODE);
        // when
        List<CheckDataAssigneeDto> assignees = this.testee.process(testData);

        // then
        assertThat("NO ASSIGNEES RECEIVED", assignees, notNullValue());
        assertThat("NUMBER OF RECEIVED ASSIGNEES NOT AS EXPECTED", assignees.size(), is(1));
        assertThat("NUMBER OF RECEIVED ASSIGNEES NOT AS EXPECTED", assignees.get(0).getRegistrationMode(), nullValue());
    }

    @Test
    public void parseAssigneesNegativeInvalidSubofIdx() {
        // given
        JsonNode testData = super.getTestDatas(ASSIGNEES_TEST_DATA_NEGATIVE_INVALID_SUBOF_IDX);

        try {
            // when
            this.testee.process(testData);

            // then
            fail("NO EXCEPTION HAS BEEN THROWN. TEST DATA ARE CORRUPT (INVALID SUBOF INDEX). PARSING SHOULD THROW AN EXCEPTION.");
        } catch (RuntimeException ex) {
        }
    }
}