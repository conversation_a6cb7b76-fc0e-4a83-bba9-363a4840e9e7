package ch.diso.llqa.db.migration.rowmapper;

import ch.diso.llqa.db.migration.dto.WorkflowElementTagDto;
import ch.diso.llqa.db.migration.rowMapper.WorkflowElementTagsRowMapper;
import org.junit.Test;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


public class WorkflowElementTagsRowMapperTest {

    private final long ID = 0;
    private final String TAG_1 = "MAC";
    private final String TAG_2 = "Produktionsprozess";
    private final String TAG_3 = "Montage";
    private final String TAG_4 = null;
    private final String TAG_5 = "MILL S/X BG1";
    private final String TAG_6 = null;

    @Test
    public void testMapRow() throws SQLException {

        //given
        WorkflowElementTagsRowMapper workflowElementTagsRowMapper = new WorkflowElementTagsRowMapper();
        ResultSet workflowElementTagsResultSet = createResultSet();
        int line = 1;

        WorkflowElementTagDto tag_1 = new WorkflowElementTagDto();
        tag_1.setWorkflowElementId(ID);
        tag_1.setTag(TAG_1);
        tag_1.setPosition(1);

        WorkflowElementTagDto tag_2 = new WorkflowElementTagDto();
        tag_2.setWorkflowElementId(ID);
        tag_2.setTag(TAG_2);
        tag_2.setPosition(2);

        WorkflowElementTagDto tag_3 = new WorkflowElementTagDto();
        tag_3.setWorkflowElementId(ID);
        tag_3.setTag(TAG_3);
        tag_3.setPosition(3);

        WorkflowElementTagDto tag_5 = new WorkflowElementTagDto();
        tag_5.setWorkflowElementId(ID);
        tag_5.setTag(TAG_5);
        tag_5.setPosition(5);

        //when
        List<WorkflowElementTagDto>  workflowElementTagDtoList = workflowElementTagsRowMapper.mapRow(workflowElementTagsResultSet, line);

        //then
        assertNotNull("ROWMAPPER RETURNED A NULL OBJECT", workflowElementTagDtoList);
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 4, workflowElementTagDtoList.size());

        assertTrue("TAG_1 OBJECT NOT EQUALS", workflowElementTagDtoList.stream()
                .anyMatch(actualTag1-> actualTag1.getWorkflowElementId() == tag_1.getWorkflowElementId() &&
                        actualTag1.getPosition() == tag_1.getPosition() &&
                        actualTag1.getTag() == tag_1.getTag() ));

        assertTrue("TAG_2 OBJECT NOT EQUALS", workflowElementTagDtoList.stream()
                .anyMatch(actualTag2-> actualTag2.getWorkflowElementId() == tag_2.getWorkflowElementId() &&
                        actualTag2.getPosition() == tag_2.getPosition() &&
                        actualTag2.getTag() == tag_2.getTag() ));

        assertTrue("TAG_3 OBJECT NOT EQUALS", workflowElementTagDtoList.stream()
                .anyMatch(actualTag3-> actualTag3.getWorkflowElementId() == tag_3.getWorkflowElementId() &&
                        actualTag3.getPosition() == tag_3.getPosition() &&
                        actualTag3.getTag() == tag_3.getTag() ));

        assertTrue("TAG_5 OBJECT NOT EQUALS", workflowElementTagDtoList.stream()
                .anyMatch(actualTag5-> actualTag5.getWorkflowElementId() == tag_5.getWorkflowElementId() &&
                        actualTag5.getPosition() == tag_5.getPosition() &&
                        actualTag5.getTag() == tag_5.getTag() ));
    }

    private ResultSet createResultSet() throws SQLException{

        ResultSet rs = mock(ResultSet.class);
        when(rs.getLong("id")).thenReturn(ID);
        when(rs.getString("tag_1")).thenReturn(TAG_1);
        when(rs.getString("tag_2")).thenReturn(TAG_2);
        when(rs.getString("tag_3")).thenReturn(TAG_3);
        when(rs.getString("tag_4")).thenReturn(TAG_4);
        when(rs.getString("tag_5")).thenReturn(TAG_5);
        when(rs.getString("tag_6")).thenReturn(TAG_6);
        return  rs;
    }
}
