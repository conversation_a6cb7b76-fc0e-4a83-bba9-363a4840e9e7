package ch.diso.llqa.db.migration.processor.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsRawValueDto;
import ch.diso.llqa.db.migration.processor.JsonProcessorTest;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static junit.framework.TestCase.assertTrue;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertEquals;

public class MeasurementsRawValuesItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_01 = "MeasurementsRawValuesTestData_01.json";
    private static final String TEST_DATA_FILE_02 = "MeasurementsRawValuesTestData_02.json";
    private static final String TEST_DATA_FILE_03 = "MeasurementsRawValuesTestData_03.json";
    private static final String TEST_DATA_FILE_04 = "MeasurementsRawValuesTestData_04.json";


    @Test
    public void processTests() throws ParseException {

        processValuesTest();
        processSkipTest();
        processValueTest();
        processNullValueTest();
    }

    private void processValuesTest() throws ParseException {

        // given
        JsonNode testFileData = super.getTestDatas(TEST_DATA_FILE_01);

        // when
        MeasurementsRawValuesItemProcessor measurementsRawValuesItemProcessor = new MeasurementsRawValuesItemProcessor();
        List<MeasurementsRawValueDto> dtoList = measurementsRawValuesItemProcessor.process(testFileData);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dtoList, notNullValue());
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 24, dtoList.size());
    }


    private void processValueTest() throws ParseException {

        // given
        JsonNode testFileData = super.getTestDatas(TEST_DATA_FILE_02);

        // when
        MeasurementsRawValuesItemProcessor measurementsRawValuesItemProcessor = new MeasurementsRawValuesItemProcessor();
        List<MeasurementsRawValueDto> dtoList = measurementsRawValuesItemProcessor.process(testFileData);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dtoList, notNullValue());
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 1, dtoList.size());

        doAssert(dtoList, createValueShouldDtosTest());
    }


    private void processSkipTest() throws ParseException {

        // given
        JsonNode testFileData = super.getTestDatas(TEST_DATA_FILE_04);

        // when
        MeasurementsRawValuesItemProcessor measurementsRawValuesItemProcessor = new MeasurementsRawValuesItemProcessor();
        List<MeasurementsRawValueDto> dtoList = measurementsRawValuesItemProcessor.process(testFileData);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dtoList, notNullValue());
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 1, dtoList.size());

        doAssert(dtoList, createSkipShouldDtosTest());
    }


    private void processNullValueTest() throws ParseException {

        // given
        JsonNode testFileData = super.getTestDatas(TEST_DATA_FILE_03);

        // when
        MeasurementsRawValuesItemProcessor measurementsRawValuesItemProcessor = new MeasurementsRawValuesItemProcessor();
        List<MeasurementsRawValueDto> dtoList = measurementsRawValuesItemProcessor.process(testFileData);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dtoList, notNullValue());
        assertEquals("NUMBER OF DTOs NOT AS EXPECTED", 2, dtoList.size());

        doAssert(dtoList, createMatrciesNullValuesShouldDtosTest());
    }


    private void doAssert(List<MeasurementsRawValueDto> actualDtos, List<MeasurementsRawValueDto> shouldDtos) {

        Function<MeasurementsRawValueDto,List<Object>> assertKeys = ak -> Arrays.asList(ak.getMeasurementId(),
                                                                                        ak.getValue(),
                                                                                        ak.isSkip(),
                                                                                        ak.getTempMatricesCellId());

        assertTrue("MeasurementsRawValuesItem CONTENT NOT EQUALS",actualDtos.stream()
                .map(assertKeys)
                .collect(Collectors.toList())
                .equals(shouldDtos.stream()
                        .map(assertKeys)
                        .collect(Collectors.toList())));
    }

    private List<MeasurementsRawValueDto> createValueShouldDtosTest() throws ParseException {

        MeasurementsRawValueDto dto = new MeasurementsRawValueDto();
        dto.setMeasurementId(6955L);
        dto.setValue("4.6");
        return new ArrayList(){{ add(dto); }};
    }

    private List<MeasurementsRawValueDto> createSkipShouldDtosTest() throws ParseException {

        MeasurementsRawValueDto dto = new MeasurementsRawValueDto();
        dto.setMeasurementId(6955L);
        dto.setSkip(true);
        return new ArrayList(){{ add(dto); }};
    }

    private List<MeasurementsRawValueDto> createMatrciesNullValuesShouldDtosTest() throws ParseException {

        MeasurementsRawValueDto dto = new MeasurementsRawValueDto();
        dto.setMeasurementId(6955L);
        dto.setTempMatricesCellId("1:1");

        MeasurementsRawValueDto dto2 = new MeasurementsRawValueDto();
        dto2.setMeasurementId(6955L);
        dto2.setTempMatricesCellId("1:2");

        return new ArrayList(){{ add(dto); add(dto2); }};
    }
}