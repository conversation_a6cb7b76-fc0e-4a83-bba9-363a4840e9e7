package ch.diso.llqa.db.migration.processor.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsMetadataDto;
import ch.diso.llqa.db.migration.processor.JsonProcessorTest;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class MeasurementsMetadataItemProcessorTest extends JsonProcessorTest {

    private static final String TEST_DATA_FILE_01 = "MeasurementsMetadataTestData_01.json";
    private static final long ID = 1l;
    private static final long COMMENT_BY = 106l;


    @Test
    public void generateDto() {

        // given
        JsonNode testData = super.getTestDatas(TEST_DATA_FILE_01);

        // when
        MeasurementsMetadataItemProcessor measurementsMetadataItemProcessor = new MeasurementsMetadataItemProcessor();
        MeasurementsMetadataDto dto = measurementsMetadataItemProcessor.process(testData);

        // then
        assertThat("PROCESSOR RETURNED A NULL OBJECT", dto, notNullValue());
        assertThat("PRIMARY KEY NOT AS EXPECTED", dto.getPk(), is(new Long(1l)));
        doAssert(dto);
    }

    private void doAssert(MeasurementsMetadataDto dto) {
        assertThat(dto.getCommentBy(), is(COMMENT_BY));
        assertThat(dto.getPk(), is(ID));
    }
}