package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.DataChangesChangedetailsDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class DataChangesChangeDetailsItemWriter extends JdbcBatchItemWriter<DataChangesChangedetailsDto> {

    private static final String SQL_UPDATE = "UPDATE data_changes SET old_data = CAST(:oldData AS JSON), new_data = CAST(:newData AS JSON), field = :field, tmp_tgt = :tgt WHERE id = :pk";

    @Autowired
    public DataChangesChangeDetailsItemWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL_UPDATE);
        setDataSource(dataSource);
    }
}