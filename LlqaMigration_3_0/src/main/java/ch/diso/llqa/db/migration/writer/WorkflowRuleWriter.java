package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.WorkflowRuleDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
@Scope("prototype")
public class WorkflowRuleWriter extends JdbcBatchItemWriter<WorkflowRuleDto> {
    public static final String SQL =
            "INSERT INTO workflow_rules (workflow_element_id, workflow_element_group_id, inverse, trigger, created_at, created_by, updated_at, updated_by) " +
                    "VALUES (:ownerWorkflowElementId, :targetWorkflowElementGroupId, :inverse, :trigger, :createdAt, :createdBy, :updatedAt, :updatedBy)";

    @Autowired
    public WorkflowRuleWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL);
        setDataSource(dataSource);
    }
}