package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.WorkflowElementTagDto;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class WorkflowElementTagsItemWriter implements ItemWriter<WorkflowElementTagDto> {
   private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String SQL_GET_TAG_ID_FROM_POSITION = "SELECT MIN(id) FROM tags WHERE seqnum = ?";
    private static final String SQL_GET_TAG_VALUE_ID = "SELECT MIN(id) FROM tag_values WHERE tag_id = (" + SQL_GET_TAG_ID_FROM_POSITION + ") AND tmp_name = ?";
    private static final String SQL_GET_WF_ELT_TAG_ID = "SELECT MIN(id) FROM workflow_element_tags WHERE workflow_element_id = ? AND tag_id = (" + SQL_GET_TAG_ID_FROM_POSITION + ")";
    private static final String SQL_INSERT_WF_ELT_TAG = "INSERT INTO workflow_element_tags (workflow_element_id, tag_id, created_at, created_by, updated_at, updated_by) " +
                                                        "SELECT ?, (" + SQL_GET_TAG_ID_FROM_POSITION + "), ?, ?, ?, ? " +
                                                        "WHERE (" + SQL_GET_TAG_ID_FROM_POSITION + ") IS NOT NULL";
    private static final String SQL_INSERT_WF_ELT_TAG_VAL = "INSERT INTO workflow_element_tag_values (workflow_element_tag_id, tag_value_id, created_at, created_by, updated_at, updated_by) " +
                                                            "SELECT (" + SQL_GET_WF_ELT_TAG_ID + "), (" + SQL_GET_TAG_VALUE_ID + "), ?, ?, ?, ? " +
                                                            "WHERE (" + SQL_GET_TAG_VALUE_ID + ") IS NOT NULL" +
                                                            "  AND (" + SQL_GET_WF_ELT_TAG_ID + ") IS NOT NULL";

   @Autowired
   private JdbcTemplate jdbcTemplate;

   @Override
   public void write(List<? extends WorkflowElementTagDto> workflowElementTagDtos) {

      final String dbg = workflowElementTagDtos.stream()
                                               .map(dto -> "position >" + dto.getPosition() + "<, wf elt id >" + dto.getWorkflowElementId() + "<, tag >" + dto.getTag() + "<")
                                               .collect(Collectors.joining("\n"));
      this.logger.debug(dbg);

      jdbcTemplate.batchUpdate(SQL_INSERT_WF_ELT_TAG, new BatchPreparedStatementSetter() {
         @Override
         public void setValues(PreparedStatement ps, int idx) throws SQLException {
            WorkflowElementTagDto wfEltTagDto = workflowElementTagDtos.get(idx);
            ps.setLong(1, wfEltTagDto.getWorkflowElementId());
            ps.setInt(2, wfEltTagDto.getPosition());
            ps.setTimestamp(3, wfEltTagDto.getCreatedAt());
            ps.setString(4, wfEltTagDto.getCreatedBy());
            ps.setTimestamp(5, wfEltTagDto.getUpdatedAt());
            ps.setString(6, wfEltTagDto.getUpdatedBy());
            ps.setInt(7, wfEltTagDto.getPosition());
         }

         @Override
         public int getBatchSize() {
            return workflowElementTagDtos.size();
         }
      });

      jdbcTemplate.batchUpdate(SQL_INSERT_WF_ELT_TAG_VAL, new BatchPreparedStatementSetter() {
         @Override
         public void setValues(PreparedStatement ps, int idx) throws SQLException {
            WorkflowElementTagDto wfEltTagDto = workflowElementTagDtos.get(idx);
            ps.setLong(1, wfEltTagDto.getWorkflowElementId());
            ps.setInt(2, wfEltTagDto.getPosition());
            ps.setInt(3, wfEltTagDto.getPosition());
            ps.setString(4, wfEltTagDto.getTag());
            ps.setTimestamp(5, wfEltTagDto.getCreatedAt());
            ps.setString(6, wfEltTagDto.getCreatedBy());
            ps.setTimestamp(7, wfEltTagDto.getUpdatedAt());
            ps.setString(8, wfEltTagDto.getUpdatedBy());
            ps.setInt(9, wfEltTagDto.getPosition());
            ps.setString(10, wfEltTagDto.getTag());
            ps.setLong(11, wfEltTagDto.getWorkflowElementId());
            ps.setInt(12, wfEltTagDto.getPosition());
         }

         @Override
         public int getBatchSize() {
            return workflowElementTagDtos.size();
         }
      });
   }
}