package ch.diso.llqa.db.migration.common;

import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class JsonCreater {

    private static final String RUBY_COMMAND = "ruby";
    private static final String RUBY_SCRIPT = "main.rb";
    private static final String RUBY_PARAMETER_DATABASE_NAME = "database_name";
    private static final String RUBY_PARAMETER_TABLE_NAME = "table_name";
    private static final String RUBY_PARAMETER_COLUMN_NAME = "column_name";
    private static final String RUBY_PARAMETER_COLUN_TYPE = "column_type";
    private static final String RUBY_PARAMETER_FETCH_SIZE = "fetch_size";
    private static final String RUBY_PARAMETER_MIN_ID = "min_id";
    private static final String RUBY_PARAMETER_PASSWORD = "password";
    private static final Logger logger = LoggerFactory.getLogger(JsonCreater.class);

    public String getJson(JsonItemReaderConfiguration configuration) throws IOException, InterruptedException {
        ProcessBuilder pBuilder = createProcessBuilder(configuration, new File(this.getDirectory()));
        String command = pBuilder.command()
                .stream()
                .collect(Collectors.joining(" "));
        logger.debug("START YAML READER >{}< (ruby)", command);

        File temp = this.redirectContentToFile(pBuilder, configuration);

        Process process = pBuilder.start();
        process.waitFor();
        process.destroy();
        logger.debug("YAML READER TERMINATED");

        String fileContent = this.readTmpFile(temp.getAbsolutePath());

        temp.delete();

        return fileContent;
    }

    private String readTmpFile(String tempFileNameAbs) throws IOException {
        logger.debug("READ TEMP FILE >" + tempFileNameAbs + "<");
        InputStreamReader tempFileRead = new InputStreamReader(new FileInputStream(tempFileNameAbs), StandardCharsets.UTF_8);
        BufferedReader tempFileBuffRead = new BufferedReader(tempFileRead);
        String sCurrentLine = null;
        StringBuffer output = new StringBuffer();
        while ((sCurrentLine = tempFileBuffRead.readLine()) != null) {
            output.append(sCurrentLine);
        }
        logger.debug("READ TEMP FILE DONE");
        return output.toString();
    }

    private File redirectContentToFile(ProcessBuilder pBuilder, JsonItemReaderConfiguration configuration) throws IOException {
        File temp = this.createTempFile(configuration);
        final String tempFileNameAbs = temp.getAbsolutePath();
        logger.debug("REDIRECT YAML OUTPUT TO >" + tempFileNameAbs + "<");
        pBuilder.redirectOutput(temp);
        return temp;
    }

    /**
     * create a ruby process process
     *
     * @param configuration the configuration parameters from the ruby process
     * @param workingDir    the directory from the ruby script
     * @return the process to be started
     */
    private ProcessBuilder createProcessBuilder(JsonItemReaderConfiguration configuration, File workingDir) {
        return new ProcessBuilder(
                RUBY_COMMAND,
                RUBY_SCRIPT,
                RUBY_PARAMETER_DATABASE_NAME + "=" + configuration.getDatabaseName(),
                RUBY_PARAMETER_TABLE_NAME + "=" + configuration.getTableName(),
                RUBY_PARAMETER_COLUMN_NAME + "=" + configuration.getColumnName(),
                RUBY_PARAMETER_COLUN_TYPE + "=" + configuration.getColumnType().getColumnType(),
                RUBY_PARAMETER_FETCH_SIZE + "=" + String.valueOf(configuration.getFetchSize()),
                RUBY_PARAMETER_MIN_ID + "=" + String.valueOf(configuration.getMinId()),
                RUBY_PARAMETER_PASSWORD + "=" + configuration.getPassword())
                .directory(workingDir);
    }

    /**
     * create a temporary file to redirect the JSON to it
     *
     * @param configuration configuration used to build up a propper file name
     * @return the temporary file
     */
    private File createTempFile(JsonItemReaderConfiguration configuration) throws IOException {
        final String tempFilePrefix = configuration.getDatabaseName() + "_" + configuration.getTableName() + "_" +
              configuration.getColumnName() + "_" + configuration.getMinId() + "_" + configuration.getFetchSize() + "_" + configuration.getPassword() + "_" ;
        return File.createTempFile(tempFilePrefix, ".json");
    }

    protected String getDirectory() {
        URL url = this.getClass().getClassLoader().getResource("YamlExport/");
        File file = null;
        try {
            file = new File(url.toURI());
        } catch (URISyntaxException e) {
            file = new File(url.getPath());
        } finally {
            return file.getAbsolutePath();
        }
    }
}
