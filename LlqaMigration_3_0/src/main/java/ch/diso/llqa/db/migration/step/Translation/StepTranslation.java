package ch.diso.llqa.db.migration.step.Translation;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.TranslationDto;
import ch.diso.llqa.db.migration.processor.TranslationItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.step.JsonMigrationStep;
import ch.diso.llqa.db.migration.writer.TranslationWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

/**
 * creates the step for all translation migrations
 *
 * <AUTHOR>
 */
public abstract class StepTranslation extends JsonMigrationStep {

    private static final String UPDATE_CHILD_TALBE_FOREIGN_KEY = "UPDATE %s SET %s = CAST (? AS json) WHERE id = ?";
    public static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

    @Autowired
    private TranslationItemProcessor processor;

    @Autowired
    private TranslationWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, this.getTableName(), this.getSrcColumnName(), COLUMN_TYPE, this.getFetchSize(), this.getMinId(), this.password));
        writer.setUpdateSql(this.generateUpdateTranslationSql());

        return stepBuilderFactory.get(this.getStepName()).listener(stepCompletionListener)
                .<JsonNode, TranslationDto> chunk(this.getChunkSize())
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    protected String generateUpdateTranslationSql() {
        if (!StringUtils.isEmpty(this.getTableName()) && !StringUtils.isEmpty(this.getDestColumnName())) {
            return String.format(UPDATE_CHILD_TALBE_FOREIGN_KEY, this.getTableName(), this.getDestColumnName());
        }
        else {
            throw new RuntimeException("CHILD TABLE AND ITS COLUMN HAVE TO BE DEFINED");
        }
    }

    /**
     * returns the name of the source translation column in the source table
     *
     * @return the source translation column in the source table
     */
    protected abstract String getSrcColumnName();

    /**
     * returns the name of the destination translation column in the source table
     *
     * @return the destination translation column in the source table
     */
    protected abstract String getDestColumnName();

    /**
     * returns the name of the source tables name
     *
     * @return the  source tables name
     */
    protected abstract String getTableName();

    /**
     * returns the chunck size from the writer
     *
     * @return chunck size from the writer
     */
    protected abstract int getChunkSize();

    /**
     * returns the fetch size from the reader
     *
     * @return fetch size from the reader
     */
    protected abstract int getFetchSize();

    /**
     * returns the start PK to start the reader from
     *
     * @return the start PK to start the reader from
     */
    protected abstract int getMinId();

    /**
     * return the currents step name
     *
     * @return the current step name
     */
    protected abstract String getStepName();
}