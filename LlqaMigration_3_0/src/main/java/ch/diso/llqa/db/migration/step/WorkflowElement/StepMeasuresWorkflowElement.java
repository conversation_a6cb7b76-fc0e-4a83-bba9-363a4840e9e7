package ch.diso.llqa.db.migration.step.WorkflowElement;

import ch.diso.llqa.db.migration.writer.WorkflowElement.MeasureWorkflowElementWriter;
import ch.diso.llqa.db.migration.writer.WorkflowElement.WorkflowElementWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepMeasuresWorkflowElement extends StepWorkflowElement {

    private static final String SELECT_SQL =
            "SELECT m.id AS %s, 'measure' AS %s, real_id AS %s, m.code AS %s " +
            "FROM measures m INNER JOIN steps s ON m.step_id = s.id" +
            "                INNER JOIN procedures p ON (s.procedure_id = p.id) " +
            "ORDER BY m.id";

    @Autowired
    private MeasureWorkflowElementWriter measureWorkflowElementWriter;

    @Value("${measures.workflowelement.fetch.size}")
    private int fetchSize;

    @Value("${measures.workflowelement.chunk.size}")
    private int chunkSize;

    @Override
    protected WorkflowElementWriter getWorkflowElementWriter() {
        return this.measureWorkflowElementWriter;
    }

    @Override
    protected String generateSelectSql() {
        return String.format(SELECT_SQL, PK_COLOUMN_NAME, TABLE_COLUMN_NAME, REAL_ID_COLUMN_NAME, CODE_COLUMN_NAME);
    }

    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    @Override
    protected String getStepName() { return "stepMeasuresWorkflowElement"; }
}