package ch.diso.llqa.db.migration.step.Translation;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepWorkflowElementTagValuesName extends StepTranslation {

    private static final String TABLE_NAME = "tags";
    private static final String SRC_COLUMN_NAME = "value";
    private static final String DEST_COLUMN_NAME = "name";
    public static final int MIN_ID = 1;

    @Value("${workflow.element.tag.values.value.translation.fetch.size}")
    private int fetchSize;

    @Value("${workflow.element.tag.values.value.translation.chunk.size}")
    private int chunkSize;

    @Override
    protected String getSrcColumnName() {
        return SRC_COLUMN_NAME;
    }

    @Override
    protected String getDestColumnName() {
        return DEST_COLUMN_NAME;
    }

    @Override
    protected String getTableName() {
        return TABLE_NAME;
    }

    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    @Override
    protected int getMinId() {
        return MIN_ID;
    }

    @Override
    protected String getStepName() { return "stepWorkflowElementTagValuesName"; }
}