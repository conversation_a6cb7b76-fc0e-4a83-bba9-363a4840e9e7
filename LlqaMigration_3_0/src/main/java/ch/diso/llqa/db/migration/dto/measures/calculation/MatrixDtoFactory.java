package ch.diso.llqa.db.migration.dto.measures.calculation;

import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.util.StringUtils;

final class MatrixDtoFactory {

    private static final String MX_YSIZE = "mx_ysize";
    private static final String MX_FORMULA = "mx_formula";
    private static final String MX_XTOP = "mx_xtop";
    private static final String MX_XTBOTTOM = "mx_xtbottom";
    private static final String MX_YLEFT = "mx_yleft";
    private static final String MX_YRIGHT = "mx_yright";
    private static final String MX_RLEFT = "mx_rleft";
    private static final String MX_RIGHT = "mx_right";
    private static final String MX_CTOP = "mx_ctop";
    private static final String MX_CBOTTOM = "mx_cbottom";
    private static final String MX_FTOP = "mx_ftop";
    private static final String MX_FPLC = "mx_fplc";

    static void createMatrix(JsonNode yamlNode, MeasureTypeDto measureTypeDto) {
        MatrixDto matrixDto = createMatrixDto(yamlNode);
        measureTypeDto.setMatrix(matrixDto);
        matrixDto.setMeasureTypeDto(measureTypeDto);
        for (int rowNumber = 1; rowNumber < matrixDto.getYSize() + 1; rowNumber++) {
            createMatrixRowHeader(matrixDto, yamlNode, rowNumber);
            for (int colNumber = 1; colNumber < matrixDto.getXSize() + 1; colNumber++) {
                if (rowNumber == 1) {
                    createMatrixColHeader(matrixDto, yamlNode, colNumber);
                }
                createMatrixCell(matrixDto, yamlNode, rowNumber, colNumber);
            }
        }
        matrixDto.cleanup();
    }

    private static MatrixDto createMatrixDto(JsonNode yamlNode) {

        // remove brackets from mean() function. For example "a1+b1+c1+mean([a1,b1])+a1" should be
        // "a1+b1+c1+mean(a1,b1)+a1".
        String formula = yamlNode
              .path(MX_FORMULA)
              .asText();
        int startIndex = formula.indexOf("mean(");
        if (startIndex >= 0) {
            startIndex += 5;
            int endIndex = formula.indexOf(")", startIndex);
            String sub = formula.substring(startIndex, endIndex);
            sub = sub.replace("[", "");
            sub = sub.replace("]", "");
            String subPart1 = formula.substring(0, startIndex);
            String subPart2 = formula.substring(endIndex);
            formula = subPart1 + sub + subPart2;
        }

        MatrixDto matrixDto = new MatrixDto();
        matrixDto.setXSize(yamlNode.path(MeasureType.MX_XSIZE).asInt());
        matrixDto.setYSize(yamlNode.path(MX_YSIZE).asInt());
        matrixDto.setFormula(formula);
        matrixDto.setXTop(createTitle(yamlNode.path(MX_XTOP).asText(), yamlNode.path(MX_XTBOTTOM).asText()));
        matrixDto.setYLeft(createTitle(yamlNode.path(MX_YLEFT).asText(), yamlNode.path(MX_YRIGHT).asText()));
        return matrixDto;
    }

    private static void createMatrixCell(MatrixDto matrixDto, JsonNode yamlNode, int rowNumber, int colNumber) {
        MatrixCellDto cellDto = new MatrixCellDto();
        String rowcolIndex = String.valueOf(rowNumber) + String.valueOf(colNumber);
        JsonNode primaryTitleNode = yamlNode.path(MX_FTOP + rowcolIndex);
        JsonNode secondaryTitleNode = yamlNode.path(MX_FPLC + rowcolIndex);
        cellDto.setCellHeader(createTitle(primaryTitleNode.isNull() ? "" : primaryTitleNode.asText(), secondaryTitleNode.isNull() ? "" : secondaryTitleNode.asText()));
        cellDto.setColumnHeaderDto(matrixDto.getColumnHeaderDtos().get(colNumber));
        cellDto.setRowHeaderDto(matrixDto.getRowHeaderDtos().get(rowNumber));
        matrixDto.addCellDto(cellDto);
    }

    private static void createMatrixRowHeader(MatrixDto matrixDto, JsonNode yamlNode, int rowIndex) {
        MatrixRowHeaderDto rowHeaderDto = new MatrixRowHeaderDto();
        rowHeaderDto.setRowNumber(rowIndex);
        JsonNode primaryTitleNode = yamlNode.path(MX_RLEFT + rowIndex);
        JsonNode secondaryTitleNode = yamlNode.path(MX_RIGHT + rowIndex);
        rowHeaderDto.setValueLeft(createTitle(primaryTitleNode.isNull() ? "" : primaryTitleNode.asText(), secondaryTitleNode.isNull() ? "" : secondaryTitleNode.asText()));
        rowHeaderDto.setMatrixDto(matrixDto);
        matrixDto.addRowHeaderDto(rowHeaderDto);
    }

    private static void createMatrixColHeader(MatrixDto matrixDto, JsonNode yamlNode, int colIndex) {
        MatrixColumnHeaderDto colHeaderDto = new MatrixColumnHeaderDto();
        colHeaderDto.setColNumber(colIndex);
       JsonNode primaryTitleNode = yamlNode.path(MX_CTOP + colIndex);
       JsonNode secondaryTitleNode = yamlNode.path(MX_CBOTTOM + colIndex);
        colHeaderDto.setValueTop(createTitle(primaryTitleNode.isNull() ? "" : primaryTitleNode.asText(), secondaryTitleNode.isNull() ? "" : secondaryTitleNode.asText()));
        colHeaderDto.setMatrixDto(matrixDto);
        matrixDto.addColHeaderDto(colHeaderDto);
    }

    private static String createTitle(String primaryTitle, String secondaryTitle) {
        StringBuffer title = new StringBuffer();
        if (StringUtils.isEmpty(secondaryTitle)) {
            if (!StringUtils.isEmpty(primaryTitle)) {
                title.append(primaryTitle);
            }
        } else if (StringUtils.isEmpty(primaryTitle)) {
            if (!StringUtils.isEmpty(secondaryTitle)) {
                title.append(secondaryTitle);
            }
        } else {
            title.append(primaryTitle);
            title.append(" - ");
            title.append(secondaryTitle);
        }
        return title.length() > 0 ? title.toString() : null;
    }
}
