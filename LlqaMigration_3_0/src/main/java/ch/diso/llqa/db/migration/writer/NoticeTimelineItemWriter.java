package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.NoticeTimelineDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class NoticeTimelineItemWriter extends JdbcBatchItemWriter<NoticeTimelineDto> {

    private static final String SQL = "INSERT INTO notice_timelines (notice_id, user_id, status, timestamp, comment, created_at, created_by, updated_at, updated_by) " +
            "VALUES (:noticeId, (SELECT id FROM USERS WHERE id = :userId), :status, :timestamp, :comment, :createdAt, :createdBy, :updatedAt, :updatedBy)";

    @Autowired
    public NoticeTimelineItemWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL);
        setDataSource(dataSource);
    }
}