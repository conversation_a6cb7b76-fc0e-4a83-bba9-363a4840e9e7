package ch.diso.llqa.db.migration.step.flowcontrol;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepStepsFlowcontrol extends StepWorkflowRule {
    private static final String TABLE_NAME = "steps";

    @Value("${steps.flow.control.fetch.size}")
    private int fetchSize;

    @Value("${steps.flow.control.chunk.size}")
    private int chunkSize;

    /**
     * {@inheritDoc}
     */
    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    /**
     * get the name from the table to do the flow control migration on it
     *
     * @return the name from the table to do the flow control migration on it. always 'step'
     */
    @Override
    protected String getTableName() {
        return TABLE_NAME;
    }

    @Override
    protected String getSelectRealIdSql() {
        return "SELECT p2.real_id " +
                "FROM STEPS s2 INNER JOIN procedures p2 ON s2.procedure_id = p2.id " +
                "WHERE s2.id = %d";
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected String getStepName() {
        return "stepStepsFlowcontrol";
    }
}