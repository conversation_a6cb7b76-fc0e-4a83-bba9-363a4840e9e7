package ch.diso.llqa.db.migration.step.sql;

import java.io.File;
import java.net.URISyntaxException;
import java.net.URL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;

@Component
@Scope("prototype")
public class PostMigrate extends SqlScriptTasklet {

    @Value("${post.migrate.scripts}")
    private String scripts;

    @Override
    protected String getStepName() {
        return "postMigrate";
    }

    @Override
    protected String getSqlScriptJsonFilename() {
        return String.valueOf(Paths.get(this.getDirectory(), scripts));
    }

    @Override
    protected String getDirectory() {
        URL url = this.getClass().getClassLoader().getResource("sql/");
        File file = null;
        try {
            file = new File(url.toURI());
        } catch (URISyntaxException e) {
            file = new File(url.getPath());
        } finally {
            return file.getAbsolutePath();
        }
    }
}