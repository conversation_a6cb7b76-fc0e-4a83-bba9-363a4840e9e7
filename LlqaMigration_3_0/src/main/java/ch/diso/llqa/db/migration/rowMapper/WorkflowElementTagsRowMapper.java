package ch.diso.llqa.db.migration.rowMapper;

import ch.diso.llqa.db.migration.dto.WorkflowElementTagDto;
import ch.diso.llqa.db.migration.step.StepWorkflowElementTags;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

import static java.lang.Integer.parseInt;

@Component
public class WorkflowElementTagsRowMapper implements RowMapper<List<WorkflowElementTagDto>> {

    @Override
    public List<WorkflowElementTagDto> mapRow(ResultSet resultSet, int i)  {

        return StepWorkflowElementTags.TAG_COLUMN_NAMES.entrySet()
                .stream()
                .map(tag -> {
                    WorkflowElementTagDto workflowElementTagDto = new WorkflowElementTagDto();
                    try {
                        workflowElementTagDto.setWorkflowElementId(resultSet.getLong(StepWorkflowElementTags.FK_COLUMN_NAME));
                        workflowElementTagDto.setTag(resultSet.getString(tag.getValue()));
                        workflowElementTagDto.setPosition(parseInt(tag.getKey()));
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                    return workflowElementTagDto;
                })
                .filter(dto->dto.getTag() != null)
                .collect(Collectors.toList());
    }
}