package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnHeadersDto;
import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnContentsDto;
import ch.diso.llqa.db.migration.step.StepConfigColumnHeaders;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ConfigColumnsItemWriter<T extends ConfigColumnHeadersDto> implements ItemWriter<List<T>> {

    private static final String INSERT_CONFIG_TABLES_COL_HEADER_SQL =
            "INSERT INTO config_table_col_headers (config_table_id, col_header, seqnum, created_at, created_by, updated_at, updated_by) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?);";

    private static final String SELECT_COL_HEADER_ID = "SELECT headers.id " +
          "FROM config_table_col_headers AS headers " +
          "WHERE headers.config_table_id = ${config_table_id} and headers.seqnum = ${seqnum}";

    private static final String INSERT_CONFIG_ENTRIE_COL_CONTENTS_SQL =
            "INSERT INTO config_entrie_col_contents (config_entry_id, config_table_col_header_id, content, seqnum, created_at, created_by, updated_at, updated_by) " +
                  "SELECT ?, (" + SELECT_COL_HEADER_ID + "), ?, ?, ?, ?, ?, ? "+
            "WHERE EXISTS (" + SELECT_COL_HEADER_ID + ");";

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void write(List<? extends List<T>> lists) {

        final List<ConfigColumnHeadersDto> configColumnDtoList = lists.stream()
                .flatMap(dto -> dto.stream())
                .collect(Collectors.toList());

        for (ConfigColumnHeadersDto configColumnDto: configColumnDtoList) {

            if (configColumnDto.getClass().equals(ConfigColumnHeadersDto.class)) {
                jdbcTemplate.update(connection -> createPreparedStatementConfigColumn(connection, INSERT_CONFIG_TABLES_COL_HEADER_SQL, configColumnDto));
            } else if (configColumnDto.getClass().equals(ConfigColumnContentsDto.class)) {
                String nestedInsertSqlStatement = StrSubstitutor.replace(INSERT_CONFIG_ENTRIE_COL_CONTENTS_SQL, new LinkedHashMap<String, String>() {{
                    put("config_table_id", Long.toString(((ConfigColumnContentsDto) configColumnDto).getConfigColHeaderId()));
                    put("seqnum", Integer.toString(configColumnDto.getSeqnum()));
                }});
                jdbcTemplate.update(connection -> createPreparedStatementConfigColumn(connection, nestedInsertSqlStatement, configColumnDto));
            }
        }
    }

    private static PreparedStatement createPreparedStatementConfigColumn(Connection connection, String sql, ConfigColumnHeadersDto dto) throws SQLException{
        PreparedStatement insertStatement = connection.prepareStatement(sql, new String[] {StepConfigColumnHeaders.PK_COLUMN_NAME});
        insertStatement.setLong(1, dto.getConfigId());
        insertStatement.setString(2, dto.getContent());
        insertStatement.setInt(3, dto.getSeqnum());
        insertStatement.setTimestamp(4, dto.getCreatedAt());
        insertStatement.setString(5, dto.getCreatedBy());
        insertStatement.setTimestamp(6, dto.getUpdatedAt());
        insertStatement.setString(7, dto.getUpdatedBy());
        return insertStatement;
    }
}