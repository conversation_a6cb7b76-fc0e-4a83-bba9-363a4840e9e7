package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.MeasureMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Pattern;

@Component
public class MeasuresMetadataItemProcessor extends JsonContentListProcessor<MeasureMetadataDto> {

    private static final String REGEX = "%0.[0-9]f";
    private static final String DOT = ".";
    private static final String FLOATFORMAT = "floatformat";
    private static final String SQL_SELECT = "SELECT * FROM measure_types WHERE measure_id = %d";

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    protected List<MeasureMetadataDto> generateDto(long pk, JsonNode yamlNode) {
        List<MeasureMetadataDto> dtos = Lists.newArrayList();
        if (isMeasureTypeForMeasureIdExisting(pk)) {
            MeasureMetadataDto dto = new MeasureMetadataDto();
            dto.setPk(pk);
            String floatformat = yamlNode.path(FLOATFORMAT).asText();
            if (isFloatingPointFormat(floatformat)) {
                dto.setNumberDecimals(extractDecimalPlace(floatformat));
            }
            dtos.add(dto);
        }
        return dtos;
    }

    private boolean isMeasureTypeForMeasureIdExisting(long measureId) {
        return jdbcTemplate.queryForList(String.format(SQL_SELECT, measureId) ).size() > 0;
    }

    private boolean isFloatingPointFormat(String decimalFormatPattern) {
        return Pattern.compile(REGEX).matcher(decimalFormatPattern).matches();
    }

    private int extractDecimalPlace(String floatformat) {
        int startIndex = floatformat.indexOf(DOT) + 1;
        int endIndex = startIndex + 1;
        return Integer.valueOf(floatformat.substring(startIndex, endIndex));
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}
