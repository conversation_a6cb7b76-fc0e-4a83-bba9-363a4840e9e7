package ch.diso.llqa.db.migration.processor.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsMetadataDto;
import ch.diso.llqa.db.migration.processor.JsonContentProcessor;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

import java.util.Iterator;


@Component
public class MeasurementsMetadataItemProcessor extends JsonContentProcessor<MeasurementsMetadataDto> {

    @Override
    protected MeasurementsMetadataDto generateDto(long pk, JsonNode yamlNode) {
        MeasurementsMetadataDto dto = new MeasurementsMetadataDto();
        Iterator<String> fieldNames = yamlNode.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            Long fieldValue = yamlNode.path(fieldName).asLong();
            dto.setPk(pk);
            dto.setCommentBy(fieldValue);
        }
        return dto;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}