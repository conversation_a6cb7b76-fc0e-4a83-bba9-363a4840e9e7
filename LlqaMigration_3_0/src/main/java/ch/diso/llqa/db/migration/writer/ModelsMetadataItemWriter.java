package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.ModelsMetadataDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class ModelsMetadataItemWriter extends JdbcBatchItemWriter<ModelsMetadataDto> {

    private static final String SQL_INSERT = "INSERT INTO active_procedures_preallocations " +
            "(active_procedure_id, user_group_id, user_id, check_types_id, created_at, updated_at, created_by, updated_by)" +
            " VALUES (:activeProcedureId, (SELECT id FROM user_groups WHERE id = :groupId), (SELECT id FROM users WHERE id = :userId), :checkTypeId, :createdAt, :updatedAt, :createdBy, :updatedBy) ";

    @Autowired
    public ModelsMetadataItemWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL_INSERT);
        setDataSource(dataSource);
    }
}
