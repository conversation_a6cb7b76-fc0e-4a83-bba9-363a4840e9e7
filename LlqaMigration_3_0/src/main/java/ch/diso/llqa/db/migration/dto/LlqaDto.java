package ch.diso.llqa.db.migration.dto;

import ch.diso.llqa.db.migration.common.CommonConstants;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
public class LlqaDto implements Cloneable {
    private Long pk;
    private Timestamp createdAt = new Timestamp(System.currentTimeMillis());
    private String createdBy = CommonConstants.MIGRATION_USER;
    private Timestamp updatedAt = new Timestamp(System.currentTimeMillis());
    private String updatedBy = CommonConstants.MIGRATION_USER;

    @Override
    public LlqaDto clone() throws CloneNotSupportedException {
        LlqaDto clonedRawValue = (LlqaDto) super.clone();

        clonedRawValue.setPk(this.getPk());
        clonedRawValue.setCreatedBy(this.getCreatedBy());
        clonedRawValue.setUpdatedAt(this.getUpdatedAt());
        clonedRawValue.setCreatedAt(this.getCreatedAt());
        clonedRawValue.setUpdatedBy(this.getUpdatedBy());

        return clonedRawValue;
    }
}