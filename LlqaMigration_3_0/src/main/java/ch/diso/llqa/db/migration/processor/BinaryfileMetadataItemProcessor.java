package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.BinaryfilesMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Component
public class BinaryfileMetadataItemProcessor extends JsonContentListProcessor<BinaryfilesMetadataDto> {

    @Override
    protected List<BinaryfilesMetadataDto> generateDto(long pk, JsonNode yamlNode) {
        List<BinaryfilesMetadataDto> dtoList = new ArrayList<BinaryfilesMetadataDto>();
        BinaryfilesMetadataDto dto;
        Iterator<String> fieldNames = yamlNode.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            String fieldValue = yamlNode.path(fieldName).asText();
            dto = new BinaryfilesMetadataDto();
            dto.setBinaryfileId(pk);
            dto.setType(fieldName);
            dto.setValue(fieldValue);
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }

}