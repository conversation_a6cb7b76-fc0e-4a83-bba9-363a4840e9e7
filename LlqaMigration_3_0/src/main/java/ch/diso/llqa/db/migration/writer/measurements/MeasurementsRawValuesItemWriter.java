package ch.diso.llqa.db.migration.writer.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsRawValueDto;
import java.util.Collection;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static ch.diso.llqa.db.migration.step.measurements.StepMeasurementsRawValues.KEY_SPLITTER_CHAR;

@Component
public class MeasurementsRawValuesItemWriter implements ItemWriter<List<MeasurementsRawValueDto>> {
    
    private static final String UPDATE_MEASUREMENTS = "UPDATE measurements SET skip = ? WHERE id = ?";

    private static final String INSERT_MEASUREMENT_VALUES =
            "INSERT INTO measurement_values (measurement_id, matrices_cell_id, value, value_type, created_at, created_by, updated_at, updated_by) " +
                    "VALUES (?, ${subquery}, ?, ?, ?, ?, ?, ?);";

    private static final String INSERT_MEASUREMENT_VALUES_SUBQUERY =
            "(SELECT maCells.id " +
                    "FROM measurements AS me " +
                    "INNER JOIN measures AS mea ON mea.id = me.measure_id " +
                    "INNER JOIN measure_types AS mt ON mt.measure_id = mea.id AND mt.measure_type = mea.measure_type " +
                    "INNER JOIN matrices AS ma ON ma.measure_type_id = mt.id " +
                    "INNER JOIN matrices_column_headers AS maColHeader ON maColHeader.matrices_id = ma.id " +
                    "INNER JOIN matrices_row_headers AS maRowHeader ON maRowHeader.matrices_id = ma.id " +
                    "INNER JOIN matrices_cells AS maCells ON maCells.matrices_row_header_id = maRowHeader.id AND maCells.matrices_col_header_id = maColHeader.id " +
                    "WHERE me.id = ${measurement_id} AND maColHeader.column_index = ${column_header_id} AND maRowHeader.row_index = ${row_header_id})";

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void write(List<? extends List<MeasurementsRawValueDto>> lists) {

        final List<MeasurementsRawValueDto> measurementsRawValueDtoList = lists.stream()
                                                                               .flatMap(Collection::stream)
                                                                               .collect(Collectors.toList());

        final Pattern keySplitterCharPattern = Pattern.compile(String.valueOf(KEY_SPLITTER_CHAR));

        for (MeasurementsRawValueDto measurementsRawValueDto : measurementsRawValueDtoList) {

            String nestedSqlStatement = "null";

            if (measurementsRawValueDto.isSkip()){
                jdbcTemplate.update(connection -> createPreparedUpdateStatement(connection, UPDATE_MEASUREMENTS, measurementsRawValueDto));
                continue;
            }

            if(StringUtils.isNotBlank(measurementsRawValueDto.getTempMatricesCellId())){

                int[] idArray = parseIntArray(measurementsRawValueDto.getTempMatricesCellId(), keySplitterCharPattern);

                nestedSqlStatement = StrSubstitutor.replace(INSERT_MEASUREMENT_VALUES_SUBQUERY, new LinkedHashMap<String, Long>() {{
                    put("column_header_id", (long) idArray[0]);
                    put("row_header_id", (long) idArray[1]);
                    put("measurement_id", measurementsRawValueDto.getMeasurementId());
                }});
            }

            LinkedHashMap<String, String> stringPlaceholders = new LinkedHashMap<>();
            stringPlaceholders.put("subquery", nestedSqlStatement);

            jdbcTemplate.update(connection -> createPreparedInsertStatement(connection,
                                                                            StrSubstitutor.replace(INSERT_MEASUREMENT_VALUES, stringPlaceholders),
                                                                            measurementsRawValueDto));
        }
    }

    private static PreparedStatement createPreparedInsertStatement(Connection connection, String sql, MeasurementsRawValueDto dto) throws SQLException {

        PreparedStatement insertStatement = connection.prepareStatement(sql);
        insertStatement.setLong(1, dto.getMeasurementId());
        insertStatement.setString(2, dto.getValue() != null ? dto.getValue().substring(0, Math.min(dto.getValue().length(), 128)) : null);
        insertStatement.setString(3, dto.getValueType() != null ? dto.getValueType().name() : null);
        insertStatement.setTimestamp(4, dto.getCreatedAt());
        insertStatement.setString(5, dto.getCreatedBy());
        insertStatement.setTimestamp(6, dto.getUpdatedAt());
        insertStatement.setString(7, dto.getUpdatedBy());

        return insertStatement;
    }

    private static PreparedStatement createPreparedUpdateStatement(Connection connection, String sql, MeasurementsRawValueDto dto) throws SQLException {

        PreparedStatement statement = connection.prepareStatement(sql);
        statement.setBoolean(1, dto.isSkip());
        statement.setLong(2, dto.getMeasurementId());
        return statement;
    }

    private static int[] parseIntArray(String value, Pattern pattern) {

        return pattern.splitAsStream(value)
                .mapToInt(Integer::parseInt)
                .toArray();
    }
}
