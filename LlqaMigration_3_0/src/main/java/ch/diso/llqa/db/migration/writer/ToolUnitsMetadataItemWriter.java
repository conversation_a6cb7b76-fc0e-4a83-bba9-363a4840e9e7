package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.ToolUnitMetadataDto;
import javax.sql.DataSource;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class ToolUnitsMetadataItemWriter extends JdbcBatchItemWriter<ToolUnitMetadataDto> {

   private static final String SQL_UPDATE = "UPDATE tool_units SET comment_by = :commentedBy WHERE id = :pk AND :commentedBy IN (SELECT id FROM users)";

   @Autowired
   public ToolUnitsMetadataItemWriter(@Qualifier("dataSource") DataSource dataSource) {
      setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
      setSql(SQL_UPDATE);
      setDataSource(dataSource);
   }
}