package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.NoticePathDto;
import ch.diso.llqa.db.migration.processor.NoticePathProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.NoticePathItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepNoticePath extends JsonMigrationStep {

    public static final String TABLE_NAME = "notices";
    public static final String COLUMN_NAME = "path";
    public static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;
    public static final int MIN_ID = 1;

    @Value("${notices.path.fetch.size}")
    private int fetchSize;

    @Value("${notices.path.chunk.size}")
    private int chunkSize;

    @Autowired
    private NoticePathProcessor processor;

    @Autowired
    private NoticePathItemWriter itemWriter;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));

        return stepBuilderFactory.get("stepNoticePath").listener(stepCompletionListener)
                .<JsonNode, NoticePathDto> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(itemWriter)
                .build();
    }
}