package ch.diso.llqa.db.migration.util;

import ch.diso.llqa.db.migration.dto.measures.calculation.*;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;

public final class MeasuresCalculationItemWriterUtil {

    private static final String HISTORY_FIELDS = ", created_at, updated_at, created_by, updated_by";

    private static final String INSERT_MEASURE_TYPE_SQL =
            "INSERT INTO measure_types (measure_id, measure_type, optional, internal, number_decimals, target_value, target_unit, threshold_value, threshold_unit," +
                    " min_value, max_value, unit, value, min_length, expected, comparator, grouping, list_content, reg_exp" + HISTORY_FIELDS + ") " +
            "VALUES (?, ? , ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CAST(? AS JSON), ?, ?, ?, ?, ?)";

    private static final String INSERT_MATRIX_SQL =
            "INSERT INTO matrices (measure_type_id, x_size, y_size, formula, x_top, y_left ,created_at, updated_at, created_by, updated_by) " +
            "VALUES (?, ? , ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String INSERT_MATRIX_ROW_HEADERS_SQL =
            "INSERT INTO matrices_row_headers (matrices_id, row_index, value_left" + HISTORY_FIELDS + ")" +
            " VALUES (?, ?, ?, ?, ?, ?, ?)";

    private static final String INSERT_MATRIX_COLUMN_HEADER_SQL =
            "INSERT INTO matrices_column_headers (matrices_id, column_index, value_top" + HISTORY_FIELDS + ")" +
            " VALUES (?, ?, ?, ?, ?, ?, ?)";

    private static final String INSERT_MATRIX_CELL_SQL =
            "INSERT INTO matrices_cells (matrices_row_header_id, matrices_col_header_id, cell_header" + HISTORY_FIELDS + ")" +
            " VALUES (?, ?, ?, ?, ?, ?, ?)";


    private static final String[] COLUMN_NAMES = {"id"};

    public static PreparedStatement createPreparedStatementMeasureType(Connection connection, MeasureTypeDto dto) throws SQLException {
        PreparedStatement insertStatement = connection.prepareStatement(INSERT_MEASURE_TYPE_SQL, COLUMN_NAMES);
        insertStatement.setLong(1, dto.getMeasureid());
        insertStatement.setInt(2, dto.getMeasureType().getMtCode());
        insertStatement.setBoolean(3, dto.getOptional());
        insertStatement.setBoolean(4, dto.getInternal());
        insertStatement.setInt(5, dto.getNumberDecimals());
        insertStatement.setDouble(6, dto.getTargetValue());
        insertStatement.setString(7, dto.getTargetUnit() != null ? dto.getTargetUnit().substring(0,  Math.min(dto.getTargetUnit().length(), 10)) : null);
        insertStatement.setDouble(8, dto.getThresholdValue());
        insertStatement.setString(9, dto.getThresholdUnit() != null ? dto.getThresholdUnit().substring(0, Math.min(dto.getThresholdUnit().length(), 10)) : null);
        insertStatement.setDouble(10, dto.getMinValue());
        insertStatement.setDouble(11, dto.getMaxValue());
        insertStatement.setString(12, dto.getUnit() != null ? dto.getUnit().substring(0, Math.min(dto.getUnit().length(), 10)) : null);
        insertStatement.setDouble(13, dto.getValue());
        if (dto.getMinLength() != null) {
            insertStatement.setInt(14, dto.getMinLength());
        } else {
            insertStatement.setNull(14, Types.INTEGER);
        }
        insertStatement.setInt(15, dto.getExpected());
        insertStatement.setInt(16, dto.getComparator());
        insertStatement.setString(17, dto.getGrouping() != null ? dto.getGrouping().substring(0, Math.min(dto.getGrouping().length(), 50)) : null);
        insertStatement.setString(18, dto.getListContent());
        insertStatement.setString(19, dto.getRegexp() != null ? dto.getRegexp().substring(0, Math.min(dto.getRegexp().length(), 4000)) : null);
        insertStatement.setTimestamp(20, dto.getCreatedAt());
        insertStatement.setTimestamp(21, dto.getUpdatedAt());
        insertStatement.setString(22, dto.getCreatedBy());
        insertStatement.setString(23, dto.getUpdatedBy());
        return insertStatement;
    }

    public static PreparedStatement createPreparedStatementMatrix(Connection connection, MatrixDto dto) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(INSERT_MATRIX_SQL, COLUMN_NAMES);
        statement.setLong(1, dto.getMeasureTypeDto().getPk());
        statement.setInt(2, dto.getXSize());
        statement.setInt(3, dto.getYSize());
        statement.setString(4, dto.getFormula());
        statement.setString(5, dto.getXTop());
        statement.setString(6, dto.getYLeft());
        statement.setTimestamp(7, dto.getCreatedAt());
        statement.setTimestamp(8, dto.getUpdatedAt());
        statement.setString(9, dto.getCreatedBy());
        statement.setString(10, dto.getUpdatedBy());
        return statement;
    }

    public static PreparedStatement createPreparedStatementRowHeaderMatrix(Connection connection, MatrixRowHeaderDto dto) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(INSERT_MATRIX_ROW_HEADERS_SQL, COLUMN_NAMES);
        statement.setLong(1, dto.getMatrixDto().getPk());
        statement.setInt(2, dto.getRowNumber() - 1);
        statement.setString(3, dto.getValueLeft());
        statement.setTimestamp(4, dto.getCreatedAt());
        statement.setTimestamp(5, dto.getUpdatedAt());
        statement.setString(6, dto.getCreatedBy());
        statement.setString(7, dto.getUpdatedBy());
        return statement;
    }

    public static PreparedStatement createPreparedStatementColHeaderMatrix(Connection connection, MatrixColumnHeaderDto dto) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(INSERT_MATRIX_COLUMN_HEADER_SQL, COLUMN_NAMES);
        statement.setLong(1, dto.getMatrixDto().getPk());
        statement.setInt(2, dto.getColNumber() - 1);
        statement.setString(3, dto.getValueTop());
        statement.setTimestamp(4, dto.getCreatedAt());
        statement.setTimestamp(5, dto.getUpdatedAt());
        statement.setString(6, dto.getCreatedBy());
        statement.setString(7, dto.getUpdatedBy());
        return statement;
    }

    public static PreparedStatement createPreparedStatementMatrixCell(Connection connection, MatrixCellDto dto) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(INSERT_MATRIX_CELL_SQL, COLUMN_NAMES);
        statement.setLong(1, dto.getRowHeaderDto().getPk());
        statement.setLong(2, dto.getColumnHeaderDto().getPk());
        statement.setString(3, dto.getCellHeader());
        statement.setTimestamp(4, dto.getCreatedAt());
        statement.setTimestamp(5, dto.getUpdatedAt());
        statement.setString(6, dto.getCreatedBy());
        statement.setString(7, dto.getUpdatedBy());
        return statement;
    }
}
