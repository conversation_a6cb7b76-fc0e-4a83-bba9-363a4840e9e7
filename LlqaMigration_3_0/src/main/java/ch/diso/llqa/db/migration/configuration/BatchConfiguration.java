package ch.diso.llqa.db.migration.configuration;

import ch.diso.llqa.db.migration.step.*;
import ch.diso.llqa.db.migration.step.Translation.*;
import ch.diso.llqa.db.migration.step.WorkflowElement.*;
import ch.diso.llqa.db.migration.step.flowcontrol.StepMeasuresFlowcontrol;
import ch.diso.llqa.db.migration.step.flowcontrol.StepProceduresFlowcontrol;
import ch.diso.llqa.db.migration.step.flowcontrol.StepStepsFlowcontrol;
import ch.diso.llqa.db.migration.step.measurements.StepMeasurementsHistories;
import ch.diso.llqa.db.migration.step.measurements.StepMeasurementsMetadata;
import ch.diso.llqa.db.migration.step.measurements.StepMeasurementsRawValues;
import ch.diso.llqa.db.migration.step.sql.PostMigrate;
import ch.diso.llqa.db.migration.step.sql.PreMigrate;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableBatchProcessing
@SpringBootApplication
@ComponentScan("ch.diso.llqa.db.migration")
public class BatchConfiguration {

    @Autowired
    public JobBuilderFactory jobBuilderFactory;

    @Autowired
    private PreMigrate preMigrate;
    @Autowired
    private PostMigrate postMigrate;

    @Autowired
    private StepUserTypeAheadUnit stepUserTypeAheadUnit;
    @Autowired
    private StepBinaryfilesMetadata stepBinaryfilesMetadata;
    @Autowired
    private StepTagValues stepTagValues;
    @Autowired
    private StepMeasuresCalculation stepMeasuresCalculation;
    @Autowired
    private StepMeasuresMetadata stepMeasuresMetadata;
    @Autowired
    private StepCheckMetadata stepCheckMetadata;
    @Autowired
    private StepModelsMetadata stepModelsMetadata;
    @Autowired
    private StepUnitsMetadata stepUnitsMetadata;
    @Autowired
    private StepToolUnitsMetadata stepToolUnitsMetadata;
    @Autowired
    private StepCheckCheckdata stepCheckCheckdata;
    @Autowired
    private StepNoticeTimeline stepNoticeTimeline;
    @Autowired
    private StepNoticePath stepNoticePath;
    @Autowired
    private StepDataChangesChangedetails stepDataChangesChangedetails;
    @Autowired
    private StepProcedureProcessingTime stepProcedureProcessingTime;

    @Autowired
    private StepProceduresWorkflowElement stepProceduresWorkflowElement;
    @Autowired
    private StepWorkflowElementTags stepWorkflowElementTags;
    @Autowired
    private StepStepsWorkflowElement stepStepsWorkflowElement;
    @Autowired
    private StepMeasuresWorkflowElement stepMeasuresWorkflowElement;
    @Autowired
    private StepModelsWorkflowElement stepModelsWorkflowElement;
    @Autowired
    private StepCheckTypesWorkflowElement stepCheckTypesWorkflowElement;
    @Autowired
    private StepDeviceTypesWorkflowElement stepDeviceTypesWorkflowElement;
    @Autowired
    private StepConfigEntriesWorkflowElement stepConfigEntriesWorkflowElement;
    @Autowired
    private StepToolTypeWorkflowElement stepToolTypeWorkflowElement;

    @Autowired
    private StepProceduresTitle stepProceduresTitle;
    @Autowired
    private StepProceduresDescription stepProceduresDescription;
    @Autowired
    private StepStepsTitle stepStepsTitle;
    @Autowired
    private StepStepsDescription stepStepsDescription;
    @Autowired
    private StepMeasuresTitle stepMeasuresTitle;
    @Autowired
    private StepMeasuresDescription stepMeasuresDescription;
    @Autowired
    private StepModelsTitle stepModelsTitle;
    @Autowired
    private StepModelsDescription stepModelsDescription;
    @Autowired
    private StepDeviceTypesTitle stepDeviceTypesTitle;
    @Autowired
    private StepDeviceTypesDescription stepDeviceTypesDescription;
    @Autowired
    private StepCheckTypesTitle stepCheckTypesTitle;
    @Autowired
    private StepCheckTypesDescription stepCheckTypesDescription;
    @Autowired
    private StepTooltypesTitle stepTooltypesTitle;
    @Autowired
    private StepTooltypesDescription stepTooltypesDescription;
    @Autowired
    private StepMeasurementErrCatName stepMeasurementErrCatName;
    @Autowired
    private StepWorkflowElementTagValuesName stepWorkflowElementTagValuesName;

    @Autowired
    private StepProceduresFlowcontrol stepProceduresFlowcontrol;
    @Autowired
    private StepStepsFlowcontrol stepStepsFlowcontrol;
    @Autowired
    private StepMeasuresFlowcontrol stepMeasuresFlowcontrol;

    @Autowired
    private StepConfigColumnHeaders stepConfigColumnsHeaders;
    @Autowired
    private StepConfigColumnContents stepConfigColumnsContents;

    @Autowired
    private StepMeasurementsMetadata stepMeasurementsMetadata;
    @Autowired
    private StepMeasurementsHistories stepMeasurementsHistories;
    @Autowired
    private StepMeasurementsRawValues stepMeasurementsRawValues;

    public static void main(String[] args) {
        SpringApplication.run(BatchConfiguration.class, args);
    }

    @Bean
    public Job migrationJob() {
        return jobBuilderFactory
                .get("migrationJob")
                .incrementer(new RunIdIncrementer())

                .start(preMigrate.createStep())

                .next(stepUserTypeAheadUnit.createStep())
                .next(stepBinaryfilesMetadata.createStep())
                .next(stepTagValues.createStep())
                .next(stepMeasuresCalculation.createStep())
                .next(stepMeasuresMetadata.createStep())
                .next(stepCheckMetadata.createStep())
                .next(stepModelsMetadata.createStep())
                .next(stepUnitsMetadata.createStep())
                .next(stepToolUnitsMetadata.createStep())
                .next(stepNoticeTimeline.createStep())
                .next(stepNoticePath.createStep())
                .next(stepProcedureProcessingTime.createStep())

                // WORKFLOW ELEMENT STEPS (INCLUDING WORKFLOW ELEMENT GROUP BUILDING)
                .next(stepProceduresWorkflowElement.createStep())
                .next(stepWorkflowElementTags.createStep())
                .next(stepStepsWorkflowElement.createStep())
                .next(stepMeasuresWorkflowElement.createStep())
                .next(stepModelsWorkflowElement.createStep())
                .next(stepCheckTypesWorkflowElement.createStep())
                .next(stepDeviceTypesWorkflowElement.createStep())
                .next(stepConfigEntriesWorkflowElement.createStep())
                .next(stepToolTypeWorkflowElement.createStep())

                // TRANSLATION STEPS
                .next(stepProceduresTitle.createStep())
                .next(stepProceduresDescription.createStep())
                .next(stepStepsTitle.createStep())
                .next(stepStepsDescription.createStep())
                .next(stepMeasuresTitle.createStep())
                .next(stepMeasuresDescription.createStep())
                .next(stepModelsTitle.createStep())
                .next(stepModelsDescription.createStep())
                .next(stepDeviceTypesTitle.createStep())
                .next(stepDeviceTypesDescription.createStep())
                .next(stepCheckTypesTitle.createStep())
                .next(stepCheckTypesDescription.createStep())
                .next(stepTooltypesTitle.createStep())
                .next(stepTooltypesDescription.createStep())
                .next(stepMeasurementErrCatName.createStep())
                .next(stepWorkflowElementTagValuesName.createStep())

                // WORKFLOW RULES STEPS / FLOWCONTROL
                .next(stepProceduresFlowcontrol.createStep())
                .next(stepStepsFlowcontrol.createStep())
                .next(stepMeasuresFlowcontrol.createStep())

                // CONFIG TABLE COLUMN REORG. STEPS
                .next(stepConfigColumnsHeaders.createStep())
                .next(stepConfigColumnsContents.createStep())

                // MEASUREMENTS STEPS
                .next(stepMeasurementsMetadata.createStep())
                .next(stepMeasurementsRawValues.createStep())
                .next(stepDataChangesChangedetails.createStep())
                .next(stepCheckCheckdata.createStep())
                .next(stepMeasurementsHistories.createStep())
                .next(postMigrate.createStep())

                .build();
    }

}
