package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.BinaryfilesMetadataDto;
import ch.diso.llqa.db.migration.dto.measures.calculation.MeasureTypeContainerDto;
import ch.diso.llqa.db.migration.processor.MeasuresCalculationItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.ListUnpackingItemWriter;
import ch.diso.llqa.db.migration.writer.MeasuresCalculationItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StepMeasuresCalculation extends JsonMigrationStep {

    private static final String TABLE_NAME = "measures";

    private static final String COLUMN_NAME = "calculation";

    private static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

    @Value("${measures.calculation.fetch.size}")
    private int fetchSize;

    @Value("${measures.calculation.chunk.size}")
    private int chunkSize;

    public static final int MIN_ID = 1;

    @Autowired
    private MeasuresCalculationItemProcessor processor;

    @Autowired
    private MeasuresCalculationItemWriter writer;

    private ListUnpackingItemWriter writerWrapper;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));
        writerWrapper = new ListUnpackingItemWriter<BinaryfilesMetadataDto>();
        writerWrapper.setDelegate(writer);
        return stepBuilderFactory.get("stepMeasuresCalculation").listener(stepCompletionListener)
                .<JsonNode, List<MeasureTypeContainerDto>> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writerWrapper)
                .build();
    }
}
