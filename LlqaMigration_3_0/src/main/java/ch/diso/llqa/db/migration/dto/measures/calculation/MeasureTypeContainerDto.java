package ch.diso.llqa.db.migration.dto.measures.calculation;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import lombok.Getter;
import lombok.Setter;
import org.assertj.core.util.Lists;

import java.util.List;

@Getter
@Setter
public class MeasureTypeContainerDto extends LlqaDto {

    private List<MeasureTypeDto> measureTypes = Lists.newArrayList();

    public void addMeasureType(MeasureTypeDto measureTypeDto) {
        if (measureTypeDto.isValid()) {
            measureTypes.add(measureTypeDto);
        }
    }
}
