package ch.diso.llqa.db.migration.step.Translation;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepModelsDescription extends StepWorkflowElementTranslation {

    private static final String TABLE_NAME = "models";
    private static final String COLUMN_NAME = "description";
    public static final int MIN_ID = 1;

    @Value("${models.description.translation.fetch.size}")
    private int fetchSize;

    @Value("${models.description.translation.chunk.size}")
    private int chunkSize;

    @Override
    protected String getSrcColumnName() {
        return COLUMN_NAME;
    }

    @Override
    protected String getDestColumnName() {
        return COLUMN_NAME;
    }

    @Override
    protected String getTableName() {
        return TABLE_NAME;
    }

    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    @Override
    protected int getMinId() {
        return MIN_ID;
    }

    @Override
    protected String getStepName() { return "stepModelsDescription"; }
}