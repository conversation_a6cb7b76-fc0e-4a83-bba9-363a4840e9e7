package ch.diso.llqa.db.migration.step.WorkflowElement;

import ch.diso.llqa.db.migration.dto.WorkflowElementDto;
import ch.diso.llqa.db.migration.rowMapper.WorkflowElementRowMapper;
import ch.diso.llqa.db.migration.step.MigrationStep;
import ch.diso.llqa.db.migration.writer.WorkflowElement.WorkflowElementWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Step;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * base class to create a workflow abstraction for
 * <ul>
 *     <li>procedures</li>
 *     <li>steps</li>
 *     <li>measures</li>
 *     <li>check types</li>
 *     <li>device types</li>
 *     <li>models</li>
 *     <li>config entries</li>
 * </ul>
 *
 * <AUTHOR>
 */
public abstract class StepWorkflowElement extends MigrationStep {

    public static final String PK_COLOUMN_NAME = "id";
    public static final String REAL_ID_COLUMN_NAME = "real_id";
    public static final String CODE_COLUMN_NAME = "code";
    public static final String TABLE_COLUMN_NAME = "childTable";

    private final static Logger logger = LoggerFactory.getLogger(StepProceduresWorkflowElement.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private WorkflowElementRowMapper workflowElementRowMapper;

    /**
     * {@inheritDoc}
     */
    @Override
    public Step createStep() {
        return stepBuilderFactory.get(this.getStepName())
                .listener(stepCompletionListener)
                .<WorkflowElementDto, WorkflowElementDto>chunk(this.getChunkSize())
                .reader(this.createReader())
                .writer(this.getWorkflowElementWriter())
                .build();
    }

    /**
     * get the workflow element writer
     *
     * @return the workflow element writer
     */
    protected abstract WorkflowElementWriter getWorkflowElementWriter();

    /**
     * create the reader to reade the workflow element from the source database table
     *
     * @return the reader
     */
    private ItemReader<WorkflowElementDto> createReader() {
        JdbcCursorItemReader<WorkflowElementDto> reader = new JdbcCursorItemReader<WorkflowElementDto>();

        reader.setSql(this.generateSelectSql());
        logger.debug("SELECT STATEMENT >{}<", reader.getSql());
        reader.setDataSource(jdbcTemplate.getDataSource());
        reader.setRowMapper(workflowElementRowMapper);
        reader.setFetchSize(this.getFetchSize());

        return reader;
    }

    /**
     * generate the select SQL query
     *
     * @return the sql query to select the workflow element
     */
    protected abstract String generateSelectSql();

    /**
     * returns the chunck size from the writer
     *
     * @return chunck size from the writer
     */
    protected abstract int getChunkSize();

    /**
     * returns the fetch size from the reader
     *
     * @return fetch size from the reader
     */
    protected abstract int getFetchSize();

    /**
     * return the currents step name
     *
     * @return the current step name
     */
    protected abstract String getStepName();
}