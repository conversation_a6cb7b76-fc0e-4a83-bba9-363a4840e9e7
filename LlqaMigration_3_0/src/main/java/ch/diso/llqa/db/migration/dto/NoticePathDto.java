package ch.diso.llqa.db.migration.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Getter
@Setter
public class NoticePathDto extends LlqaDto {

    @Getter
    public enum Type {
        CHECK_STEP("CheckStep"),
        CHECL_GENERAL("CheckGeneral");

        private final String type;

        Type(String type) {
            this.type = type;
        }

        public static Type getType(String type) {
            if (type == null) {
                return null;
            }
            Optional<Type> typeOptional = Arrays.stream(Type.values())
                    .filter(t -> type.equals(t.getType()))
                    .findAny();
            if (typeOptional.isPresent()) {
                return typeOptional.get();
            } else {
                throw new RuntimeException("UNKNOWN TYPE >" + type + "<");
            }
        }
    }

    private Type type;
    private Long urlCheckId;
    private List<NoticePathElementDto> noticePathElementDtos;

    public void addNoticePathElement(long noticeId, String type, Long childId, int seq) {
        NoticePathElementDto dto = new NoticePathElementDto();
        dto.setNoticeId(noticeId);
        dto.setType(NoticePathElementDto.Type.getType(type));
        dto.setChildId(childId);
        dto.setSeq(seq);
        this.addNoticePathElement(dto);
    }

    private void addNoticePathElement(NoticePathElementDto element) {
        if (this.noticePathElementDtos == null)
            this.noticePathElementDtos = new ArrayList<>();
        this.noticePathElementDtos.add(element);
    }
}