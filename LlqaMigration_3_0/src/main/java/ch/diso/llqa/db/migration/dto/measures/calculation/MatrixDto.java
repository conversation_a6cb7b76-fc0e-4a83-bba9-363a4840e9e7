package ch.diso.llqa.db.migration.dto.measures.calculation;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
public class MatrixDto extends LlqaDto {

    private MeasureTypeDto measureTypeDto;
    private int xSize;

    private int ySize;

    private String formula;

    private String xTop;

    private String yLeft;

    Map<Integer, MatrixColumnHeaderDto> columnHeaderDtos = Maps.newHashMap();

    Map<Integer, MatrixRowHeaderDto> rowHeaderDtos = Maps.newHashMap();

    Set<MatrixCellDto> cellDtos = Sets.newHashSet();

    public void addColHeaderDto(MatrixColumnHeaderDto colHeadersDto) {
        if (colHeadersDto != null) {
            columnHeaderDtos.put(colHeadersDto.getColNumber(), colHeadersDto);
        }
    }

    public void addRowHeaderDto(MatrixRowHeaderDto rowHeadersDto) {
        if (rowHeadersDto != null) {
            rowHeaderDtos.put(rowHeadersDto.getRowNumber(), rowHeadersDto);
        }
    }

    public void addCellDto(MatrixCellDto cellsDto) {
        if (cellsDto != null) {
            cellDtos.add(cellsDto);
        }
    }

    public void cleanup() {
        cleanupCols();
        cleanupRows();

    }

    private void cleanupCols() {
        List<MatrixColumnHeaderDto> referencedColHeadersInCells = cellDtos.stream()
                .map(MatrixCellDto::getColumnHeaderDto)
                .collect(Collectors.toList());
        columnHeaderDtos = columnHeaderDtos.entrySet().stream()
                .filter(dto -> !dto.getValue().isEmpty() || referencedColHeadersInCells.contains(dto.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private void cleanupRows() {
        List<MatrixRowHeaderDto> referencedRowHeadersInCells = cellDtos.stream()
                .map(MatrixCellDto::getRowHeaderDto)
                .collect(Collectors.toList());
        rowHeaderDtos = rowHeaderDtos.entrySet().stream()
                .filter(dto -> !dto.getValue().isEmpty() || referencedRowHeadersInCells.contains(dto.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

}
