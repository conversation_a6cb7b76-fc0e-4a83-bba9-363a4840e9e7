package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.DataChangesChangedetailsDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.stereotype.Component;

@Component
public class DataChangesChangedetailsItemProcessor extends JsonContentProcessor<DataChangesChangedetailsDto> {

    private static final String KEY_FIELD = "field";
    private static final String KEY_OLDDATA = "olddata";
    private static final String KEY_INITDATA = "initdata";
    private static final String KEY_NEWDATA = "newdata";
    private static final String KEY_TGT = "tgt";
    private static final String KEY_VERSION = "version";
    private static final String KEY_CHANGES = "changes";
    private static final String PROC_REALID = "proc_realid";
    private static final String KEY_TYPE = "type";
    private static final String KEY_OLDCODE = "oldcode";
    private static final String KEY_NEWCODE = "newcode";
    private static final String KEY_VALUE = "value";

    private static final String REGEX_MATCHES_ARRAY = "^[\\[](.*)[]]$";
    private static final String REGEX_MATCHES_OBJECT = "^[{](.*)[}]$";

    @Override
    protected DataChangesChangedetailsDto generateDto(long pk, JsonNode yamlNode) {
        DataChangesChangedetailsDto dto = new DataChangesChangedetailsDto();
        dto.setPk(pk);
        dto.setField(yamlNode.path(KEY_FIELD).textValue());
        dto.setOldData(processOldData(yamlNode));
        dto.setNewData(processNewData(yamlNode));
        dto.setTgt(yamlNode.path(KEY_TGT).textValue());
        return dto;
    }

    private String processOldData(JsonNode yamlNode) {
        if (yamlNode.has(KEY_OLDDATA)) {
            return process(yamlNode, KEY_OLDDATA);
        }
        return null;
    }

    private String processNewData(JsonNode yamlNode) {
        if (yamlNode.has(KEY_VERSION)) {
            return yamlNode.toString();
        }

        if (yamlNode.has(KEY_CHANGES)) {
            return yamlNode.toString();
        }

        if (yamlNode.has(PROC_REALID)) {
            return yamlNode.toString();
        }

        if (yamlNode.has(KEY_TYPE)) {
            return yamlNode.toString();
        }

        if (yamlNode.has(KEY_OLDCODE) && yamlNode.has(KEY_NEWCODE)) {
            return yamlNode.toString();
        }

        if (yamlNode.has(KEY_INITDATA)) {
            return process(yamlNode, KEY_INITDATA);
        }

        if (yamlNode.has(KEY_NEWDATA)) {
            return process(yamlNode, KEY_NEWDATA);
        }

        return null;
    }

    private String process(JsonNode node, String key) {
        JsonNode jsonNode = node.path(key);
        if (JsonNodeType.NULL == jsonNode.getNodeType()) {
            return null;
        }
        if (isArrayOrObject(jsonNode.textValue())) {
            return jsonNode.textValue();
        } else {
            return createObjectNode(jsonNode).toString();
        }
    }

    private boolean isArrayOrObject(String textValue) {
        return textValue.matches(REGEX_MATCHES_ARRAY) || textValue.matches(REGEX_MATCHES_OBJECT);
    }

    private ObjectNode createObjectNode(JsonNode node) {
        ObjectNode objectNode = JsonNodeFactory.instance.objectNode();
        objectNode.put(KEY_VALUE, node.asText());
        return objectNode;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}
