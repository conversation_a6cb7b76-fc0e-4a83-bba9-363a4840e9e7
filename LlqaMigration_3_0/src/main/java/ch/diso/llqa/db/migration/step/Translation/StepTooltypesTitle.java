package ch.diso.llqa.db.migration.step.Translation;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepTooltypesTitle extends StepWorkflowElementTranslation {

    private static final String TABLE_NAME = "tool_types";
    private static final String SRC_COLUMN_NAME = "title";
    private static final String DEST_COLUMN_NAME = "title";
    public static final int MIN_ID = 1;

    @Value("${tooltypes.title.translation.fetch.size}")
    private int fetchSize;

    @Value("${tooltypes.title.translation.chunk.size}")
    private int chunkSize;

    @Override
    protected String getSrcColumnName() {
        return SRC_COLUMN_NAME;
    }

    @Override
    protected String getDestColumnName() {
        return DEST_COLUMN_NAME;
    }

    @Override
    protected String getTableName() {
        return TABLE_NAME;
    }

    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    @Override
    protected int getMinId() {
        return MIN_ID;
    }

    @Override
    protected String getStepName() { return "stepTooltypesTitle"; }
}