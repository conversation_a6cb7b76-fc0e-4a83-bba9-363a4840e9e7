package ch.diso.llqa.db.migration.writer.WorkflowElement;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
public class ModelWorkflowElementWriter extends WorkflowElementWriter {

    /**
     * {@inheritDoc}
     */
    @Override
    protected String generateSelectWorkflowElementGroupIdSqlQuery(Long realId, String code, String idKey) {
        String sqlFormat =
                "SELECT DISTINCT(wfEltGrp.id) AS %s " +
                "FROM models m INNER JOIN workflow_elements wfElt ON m.workflow_element_id = wfElt.id " +
                "              INNER JOIN workflow_element_groups wfEltGrp ON wfElt.workflow_element_group_id = wfEltGrp.id " +
                "WHERE m.real_id = %d" +
                "  AND wfEltGrp.child_table = 'model'";
        return String.format(sqlFormat, idKey, realId);
    }
}