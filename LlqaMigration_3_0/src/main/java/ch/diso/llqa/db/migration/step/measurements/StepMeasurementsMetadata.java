package ch.diso.llqa.db.migration.step.measurements;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.measurements.MeasurementsMetadataDto;
import ch.diso.llqa.db.migration.processor.measurements.MeasurementsMetadataItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.step.JsonMigrationStep;
import ch.diso.llqa.db.migration.writer.measurements.MeasurementsMetadataItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepMeasurementsMetadata extends JsonMigrationStep {

    public static final String TABLE_NAME = "measurements";

    public static final String COLUMN_NAME = "metadata";

    public static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

    @Value("${measurements.metadata.fetch.size}")
    private int fetchSize;

    @Value("${measurements.metadata.chunk.size}")
    private int chunkSize;

    public static final int MIN_ID = 1;

    @Autowired
    private MeasurementsMetadataItemProcessor processor;

    @Autowired
    private MeasurementsMetadataItemWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));

        return stepBuilderFactory.get("stepMeasurementsMetadata").listener(stepCompletionListener)
                .<JsonNode, MeasurementsMetadataDto> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
}
