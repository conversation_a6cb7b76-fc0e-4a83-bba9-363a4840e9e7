package ch.diso.llqa.db.migration.step.Translation;

import org.springframework.util.StringUtils;

public abstract class StepWorkflowElementTranslation extends StepTranslation {
    private static final String SQL_UPDATE_TRANSLATION = "UPDATE workflow_elements " +
            "SET %s = CAST (? AS json) " +
            "WHERE id = (SELECT wfElt.id FROM workflow_elements wfElt INNER JOIN %s child ON (wfElt.id = child.workflow_element_id) WHERE child.id = ?)";

    @Override
    protected String generateUpdateTranslationSql() {
        if (!StringUtils.isEmpty(this.getTableName()) && !StringUtils.isEmpty(this.getSrcColumnName())) {
            return String.format(SQL_UPDATE_TRANSLATION, this.getSrcColumnName(), this.getTableName());
        }
        else {
            throw new RuntimeException("CHILD TABLE AND ITS COLUMN HAVE TO BE DEFINED");
        }
    }
}
