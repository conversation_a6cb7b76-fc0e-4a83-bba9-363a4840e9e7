package ch.diso.llqa.db.migration.writer.WorkflowElement;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
public class ProcedureWorkflowElementWriter extends WorkflowElementWriter {

    /**
     * {@inheritDoc}
     */
    @Override
    protected String generateSelectWorkflowElementGroupIdSqlQuery(Long realId, String code, String idKey) {
        String sqlFormat =
                "SELECT DISTINCT(wfEltGrp.id) AS %s " +
                "FROM procedures p INNER JOIN workflow_elements wfElt ON p.workflow_element_id = wfElt.id " +
                "                  INNER JOIN workflow_element_groups wfEltGrp ON wfElt.workflow_element_group_id = wfEltGrp.id " +
                "WHERE p.real_id = %d" +
                "  AND wfEltGrp.child_table = 'procedure'";
        return String.format(sqlFormat, idKey, realId);
    }
}