package ch.diso.llqa.db.migration.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.listener.StepExecutionListenerSupport;
import org.springframework.stereotype.Component;

@Component
public class StepCompletionListener extends StepExecutionListenerSupport {

    private final static Logger logger = LoggerFactory.getLogger(StepCompletionListener.class);

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        logger.info("Step {} is in Status {}", stepExecution.getStepName(), stepExecution.getStatus());
        return stepExecution.getExitStatus();
    }
}
