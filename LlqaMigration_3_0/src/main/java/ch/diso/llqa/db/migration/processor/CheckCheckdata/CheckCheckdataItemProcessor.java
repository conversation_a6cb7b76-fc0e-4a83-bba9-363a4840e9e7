package ch.diso.llqa.db.migration.processor.CheckCheckdata;

import ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto;
import ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckContainerDto;
import ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckDto;
import ch.diso.llqa.db.migration.processor.JsonContentProcessor;
import ch.diso.llqa.db.migration.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckDto.WorkflowElementType.*;

@Component
public class CheckCheckdataItemProcessor extends JsonContentProcessor<WorkflowElementCheckContainerDto> {

    private final static Logger logger = LoggerFactory.getLogger(CheckCheckdataItemProcessor.class);

    private static final String SQ_WORKFLOW_ELEMENT_CHECK = "sq_workflow_element_checks";
    public static final String WORKFLOW_ELEMENT_CHECK_PK_ALIAS = "pk";
    public static final String SELECT_SEQUENCE_WORKFLOW_ELEMENT_CHECK_PK = "SELECT NEXTVAL('" + SQ_WORKFLOW_ELEMENT_CHECK + "') AS " + WORKFLOW_ELEMENT_CHECK_PK_ALIAS;

    private static final String YAML_NODE_NAME = "yaml";
    private static final String ID_NODE_NAME = "id";
    private static final String MEASUREMENT_ID_NODE_NAME = "mid";
    private static final String ASSIGNEE_NODE_NAME = "assignee";
    // private static final String ACRIVE_PROCEDURE_NODE_NAME = "ap_id";
    // private static final String REAL_ID_NODE_NAME = "rid";
    private static final String STATE_NODE_NAME = "status";
    // private static final String CODE_NODE_NAME = "code";
    private static final String COMMITTED_NODE_NAME = "committed";
    private static final String LOCKED_NODE_NAME = "locked";
    private static final String ENFORCE_NODE_NAME = "enforce";
    private static final String REMAINING_TIME_NODE_NAME = "remaining_time";
    private static final String ASSIGNEES_NODE_NAME = "assignees";
    private static final String PROCEDURES_NODE_NAME = "procedures";
    private static final String STEPS_NODE_NAME = "steps";
    private static final String MEASURES_NODE_NAME = "measures";
    // private static final String COMPLETE_MEASURE_NODE_NAME = "completem";
    // private static final String COMPLETED_TOOL_NODE_NAME = "completet";
    // private static final String STEP_TYPE_NODE_NAME = "steptype";
    // private static final String STEP_STATE_NODE_NAME = "cstatus";
    // private static final String MEASURE_TYPE_NODE_NAME = "mtype";
    // private static final String NEEDS_TOOL_NODE_NAME = "needstool";
    // private static final String COMPLETE_CODE_NODE_NAME = "cmplcode";
    private static final String CLOSED_BY_NODE_NAME = "closedby";

    @Autowired
    private CheckCheckdataAssigneeItemProcessor checkCheckdataAssigneeItemProcessor;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    protected WorkflowElementCheckContainerDto generateDto(long checkId, JsonNode yamlNode) {
        WorkflowElementCheckContainerDto dto = new WorkflowElementCheckContainerDto();
        dto.setAssignees(this.parseAssignees(checkId, yamlNode));
        List<WorkflowElementCheckDto> procedures = this.parseProcedures(checkId, yamlNode, dto);
        dto.setWorkflowElementCheckDtos(procedures);
        dto.setClosedBy(JsonUtils.getLongValue(yamlNode, CLOSED_BY_NODE_NAME));
        return dto;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }

    private List<WorkflowElementCheckDto> parseProcedures(long checkId, JsonNode yamlNode, WorkflowElementCheckContainerDto dto) {
        List<WorkflowElementCheckDto> workflowElementCheckDtos = new ArrayList<>();
        JsonNode proceduresNode = yamlNode.get(PROCEDURES_NODE_NAME);
        if (proceduresNode == null)
            return null;
        if (!proceduresNode.isArray())
            throw new RuntimeException("PROCEDURES NODE NOT VALID ==> NOT AN ARRAY >" + proceduresNode.getNodeType().toString() + "<");
        int procedureSeqNumber = 0;
        for (JsonNode currentProcedureNode : proceduresNode) {
            WorkflowElementCheckDto producerWorkflowElementCheck = this.generateProcedureWorkflowEltCheckDto(checkId,
                  currentProcedureNode, dto, ++procedureSeqNumber, null, null);
            if (producerWorkflowElementCheck != null)
                workflowElementCheckDtos.add(producerWorkflowElementCheck);
            workflowElementCheckDtos.addAll(this.parseSteps(checkId, currentProcedureNode, dto, procedureSeqNumber));
        }
        return workflowElementCheckDtos;
    }

    private List<WorkflowElementCheckDto> parseSteps(long checkId, JsonNode procedureNode,
          WorkflowElementCheckContainerDto dto, int procedureSeqNumber) {
        List<WorkflowElementCheckDto> stepWorkflowElementCheckDtos = new ArrayList<>();
        JsonNode stepNodes = procedureNode.get(STEPS_NODE_NAME);
        if (stepNodes == null)
            return null;
        if (!stepNodes.isArray())
            throw new RuntimeException("STEPS NODE NOT VALID ==> NOT AN ARRAY >" + stepNodes.getNodeType().toString() + "<");
        int stepSeqNumber = 0;
        for (JsonNode currentStepNode : stepNodes) {
            WorkflowElementCheckDto stepWorkflowElementCheck = this.generateStepWorkflowEltCheckDto(checkId,
                  currentStepNode, dto, procedureSeqNumber, ++stepSeqNumber, null);
            if (stepWorkflowElementCheck != null)
                stepWorkflowElementCheckDtos.add(stepWorkflowElementCheck);
            stepWorkflowElementCheckDtos.addAll(this.parseMeasures(checkId, currentStepNode, procedureSeqNumber, stepSeqNumber));
        }
        return stepWorkflowElementCheckDtos;
    }

    private List<WorkflowElementCheckDto> parseMeasures(long checkId, JsonNode stepNode, int procedureSeqNumber,
          int stepSeqNumber) {
        List<WorkflowElementCheckDto> measureWorkflowElementCheckDtos = new ArrayList<>();
        JsonNode measureNodes = stepNode.get(MEASURES_NODE_NAME);
        if (measureNodes == null)
            return null;
        if (!measureNodes.isArray())
            throw new RuntimeException("MEASURES NODE NOT VALID ==> NOT AN ARRAY >" + measureNodes.getNodeType().toString() + "<");
        int measureSeqNumber = 0;
        for (JsonNode currentMeasureNode : measureNodes) {
            WorkflowElementCheckDto measureWorkflowElementCheck = this.generateMeasureWorkflowEltCheckDto(checkId, currentMeasureNode, procedureSeqNumber, stepSeqNumber, ++measureSeqNumber);
            if (measureWorkflowElementCheck != null)
                measureWorkflowElementCheckDtos.add(measureWorkflowElementCheck);
        }
        return measureWorkflowElementCheckDtos;
    }

    private WorkflowElementCheckDto generateProcedureWorkflowEltCheckDto(long checkId, JsonNode procedureNode,
          WorkflowElementCheckContainerDto dto, int procedureSeqNumber, Integer stepSeqNumber, Integer measureSeqNumber) {
        Integer procedureAssigneeIdx = JsonUtils.getIntegerValue(procedureNode, ASSIGNEE_NODE_NAME);
        //Long procedureActiveProcedureId = JsonUtils.getLongValue(procedureNode, ACRIVE_PROCEDURE_NODE_NAME);
        //Long procedureRealId = JsonUtils.getLongValue(procedureNode, REAL_ID_NODE_NAME);
        Long procedureId = JsonUtils.getLongValue(procedureNode, ID_NODE_NAME);
        Integer procedureState = JsonUtils.getIntegerValue(procedureNode, STATE_NODE_NAME);
        //String procedureCode = JsonUtils.getStringValue(procedureNode, CODE_NODE_NAME);
        Long procedureCommittedUserId = this.getCommitedBy(procedureNode);
        Boolean procedureLocked = JsonUtils.getBooleanFromIntegerValue(procedureNode, LOCKED_NODE_NAME);
        Short procedureEnforce = JsonUtils.getShortValue(procedureNode, ENFORCE_NODE_NAME);
        Long procedureRemainingTime = JsonUtils.getLongValue(procedureNode, REMAINING_TIME_NODE_NAME);

        WorkflowElementCheckDto workflowElt = new WorkflowElementCheckDto();
        workflowElt.setPk(this.generateAssigneeBlockPk());
        workflowElt.setCheckId(checkId);
        workflowElt.setAssigneeOldIndex(procedureAssigneeIdx);
        workflowElt.setChildId(procedureId);
        workflowElt.setWorkflowElementType(PROCEDURE);
        workflowElt.setState(procedureState);
        workflowElt.setCommitedBy(procedureCommittedUserId);
        workflowElt.setLocked((procedureLocked != null) && procedureLocked);
        workflowElt.setRemainingTime(procedureRemainingTime);
        workflowElt.setEnforce(procedureEnforce != null ? procedureEnforce : 0);
        workflowElt.setProcedureSeqNumber(procedureSeqNumber);
        workflowElt.setStepSeqNumber(stepSeqNumber);
        workflowElt.setMeasureSeqNumber(measureSeqNumber);
        return workflowElt;
    }

    private WorkflowElementCheckDto generateStepWorkflowEltCheckDto(long checkId, JsonNode stepNode,
          WorkflowElementCheckContainerDto dto, int procedureSeqNumber, Integer stepSeqNumber, Integer measureSeqNumber) {
        Integer stepAssigneeIdx = JsonUtils.getIntegerValue(stepNode, ASSIGNEE_NODE_NAME);
        Long stepId = JsonUtils.getLongValue(stepNode, ID_NODE_NAME);
        Integer stepState = JsonUtils.getIntegerValue(stepNode, STATE_NODE_NAME);
        //String stepCode = JsonUtils.getStringValue(stepNode, CODE_NODE_NAME);
        Long stepCommittedUserId = this.getCommitedBy(stepNode);
        //Boolean stepCompleteMeasure = JsonUtils.getBooleanValue(stepNode, COMPLETE_MEASURE_NODE_NAME);
        //Boolean stepCompleteTool = JsonUtils.getBooleanValue(stepNode, COMPLETED_TOOL_NODE_NAME);
        Boolean stepLocked = JsonUtils.getBooleanFromIntegerValue(stepNode, LOCKED_NODE_NAME);
        Short stepEnforce = JsonUtils.getShortValue(stepNode, ENFORCE_NODE_NAME);
        //Integer stepType = JsonUtils.getIntegerValue(stepNode, STEP_TYPE_NODE_NAME);

        WorkflowElementCheckDto workflowElt = new WorkflowElementCheckDto();
        workflowElt.setPk(this.generateAssigneeBlockPk());
        workflowElt.setCheckId(checkId);
        workflowElt.setAssigneeOldIndex(stepAssigneeIdx);
        workflowElt.setChildId(stepId);
        workflowElt.setWorkflowElementType(STEP);
        workflowElt.setState(stepState);
        workflowElt.setCommitedBy(stepCommittedUserId);
        workflowElt.setLocked(stepLocked != null && stepLocked);
        workflowElt.setEnforce(stepEnforce != null ? stepEnforce : 0);
        workflowElt.setProcedureSeqNumber(procedureSeqNumber);
        workflowElt.setStepSeqNumber(stepSeqNumber);
        workflowElt.setMeasureSeqNumber(measureSeqNumber);
        return workflowElt;
    }

    private WorkflowElementCheckDto generateMeasureWorkflowEltCheckDto(long checkId, JsonNode measureNode,
          int procedureSeqNumber, Integer stepSeqNumber, Integer measureSeqNumber) {
        Long measureId = JsonUtils.getLongValue(measureNode, ID_NODE_NAME);
        Long measurementId = JsonUtils.getLongValue(measureNode, MEASUREMENT_ID_NODE_NAME);
        Integer measureState = JsonUtils.getIntegerValue(measureNode, STATE_NODE_NAME);
        //Integer measureStepStatus = JsonUtils.getIntegerValue(measureNode, STEP_STATE_NODE_NAME);
        //Integer measureType = JsonUtils.getIntegerValue(measureNode, MEASURE_TYPE_NODE_NAME);
        //String measureCode = JsonUtils.getStringValue(measureNode, CODE_NODE_NAME);
        //Long measureNeedsTool = JsonUtils.getLongValue(measureNode, NEEDS_TOOL_NODE_NAME);
        //Integer measureCompleteCode = JsonUtils.getIntegerValue(measureNode, COMPLETE_CODE_NODE_NAME);
        Boolean measureLocked = JsonUtils.getBooleanFromIntegerValue(measureNode, LOCKED_NODE_NAME);
        Short measureEnforce = JsonUtils.getShortValue(measureNode, ENFORCE_NODE_NAME);

        WorkflowElementCheckDto workflowElt = new WorkflowElementCheckDto();
        workflowElt.setPk(this.generateAssigneeBlockPk());
        workflowElt.setCheckId(checkId);
        workflowElt.setChildId(measureId);
        if (measurementId != null && measurementId > 0)
            workflowElt.setMeasurementId(measurementId);
        workflowElt.setWorkflowElementType(MEASURE);
        workflowElt.setState(measureState);
        workflowElt.setLocked(measureLocked != null && measureLocked);
        workflowElt.setEnforce(measureEnforce != null ? measureEnforce : 0);
        workflowElt.setProcedureSeqNumber(procedureSeqNumber);
        workflowElt.setStepSeqNumber(stepSeqNumber);
        workflowElt.setMeasureSeqNumber(measureSeqNumber);
        return workflowElt;
    }

    private Long getCommitedBy(JsonNode node) {
        Boolean committedCheck = JsonUtils.getBooleanValue(node, COMMITTED_NODE_NAME);
        if (committedCheck != null) {
            if (committedCheck) {
                logger.debug("COMMITTED ATTRIBUTE SHOULD BE FALSE OR A NUMBER, BUT NEVER >TRUE<");
            }
            return null;
        } else {
            Long committedUserId = JsonUtils.getLongValue(node, COMMITTED_NODE_NAME);
            return committedUserId;
        }
    }

    /**
     * generate a workflow element check primary key
     *
     * @return an auto generated key from a DB sequence
     */
    private long generateAssigneeBlockPk() {
        List<Map<String, Object>> rows = jdbcTemplate.queryForList(SELECT_SEQUENCE_WORKFLOW_ELEMENT_CHECK_PK);
        if (rows.size() == 1) {
            Map<String, Object> records = rows.get(0);
            Object primaryKey = records.get(WORKFLOW_ELEMENT_CHECK_PK_ALIAS);
            if (primaryKey instanceof Long)
                return (Long) primaryKey;
            else
                throw new RuntimeException("COULD NOT GENERATE A NEW ASSIGNEE BLOCK PRIMARY KEY (NOT A NUMBER)");
        } else {
            throw new RuntimeException("COULD NOT GENERATE A NEW ASSIGNEE BLOCK PRIMARY KEY");
        }
    }

    private List<CheckDataAssigneeDto> parseAssignees(long checkId, JsonNode yamlNode) {
        JsonNode assigneesNode = this.generateAssigneesNode(checkId, yamlNode.get(ASSIGNEES_NODE_NAME));
        return this.checkCheckdataAssigneeItemProcessor.process(assigneesNode);
    }

    protected JsonNode generateAssigneesNode(long checkId, JsonNode assigneeNodeArray) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode assigneeNode = mapper.createObjectNode();

        assigneeNode.put(ID_NODE_NAME, checkId);
        assigneeNode.put(YAML_NODE_NAME, assigneeNodeArray);

        return assigneeNode;
    }
}