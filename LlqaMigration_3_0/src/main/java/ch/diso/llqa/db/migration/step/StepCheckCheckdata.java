package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckContainerDto;
import ch.diso.llqa.db.migration.processor.CheckCheckdata.CheckCheckdataItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.CheckCheckdataItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepCheckCheckdata extends JsonMigrationStep {

    private static final String TABLE_NAME = "checks";
    private static final String COLUMN_NAME = "checkdata";
    private static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;
    private static final int MIN_ID = 1;

    @Value("${checks.checkdata.fetch.size}")
    private int fetchSize;

    @Value("${checks.checkdata.chunk.size}")
    private int chunkSize;

    @Autowired
    private CheckCheckdataItemProcessor processor;

    @Autowired
    private CheckCheckdataItemWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, this.fetchSize, MIN_ID, this.password));
        return stepBuilderFactory.get("stepCheckCheckdata")
                .listener(stepCompletionListener)
                .<JsonNode, WorkflowElementCheckContainerDto>chunk(this.chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
}