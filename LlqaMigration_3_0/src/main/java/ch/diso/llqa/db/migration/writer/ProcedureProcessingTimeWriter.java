package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.ProcedureProcessingTimeDto;
import javax.sql.DataSource;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class ProcedureProcessingTimeWriter extends JdbcBatchItemWriter<ProcedureProcessingTimeDto> {

   public static final String SQL = "UPDATE procedures SET processing_time = :processingTime WHERE ID = :pk";

   @Autowired
   public ProcedureProcessingTimeWriter(@Qualifier("dataSource") DataSource dataSource) {
      setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
      setSql(SQL);
      setDataSource(dataSource);
   }
}