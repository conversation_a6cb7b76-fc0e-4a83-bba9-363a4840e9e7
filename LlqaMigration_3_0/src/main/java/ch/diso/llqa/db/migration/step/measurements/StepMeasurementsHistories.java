package ch.diso.llqa.db.migration.step.measurements;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.measurements.MeasurementsHistoriesDto;
import ch.diso.llqa.db.migration.processor.measurements.MeasurementsHistoriesItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.step.JsonMigrationStep;
import ch.diso.llqa.db.migration.writer.measurements.MeasurementsHistoriesItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepMeasurementsHistories extends JsonMigrationStep {

    public static final String TABLE_NAME = "measurements";

    public static final String COLUMN_NAME = "previous_measurements";

    public static final ColumnType COLUMN_TYPE = ColumnType.ARRAY;

    @Value("${measurements.history.fetch.size}")
    private int fetchSize;

    @Value("${measurements.history.chunk.size}")
    private int chunkSize;

    public static final int MIN_ID = 1;

    @Autowired
    private MeasurementsHistoriesItemProcessor processor;

    @Autowired
    private MeasurementsHistoriesItemWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));

        return stepBuilderFactory.get("stepMeasurementsHistories").listener(stepCompletionListener)
                .<JsonNode, List<MeasurementsHistoriesDto>> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
}
