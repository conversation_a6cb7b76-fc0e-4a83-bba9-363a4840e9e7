package ch.diso.llqa.db.migration.processor.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsRawValueDto;
import ch.diso.llqa.db.migration.processor.JsonContentProcessor;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Objects;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static ch.diso.llqa.db.migration.step.measurements.StepMeasurementsRawValues.KEY_SPLITTER_CHAR;


@Component
public class MeasurementsRawValuesItemProcessor extends JsonContentProcessor<List<MeasurementsRawValueDto>> {

    @Override
    protected List<MeasurementsRawValueDto> generateDto(long pk, JsonNode yamlNode) {

        List<MeasurementsRawValueDto> measurementsRawValueDtoList = new ArrayList<>();

        try {
            ObjectMapper mapper = new ObjectMapper();
            String yamlNodeString = yamlNode.toString();
            MeasurementsRawValueDto measurementsRawValueDto = mapper.readValue(yamlNodeString, new TypeReference<MeasurementsRawValueDto>() { });
            measurementsRawValueDto.setValueType(MeasurementsRawValueDto.ValueType.UNKNOWN);

            if (Objects.nonNull(measurementsRawValueDto.getReduce())) {
                measurementsRawValueDto.setMeasurementId(pk);
                try {
                    MeasurementsRawValueDto reduceRawValueDto = measurementsRawValueDto.clone();
                    reduceRawValueDto.setValueType(MeasurementsRawValueDto.ValueType.TIME_STAMP_REDUCE);
                    reduceRawValueDto.setValue(reduceRawValueDto.getReduce().toString());
                    measurementsRawValueDtoList.add(reduceRawValueDto);
                } catch (CloneNotSupportedException e) {
                    e.printStackTrace();
                }
                measurementsRawValueDto.setReduce(null);
                measurementsRawValueDtoList.add(measurementsRawValueDto);
            }
            else if (measurementsRawValueDto.isSkip() || measurementsRawValueDto.getCellValues().size() == 0){
                measurementsRawValueDto.setMeasurementId(pk);
                measurementsRawValueDtoList.add(measurementsRawValueDto);
            } else {
                measurementsRawValueDtoList = measurementsRawValueDto.getCellValues()
                                                                     .entrySet()
                                                                     .stream()
                                                                     .map(e -> new MeasurementsRawValueDto() {{
                                                                         setMeasurementId(pk);
                                                                         setValue(e.getValue());
                                                                         setTempMatricesCellId(modifyListKey(e.getKey()));
                                                                     }})
                                                                     .collect(Collectors.toList());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return measurementsRawValueDtoList;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }

    /**
     * Umwandeln einer Positionsangabe innerhalb eines Arrays resp. einer Matrix
     * aus einer Messung.
     *
     * @param key in der Form {@code a1}
     * @return Resultat als {@code 0:0}
     */
    public static String modifyListKey(String key) {

        if (!key.matches(MeasurementsRawValueDto.MATRIX_MAX_DIMENSION_PATTERN)) {
            return key;
        }

        int firstChar = key.charAt(0) - 'a';
        int secondChar = key.charAt(1) - '1';

        String modifiedKey = "" + firstChar +  KEY_SPLITTER_CHAR + secondChar;

        return key.replaceFirst(key, modifiedKey);
    }
}
