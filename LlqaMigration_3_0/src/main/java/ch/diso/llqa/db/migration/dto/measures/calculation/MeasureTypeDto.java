package ch.diso.llqa.db.migration.dto.measures.calculation;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MeasureTypeDto extends LlqaDto {

    private final long measureid;

    private MeasureType measureType;

    private final Boolean internal;

    private final Boolean optional;

    private final int numberDecimals;

    private double targetValue;

    private String targetUnit;

    private double thresholdValue;

    private String thresholdUnit;

    private String unit;

    private double value;

    private double minValue;

    private double maxValue;

    private Integer minLength;

    private String regexp;

    private int expected;

    private int comparator;

    private int listLength;

    private String listContent;

    private String grouping;

    private MatrixDto matrix;

    private boolean valid;

    MeasureTypeDto(long measureid, MeasureType measureType, boolean internal, boolean optional) {
        this.measureid = measureid;
        this.measureType = measureType;
        this.internal = internal;
        this.optional = optional;
        this.numberDecimals = 0;
    }

    public boolean hasMatrix() {
        return matrix != null;
    }
}
