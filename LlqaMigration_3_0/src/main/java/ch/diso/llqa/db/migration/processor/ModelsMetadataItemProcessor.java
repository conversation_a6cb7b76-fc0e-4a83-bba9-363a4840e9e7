package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.ModelsMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Component
public class ModelsMetadataItemProcessor extends JsonContentListProcessor<ModelsMetadataDto> {

    public static final int INVALID_ACTIVE_PROCEDURE_ID = 0;
    private Logger logger = LoggerFactory.getLogger(ModelsMetadataItemProcessor.class);

    static final String SQL_SELECT = "SELECT a.id FROM models m " +
            "INNER JOIN active_procedures a ON m.id = a.model_id " +
            "INNER JOIN procedures p ON a.procedure_id = p.id  " +
            "WHERE p.real_id = %d and m.id = %d";

    static final String COLUMN_ID = "id";

    private static final String NODE_FIELD_PREALLOCATE = "_preallocate";
    private static final String NODE_FIELD_USER_ID = "user_id";
    private static final String NODE_FIELD_GROUP_ID = "group_id";

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    protected List<ModelsMetadataDto> generateDto(long pk, JsonNode yamlNode) {
        List<ModelsMetadataDto> dtos = Lists.newArrayList();
        JsonNode root = yamlNode.get(NODE_FIELD_PREALLOCATE);
        Iterator<String> procedureRealidIterator = root.fieldNames();
        while (procedureRealidIterator.hasNext()) {
            String procedureRealidKey = procedureRealidIterator.next();
            processChecktypeIdNodes(pk, procedureRealidKey, root.path(procedureRealidKey), dtos);
        }
        return dtos;
    }

    private void processChecktypeIdNodes(long pk, String procedureRealidKey, JsonNode procedureRealIdNode, List<ModelsMetadataDto> dtos) {
        Iterator<String> checkTypIdIterator = procedureRealIdNode.fieldNames();
        while (checkTypIdIterator.hasNext()) {
            String checkTypeKey = checkTypIdIterator.next();
            JsonNode checkTypeNode = procedureRealIdNode.path(checkTypeKey);
            ModelsMetadataDto dto = createDto(pk, Long.valueOf(procedureRealidKey), Long.valueOf(checkTypeKey), checkTypeNode);
            if (dto.getActiveProcedureId() != INVALID_ACTIVE_PROCEDURE_ID) {
                dtos.add(dto);
            }
        }
    }

    private ModelsMetadataDto createDto(long modelId, long keyProcedureRealid, long checkTypeId, JsonNode checkTypeNode) {
        ModelsMetadataDto dto = new ModelsMetadataDto();
        dto.setActiveProcedureId(getActiveProcedureIdByModelAndProcedureRealId(modelId, keyProcedureRealid));
        dto.setCheckTypeId(Long.valueOf(checkTypeId));
        long userId = checkTypeNode.path(NODE_FIELD_USER_ID).asLong();
        dto.setUserId(userId > 0 ? userId : null);
        long groupId = checkTypeNode.path(NODE_FIELD_GROUP_ID).asLong();
        dto.setGroupId(groupId > 0 ? groupId : null);
        return dto;
    }


    private long getActiveProcedureIdByModelAndProcedureRealId(long modelId, long procRealId) {
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(String.format(SQL_SELECT, procRealId, modelId));
        int resultSize = resultList.size();
        if (resultSize > 1) {
            throw new RuntimeException(String.format("MORE THAN ONE ACTIVE PROCEDURE FOR PROCEDURE REALID >%d< AND MODEL ID >%d< exists", procRealId, modelId));
        }
        if (resultSize == 0) {
            logger.info(String.format("THERE IS NO ACTIVE PROCEDURE FOR PROCEDURE REALID >%d< AND MODEL ID >%d<", procRealId, modelId));
            return INVALID_ACTIVE_PROCEDURE_ID;
        }
        return (long) resultList.get(0).get(COLUMN_ID);
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}