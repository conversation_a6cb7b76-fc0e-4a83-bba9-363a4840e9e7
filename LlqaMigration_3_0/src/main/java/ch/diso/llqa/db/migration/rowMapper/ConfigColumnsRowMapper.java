package ch.diso.llqa.db.migration.rowMapper;

import ch.diso.llqa.db.migration.common.IndexedMapValue;
import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnHeadersDto;
import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnContentsDto;
import ch.diso.llqa.db.migration.step.StepConfigColumnContents;
import ch.diso.llqa.db.migration.step.StepConfigColumnHeaders;
import lombok.Setter;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;


@Setter
@Component
@Scope("prototype")
public class ConfigColumnsRowMapper<T extends ConfigColumnHeadersDto>  implements RowMapper<List<? extends ConfigColumnHeadersDto>> {

    private List<IndexedMapValue> columnNames;
    private Class<T> type;

    @Override
    public List<? extends ConfigColumnHeadersDto> mapRow(ResultSet resultSet, int i) {

        Assert.notNull(columnNames, "You must set columnNames!");
        Assert.notNull(type, "You must set type!");

        return columnNames
                .stream()
                .map(column -> mapRowConfigColumnDto(type, resultSet, column))
                .filter(column->column.getContent() != null)
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private <T extends ConfigColumnHeadersDto> T mapRowConfigColumnDto(Class<T> type, ResultSet resultSet, IndexedMapValue indexedMapValue) {

        T configColumnDto = null;
        try {
            configColumnDto = type.newInstance();
            configColumnDto.setConfigId(resultSet.getLong(StepConfigColumnHeaders.PK_COLUMN_NAME));
            configColumnDto.setSeqnum(indexedMapValue.getPosition());
            String content = resultSet.getString(indexedMapValue.getValue());
            if (configColumnDto.getClass().equals(ConfigColumnContentsDto.class)){
                long configColHeaderId = resultSet.getLong(StepConfigColumnContents.CONFIG_TABLE_ID_COLUMN_NAME);
                if(configColHeaderId > 0){
                    ((ConfigColumnContentsDto)configColumnDto).setConfigColHeaderId(configColHeaderId);
                    if(content == null){
                        content = "";
                    }   
                }
            }
            configColumnDto.setContent(content);
        } catch (InstantiationException | IllegalAccessException | SQLException e) {
            e.printStackTrace();
        }
        return configColumnDto;
    }
}