package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.ProcedureProcessingTimeDto;
import ch.diso.llqa.db.migration.processor.ProcedureProcessingTimeProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.ProcedureProcessingTimeWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepProcedureProcessingTime extends JsonMigrationStep {

   private static final String TABLE_NAME = "procedures";
   private static final String COLUMN_NAME = "processing_time_old";
   private static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;
   private static final int MIN_ID = 1;

   @Value("${procedures.processing.time.fetch.size}")
   private int fetchSize;

   @Value("${procedures.processing.time.chunk.size}")
   private int chunkSize;

   @Autowired
   private ProcedureProcessingTimeProcessor processor;

   @Autowired
   private ProcedureProcessingTimeWriter writer;

   @Override
   public Step createStep() {
      reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));
      return stepBuilderFactory.get("stepProcedureProcessingTime")
                               .listener(stepCompletionListener)
                               .<JsonNode, ProcedureProcessingTimeDto>chunk(chunkSize)
                               .reader(reader)
                               .processor(processor)
                               .writer(writer)
                               .build();
   }
}