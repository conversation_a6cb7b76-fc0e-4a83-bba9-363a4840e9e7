package ch.diso.llqa.db.migration.dto.CheckCheckdata;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import java.util.Arrays;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CheckDataAssigneeDto extends LlqaDto {

    private static final String KEY_NULL_PLACEHOLDER = "<<EMPTY>>";
    private static final String KEY_SEPARATOR = "_";

    @Getter
    public enum RegistrationMode {
        COMPLETE_ONLY(1),
        MAY_PARTITION(2),
        MUST_PARTITION(3),
        APPENDED(4);

        private final int mode;

        RegistrationMode(int mode) {
            this.mode = mode;
        }

        public static RegistrationMode getRegMode(final Integer mode) {
            if (mode == null) {
                return null;
            }
            Optional<RegistrationMode> regMode = Arrays.stream(RegistrationMode.values())
                    .filter(rm -> mode.equals(rm.getMode()))
                    .findAny();
            if (regMode.isPresent()) {
                return regMode.get();
            } else {
                return null;
            }
        }
    }

    private long checkId;
    private Long userGroupId;
    private Long userId;
    private Integer oldAssigneeIndex;
    private Integer oldAssigneeParentIndex;
    private RegistrationMode registrationMode;
}