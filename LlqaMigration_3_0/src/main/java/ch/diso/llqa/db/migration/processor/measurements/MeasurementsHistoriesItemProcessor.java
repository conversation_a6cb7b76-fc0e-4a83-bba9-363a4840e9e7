package ch.diso.llqa.db.migration.processor.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsHistoriesDto;
import ch.diso.llqa.db.migration.processor.JsonContentListProcessor;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Component
public class MeasurementsHistoriesItemProcessor extends JsonContentListProcessor<MeasurementsHistoriesDto> {

    @Override
    @SuppressWarnings("unchecked")
    protected List<MeasurementsHistoriesDto> generateDto(long pk, JsonNode yamlNode)  {

        if (!isYamlNodeOfExpectedType(yamlNode)) {
            return null;
        }

        List<MeasurementsHistoriesDto> dtoList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        mapper.setDateFormat(df);
        String yamlNodeString = yamlNode.toString();

        try {
            dtoList = mapper.readValue(yamlNodeString, new TypeReference<List<MeasurementsHistoriesDto>>() { });
        } catch (IOException e) {
            e.printStackTrace();
        }

        for (MeasurementsHistoriesDto dto : dtoList) {
            dto.setUpdatedBy(dto.getSavedBy() > 0 ? dto.getSavedBy().toString() : dto.getUpdatedBy());
            dto.setCreatedAt(dto.getUpdatedAt());
        }
        return dtoList;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isArray();
    }
}