package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.common.CommonConstants;
import ch.diso.llqa.db.migration.dto.TagValuesValuesDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Component
public class TagValuesItemProcessor implements ItemProcessor<JsonNode, List<TagValuesValuesDto>> {

    private static final String DEFAULT_LANGUAGE = "en";

    private long getPkFromNode(JsonNode node) {
        JsonNode idNode = this.getSubNode(node, CommonConstants.JSON_ID_NODE_KEY, JsonNodeType.NUMBER);
        if (idNode != null)
            return idNode.asLong();
        else
            throw new RuntimeException("JsonContentProcessor INVALID JSON CONTENT: PRIMARY KEY");
    }

    private List<String> getTagValuesFromNode(JsonNode node) {
        JsonNode tagValues = this.getSubNode(node, CommonConstants.JSON_YAML_NODE_KEY, JsonNodeType.ARRAY);
        if (tagValues != null)
            return StreamSupport.stream(Spliterators.spliteratorUnknownSize(tagValues.elements(), Spliterator.ORDERED), false)
                                .map(JsonNode::asText)
                                .collect(Collectors.toList());
        else
            throw new RuntimeException("JsonContentProcessor INVALID JSON CONTENT: TAG VALUES");
    }

    private Set<String> getLanguagesFromNode(JsonNode node) {
        ArrayNode languages = (ArrayNode) this.getSubNode(node, CommonConstants.JSON_LANGUAGE_NODE_KEY, JsonNodeType.ARRAY);
        if (languages != null) {
            return StreamSupport.stream(Spliterators.spliteratorUnknownSize(languages.elements(), Spliterator.ORDERED), false)
                                .map(JsonNode::asText)
                                .collect(Collectors.toSet());
        }
        else {
            return new HashSet<>();
        }
    }

    private JsonNode getSubNode(JsonNode node, String key, JsonNodeType nodeType) {
        JsonNode subNode = node.path(key);
        if (subNode != null && subNode.getNodeType() == nodeType) {
            return subNode;
        } else {
            throw new RuntimeException("JsonContentProcessor INVALID JSON CONTENT: YAML NODE TYPE NOT AS EXPECTED >" + node != null ? subNode.getNodeType().toString() : "---" + "<");
        }
    }

    @Override
    public List<TagValuesValuesDto> process(JsonNode node) {
        final long pk = this.getPkFromNode(node);
        final Set<String> languages = this.getLanguagesFromNode(node);
        final List<String> tagValues = this.getTagValuesFromNode(node);

        List<TagValuesValuesDto> tagValueDtos = new ArrayList<>();
        for (String tagValue : tagValues) {
            ObjectNode tagValueNode = JsonNodeFactory.instance.objectNode();
            if (languages.size() != 0) {
                for (String currentLanguage : languages) {
                    tagValueNode.put(currentLanguage, tagValue);
                }
            }
            else {
                tagValueNode.put(DEFAULT_LANGUAGE, tagValue);
            }
            TagValuesValuesDto dto = new TagValuesValuesDto();
            dto.setPk(pk);
            dto.setTagValue(tagValueNode.toString());
            dto.setOldTag(tagValue);
            tagValueDtos.add(dto);
        }

        return tagValueDtos.size() > 0 ? tagValueDtos : null;
    }
}