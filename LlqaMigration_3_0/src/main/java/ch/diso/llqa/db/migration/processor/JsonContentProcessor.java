package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.common.CommonConstants;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.item.ItemProcessor;

public abstract class JsonContentProcessor<Dto> implements ItemProcessor<JsonNode, Dto> {
    /**
     * get the primary key (source tables "ID") from a JSON record.
     *
     * @param jsonNode JSON object representing a record (PK and YAML content)
     * @return the records primary key. '-1' if failed
     */
    private long getPkFromNode(JsonNode jsonNode) {
        JsonNode idNode = jsonNode.path(CommonConstants.JSON_ID_NODE_KEY);
        if (idNode != null && idNode.isNumber())
            return idNode.asLong();
        else
            throw new RuntimeException("JsonContentProcessor INVALID JSON CONTENT: PRIMARY KEY");
    }

    /**
     * get the records JAML content as JSON
     *
     * @param jsonNode JSON object representing a record (PK and YAML content)
     * @return the records YAML content as JSON. 'null' if failed
     */
    private JsonNode getYamlFromNode(JsonNode jsonNode) {
        JsonNode yamlNode = jsonNode.path(CommonConstants.JSON_YAML_NODE_KEY);
        if (this.isYamlNodeOfExpectedType(yamlNode)) {
            return yamlNode;
        } else {
            throw new RuntimeException("JsonContentProcessor INVALID JSON CONTENT: YAML NODE TYPE NOT AS EXPECTED >" + yamlNode.getNodeType().toString() + "<");
        }
    }

    @Override
    public Dto process(JsonNode jsonNode) {
        long pk = getPkFromNode(jsonNode);
        JsonNode yamlNode = getYamlFromNode(jsonNode);
        return this.generateDto(pk, yamlNode);
    }

    /**
     * generate a DTO from a records primary key and its yaml content formated as JSON
     *
     * @param pk       the records origin primary key
     * @param yamlNode the YAML content formated as JSON
     * @return the DTO to be saved. can be null if needed.
     */
    protected abstract Dto generateDto(long pk, JsonNode yamlNode);

    /**
     * test if the YAML content node is of the correct type (object or array
     *
     * @param jsonNode the YAML content node
     * @return true/false
     */
    protected abstract boolean isYamlNodeOfExpectedType(JsonNode jsonNode);
}
