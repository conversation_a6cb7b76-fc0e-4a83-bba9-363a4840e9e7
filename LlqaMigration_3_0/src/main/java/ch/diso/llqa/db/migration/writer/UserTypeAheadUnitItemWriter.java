package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.UserTypeAheadUnit;
import javax.sql.DataSource;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class UserTypeAheadUnitItemWriter extends JdbcBatchItemWriter<UserTypeAheadUnit> {

    public static final String SQL = "INSERT INTO user_metadata (user_id, key, value, created_at, created_by, updated_at, updated_by) " +
            "VALUES (:userId, 'UNIT_VALUES', :typeAheadUnit, :createdAt, :createdBy, :updatedAt, :updatedBy)";

    @Autowired
    public UserTypeAheadUnitItemWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL);
        setDataSource(dataSource);
    }
}