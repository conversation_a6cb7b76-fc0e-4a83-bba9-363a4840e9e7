package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.TagValuesValuesDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;

@Component
public class TagValuesItemWriter extends JdbcBatchItemWriter<List<TagValuesValuesDto>> {

   private static final String SQL_INSERT =
         "INSERT INTO tag_values (tag_id, name, tmp_name, created_at, updated_at, created_by, updated_by) " +
               "VALUES (:pk, cast(:tagValue as json), :oldTag, :createdAt, :updatedAt, :createdBy, :updatedBy)";

   @Autowired
   public TagValuesItemWriter(@Qualifier("dataSource") DataSource dataSource) {
      setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
      setSql(SQL_INSERT);
      setDataSource(dataSource);
   }
}