package ch.diso.llqa.db.migration.step;


import ch.diso.llqa.db.migration.common.IndexedMapValue;
import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnContentsDto;
import ch.diso.llqa.db.migration.rowMapper.ConfigColumnsRowMapper;
import ch.diso.llqa.db.migration.writer.ConfigColumnsItemWriter;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Step;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class StepConfigColumnContents extends MigrationStep {

    public static final String PK_COLUMN_NAME = "id";
    public static final String CONFIG_TABLE_ID_COLUMN_NAME = "configtable_id";

    public static final List<IndexedMapValue> CONFIG_TABLE_COLUMN_NAMES = Arrays.asList(
            new IndexedMapValue(1,"col1", "col1"),
            new IndexedMapValue(2,"col2", "col2"),
            new IndexedMapValue(3,"col3", "col3"),
            new IndexedMapValue(4,"col4", "col4"),
            new IndexedMapValue(5,"col5", "col5"),
            new IndexedMapValue(6,"col6", "col6")
    );
    private static final String TABLE_NAMES = "config_entries";

    private static final String SQL_TEMPLATE =
            "SELECT  e.${id}, " +
                    "e.configtable_id, " +
                    "e.${col1}, " +
                    "e.${col2}, " +
                    "e.${col3}, " +
                    "e.${col4}, " +
                    "e.${col5}, " +
                    "e.${col6} "+
            "FROM ${config_entries} AS e "+
            "ORDER BY e.${id}, e.configtable_id";

    private final static Logger logger = LoggerFactory.getLogger(StepConfigColumnContents.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ConfigColumnsItemWriter<ConfigColumnContentsDto> configColumnsItemWriter;

    @Autowired
    private ConfigColumnsRowMapper configColumnsRowMapper;

    @Override
    public Step createStep() {

        return stepBuilderFactory.get("stepConfigColumns")
                .listener(stepCompletionListener)
                .<List<ConfigColumnContentsDto>, List<ConfigColumnContentsDto>>chunk(this.getChunkSize())
                .reader(this.createReader())
                .writer(configColumnsItemWriter)
                .build();
    }
    
    private ItemReader<List<ConfigColumnContentsDto>> createReader() {
        JdbcCursorItemReader<List<ConfigColumnContentsDto>> reader = new JdbcCursorItemReader<>();

        LinkedHashMap<String, String> sqlTemplateVars = this.createSqlTemplateVars();
        reader.setSql(StrSubstitutor.replace(SQL_TEMPLATE, sqlTemplateVars));
        logger.debug("SELECT STATEMENT >{}<", reader.getSql());

        reader.setDataSource(jdbcTemplate.getDataSource());


        configColumnsRowMapper = new ConfigColumnsRowMapper();
        configColumnsRowMapper.setColumnNames(CONFIG_TABLE_COLUMN_NAMES);
        configColumnsRowMapper.setType(ConfigColumnContentsDto.class);
        reader.setRowMapper(configColumnsRowMapper);
        reader.setFetchSize(this.getFetchSize());

        return reader;
    }

    private LinkedHashMap<String, String> createSqlTemplateVars(){

        return new LinkedHashMap<String, String>(){{
            put(PK_COLUMN_NAME, PK_COLUMN_NAME);
            putAll(CONFIG_TABLE_COLUMN_NAMES.stream()
                    .collect(Collectors.toMap(IndexedMapValue::getKey, IndexedMapValue::getValue)));
            put(TABLE_NAMES, TABLE_NAMES);
        }};
    }

    @Value("${config.columns.fetch.size}")
    private int fetchSize;

    @Value("${config.columns.chunk.size}")
    private int chunkSize;

    protected int getChunkSize() { return this.chunkSize; }

    protected int getFetchSize() { return this.fetchSize; }

}
