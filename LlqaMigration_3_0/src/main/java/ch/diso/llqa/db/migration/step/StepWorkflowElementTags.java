package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.dto.WorkflowElementTagDto;
import ch.diso.llqa.db.migration.rowMapper.WorkflowElementTagsRowMapper;
import ch.diso.llqa.db.migration.writer.ListUnpackingItemWriter;
import ch.diso.llqa.db.migration.writer.WorkflowElementTagsItemWriter;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Step;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class StepWorkflowElementTags extends MigrationStep {

    public static final String PK_COLUMN_NAME = "id";
    public static final String FK_COLUMN_NAME = "wfEltID";
    public static final LinkedHashMap<String, String> TAG_COLUMN_NAMES = new LinkedHashMap() {{
            put("1", "tag_1");
            put("2", "tag_2");
            put("3", "tag_3");
            put("4", "tag_4");
            put("5", "tag_5");
            put("6", "tag_6");
        }};
    private static final String TABLE_NAME = "procedures";

    private static final String SQL_TEMPLATE = "SELECT wfElt.${id} as wfEltID, ${1}, ${2}, ${3}, ${4}, ${5}, ${6} " +
                                                "FROM ${tableName} as p " +
                                                "INNER JOIN workflow_elements wfElt ON (p.workflow_element_id = wfElt.${id}) " +
                                                "ORDER BY wfElt.${id}";

    private final static Logger logger = LoggerFactory.getLogger(StepWorkflowElementTags.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private WorkflowElementTagsRowMapper workflowElementTagsRowMapper;

    @Autowired
    private WorkflowElementTagsItemWriter workflowElementTagsItemWriter;

    private ListUnpackingItemWriter writerWrapper;

    @Override
    public Step createStep() {
        writerWrapper = new ListUnpackingItemWriter<WorkflowElementTagDto>();
        writerWrapper.setDelegate(workflowElementTagsItemWriter);

        return stepBuilderFactory.get("stepWorkflowElementTags")
                .listener(stepCompletionListener)
                .<List<WorkflowElementTagDto>, List<WorkflowElementTagDto>>chunk(this.getChunkSize())
                .reader(this.createReader())
                .writer(writerWrapper)
                .build();
    }

    private ItemReader<List<WorkflowElementTagDto>> createReader() {
        JdbcCursorItemReader<List<WorkflowElementTagDto>> reader = new JdbcCursorItemReader<>();

        LinkedHashMap<String, String> sqlTemplateVars = this.createSqlTemplateVars();
        reader.setSql(StrSubstitutor.replace(SQL_TEMPLATE, sqlTemplateVars));
        logger.debug("SELECT STATEMENT >{}<", reader.getSql());

        reader.setDataSource(jdbcTemplate.getDataSource());
        reader.setRowMapper(workflowElementTagsRowMapper);
        reader.setFetchSize(this.getFetchSize());

        return reader;
    }

    private LinkedHashMap<String, String> createSqlTemplateVars(){

        return new LinkedHashMap<String, String>(){{
            put("id", PK_COLUMN_NAME);
            putAll(TAG_COLUMN_NAMES);
            put("tableName", TABLE_NAME);
        }};
    }

    @Value("${workflowelement.tags.fetch.size}")
    private int fetchSize;

    @Value("${workflowelement.tags.chunk.size}")
    private int chunkSize;

    protected int getChunkSize() { return this.chunkSize; }

    protected int getFetchSize() { return this.fetchSize; }
}
