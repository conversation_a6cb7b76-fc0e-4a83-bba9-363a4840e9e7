package ch.diso.llqa.db.migration.step.flowcontrol;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.WorkflowRuleDto;
import ch.diso.llqa.db.migration.processor.flowcontrol.FlowcontrolItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.step.JsonMigrationStep;
import ch.diso.llqa.db.migration.writer.ListUnpackingItemWriter;
import ch.diso.llqa.db.migration.writer.WorkflowRuleWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * base class from all flow control migration steps
 * <ul>
 * <li>procedures</li>
 * <li>steps</li>
 * <li>measures</li>
 * </ul>
 *
 * <AUTHOR>
 */
public abstract class StepWorkflowRule extends JsonMigrationStep {

    private static final String COLUMN_NAME = "flowcontrol";
    private static final ColumnType COLUMN_TYPE = ColumnType.ARRAY;
    private static final int MIN_ID = 1;

    @Autowired
    private WorkflowRuleWriter writer;

    @Autowired
    private FlowcontrolItemProcessor flowcontrolItemProcessor;

    private ListUnpackingItemWriter writerWrapper;

    @Override
    public Step createStep() {
        flowcontrolItemProcessor.setChildTableName(this.getTableName());
        flowcontrolItemProcessor.setSelectRealIdSqlFormat(this.getSelectRealIdSql());
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, this.getTableName(), this.getColumnName(), getColumnType(), this.getFetchSize(), this.getMinId(), this.password));

        writerWrapper = new ListUnpackingItemWriter<WorkflowRuleDto>();
        writerWrapper.setDelegate(writer);
        return stepBuilderFactory.get(this.getStepName()).listener(stepCompletionListener)
                .<JsonNode, List<WorkflowRuleDto>>chunk(this.getChunkSize())
                .reader(reader)
                .processor(this.flowcontrolItemProcessor)
                .writer(writerWrapper)
                .build();
    }

    /**
     * get the fetch size from the flow control step
     *
     * @return the fetch size from the flow control step
     */
    protected abstract int getFetchSize();

    /**
     * get the writer chunck size from the flow control step
     *
     * @return the writer chunck size from the flow control step
     */
    protected abstract int getChunkSize();

    /**
     * get the table name to generate the flow control migration on it
     *
     * @return the table name to generate the flow control migration on it
     */
    protected abstract String getTableName();

    /**
     * get the sql query to seledt the real id (groupe id)
     *
     * @return the sql query to seledt the real id (groupe id)
     */
    protected abstract String getSelectRealIdSql();

    /**
     * return the currents step name
     *
     * @return the current step name
     */
    protected abstract String getStepName();

    /**
     * get the name from the column to generate the flow control migration on it
     *
     * @return the name from the column to generate the flow control migration on it. always 'flowcontrol'
     */
    protected String getColumnName() {
        return COLUMN_NAME;
    }

    /**
     * get the start primary key from the reader
     *
     * @return the start primary key from the reader. allways '1'
     */
    protected int getMinId() {
        return MIN_ID;
    }

    /**
     * get the type from the column
     *
     * @return always {@link ColumnType#OBJECT}
     */
    protected ColumnType getColumnType() {
        return COLUMN_TYPE;
    }
}