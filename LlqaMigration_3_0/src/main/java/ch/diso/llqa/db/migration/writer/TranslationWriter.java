package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.TranslationDto;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Component
@Scope("prototype")
public class TranslationWriter implements ItemWriter<TranslationDto> {

    private static String EMPTY_TITLE = "{}";

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private String updateSql;

    @Override
    public void write(List<? extends TranslationDto> translationItems) {
        jdbcTemplate.batchUpdate(this.updateSql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int idx) throws SQLException {
                TranslationDto translationItem = translationItems.get(idx);
                final String title = !StringUtils.isEmpty(translationItem.getContent()) ? translationItem.getContent() : EMPTY_TITLE;
                ps.setString(1, title);
                ps.setLong(2, translationItem.getPk());
            }

            @Override
            public int getBatchSize() {
                return translationItems.size();
            }
        });
    }

    public void setUpdateSql(String updateSql) {
        this.updateSql = updateSql;
    }
}