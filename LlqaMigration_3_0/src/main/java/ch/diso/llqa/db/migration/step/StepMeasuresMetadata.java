package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.MeasureMetadataDto;
import ch.diso.llqa.db.migration.processor.MeasuresMetadataItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.ListUnpackingItemWriter;
import ch.diso.llqa.db.migration.writer.MeasuresMetadataItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StepMeasuresMetadata extends JsonMigrationStep {

    private static final String TABLE_NAME = "measures";

    private static final String COLUMN_NAME = "metadata";

    private static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

    @Value("${measures.metadata.fetch.size}")
    private int fetchSize;

    @Value("${measures.metadata.chunk.size}")
    private int chunkSize;

    private static final int MIN_ID = 1;

    @Autowired
    private MeasuresMetadataItemProcessor processor;

    @Autowired
    private MeasuresMetadataItemWriter writer;

    private ListUnpackingItemWriter writerWrapper;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));
        writerWrapper = new ListUnpackingItemWriter<MeasureMetadataDto>();
        writerWrapper.setDelegate(writer);
        return stepBuilderFactory.get("stepMeasuresMetadata").listener(stepCompletionListener)
                .<JsonNode, List<MeasureMetadataDto>> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writerWrapper)
                .build();
    }
}
