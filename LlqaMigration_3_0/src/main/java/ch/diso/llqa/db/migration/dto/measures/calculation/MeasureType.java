package ch.diso.llqa.db.migration.dto.measures.calculation;

import com.fasterxml.jackson.databind.JsonNode;

import java.util.Arrays;

import static ch.diso.llqa.db.migration.dto.measures.calculation.MatrixDtoFactory.createMatrix;

public enum MeasureType {

    MT1(1) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT1, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t1_targetv = yamlNode.path(T_1_TARGETV);
            JsonNode t1_targetu = yamlNode.path(T_1_TARGETU);
            JsonNode t1_tholdv = yamlNode.path(T_1_THOLDV);
            JsonNode t1_tholdu = yamlNode.path(T_1_THOLDU);
            dto.setValid(validate(t1_targetv, t1_targetv, t1_tholdv, t1_tholdu));
            dto.setTargetValue(t1_targetv.asDouble());
            dto.setTargetUnit(t1_targetu.asText());
            dto.setThresholdValue(t1_tholdv.asDouble());
            dto.setThresholdUnit(t1_tholdu.asText());
            return dto;
        }
    },
    MT2(2) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT2, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t2_minv = yamlNode.path(T_2_MINV);
            JsonNode t2_maxv = yamlNode.path(T_2_MAXV);
            JsonNode t2_unit = yamlNode.path(T_2_UNIT);
            dto.setValid(validate(t2_minv, t2_maxv, t2_unit));
            dto.setMinValue(t2_minv.asDouble());
            dto.setMaxValue(t2_maxv.asDouble());
            dto.setUnit(t2_unit.asText());
            return dto;
        }
    },
    MT3(3) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT3, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t3_minlen = yamlNode.path(T_3_MINLEN);
            dto.setValid(MeasureType.validate(t3_minlen));
            dto.setMinLength(t3_minlen.asInt());
            return dto;
        }
    },
    MT4(4) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT4, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t4_regexp = yamlNode.path(T_4_REGEXP);
            dto.setValid(MeasureType.validate(t4_regexp));
            dto.setRegexp(t4_regexp.asText());
            return dto;
        }
    },
    MT5(5) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT5, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t5_expected = yamlNode.path(T_5_EXPECTED);
            dto.setValid(MeasureType.validate(t5_expected));
            dto.setExpected(t5_expected.asInt());
            return dto;
        }
    },
    MT6(6) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return MeasureType.createDtoWithMatrix(measureId, yamlNode, MT1, MT6);
        }
    },
    MT7(7) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return MeasureType.createDtoWithMatrix(measureId, yamlNode, MT2, MT7);
        }
    },
    MT8(8) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT8, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t8_comparator = yamlNode.path(T_8_COMPARATOR);
            JsonNode t8_value = yamlNode.path(T_8_VALUE);
            JsonNode t8_unit = yamlNode.path(T_8_UNIT);
            dto.setValid(MeasureType.validate(t8_comparator, t8_value, t8_unit));
            dto.setComparator(t8_comparator.asInt());
            dto.setValue(t8_value.asDouble());
            dto.setUnit(t8_unit.asText());
            return dto;
        }
    },
    MT9(9) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return MeasureType.createDtoWithMatrix(measureId, yamlNode, MT8, MT9);
        }
    },
    MT10(10) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT10, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t10_comparator = yamlNode.path(T_10_COMPARATOR);
            JsonNode t10_value = yamlNode.path(T_10_VALUE);
            JsonNode t10_unit = yamlNode.path(T_10_UNIT);
            dto.setValid(MeasureType.validate(t10_comparator, t10_value, t10_unit));
            dto.setComparator(t10_comparator.asInt());
            dto.setValue(t10_value.asDouble());
            dto.setUnit(t10_unit.asText());
            return dto;
        }
    },
    MT11(11) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT11, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t11_unit = yamlNode.path(T_11_UNIT);
            dto.setValid(MeasureType.validate(t11_unit));
            dto.setUnit(t11_unit.asText());
            return dto;
        }
    },
    MT12(12) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return createDtoWithMatrix(measureId, yamlNode, MT11, MT12);
        }
    },
    MT13(13) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return MeasureType.createMeasureTypeDtoMT13OrMT14(measureId, yamlNode, MT13);
        }
    },
    MT14(14) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return MeasureType.createMeasureTypeDtoMT13OrMT14(measureId, yamlNode, MT14);
        }
    },
    MT15(15) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return createMeasureTypeDto15Or16(measureId, yamlNode, MT15);
        }
    },
    MT16(16) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            return createMeasureTypeDto15Or16(measureId, yamlNode, MT16);
        }
    },
    MT17(17) {
        @Override
        public MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode) {
            MeasureTypeDto dto = new MeasureTypeDto(measureId, MT17, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
            JsonNode t17_len = yamlNode.path(T_17_LEN);
            JsonNode t17_list = yamlNode.path(T_17_LIST);
            dto.setValid(MeasureType.validate(t17_len, t17_list));
            dto.setListLength(t17_len.asInt());
            dto.setListContent(t17_list.toString());
            return dto;
        }
    };

    public static final String T_17_LIST = "t17_list";
    public static final String T_17_LEN = "t17_len";
    public static final String T_11_UNIT = "t11_unit";
    public static final String T_10_UNIT = "t10_unit";
    public static final String T_10_VALUE = "t10_value";
    public static final String T_10_COMPARATOR = "t10_comparator";
    public static final String T_8_UNIT = "t8_unit";
    public static final String T_8_VALUE = "t8_value";
    public static final String T_8_COMPARATOR = "t8_comparator";
    public static final String MX_XSIZE = "mx_xsize";
    public static final String T_5_EXPECTED = "t5_expected";
    public static final String T_4_REGEXP = "t4_regexp";
    public static final String T_3_MINLEN = "t3_minlen";
    public static final String T_2_UNIT = "t2_unit";
    public static final String T_2_MAXV = "t2_maxv";
    public static final String T_2_MINV = "t2_minv";
    public static final String OPTIONAL = "optional";
    public static final String INTERNAL = "internal";
    public static final String T_1_THOLDU = "t1_tholdu";
    public static final String T_1_THOLDV = "t1_tholdv";
    public static final String T_1_TARGETU = "t1_targetu";
    public static final String T_1_TARGETV = "t1_targetv";
    public static final String CMPLCODE = "complcode";
    public static final String T_15_COMPARATOR = "t15_comparator";
    public static final String T_15_VALUE = "t15_value";

    private int mtCode;

    MeasureType(int mtCode) {
        this.mtCode = mtCode;
    }


    public abstract MeasureTypeDto createMeasureTypeDto(long measureId, JsonNode yamlNode);

    public int getMtCode() {
        return this.mtCode;
    }

    private static boolean validate(JsonNode... nodes) {
        return Arrays.stream(nodes).noneMatch(JsonNode::isMissingNode);
    }

    private static MeasureTypeDto createDtoWithMatrix(long measureId, JsonNode yamlNode, MeasureType... types) {
        MeasureTypeDto dto = types[0].createMeasureTypeDto(measureId, yamlNode);
        dto.setMeasureType(types[1]);
        if (dto.isValid()) {
            dto.setValid(MeasureType.validate(yamlNode.path(MX_XSIZE)));
            if (dto.isValid()) {
                createMatrix(yamlNode, dto);
            }
        }
        return dto;
    }

    private static MeasureTypeDto createMeasureTypeDtoMT13OrMT14(long measureId, JsonNode yamlNode, MeasureType type) {
        MeasureTypeDto dto = new MeasureTypeDto(measureId, type, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
        JsonNode cmplcode = yamlNode.path(CMPLCODE);
        dto.setValid(MeasureType.validate(cmplcode));
        dto.setGrouping(cmplcode.asText());
        return dto;
    }

    private static MeasureTypeDto createMeasureTypeDto15Or16(long measureId, JsonNode yamlNode, MeasureType measureType) {
        MeasureTypeDto dto = new MeasureTypeDto(measureId, measureType, yamlNode.path(INTERNAL).asBoolean(), yamlNode.path(OPTIONAL).asBoolean());
        JsonNode t15_comparator = yamlNode.path(T_15_COMPARATOR);
        JsonNode t15_value = yamlNode.path(T_15_VALUE);
        JsonNode complcode = yamlNode.path(CMPLCODE);
        dto.setValid(validate(t15_comparator, t15_value, complcode));
        dto.setComparator(t15_comparator.asInt());
        dto.setValue(t15_value.asDouble());
        dto.setGrouping(complcode.asText());
        return dto;
    }

}
