package ch.diso.llqa.db.migration.processor.flowcontrol;

import ch.diso.llqa.db.migration.dto.WorkflowRuleDto;
import ch.diso.llqa.db.migration.processor.JsonContentListProcessor;
import ch.diso.llqa.db.migration.processor.flowcontrol.WorkflowRuleWorkflowGroupLoader.WorkflowRuleWorkflowGroupLoader;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Collection;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Scope("prototype")
public class FlowcontrolItemProcessor extends JsonContentListProcessor<WorkflowRuleDto> {

    public static String SQL_WF_ELT_ID = "wf_elt_id";
    public static String SELECT_WORKFLOW_ELEMENT_FROM_CHILD_TABLE = "SELECT wfElt.id AS " + SQL_WF_ELT_ID + " FROM %s childTable INNER JOIN workflow_elements wfElt ON childTable.workflow_element_id = wfElt.id WHERE childTable.id = %d";

    private static String FLOWCONTROL_TYPE_KEY = "type";
    private static String FLOWCONTROL_CODE_KEY = "code";
    private static String FLOWCONTROL_RES_KEY = "res";
    private static String FLOWCONTROL_TRIGGER_KEY = "trigger";
    private static String FLOWCONTROL_INVERSE_KEY = "inverse";

    protected String childTableName;
    protected String selectRealIdSqlFormat;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    protected List<WorkflowRuleDto> generateDto(long childTablePk, JsonNode yamlNode) {
        long workFlowElementPk = this.getWorkflowElementIdFromChildId(childTablePk);
        List<WorkflowRuleDto> workflowRules = this.transfomrWorkflowRules(childTablePk, yamlNode, workFlowElementPk);
        return !workflowRules.isEmpty() ? workflowRules : null;
    }

    /**
     * transform the YAML workflow rules array to the new workflow
     *
     * @param yamlNode          the old YAML workflow rules as JSON array
     * @param workFlowElementPk primary key of the origin workflow element (PK of procedure, step or measure)
     * @return list of generated workflow rules
     */
    private List<WorkflowRuleDto> transfomrWorkflowRules(long childTablePk, JsonNode yamlNode, long workFlowElementPk) {
        List<WorkflowRuleDto> workflowRules = new ArrayList<>();

        for (JsonNode yamlFlowcontrolElement : yamlNode) {
            List<WorkflowRuleDto> currentWorkflowRules = this.transformWorkflowRule(childTablePk, yamlFlowcontrolElement, workFlowElementPk);
            if (currentWorkflowRules != null) {
                workflowRules.addAll(currentWorkflowRules);
            }
        }
        return workflowRules;
    }

    /**
     * transform a YAML flowcontrol element to a new workflow rule
     *
     * @param yamlFlowcontrolElement the old YAML flowcontrol element as JSON
     * @param ownerWorkFlowElementPk      primary key of the origin workflow element (PK of procedure, step or measure)
     * @return the transformed workflow role
     */
    private List<WorkflowRuleDto> transformWorkflowRule(long childTablePk, JsonNode yamlFlowcontrolElement, long ownerWorkFlowElementPk) {
        if (yamlFlowcontrolElement == null || !yamlFlowcontrolElement.isObject()) {
            throw new RuntimeException("INVALID YAML FLOW CONTROL ELEMENT");
        }
        final String type = this.getStringFromJsonNode(yamlFlowcontrolElement, FLOWCONTROL_TYPE_KEY);
        final String code = this.getStringFromJsonNode(yamlFlowcontrolElement, FLOWCONTROL_CODE_KEY);
        final String res = this.getStringFromJsonNode(yamlFlowcontrolElement, FLOWCONTROL_RES_KEY);
        final String trigger = this.getStringFromJsonNode(yamlFlowcontrolElement, FLOWCONTROL_TRIGGER_KEY);
        final boolean inverse = this.getBooleanFromJsonNode(yamlFlowcontrolElement, FLOWCONTROL_INVERSE_KEY);
        WorkflowRuleWorkflowGroupLoader wfRuleGroupLoader = this.getWorkflowRuleWorkflowGroupLoader(type);
        final String selectRealIdSql = String.format(this.selectRealIdSqlFormat, childTablePk);
        Collection<Long> targetWorkflowGroupPks = wfRuleGroupLoader.loadWorkflowElementGroupPks(this.jdbcTemplate, selectRealIdSql, code);
        if (!targetWorkflowGroupPks.isEmpty()) {
            if (Strings.isNullOrEmpty(res))
                throw new RuntimeException("'RES' FOR RULE PK >" + ownerWorkFlowElementPk + "<, TYPE >" + type + "<, CODE >" + code + "< NOT SET");
            if (Strings.isNullOrEmpty(trigger))
                throw new RuntimeException("'TRIGGER' FOR RULE PK >" + ownerWorkFlowElementPk + "<, TYPE >" + type + "<, CODE >" + code + "< NOT SET");
            List<WorkflowRuleDto> workflowRuleDtos = new ArrayList<>();
            for (Long pk : targetWorkflowGroupPks) {
                WorkflowRuleDto currentDto = new WorkflowRuleDto();
                currentDto.setOwnerWorkflowElementId(ownerWorkFlowElementPk);
                currentDto.setResult(res);
                currentDto.setTrigger(trigger);
                currentDto.setInverse(inverse);
                currentDto.setTargetWorkflowElementGroupId(pk);
                workflowRuleDtos.add(currentDto);
            }
            return workflowRuleDtos;
        }
        else {
            return null;
        }
    }

    /**
     * get the text from a node
     *
     * @param node the node to get the string node from
     * @param key  the key from the child string node
     * @return the string content
     */
    private String getStringFromJsonNode(JsonNode node, String key) {
        if (node != null) {
            final JsonNode stringNode = node.get(key);
            if (stringNode != null && stringNode.isTextual())
                return stringNode.asText();
            else
                return null;

        } else {
            return null;
        }
    }

    /**
     * get the boolean context from a node
     *
     * @param node the node to get the boolean node from
     * @param key  the key from the child boolean node
     * @return the boolean content, false if node not avaiable
     */
    private boolean getBooleanFromJsonNode(JsonNode node, String key) {
        if (node != null) {
            final JsonNode stringNode = node.get(key);
            if (stringNode != null && stringNode.isBoolean())
                return stringNode.asBoolean();
            else
                return false;

        } else {
            return false;
        }
    }

    /**
     * get the group loader for a specific workflow group
     *
     * @param type the type from the workflow group
     *             <ul>
     *             <li></li>
     *             <li>M: measure </li>
     *             <li>S: step</li>
     *             <li>P: procedure</li>
     *             <li>CO: config entry</li>
     *             <li>DT: device type</li>
     *             <li>MD: model</li>
     *             <li>CT: check type</li>
     *             </ul>
     * @return the group loader for the given type
     */
    private WorkflowRuleWorkflowGroupLoader getWorkflowRuleWorkflowGroupLoader(String type) {
        WorkflowRuleWorkflowGroupLoader wfRuleGrpLoader = (WorkflowRuleWorkflowGroupLoader) applicationContext.getBean(type);
        if (wfRuleGrpLoader != null) {
            return wfRuleGrpLoader;
        } else {
            throw new RuntimeException("COULD NOT LOAD WORKFLOW RULE GROUP LOADER FOR TYPE >" + type + "<");
        }
    }

    /**
     * test if the node is of type array
     *
     * @return 'true' if the node is of type array. otherwise 'false'
     */
    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isArray();
    }

    /**
     * get the ID from the workflow element from a child table (such as: procedures, steps or measures)
     *
     * @param childTablePk the ID from the child table
     * @return the ID from the parent table (workflow_elements)
     */
    private long getWorkflowElementIdFromChildId(long childTablePk) {
        final String sqlQuery = this.generateSelectWorkflowElementSql(childTablePk);
        List<Map<String, Object>> rows = jdbcTemplate.queryForList(sqlQuery);
        if (rows.size() == 1) {
            Map<String, Object> records = rows.get(0);
            Object transKey = records.get(SQL_WF_ELT_ID);
            if (transKey instanceof Long) {
                return (Long) transKey;
            } else {
                throw new ClassCastException("PRIMARY KEY IS NO LONG");
            }
        } else if (rows.size() > 1) {
            throw new RuntimeException("TO MANY PRIMARY KEYS (>" + rows.size() + "<) >" + this.getChildTableName() + "< WITH PK >" + childTablePk + "<. SQL >" + sqlQuery + "<");
        } else {
            throw new RuntimeException("COULD NOT GET PRIMARY KEY FOR CHILD TABLE >" + this.getChildTableName() + "< WITH PK >" + childTablePk + "<. SQL >" + sqlQuery + "<");
        }
    }

    /**
     * generate the SQL query to select the primary key from the work flow element with given primary key from the child table
     *
     * @param childTablePk the child tables primary key (primary key from procedures, steps or measures)
     * @return the SQL query to select the primary key from workflow_elements
     */
    public String generateSelectWorkflowElementSql(long childTablePk) {
        return String.format(SELECT_WORKFLOW_ELEMENT_FROM_CHILD_TABLE, this.getChildTableName(), childTablePk);
    }

    public String getChildTableName() {
        return childTableName;
    }

    public void setChildTableName(String childTableName) {
        this.childTableName = childTableName;
    }

    public void setSelectRealIdSqlFormat(String selectRealIdSqlFormat) {
        this.selectRealIdSqlFormat = selectRealIdSqlFormat;
    }
}