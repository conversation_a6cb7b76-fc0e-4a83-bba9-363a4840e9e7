package ch.diso.llqa.db.migration.processor.CheckCheckdata;

import ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto;
import ch.diso.llqa.db.migration.processor.JsonContentListProcessor;
import ch.diso.llqa.db.migration.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class CheckCheckdataAssigneeItemProcessor extends JsonContentListProcessor<CheckDataAssigneeDto> {

    private final static Logger logger = LoggerFactory.getLogger(CheckCheckdataAssigneeItemProcessor.class);

    private AtomicLong assigneeId = new AtomicLong(1L);

    private static final String ASSIGNEE_USER_ID_KEY = "user_id";
    private static final String ASSIGNEE_GROUP_ID_KEY = "group_id";
    private static final String ASSIGNEE_REGISTRATION_MODE_KEY = "regmode";
    private static final String ASSIGNEE_SUBOF_KEY = "subof";
    private static final String ASSIGNEE_SELF_ASSIGN_KEY = "selfassign";

    @Override
    protected List<CheckDataAssigneeDto> generateDto(long checkId, JsonNode assignees) {
        List<CheckDataAssigneeDto> generatedAssignees = this.generateAssignees(checkId, assignees);
        return this.initializeAssigneeBlockReferences(generatedAssignees, assignees);
    }

    private List<CheckDataAssigneeDto> generateAssignees(long checkId, JsonNode assignees) {
        List<CheckDataAssigneeDto> generatedAssignees = new ArrayList<>();
        for (int index = 0; index < assignees.size(); ++index) {
            JsonNode currentAssigneeNode = assignees.get(index);
            if (!currentAssigneeNode.isObject())
                throw new RuntimeException("THE ASSIGNEE NODE TYPE IS NOT AN OBJECT >" + currentAssigneeNode.getNodeType() + "<");
            Long userId = JsonUtils.getLongValue(currentAssigneeNode, ASSIGNEE_USER_ID_KEY);
            Long userGroupId = JsonUtils.getLongValue(currentAssigneeNode, ASSIGNEE_GROUP_ID_KEY);
            CheckDataAssigneeDto.RegistrationMode registrationMode = CheckDataAssigneeDto.RegistrationMode.getRegMode(JsonUtils.getIntegerValue(currentAssigneeNode, ASSIGNEE_REGISTRATION_MODE_KEY));

            if (!this.isAssigneeUserIdAndGroupIdExclusiveSet(userId, userGroupId)) {
                logger.info("USER ID AND GROUP ID IS NOT EXCLUSIVELY SET. NOT SET IN CHECK >" + checkId + "<");
                userGroupId = null;
            }
            CheckDataAssigneeDto assigneeDto = new CheckDataAssigneeDto();
            assigneeDto.setPk(this.generateAssigneePk());
            assigneeDto.setCheckId(checkId);
            assigneeDto.setUserId(userId);
            assigneeDto.setUserGroupId(userGroupId);
            assigneeDto.setRegistrationMode(registrationMode);
            assigneeDto.setOldAssigneeIndex(index);
            generatedAssignees.add(assigneeDto);
        }
        return generatedAssignees;
    }

    private List<CheckDataAssigneeDto> initializeAssigneeBlockReferences(List<CheckDataAssigneeDto> assignees, JsonNode assigneeNodes) {
        for (int index = 0; index < assigneeNodes.size(); ++index) {
            JsonNode currentAssigneeNode = assigneeNodes.get(index);
            final Integer subof = JsonUtils.getIntegerValue(currentAssigneeNode, ASSIGNEE_SUBOF_KEY);
            if (subof != null) {
                Optional<CheckDataAssigneeDto> currentAssigneeBlock = this.findCheckDataAssigneeBlockDto(assignees, index);
                Optional<CheckDataAssigneeDto> referencedAssigneeBlock = this.findCheckDataAssigneeBlockDto(assignees, subof);
                if (!currentAssigneeBlock.isPresent()) {
                    throw new RuntimeException("COULD NOT FIND THE ASSIGNEE BLOCK FOR THE INDEX >" + index + "<");
                }
                if (!referencedAssigneeBlock.isPresent()) {
                    throw new RuntimeException("COULD NOT FIND THE REFERENCED ASSIGNEE BLOCK FOR THE SEARCHED SUBOF >" + subof + "<");
                }
                currentAssigneeBlock.get().setOldAssigneeParentIndex(referencedAssigneeBlock.get().getOldAssigneeIndex());
            }
        }
        return assignees;
    }

    private Optional<CheckDataAssigneeDto> findCheckDataAssigneeBlockDto(List<CheckDataAssigneeDto> assignees, int oldIndex) {
        return assignees.stream()
                        .filter(Objects::nonNull)
                        .filter(block -> oldIndex == block.getOldAssigneeIndex())
                        .findAny();
    }

    private boolean isAssigneeUserIdAndGroupIdExclusiveSet(Long userId, Long groupId) {
        return userId != null ^ groupId != null;
    }

    /**
     * generate a assignee primary key
     *
     * @return an auto generated key from a DB sequence
     */
    private long generateAssigneePk() {
        return this.assigneeId.getAndAdd(1L);
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isArray();
    }
}