package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.util.JsonUtils;
import ch.diso.llqa.db.migration.dto.NoticeTimelineDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class NoticeTimelineProcessor extends JsonContentListProcessor<NoticeTimelineDto> {

    private static final String NEW_STATE_JSON_KEY = "new_status";
    private static final String TIMESTAMP_JSON_KEY = "timestamp";
    private static final String USER_JSON_KEY = "user";
    private static final String COMMENT_JSON_KEY = "comment";

    @Override
    protected List<NoticeTimelineDto> generateDto(long noticeId, JsonNode yamlNode) {
        List<NoticeTimelineDto> timeline = new ArrayList<>();
        for (JsonNode timelineElementNode : yamlNode) {
            timeline.add(this.generateNoticeTimelineDto(noticeId, timelineElementNode));
        }
        return timeline;
    }

    private NoticeTimelineDto generateNoticeTimelineDto(long noticeId, JsonNode timelineElementNode) {
        NoticeTimelineDto timelineElement = new NoticeTimelineDto();
        timelineElement.setNoticeId(noticeId);
        timelineElement.setComment(JsonUtils.getStringValue(timelineElementNode, COMMENT_JSON_KEY));
        timelineElement.setUserId(JsonUtils.getLongValue(timelineElementNode, USER_JSON_KEY));
        timelineElement.setStatus(JsonUtils.getLongValue(timelineElementNode, NEW_STATE_JSON_KEY));
        timelineElement.setTimestamp(this.getDateFromUnixTime(JsonUtils.getLongValue(timelineElementNode, TIMESTAMP_JSON_KEY)));
        return timelineElement;
    }

    private Date getDateFromUnixTime(Long unixTs) {
        if (unixTs != null) {
            return new Date(unixTs.longValue() * 1000l);
        } else {
            return null;
        }
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isArray();
    }
}