package ch.diso.llqa.db.migration.processor.flowcontrol.WorkflowRuleWorkflowGroupLoader;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * load a workflow element from a check type code
 *
 * <AUTHOR>
 */
@Component("CT")
public class WorkflowRuleCheckTypeLoader implements WorkflowRuleWorkflowGroupLoader {

    private final static Logger logger = LoggerFactory.getLogger(WorkflowRuleCheckTypeLoader.class);
    public static final String WF_ELT_GRP_ID_COL_NAME = "wf_elt_grp_id";
    public static final String SQL_SELECT_WF_GRP_ELT_FORMAT =
            "SELECT DISTINCT(wfEltGrp.id) AS " + WF_ELT_GRP_ID_COL_NAME + " " +
                    "FROM check_types ct INNER JOIN workflow_elements wfElt ON ct.workflow_element_id = wfElt.id" +
                    "                    INNER JOIN workflow_element_groups wfEltGrp ON wfElt.workflow_element_group_id = wfEltGrp.id " +
                    "WHERE wfEltGrp.child_table = 'check_type'" +
                    "  AND ct.code = '%s'";

    /**
     * load a check type workflow element primary key by its code
     *
     * @param jdbcTemplate the JDBC template to perform the DB select
     * @param code         the child tables code. code from the check type group as in the DB
     * @return the workflow group element primary key
     */
    @Override
    public List<Long> loadWorkflowElementGroupPks(JdbcTemplate jdbcTemplate, String selectRealIdSql, String code) {
        String sqlSelect = String.format(SQL_SELECT_WF_GRP_ELT_FORMAT, code);
        List<Map<String, Object>> rows = jdbcTemplate.queryForList(sqlSelect);
        if (rows.size() >= 1) {
            List<Long> ids = rows.stream()
                                 .filter(r -> r.containsKey(WF_ELT_GRP_ID_COL_NAME))
                                 .filter(r -> r.get(WF_ELT_GRP_ID_COL_NAME) instanceof Long)
                                 .map(r -> ((Long) r.get(WF_ELT_GRP_ID_COL_NAME)).longValue())
                                 .collect(Collectors.toList());
            if (!ids.isEmpty()) {
                return ids;
            } else {
                throw new RuntimeException("NO WORKFLOW ELEMENT PRIMARY KEY FOUND IN RESULT SET. CODE >" + code + "<");
            }
        } else {
            logger.warn("NO CHECK TYPE REFERENCE FOUND WITH CODE >" + code + "<");
            return Lists.newArrayList();
        }
    }
}