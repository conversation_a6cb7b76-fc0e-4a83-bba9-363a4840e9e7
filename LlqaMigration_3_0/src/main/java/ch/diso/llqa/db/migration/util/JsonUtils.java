package ch.diso.llqa.db.migration.util;

import com.fasterxml.jackson.databind.JsonNode;

public abstract class JsonUtils {

    public static Short getShortValue(JsonNode node, String key) {
        JsonNode subNode = getValue(node, key);
        if (subNode != null && (subNode.isShort() || subNode.isInt()))
            return subNode.shortValue();
        else
            return null;
    }

    public static Integer getIntegerValue(JsonNode node, String key) {
        JsonNode subNode = getValue(node, key);
        if (subNode != null && subNode.isInt())
            return subNode.intValue();
        else
            return null;
    }

    public static Long getLongValue(JsonNode node, String key) {
        JsonNode subNode = getValue(node, key);
        if (subNode != null && (subNode.isLong() || subNode.isInt()))
            return subNode.longValue();
        else
            return null;
    }

    public static Boolean getBooleanValue(JsonNode node, String key) {
        JsonNode subNode = getValue(node, key);
        if (subNode != null && subNode.isBoolean())
            return subNode.booleanValue();
        else
            return null;
    }

    public static Boolean getBooleanFromIntegerValue(JsonNode node, String key) {
        Integer integerValue = getIntegerValue(node, key);
        if (integerValue != null) {
            if (integerValue.equals(0))
                return false;
            else
                return true;
        } else {
            return null;
        }
    }

    public static String getStringValue(JsonNode node, String key) {
        JsonNode subNode = getValue(node, key);
        if (subNode != null && subNode.isTextual())
            return subNode.textValue();
        else
            return null;
    }

    public static JsonNode getValue(JsonNode node, String key) {
        if (node != null && node.has(key)) {
            return node.get(key);
        } else {
            return null;
        }
    }
}
