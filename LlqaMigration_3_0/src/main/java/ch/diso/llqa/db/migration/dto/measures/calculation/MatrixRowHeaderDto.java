package ch.diso.llqa.db.migration.dto.measures.calculation;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import com.google.common.base.Strings;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MatrixRowHeaderDto extends LlqaDto {

    private MatrixDto matrixDto;

    private int rowNumber;

    private String valueLeft;

    public boolean isEmpty() {
        return Strings.isNullOrEmpty(valueLeft);
    }
}
