package ch.diso.llqa.db.migration.reader;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.common.CommonConstants;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.io.IOException;
import java.util.List;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
public class TagValuesReader implements ItemReader<JsonNode> {

   private static final String YAML_NODE_NAME = "yaml";
   private static final String ID_NODE_NAME = "id";

   private JsonItemReaderConfiguration nameConfiguration;

   @Value("${database.name}")
   protected String databaseName;

   @Value("${spring.datasource.password}")
   protected String password;

   @Autowired
   protected JsonItemReader valueReader;

   @Autowired
   protected JsonItemReader nameReader;

   public void configure(JsonItemReaderConfiguration configuration) {
      valueReader.configure(configuration);
      this.nameConfiguration = new JsonItemReaderConfiguration(this.databaseName, "tags", "value", ColumnType.OBJECT, 1, -1, password);
   }

   @Override
   public JsonNode read() throws IOException, InterruptedException {
      JsonNode node = valueReader.read();
      if (node != null && !node.isNull()) {
         JsonNode idNode = node.get(ID_NODE_NAME);
         if (idNode != null && idNode.isNumber()) {
            long currentId = idNode.asLong();
            this.nameConfiguration.updateMinId(currentId);
            this.nameReader.configure(this.nameConfiguration);
            JsonNode nameNodeWrapper = this.nameReader.read();
            JsonNode nameNode = nameNodeWrapper.get(YAML_NODE_NAME);
            if (nameNode != null && nameNode.isObject()) {
               ObjectMapper mapper = new ObjectMapper();
               ObjectNode nameNodeObj = (ObjectNode)nameNode;

               List<String> languages = StreamSupport.stream(Spliterators.spliteratorUnknownSize(nameNodeObj.fieldNames(), Spliterator.ORDERED), false).collect(Collectors.toList());
               ArrayNode languageNodes = mapper.createArrayNode();
               languages.forEach(lang -> languageNodes.add(lang));
               ((ObjectNode) node).putArray(CommonConstants.JSON_LANGUAGE_NODE_KEY).addAll(languageNodes);
            }
         }
         return node;
      } else {
         return null;
      }
   }
}