package ch.diso.llqa.db.migration.reader;

import ch.diso.llqa.db.migration.common.ColumnType;

public class JsonItemReaderConfiguration {

    private final String databaseName;

    private final String tableName;

    private final String columnName;

    private final ColumnType columnType;

    private final int fetchSize;

    private long minId;

    private final String password;

    public JsonItemReaderConfiguration(String databaseName, String tableName, String columnName, ColumnType columnType, int fetchSize, int minId, String password) {
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.columnName = columnName;
        this.columnType = columnType;
        this.fetchSize = fetchSize;
        this.minId = minId;
        this.password = password;
    }
    public String getDatabaseName() {
        return databaseName;
    }

    public String getTableName() {
        return tableName;
    }

    public String getColumnName() {
        return columnName;
    }

    public ColumnType getColumnType() {
        return columnType;
    }

    public int getFetchSize() {
        return fetchSize;
    }

    public long getMinId() {
        return minId;
    }

    public String getPassword() {
        return password;
    }

    public void updateMinId(long minId) {
        this.minId = minId;
    }
}
