package ch.diso.llqa.db.migration.writer;

import static ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckDto.WorkflowElementType.MEASURE;
import static ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckDto.WorkflowElementType.PROCEDURE;
import static ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckDto.WorkflowElementType.STEP;

import ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto;
import ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckContainerDto;
import ch.diso.llqa.db.migration.dto.CheckCheckdata.WorkflowElementCheckDto;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class CheckCheckdataItemWriter implements ItemWriter<WorkflowElementCheckContainerDto> {

    private static final int BULK_IDX_BOTTOM = 0;
    private static final int BULK_IDX_TOP = 1;

    private static final String SQL_SELECT_WF_ELT_FROM_PROCEDURE_ID =
            "SELECT wfElt.id " +
                    "FROM procedures p INNER JOIN workflow_elements wfElt ON p.workflow_element_id = wfElt.id " +
                    "WHERE p.id = ?";
    private static final String SQL_SELECT_WF_ELT_FROM_STEP_ID =
            "SELECT wfElt.id " +
                    "FROM steps s INNER JOIN workflow_elements wfElt ON s.workflow_element_id = wfElt.id " +
                    "WHERE s.id = ?";
    private static final String SQL_SELECT_WF_ELT_FROM_MEASURE_ID =
            "SELECT wfElt.id " +
                    "FROM measures m INNER JOIN workflow_elements wfElt ON m.workflow_element_id = wfElt.id " +
                    "WHERE m.id = ?";
    private static final String INSERT_WORKFLOW_ELEMENT_CHECK_SQL_FORMAT =
            "INSERT INTO workflow_element_checks (check_id, workflow_element_id, enforce, assignee_id, committed_by, status, locked, procedure_seqnum, step_seqnum, measure_seqnum, created_at, updated_at, created_by, updated_by) " +
                    "VALUES (?, (%s), ?, (SELECT MIN(ass.id) FROM assignees ass WHERE ass.check_id = ? AND ass.old_index = ?), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static final String INSERT_ASSIGNEE_BLOCK_SQL =
            "INSERT INTO assignees (assignee_id, old_index, user_group_id, user_id, check_id, created_at, updated_at, created_by, updated_by) " +
                    "VALUES (NULL, ?, (SELECT usrGrp.id FROM user_groups usrGrp WHERE usrGrp.id = ?), (SELECT usr.id FROM users usr WHERE usr.id = ?), ?, ?, ?, ?, ?)";
    private static final String INSERT_REGISTERED_ASSIGNEE_SQL =
            "INSERT INTO assignees (assignee_id, old_index, user_group_id, user_id, check_id, created_at, updated_at, created_by, updated_by) " +
                    "VALUES ((SELECT ass.id FROM assignees ass WHERE ass.check_id = ? AND ass.old_index = ?), ?, (SELECT usrGrp.id FROM user_groups usrGrp WHERE usrGrp.id = ?), (SELECT usr.id FROM users usr WHERE usr.id = ?), ?, ?, ?, ?, ?)";
    private static final String UPDATE_MEASUREMENT_WF_ELT_CHECK_ID = "UPDATE measurements " +
            "SET workflow_element_check_id = " +
            "(" +
            "  SELECT wfEltCheck.id" +
            "  FROM measures m INNER JOIN workflow_elements wfElt ON m.workflow_element_id = wfElt.id" +
            "                  INNER JOIN workflow_element_checks wfEltCheck ON wfElt.id = wfEltCheck.workflow_element_id" +
            "                  INNER JOIN checks c ON wfEltCheck.check_id = c.id" +
            "  WHERE m.id = ?" +
            "    AND c.id = ?" +
            ") " +
            "WHERE id = ?" +
            "  AND workflow_element_check_id IS NULL";
    private static final String UPDATE_CHECK_ASSIGNMENT_MODE_SQL = "UPDATE checks SET assignment_mode = ? WHERE ID = ?";
    private static final String UPDATE_CHECK_REGISTRATION_MODE_SQL = "UPDATE checks SET registration_mode = ? WHERE ID = ?";

    private final static Logger logger = LoggerFactory.getLogger(CheckMetadataItemWriter.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${checks.checkdata.bulk.size}")
    private int bulkSize;

    @Override
    public void write(List<? extends WorkflowElementCheckContainerDto> containerDtos) {

        logger.debug("PERSIST >{}< CHECK DATA...", containerDtos.size());

        List<CheckDataAssigneeDto> assigneesToSave = containerDtos.stream()
                                                                  .flatMap(dto -> dto.getAssignees().stream())
                                                                  .collect(Collectors.toList());
        this.saveAssignees(assigneesToSave);

        List<WorkflowElementCheckDto> workflowElementChecksToSave = containerDtos.stream()
                .flatMap(containerElt -> containerElt.getWorkflowElementCheckDtos().stream())
                .collect(Collectors.toList());
        this.saveWorkflowElementChecks(workflowElementChecksToSave);

        List<WorkflowElementCheckDto> workflowElementChecksToUpdateMeasurements = containerDtos.stream()
                .flatMap(containerElt -> containerElt.getWorkflowElementCheckDtos().stream())
                .filter(wfEltCheck -> wfEltCheck.getMeasurementId() != null)
                .filter(wfEltCheck -> MEASURE.equals(wfEltCheck.getWorkflowElementType()))
                .collect(Collectors.toList());
        this.updateMeasurementWorkflowEltCheckId(workflowElementChecksToUpdateMeasurements);

        this.updateCheckAttributes(containerDtos);

        logger.debug("...DONE");
    }

    private String generateInsertWorkflowElementSql(String selectWfEltSubQuery) {
        return String.format(INSERT_WORKFLOW_ELEMENT_CHECK_SQL_FORMAT, selectWfEltSubQuery);
    }

    private List<int[]> generateBulkIndexes(List<?> objs) {
        List<int[]> idx = new ArrayList<>();
        for (int idxBottom = 0; idxBottom < objs.size(); idxBottom += this.bulkSize) {
            int idxTop = Math.min(idxBottom + this.bulkSize, objs.size());
            idx.add(new int[]{idxBottom, idxTop});
        }
        return idx;
    }

    private void saveWorkflowElementChecks(List<WorkflowElementCheckDto> workflowElementChecks) {
        List<WorkflowElementCheckDto> workflowElementChecksProcedures = workflowElementChecks.stream()
                .filter(wfEltCheck -> PROCEDURE.equals(wfEltCheck.getWorkflowElementType()))
                .collect(Collectors.toList());
        logger.debug("PERSIST >{}< WORKFLOW CHECKS (PROCEDURE)...", workflowElementChecks.size());
        this.saveWorkflowElementChecks(this.generateInsertWorkflowElementSql(SQL_SELECT_WF_ELT_FROM_PROCEDURE_ID), workflowElementChecksProcedures);

        List<WorkflowElementCheckDto> workflowElementChecksSteps = workflowElementChecks.stream()
                .filter(wfEltCheck -> STEP.equals(wfEltCheck.getWorkflowElementType()))
                .collect(Collectors.toList());
        logger.debug("PERSIST >{}< WORKFLOW CHECKS (STEP)...", workflowElementChecks.size());
        this.saveWorkflowElementChecks(this.generateInsertWorkflowElementSql(SQL_SELECT_WF_ELT_FROM_STEP_ID), workflowElementChecksSteps);

        List<WorkflowElementCheckDto> workflowElementChecksMeasures = workflowElementChecks.stream()
                .filter(wfEltCheck -> MEASURE.equals(wfEltCheck.getWorkflowElementType()))
                .collect(Collectors.toList());
        logger.debug("PERSIST >{}< WORKFLOW CHECKS (MEASURES)...", workflowElementChecks.size());
        this.saveWorkflowElementChecks(this.generateInsertWorkflowElementSql(SQL_SELECT_WF_ELT_FROM_MEASURE_ID), workflowElementChecksMeasures);
    }

    private void saveWorkflowElementChecks(String sqlWorkflowEltCheckInsert, List<WorkflowElementCheckDto> workflowElementChecks) {
        List<int[]> bulkIndexes = this.generateBulkIndexes(workflowElementChecks);
        for (int[] idx : bulkIndexes) {
            List<WorkflowElementCheckDto> currentWfEltChecks = workflowElementChecks.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(sqlWorkflowEltCheckInsert, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    WorkflowElementCheckDto wfElt = currentWfEltChecks.get(idx);
                    ps.setLong(1, wfElt.getCheckId());
                    ps.setLong(2, wfElt.getChildId());
                    ps.setShort(3, wfElt.getEnforce());
                    if (wfElt.getAssigneeOldIndex() != null) {
                        ps.setLong(4, wfElt.getCheckId());
                        ps.setLong(5, wfElt.getAssigneeOldIndex());
                    }
                    else {
                        ps.setNull(4, Types.BIGINT);
                        ps.setNull(5, Types.INTEGER);
                    }
                    if (wfElt.getCommitedBy() != null)
                        ps.setLong(6, wfElt.getCommitedBy());
                    else
                        ps.setNull(6, Types.BIGINT);
                    if (wfElt.getState() != null)
                        ps.setLong(7, wfElt.getState());
                    else
                        ps.setNull(7, Types.SMALLINT);
                    ps.setBoolean(8, wfElt.isLocked());
                    ps.setInt(9, wfElt.getProcedureSeqNumber());
                    if (wfElt.getStepSeqNumber() != null)
                        ps.setInt(10, wfElt.getStepSeqNumber());
                    else
                        ps.setNull(10, Types.INTEGER);
                    if (wfElt.getMeasureSeqNumber() != null)
                        ps.setInt(11, wfElt.getMeasureSeqNumber());
                    else
                        ps.setNull(11, Types.INTEGER);
                    ps.setTimestamp(12, wfElt.getCreatedAt());
                    ps.setTimestamp(13, wfElt.getUpdatedAt());
                    ps.setString(14, wfElt.getCreatedBy());
                    ps.setString(15, wfElt.getUpdatedBy());
                }

                @Override
                public int getBatchSize() {
                    return currentWfEltChecks.size();
                }
            });
        }
    }

    private void updateMeasurementWorkflowEltCheckId(List<WorkflowElementCheckDto> workflowElementChecks) {
        List<int[]> bulkIndexes = this.generateBulkIndexes(workflowElementChecks);
        for (int[] idx : bulkIndexes) {
            List<WorkflowElementCheckDto> currentWfEltChecks = workflowElementChecks.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(UPDATE_MEASUREMENT_WF_ELT_CHECK_ID, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    WorkflowElementCheckDto wfElt = currentWfEltChecks.get(idx);
                    ps.setLong(1, wfElt.getChildId());
                    ps.setLong(2, wfElt.getCheckId());
                    ps.setLong(3, wfElt.getMeasurementId());
                }

                @Override
                public int getBatchSize() {
                    return currentWfEltChecks.size();
                }
            });
        }
    }

    private void saveAssignees(List<CheckDataAssigneeDto> assignees) {
        List<CheckDataAssigneeDto> blocks = assignees.stream()
                                                     .filter(assignee -> Objects.isNull(assignee.getOldAssigneeParentIndex()))
                                                     .collect(Collectors.toList());
        this.saveAssigneesBlocks(blocks);
        List<CheckDataAssigneeDto> registeredAssignees = assignees.stream()
                                                                  .filter(assignee -> Objects.nonNull(assignee.getOldAssigneeParentIndex()))
                                                                  .collect(Collectors.toList());
        this.saveRegisteredAssignees(registeredAssignees);
    }

    private void saveAssigneesBlocks(List<CheckDataAssigneeDto> blocks) {
        logger.debug("PERSIST >{}< ASSIGNEE BLOCKS...", blocks.size());
        List<int[]> bulkIndexes = this.generateBulkIndexes(blocks);
        for (int[] idx : bulkIndexes) {
            List<CheckDataAssigneeDto> currentBlocks = blocks.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(INSERT_ASSIGNEE_BLOCK_SQL, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    CheckDataAssigneeDto assigneeBlock = currentBlocks.get(idx);

                    // 1) old_index
                    if (assigneeBlock.getOldAssigneeIndex() != null) {
                        ps.setLong(1, assigneeBlock.getOldAssigneeIndex());
                    } else {
                        ps.setNull(1, Types.INTEGER);
                    }
                    // 2) user_group_id
                    if (assigneeBlock.getUserGroupId() != null) {
                        ps.setLong(2, assigneeBlock.getUserGroupId());
                    } else {
                        ps.setNull(2, Types.BIGINT);
                    }
                    // 3) user_id
                    if (assigneeBlock.getUserId() != null) {
                        ps.setLong(3, assigneeBlock.getUserId());
                    } else {
                        ps.setNull(3, Types.BIGINT);
                    }
                    // 4) check_id
                    ps.setLong(4, assigneeBlock.getCheckId());
                    // 6) created_at
                    ps.setTimestamp(5, assigneeBlock.getCreatedAt());
                    // 7) updated_at
                    ps.setTimestamp(6, assigneeBlock.getUpdatedAt());
                    // 8) created_by
                    ps.setString(7, assigneeBlock.getCreatedBy());
                    // 9) updated_by
                    ps.setString(8, assigneeBlock.getUpdatedBy());
                }

                @Override
                public int getBatchSize() {
                    return currentBlocks.size();
                }
            });
        }
    }

    private void saveRegisteredAssignees(List<CheckDataAssigneeDto> assignees) {
        logger.debug("PERSIST >{}< REGISTERED ASSIGNEES...", assignees.size());
        List<int[]> bulkIndexes = this.generateBulkIndexes(assignees);
        for (int[] idx : bulkIndexes) {
            List<CheckDataAssigneeDto> currentAssignees = assignees.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(INSERT_REGISTERED_ASSIGNEE_SQL, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    CheckDataAssigneeDto registeredAssignee = currentAssignees.get(idx);

                    // 1) old_index
                    if (registeredAssignee.getOldAssigneeParentIndex() != null) {
                        ps.setLong(1, registeredAssignee.getCheckId());
                        ps.setInt(2, registeredAssignee.getOldAssigneeParentIndex());
                    } else {
                        ps.setNull(1, Types.BIGINT);
                        ps.setNull(2, Types.INTEGER);
                    }
                    ps.setInt(3, registeredAssignee.getOldAssigneeIndex());
                    // 2) user_group_id
                    if (registeredAssignee.getUserGroupId() != null) {
                        ps.setLong(4, registeredAssignee.getUserGroupId());
                    } else {
                        ps.setNull(4, Types.BIGINT);
                    }
                    // 3) user_id
                    if (registeredAssignee.getUserId() != null) {
                        ps.setLong(5, registeredAssignee.getUserId());
                    } else {
                        ps.setNull(5, Types.BIGINT);
                    }
                    // 4) check_id
                    ps.setLong(6, registeredAssignee.getCheckId());
                    // 6) created_at
                    ps.setTimestamp(7, registeredAssignee.getCreatedAt());
                    // 7) updated_at
                    ps.setTimestamp(8, registeredAssignee.getUpdatedAt());
                    // 8) created_by
                    ps.setString(9, registeredAssignee.getCreatedBy());
                    // 9) updated_by
                    ps.setString(10, registeredAssignee.getUpdatedBy());
                }

                @Override
                public int getBatchSize() {
                    return currentAssignees.size();
                }
            });
        }
    }

    private void updateCheckAttributes(List<? extends WorkflowElementCheckContainerDto> blocks) {
        logger.debug("PERSIST >{}< CHECKS...", blocks.size());
        List<WorkflowElementCheckContainerDto> blockRegistrationModes = blocks.stream()
                                                                              .filter(dto -> dto.getRegistrationMode().isPresent())
                                                                              .filter(dto -> dto.getCheckId().isPresent())
                                                                              .collect(Collectors.toList());
        List<WorkflowElementCheckContainerDto> blockAssigneeModes = blocks.stream()
                                                                          .filter(dto -> dto.getCheckId().isPresent())
                                                                          .collect(Collectors.toList());
        List<int[]> bulkIndexesRegistrationModes = this.generateBulkIndexes(blockRegistrationModes);
        List<int[]> bulkIndexesAssigneeModes = this.generateBulkIndexes(blockAssigneeModes);
        for (int[] idx : bulkIndexesRegistrationModes) {
            List<? extends WorkflowElementCheckContainerDto> currentBlocks = blockRegistrationModes.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(UPDATE_CHECK_REGISTRATION_MODE_SQL, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    WorkflowElementCheckContainerDto block = currentBlocks.get(idx);
                    // lets just set every check's registration mode to partial
                    ps.setInt(1, 2);
                    ps.setLong(2, block.getCheckId().get());
                }
                @Override
                public int getBatchSize() {
                    return currentBlocks.size();
                }
            });
        }
        for (int[] idx : bulkIndexesAssigneeModes) {
            List<? extends WorkflowElementCheckContainerDto> currentBlocks = blockAssigneeModes.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(UPDATE_CHECK_ASSIGNMENT_MODE_SQL, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    WorkflowElementCheckContainerDto block = currentBlocks.get(idx);
                    ps.setInt(1, block.getAssigneeMode().ordinal());
                    ps.setLong(2, block.getCheckId().get());
                }
                @Override
                public int getBatchSize() {
                    return currentBlocks.size();
                }
            });
        }
    }
}
