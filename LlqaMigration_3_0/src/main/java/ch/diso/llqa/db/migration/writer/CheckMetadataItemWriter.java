package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.CheckMetadataDto;
import ch.diso.llqa.db.migration.dto.LlqaDto;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Component
public class CheckMetadataItemWriter implements ItemWriter<CheckMetadataDto> {

    public static final String DELETE_CHECK_SQL = "DELETE FROM checks WHERE id = ?";
    public static final String DELETE_MEASUREMENTS_SQL = "DELETE FROM measurements WHERE check_id = ?";
    public static final String UPDATE_COMMENT_BY_SQL = "UPDATE checks SET comment_by = ? WHERE id = ? AND ? IN (SELECT id FROM users)";

    private final static Logger logger = LoggerFactory.getLogger(CheckMetadataItemWriter.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void write(List<? extends CheckMetadataDto> dtos) {
        List<LlqaDto> deleteDtos = dtos.stream()
                                       .map(CheckMetadataDto::getTestRunDto)
                                       .filter(Objects::nonNull)
                                       .collect(Collectors.toList());
        logger.debug("ATTEMPT TO DELETE MEASUREMENTS FOR >{}< CHECKS...", deleteDtos.size());
        final int numDelMeasuremtens = this.deleteMeasurementReferences(deleteDtos);
        logger.debug("DELETED >{}< MEASUREMENTS", numDelMeasuremtens);

        logger.debug("ATTEMPT TO DELETE >{}< CHECKS...", deleteDtos.size());
        final int numDeletedChecks = this.deleteChecks(deleteDtos);
        logger.debug("DELETED >{}< CHECKS", numDeletedChecks);

        List<CheckMetadataDto> commentByDtos = dtos.stream()
                                                   .filter(dto -> Objects.nonNull(dto.getCommentByDto()))
                                                   .collect(Collectors.toList());
        logger.debug("ATTEMPT TO UPDATE COMMENT BY >{}< CHECKS...", deleteDtos.size());
        final int numUpdateCommentByChecks = this.updateCommentBy(commentByDtos);
        logger.debug("UPDATED COMMENT BY >{}< CHECKS...", numUpdateCommentByChecks);
    }

    private int deleteMeasurementReferences(List<LlqaDto> dtos) {
        return this.deleteRecords(DELETE_MEASUREMENTS_SQL, dtos);
    }

    private int deleteChecks(List<LlqaDto> dtos) {
        return this.deleteRecords(DELETE_CHECK_SQL, dtos);
    }

    private int deleteRecords(final String sql, List<LlqaDto> dtos) {
        int[] rowsAffected = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                LlqaDto dto = dtos.get(i);
                preparedStatement.setLong(1, dto.getPk());
            }

            @Override
            public int getBatchSize() {
                return dtos.size();
            }
        });
        return rowsAffected.length;
    }

    private int updateCommentBy(List<CheckMetadataDto> dtos) {
        int[] rowsAffected = jdbcTemplate.batchUpdate(UPDATE_COMMENT_BY_SQL, new BatchPreparedStatementSetter() {

            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                CheckMetadataDto dto = dtos.get(i);
                preparedStatement.setLong(1, dto.getCommentByDto().getPk());
                preparedStatement.setLong(2, dto.getPk());
                preparedStatement.setLong(3, dto.getCommentByDto().getPk());
            }

            @Override
            public int getBatchSize() {
                return dtos.size();
            }
        });
        return rowsAffected.length;
    }
}