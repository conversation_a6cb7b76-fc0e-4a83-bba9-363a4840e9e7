package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.measures.calculation.MeasureType;
import ch.diso.llqa.db.migration.dto.measures.calculation.MeasureTypeContainerDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MeasuresCalculationItemProcessor extends JsonContentListProcessor<MeasureTypeContainerDto> {

    @Override
    protected List<MeasureTypeContainerDto> generateDto(long pk, JsonNode yamlNode) {
        MeasureTypeContainerDto containerDto = new MeasureTypeContainerDto();
        containerDto.addMeasureType(MeasureType.MT1.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT2.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT3.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT4.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT5.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT6.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT7.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT8.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT9.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT10.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT11.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT12.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT13.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT14.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT15.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT16.createMeasureTypeDto(pk, yamlNode));
        containerDto.addMeasureType(MeasureType.MT17.createMeasureTypeDto(pk, yamlNode));
        return Lists.newArrayList(containerDto);
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}
