package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.MeasureMetadataDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class MeasuresMetadataItemWriter extends JdbcBatchItemWriter<MeasureMetadataDto> {

    private static final String SQL_UPDATE = "UPDATE measure_types SET number_decimals = :numberDecimals WHERE measure_id = :pk";


    @Autowired
    public MeasuresMetadataItemWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL_UPDATE);
        setDataSource(dataSource);
    }
}
