package ch.diso.llqa.db.migration.dto.measurements;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Setter
@Getter
public class MeasurementsHistoriesDto extends LlqaDto {

    private String value;

    private String comment;

    private Long status;

    private Long measurementId;

    private long measurementErrorCategoryId;

    private Long savedBy;

    public Long getMeasurementId() {
        return measurementId;
    }

    @JsonProperty("id")
    public void setMeasurementId(Long measurementId) {
        this.measurementId = measurementId;
    }

    public Long getSavedBy() {
        return savedBy;
    }

    @JsonProperty("savedby")
    public void setSavedBy(Long savedBy) {
        this.savedBy = savedBy;
    }

    public long getMeasurementErrorCategoryId() {
        return measurementErrorCategoryId;
    }

    @JsonProperty("measurementerrorcategoryid")
    public void setMeasurementErrorCategoryId(long measurementErrorCategoryId) {
        this.measurementErrorCategoryId = measurementErrorCategoryId;
    }

    @Override
    @JsonProperty("updated_at")
    public void setUpdatedAt(Timestamp updatedAt) {
        super.setUpdatedAt(updatedAt);
    }
}
