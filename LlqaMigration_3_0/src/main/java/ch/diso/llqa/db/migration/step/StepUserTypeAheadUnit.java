package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.UserTypeAheadUnit;
import ch.diso.llqa.db.migration.processor.UserTypeAheadUnitItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.UserTypeAheadUnitItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepUserTypeAheadUnit extends JsonMigrationStep {

    private static final String TABLE_NAME = "users";
    private static final String COLUMN_NAME = "userinfo";
    private static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;
    private static final int MIN_ID = 1;

    @Value("${users.typeaheadunit.fetch.size}")
    private int fetchSize;

    @Value("${users.typeaheadunit.chunk.size}")
    private int chunkSize;

    @Autowired
    private UserTypeAheadUnitItemProcessor processor;

    @Autowired
    private UserTypeAheadUnitItemWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));
        return stepBuilderFactory.get("stepUserTypeAheadUnit")
                .listener(stepCompletionListener)
                .<JsonNode, UserTypeAheadUnit>chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
}