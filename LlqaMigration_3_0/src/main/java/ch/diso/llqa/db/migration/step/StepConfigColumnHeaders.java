package ch.diso.llqa.db.migration.step;


import ch.diso.llqa.db.migration.common.IndexedMapValue;
import ch.diso.llqa.db.migration.dto.configcolumn.ConfigColumnHeadersDto;
import ch.diso.llqa.db.migration.rowMapper.ConfigColumnsRowMapper;
import ch.diso.llqa.db.migration.writer.ConfigColumnsItemWriter;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Step;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class StepConfigColumnHeaders extends MigrationStep {

    public static final String PK_COLUMN_NAME = "id";

    public static final List<IndexedMapValue> CONFIG_TABLE_COLUMN_HEADER_NAMES = Arrays.asList(
            new IndexedMapValue(1,"colheader_1", "colheader_1"),
            new IndexedMapValue(2,"colheader_2", "colheader_2"),
            new IndexedMapValue(3,"colheader_3", "colheader_3"),
            new IndexedMapValue(4,"colheader_4", "colheader_4"),
            new IndexedMapValue(5,"colheader_5", "colheader_5"),
            new IndexedMapValue(6,"colheader_6", "colheader_6")
    );

    private static final String TABLE_NAME = "config_tables";

    private static final String SQL_TEMPLATE =
            "SELECT t.${id}, " +
                    "t.${colheader_1}, " +
                    "t.${colheader_2}, " +
                    "t.${colheader_3}, " +
                    "t.${colheader_4}, " +
                    "t.${colheader_5}, " +
                    "t.${colheader_6} "+
            "FROM ${config_tables} AS t "+
            "ORDER BY t.${id}";

    private final static Logger logger = LoggerFactory.getLogger(StepConfigColumnHeaders.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ConfigColumnsItemWriter<ConfigColumnHeadersDto> configColumnsItemWriter;

    @Autowired
    private ConfigColumnsRowMapper configColumnsRowMapper;

    @Override
    public Step createStep() {

        return stepBuilderFactory.get("stepConfigColumnHeaders")
                .listener(stepCompletionListener)
                .<List<ConfigColumnHeadersDto>, List<ConfigColumnHeadersDto>>chunk(this.getChunkSize())
                .reader(this.createReader())
                .writer(configColumnsItemWriter)
                .build();
    }
    
    private ItemReader<List<ConfigColumnHeadersDto>> createReader() {
        JdbcCursorItemReader<List<ConfigColumnHeadersDto>> reader = new JdbcCursorItemReader<>();

        LinkedHashMap<String, String> sqlTemplateVars = this.createSqlTemplateVars();
        reader.setSql(StrSubstitutor.replace(SQL_TEMPLATE, sqlTemplateVars));
        logger.debug("SELECT STATEMENT >{}<", reader.getSql());

        reader.setDataSource(jdbcTemplate.getDataSource());

        configColumnsRowMapper = new ConfigColumnsRowMapper();
        configColumnsRowMapper.setColumnNames(CONFIG_TABLE_COLUMN_HEADER_NAMES);
        configColumnsRowMapper.setType(ConfigColumnHeadersDto.class);
        reader.setRowMapper(configColumnsRowMapper);

        reader.setFetchSize(this.getFetchSize());
        return reader;
    }

    private LinkedHashMap<String, String> createSqlTemplateVars(){

        return new LinkedHashMap<String, String>(){{
            put(PK_COLUMN_NAME, PK_COLUMN_NAME);
            putAll(CONFIG_TABLE_COLUMN_HEADER_NAMES
                    .stream()
                    .collect(Collectors.toMap(IndexedMapValue::getKey, IndexedMapValue::getValue)));
            put(TABLE_NAME, TABLE_NAME);
        }};
    }

    @Value("${config.columns.fetch.size}")
    private int fetchSize;

    @Value("${config.columns.chunk.size}")
    private int chunkSize;

    protected int getChunkSize() { return this.chunkSize; }

    protected int getFetchSize() { return this.fetchSize; }

}
