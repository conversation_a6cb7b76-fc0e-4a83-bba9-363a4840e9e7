package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.NoticePathDto;
import ch.diso.llqa.db.migration.dto.NoticePathElementDto;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class NoticePathItemWriter implements ItemWriter<NoticePathDto> {

    private static final int BULK_IDX_BOTTOM = 0;
    private static final int BULK_IDX_TOP = 1;

    private static String UPDATE_NOTICE_CHECK_ID_SQL = "UPDATE notices SET check_id = (SELECT chk.id FROM checks chk WHERE chk.id = ?) WHERE id = ?";
    private static String UPDATE_NOTICE_STEP_ID_SQL = "UPDATE notices SET step_id = (SELECT s.id FROM steps s WHERE s.id = ?) WHERE id = ?";

    private final static Logger logger = LoggerFactory.getLogger(NoticePathItemWriter.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${notice.path.bulk.size}")
    private int bulkSize;

    @Override
    public void write(List<? extends NoticePathDto> items) {

        logger.debug("PERSIST >{}< NOTICE PATH ITEMS...", items.size());

        this.updateAttributes(items.stream().collect(Collectors.toList()));
    }

    private void updateAttributes(List<NoticePathDto> items) {

        List<int[]> checkBulkIndexes = this.generateBulkIndexes(items);

        for (int[] idx : checkBulkIndexes) {
            List<NoticePathDto> currentItems = items.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(UPDATE_NOTICE_CHECK_ID_SQL, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    NoticePathDto path = currentItems.get(idx);
                    if (path.getUrlCheckId() != null)
                        ps.setLong(1, path.getUrlCheckId());
                    else
                        ps.setNull(1, Types.NUMERIC);

                    ps.setLong(2, path.getPk());
                }

                @Override
                public int getBatchSize() {
                    return currentItems.size();
                }
            });
        }
        List<NoticePathDto> stepItems = items.stream()
                                             .filter(item -> item.getNoticePathElementDtos()
                                                                 .stream()
                                                                 .filter(np -> Objects.nonNull(np.getChildId()))
                                                                 .anyMatch(p -> NoticePathElementDto.Type.STEP.equals(p.getType())))
                                             .collect(Collectors.toList());
        List<int[]> stepBulkIndexes = this.generateBulkIndexes(stepItems);

        for (int[] idx : stepBulkIndexes) {
            List<NoticePathDto> currentItems = stepItems.subList(idx[BULK_IDX_BOTTOM], idx[BULK_IDX_TOP]);
            jdbcTemplate.batchUpdate(UPDATE_NOTICE_STEP_ID_SQL, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int idx) throws SQLException {
                    NoticePathDto path = currentItems.get(idx);

                    long stepId = path.getNoticePathElementDtos()
                                      .stream()
                                      .filter(p -> NoticePathElementDto.Type.STEP.equals(p.getType()))
                                      .map(NoticePathElementDto::getChildId)
                                      .filter(Objects::nonNull)
                                      .findFirst()
                                      .orElse(-1L);

                    ps.setLong(1, stepId);
                    ps.setLong(2, path.getPk());
                }

                @Override
                public int getBatchSize() {
                    return currentItems.size();
                }
            });
        }
    }

    private List<int[]> generateBulkIndexes(List<?> objs) {
        List<int[]> idx = new ArrayList<>();
        for (int idxBottom = 0; idxBottom < objs.size(); idxBottom += this.bulkSize) {
            int idxTop = Math.min(idxBottom + this.bulkSize, objs.size());
            idx.add(new int[]{idxBottom, idxTop});
        }
        return idx;
    }
}