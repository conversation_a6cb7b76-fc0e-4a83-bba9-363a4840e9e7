package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.CheckMetadataDto;
import ch.diso.llqa.db.migration.processor.CheckMetadataItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.CheckMetadataItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepCheckMetadata extends JsonMigrationStep {

    private static final String TABLE_NAME = "checks";
    private static final String COLUMN_NAME = "metadata";
    private static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;
    private static final int MIN_ID = 1;

    @Value("${checks.metadata.fetch.size}")
    private int fetchSize;

    @Value("${checks.metadata.chunk.size}")
    private int chunkSize;

    protected int getChunkSize() { return this.chunkSize; }

    protected int getFetchSize() { return this.fetchSize; }

    @Autowired
    private CheckMetadataItemProcessor processor;

    @Autowired
    private CheckMetadataItemWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, this.fetchSize, MIN_ID, this.password));
        return stepBuilderFactory.get("stepCheckMetadata")
                .listener(stepCompletionListener)
                .<JsonNode, CheckMetadataDto>chunk(this.chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
}