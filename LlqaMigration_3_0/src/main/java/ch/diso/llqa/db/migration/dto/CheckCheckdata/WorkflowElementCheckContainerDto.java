package ch.diso.llqa.db.migration.dto.CheckCheckdata;

import ch.diso.llqa.db.migration.dto.CheckCheckdata.CheckDataAssigneeDto.RegistrationMode;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WorkflowElementCheckContainerDto {

    @Getter
    public enum AssigneeMode {
       FREE,
       PREASSIGN_FULL,
       DETAILED,
       PREASSIGN_FREE,
       PREASSIGN_DETAILED
    }

    private List<CheckDataAssigneeDto> assignees;
    private List<WorkflowElementCheckDto> workflowElementCheckDtos;
    private Long closedBy;

    private CheckDataAssigneeDto.RegistrationMode registrationMode;

    public Optional<Long> getCheckId() {
        List<Long> checkIds = Stream.concat(assignees != null ? assignees.stream().map(CheckDataAssigneeDto::getCheckId) : Stream.of(),
              workflowElementCheckDtos != null ? workflowElementCheckDtos.stream().map(WorkflowElementCheckDto::getCheckId) : Stream.of())
                                          .distinct()
                                          .collect(Collectors.toList());
        if (checkIds.size() == 1) {
            return Optional.of(checkIds.get(0));
        } else {
            return Optional.empty();
        }
    }

    public Optional<CheckDataAssigneeDto.RegistrationMode> getRegistrationMode() {
        if (this.assignees != null) {
           final boolean hasNulls = this.assignees.stream()
                                                  .map(CheckDataAssigneeDto::getRegistrationMode)
                                                  .anyMatch(Objects::isNull);
            if (hasNulls) {
                return Optional.empty();
            } else {
               Map<RegistrationMode, Long> registrationModes = this.assignees.stream()
                                                                             .map(CheckDataAssigneeDto::getRegistrationMode)
                                                                             .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
               final Long maxNumberElements = registrationModes.values()
                                                               .stream()
                                                               .max(Long::compareTo)
                                                               .orElse(-1L);
               return registrationModes.entrySet()
                                       .stream()
                                       .filter(e -> maxNumberElements.equals(e.getValue()))
                                       .map(Entry::getKey)
                                       .sorted()
                                       .findFirst();
            }
        } else {
            return Optional.empty();
        }
    }

    public AssigneeMode getAssigneeMode() {
       final Optional<RegistrationMode> registrationMode = this.getRegistrationMode();
       if (!registrationMode.isPresent()) {
          return AssigneeMode.PREASSIGN_FULL;
       } else {
          return AssigneeMode.PREASSIGN_FREE;
       }
    }
}