package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.listener.StepCompletionListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class MigrationStep {
    @Autowired
    protected StepCompletionListener stepCompletionListener;

    @Autowired
    protected StepBuilderFactory stepBuilderFactory;

    public abstract Step createStep();
}