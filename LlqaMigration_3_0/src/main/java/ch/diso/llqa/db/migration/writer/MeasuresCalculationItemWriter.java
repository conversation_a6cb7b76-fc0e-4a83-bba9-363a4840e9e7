package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.measures.calculation.*;
import ch.diso.llqa.db.migration.util.MeasuresCalculationItemWriterUtil;
import org.assertj.core.util.Lists;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MeasuresCalculationItemWriter implements ItemWriter<MeasureTypeContainerDto> {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void write(List<? extends MeasureTypeContainerDto> list) {
        list.stream()
            .flatMap(container -> container.getMeasureTypes().stream())
            .forEach(this::doPersist);
    }

    private void doPersist(MeasureTypeDto measureTypeDto) {
        persistMeasureType(measureTypeDto);
        if (measureTypeDto.hasMatrix()) {
            persistMatrix(measureTypeDto.getMatrix());
            MatrixDto matrixDto = measureTypeDto.getMatrix();
            persistMatrixRowHeaders(Lists.newArrayList(matrixDto.getRowHeaderDtos().values()));
            persistMatrixColHeaders(Lists.newArrayList(matrixDto.getColumnHeaderDtos().values()));
            persistMatrixCells(matrixDto);
        }
    }

    private void persistMeasureType(MeasureTypeDto measureTypeDto) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> MeasuresCalculationItemWriterUtil.createPreparedStatementMeasureType(connection, measureTypeDto), keyHolder);
        measureTypeDto.setPk(keyHolder.getKey().longValue());
    }

    private void persistMatrix(MatrixDto matrixDto) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> MeasuresCalculationItemWriterUtil.createPreparedStatementMatrix(connection, matrixDto), keyHolder);
        matrixDto.setPk(keyHolder.getKey().longValue());
    }

    private void persistMatrixRowHeaders(List<MatrixRowHeaderDto> rowHeaderDtos) {
        rowHeaderDtos.forEach(this::persistRowHeaderDto);
    }

    private void persistRowHeaderDto(MatrixRowHeaderDto rowHeadersDto) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> MeasuresCalculationItemWriterUtil.createPreparedStatementRowHeaderMatrix(connection, rowHeadersDto), keyHolder);
        rowHeadersDto.setPk(keyHolder.getKey().longValue());
    }

    private void persistMatrixColHeaders(List<MatrixColumnHeaderDto> colHeaderDtos) {
        colHeaderDtos.forEach(this::persistRowHeaderDto);
    }

    private void persistRowHeaderDto(MatrixColumnHeaderDto columnHeaderDto) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> MeasuresCalculationItemWriterUtil.createPreparedStatementColHeaderMatrix(connection, columnHeaderDto), keyHolder);
        columnHeaderDto.setPk(keyHolder.getKey().longValue());
    }

    private void persistMatrixCells(MatrixDto matrixDto) {
        matrixDto.getCellDtos().forEach(this::persistMatrixDtoCell);
    }

    private void persistMatrixDtoCell(MatrixCellDto cellDto) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> MeasuresCalculationItemWriterUtil.createPreparedStatementMatrixCell(connection, cellDto), keyHolder);
    }
}
