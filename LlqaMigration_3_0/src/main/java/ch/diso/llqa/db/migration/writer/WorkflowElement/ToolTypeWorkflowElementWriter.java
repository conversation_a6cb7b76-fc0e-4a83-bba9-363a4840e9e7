package ch.diso.llqa.db.migration.writer.WorkflowElement;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
public class ToolTypeWorkflowElementWriter extends WorkflowElementWriter {

   /**
    * generate the SQL query to select the group ID
    *
    * @param realId the real id that defines the old group
    * @param idKey the key column name the group id is to be found
    * @return always null. no versioning possible
    */
   @Override
   protected String generateSelectWorkflowElementGroupIdSqlQuery(Long realId, String code, String idKey) {
      return null;
   }
}