package ch.diso.llqa.db.migration.step.sql;

import ch.diso.llqa.db.migration.step.MigrationStep;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public abstract class SqlScriptTasklet extends MigrationStep implements Tasklet {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {

        for (String script : this.getSqlScripts()) {
            try {
                this.runSqlScript(script);
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("SQL FILE NOT FOUND >" + script + "<");
            }
        }

        return RepeatStatus.FINISHED;
    }

    private void runSqlScript(String script) throws IOException {
        this.logger.info("RUN SQL SCRIPT >" + script + "<");
        File scriptFile = new File(script);
        try (Connection connection = jdbcTemplate.getDataSource().getConnection();
             InputStream scriptStream = new FileInputStream(scriptFile)) {
            ScriptUtils.executeSqlScript(connection,  new InputStreamResource(scriptStream));
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    @Override
    public Step createStep() {

        return stepBuilderFactory.get(this.getStepName())
                .listener(stepCompletionListener)
                .tasklet(this)
                .build();
    }

    private List<String> getSqlScripts() {
        List<String> sqlSripts = new ArrayList<>();
        if (!StringUtils.isEmpty(this.getSqlScriptJsonFilename())) {
            JsonNode sqlScriptsJson = this.getSqlSripts(this.getSqlScriptJsonFilename());
            for (JsonNode scriptNode : sqlScriptsJson) {
                if (scriptNode.isTextual()) {
                    final String scriptAbsolutePath = String.valueOf(Paths.get(this.getDirectory(), scriptNode.asText()));
                    sqlSripts.add(scriptAbsolutePath);
                }
            }
        }
        return sqlSripts;
    }

    private JsonNode getSqlSripts(final String sqlScriptsJsonFile) {
        try (InputStream targetStream = new FileInputStream(sqlScriptsJsonFile)) {
            JsonNode sqlScripts = new ObjectMapper().readTree(targetStream);
            if (sqlScripts.isArray()) {
                return sqlScripts;
            } else {
                throw new RuntimeException("JSON WITH SQL SCRIPTS IS NOT AN ARRAY OF STRINGS");
            }
        } catch (Exception e) {
            throw new RuntimeException("JSON FILE WITH SQL SCRIPTS IS NOT AVAILABLE OR MIGHT CONTAIN SYNTAX ERRORS >" + sqlScriptsJsonFile + "<");
        }
    }

    protected abstract String getStepName();

    protected abstract String getSqlScriptJsonFilename();

    protected abstract String getDirectory();
}