package ch.diso.llqa.db.migration.dto.CheckCheckdata;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WorkflowElementCheckDto extends LlqaDto {

    public enum WorkflowElementType {
        PROCEDURE,
        STEP,
        MEASURE
    }

    private long checkId;
    private long childId;
    private Long measurementId;
    private WorkflowElementType workflowElementType;
    private Integer assigneeOldIndex;
    private Long commitedBy;
    private Integer state;
    private boolean locked;
    private Long remainingTime;
    private short enforce;
    private int procedureSeqNumber;
    private Integer stepSeqNumber;
    private Integer measureSeqNumber;
}