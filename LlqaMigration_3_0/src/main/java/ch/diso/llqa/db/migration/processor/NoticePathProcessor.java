package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.NoticePathDto;
import ch.diso.llqa.db.migration.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

@Component
public class NoticePathProcessor extends JsonContentProcessor<NoticePathDto> {

    private static final String TYPE_NODE_NAME = "type";
    private static final String URL_NODE_NAME = "url";
    private static final String URL_CHECK_ID_NODE_NAME = "checkid";
    private static final String PATH_NODE_NAME = "path";
    private static final String PATH_TYPE_NODE_NAME = "type";
    private static final String PATH_ID_NODE_NAME = "id";

    @Override
    protected NoticePathDto generateDto(long noticeId, JsonNode yamlNode) {
        NoticePathDto path = new NoticePathDto();
        path.setPk(noticeId);
        path.setType(NoticePathDto.Type.getType(JsonUtils.getStringValue(yamlNode, TYPE_NODE_NAME)));
        JsonNode urlNode = yamlNode.path(URL_NODE_NAME);
        if (urlNode.isMissingNode())
            throw new RuntimeException("NODE >URL< IS NOT SET FOR NOTICE >" + noticeId + "<");
        path.setUrlCheckId(JsonUtils.getLongValue(urlNode, URL_CHECK_ID_NODE_NAME));

        int idx = 0;
        JsonNode pathNode = yamlNode.path(PATH_NODE_NAME);
        if (!pathNode.isArray())
            throw new RuntimeException("THE PATH NODE HAS TO BE OF THE TYPE ARRAY");
        for (JsonNode currentPathNode : pathNode) {
            final String currentType = JsonUtils.getStringValue(currentPathNode, PATH_TYPE_NODE_NAME);
            final Long currentId = JsonUtils.getLongValue(currentPathNode, PATH_ID_NODE_NAME);
            path.addNoticePathElement(noticeId, currentType, currentId, ++idx);
        }

        return path;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}