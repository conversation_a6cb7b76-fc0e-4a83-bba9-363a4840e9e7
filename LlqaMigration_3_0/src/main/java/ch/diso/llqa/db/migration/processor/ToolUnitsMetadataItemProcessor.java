package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.ToolUnitMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class ToolUnitsMetadataItemProcessor extends JsonContentProcessor<ToolUnitMetadataDto> {
   private static final String NODE_FIELD_COMMENT_BY = "_commentby";

   @Override
   protected ToolUnitMetadataDto generateDto(long pk, JsonNode yamlNode) {
      JsonNode commentByNode = yamlNode.get(NODE_FIELD_COMMENT_BY);
      if (Objects.nonNull(commentByNode) && commentByNode.isNumber()) {
         ToolUnitMetadataDto dto = new ToolUnitMetadataDto();
         dto.setPk(pk);
         dto.setCommentedBy(commentByNode.asLong());
         return dto;
      } else {
         return null;
      }
   }

   @Override
   protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
      return jsonNode.isObject();
   }
}
