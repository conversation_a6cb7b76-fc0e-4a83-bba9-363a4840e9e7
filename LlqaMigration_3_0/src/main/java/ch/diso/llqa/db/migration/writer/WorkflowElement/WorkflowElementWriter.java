package ch.diso.llqa.db.migration.writer.WorkflowElement;

import ch.diso.llqa.db.migration.dto.WorkflowElementDto;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.util.StringUtils;

public abstract class WorkflowElementWriter implements ItemWriter<WorkflowElementDto> {

    private static final String[] WORKFLOW_ELEMENT_PK_COLUMN_NAME = new String[] {"id"};
    private static final String[] WORKFLOW_ELEMENT_GROUP_PK_COLUMN_NAME = new String[] {"id"};
    private static final String WORKFLOW_ELEMENT_INSERT_SQL = "INSERT INTO workflow_elements (title, workflow_element_group_id, created_at, updated_at, created_by, updated_by) VALUES ('{}',?,?,?,?,?)";
    private static final String WORKFLOW_ELEMENT_GROUP_INSERT_SQL = "INSERT INTO workflow_element_groups (child_table, created_at, updated_at, created_by, updated_by) VALUES (?,?,?,?,?)";
    private static final String CHILD_TABLE_UPDATE_SQL_TEMPLATE = "UPDATE %s SET workflow_element_id = ? WHERE id = ?";

    private final static Logger logger = LoggerFactory.getLogger(WorkflowElementWriter.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * create a workflow abstraction from a set of child table elements (such as: procedures, steps, device types, ...)
     *
     * @param list container with child elements (such as: procedures, steps, device types, ...)
     * @throws SQLException
     */
    @Override
    public void write(List<? extends WorkflowElementDto> list) throws SQLException {
        logger.debug("WRITE >{}< elements", list.size());

        for (WorkflowElementDto dto : list) {
            long workflowElementPk = this.insertWorkflowElement(dto);

            if (workflowElementPk > 0) {
                this.updateChildTable(workflowElementPk, dto);
            } else {
                throw new RuntimeException("WORKFLOW ELEMENT COULD NOT BE INSERTED");
            }
        }
    }

    /**
     * insert a new workflow element
     *
     * @param dto the child tables element to insert a workflow element for it
     * @return the primary key from the inserted record (generated by the sequence)
     */
    private long insertWorkflowElement(WorkflowElementDto dto) {
        logger.debug("INSERT WORKFLOW ELEMENT FOR >" + dto.getChildTable() + "< WITH PK >" + dto.getPk() + "<");

        KeyHolder holder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> generateInsertWfEltPreparedStatement(connection, dto), holder);
        Number key = holder.getKey();
        if (key != null && key.longValue() > 0l) {
            return key.longValue();
        } else {
            throw new RuntimeException("WORKFLOW ELEMENT COULD NOT BE INSERTED. GENERATED SEQUENCE NOT AVAILABLE. CHILD TABLE >" + dto.getChildTable() + "<, CHILD TABLE PK >" + dto.getPk() + "<");
        }
    }

    /**
     * insert a new workflow element
     *
     * @param connection the database connection
     * @param dto        the child tables element to insert a workflow element for it
     * @return the statement to get the inserted row from it (can be used to get access to the auto generated PK by the DB sequence)
     * @throws SQLException
     */
    private PreparedStatement generateInsertWfEltPreparedStatement(Connection connection, WorkflowElementDto dto) throws SQLException {
        long workflowElementGroupId = this.selectOrGenerateWorkflowElementGroupId(dto);
        PreparedStatement insertStatement = connection.prepareStatement(WORKFLOW_ELEMENT_INSERT_SQL, WORKFLOW_ELEMENT_PK_COLUMN_NAME);
        insertStatement.setLong(1, workflowElementGroupId);
        insertStatement.setTimestamp(2, dto.getCreatedAt());
        insertStatement.setTimestamp(3, dto.getUpdatedAt());
        insertStatement.setString(4, dto.getCreatedBy());
        insertStatement.setString(5, dto.getUpdatedBy());
        return insertStatement;
    }

    /**
     * insert a new workflow element group
     *
     * @param connection the database connection
     * @param dto        the child tables element to insert a workflow element for it
     * @return the statement to get the inserted row from it (can be used to get access to the auto generated PK by the DB sequence)
     * @throws SQLException
     */
    private PreparedStatement generateInsertWfEltGrpPreparedStatement(Connection connection, WorkflowElementDto dto) throws SQLException {
        PreparedStatement insertStatement = connection.prepareStatement(WORKFLOW_ELEMENT_GROUP_INSERT_SQL, WORKFLOW_ELEMENT_GROUP_PK_COLUMN_NAME);
        insertStatement.setString(1, dto.getChildTable());
        insertStatement.setTimestamp(2, dto.getCreatedAt());
        insertStatement.setTimestamp(3, dto.getUpdatedAt());
        insertStatement.setString(4, dto.getCreatedBy());
        insertStatement.setString(5, dto.getUpdatedBy());
        return insertStatement;
    }

    /**
     * set the foreign key from a the child table to its workflow element parent
     *
     * @param workflowElementPk the parents workflow element primary key
     * @param dto               the child tables element
     * @throws SQLException
     */
    private void updateChildTable(long workflowElementPk, WorkflowElementDto dto) throws SQLException {
        this.logger.debug("UPDATE >" + dto.getChildTable() + "< WITH PK >" + dto.getPk() + "<, FOREIGN KEY >" + workflowElementPk + "<");
        final String childTableUpdate = String.format(CHILD_TABLE_UPDATE_SQL_TEMPLATE, this.getChildTableNameFromChildTableEnum(dto.getChildTable()));

        final int rowCount = jdbcTemplate.update(connection -> {
            PreparedStatement childTableStatement = connection.prepareStatement(childTableUpdate);
            childTableStatement.setLong(1, workflowElementPk);
            childTableStatement.setLong(2, dto.getPk());
            return childTableStatement;
        });
        if (rowCount != 1) {
            throw new RuntimeException("WORKFLOW CHILD TABLE COULD NOT BE UPDATED. CHILD TABLE >" + dto.getChildTable() + "<, CHILD TABLE PK >" + dto.getPk() + "<, WORKFLOW PK >" + workflowElementPk + "<, ROW COUNT >" + rowCount + "<");
        }
    }

    private String getChildTableNameFromChildTableEnum(String childTableEnum) {
        switch (childTableEnum) {
            case "check_type":
                return "check_types";
            case "device_type":
                return "device_types";
            case "config_entry":
                return "config_entries";
            case "procedure":
                return "procedures";
            case "step":
                return "steps";
            case "measure":
                return "measures";
            case "model":
                return "models";
            case "tool_type":
                return "tool_types";
            default:
                return "";
        }
    }

    /**
     * get the group primary key from workflow_element_groups by returning an existing record or add a new group and return its auto generated ID
     *
     * @param dto the workflow element to get its group id for
     * @return the group id
     */
    private long selectOrGenerateWorkflowElementGroupId(WorkflowElementDto dto) {
        long existingWorkflowElementGroupId = this.selectWorkflowElementGroupId(dto);
        if (existingWorkflowElementGroupId <= 0) {
            return this.generateWorkflowElementGroupId(dto);
        } else {
            return existingWorkflowElementGroupId;
        }
    }

    /**
     * get an existing workflow element group id by selecting it from the database
     *
     * @param dto the workflow element
     * @return the existing group ID (PK from workflow_element_groups). '0' if no group exits yet
     */
    private long selectWorkflowElementGroupId(WorkflowElementDto dto) {
        final String idKey = "id";
        final String sql = this.generateSelectWorkflowElementGroupIdSqlQuery(dto.getRealId(), dto.getCode(), idKey);
        if (!StringUtils.isEmpty(sql)) {
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
            Set<Long> groupIds = rows.stream()
                                     .filter(row -> row.containsKey(idKey))
                                     .map(row -> row.get(idKey))
                                     .filter(Objects::nonNull)
                                     .filter(id -> id instanceof Long)
                                     .map(id -> (Long) id)
                                     .collect(Collectors.toSet());
            if (groupIds.size() == 1) {
                Optional<Long> groupId = groupIds.stream().findFirst();
                return groupId.isPresent() ? groupId.get().longValue() : 0;
            } else if (groupIds.isEmpty()) {
                return 0;
            } else {
                throw new RuntimeException("INVALID NUMBER OF GROUP IDS. NUMBER OF ELEMENTS >" + groupIds.size() + "<");
            }
        }
        else {
            return 0;
        }
    }

    /**
     * generate the SQL query to select the group ID
     *
     * @param realId the real id that defines the old group
     * @param code the workflow elements code
     * @param idKey the key column name the group id is to be found
     * @return the SQL query to select the group id
     */
    protected abstract String generateSelectWorkflowElementGroupIdSqlQuery(Long realId, String code, String idKey);

    /**
     * inserting an new record to workflow_element_groups and return its auto generated PK
     *
     * @param dto the workflow element to insert a new group to workflow_element_groups
     * @return primary key from the inserted workflow_element_groups record
     */
    private long generateWorkflowElementGroupId(WorkflowElementDto dto) {
        logger.debug("INSERT WORKFLOW ELEMENT GROUP FOR FOR >" + dto.getChildTable() + "< WITH PK >" + dto.getPk() + "< AND REAL ID >" + dto.getRealId() + ">");

        KeyHolder holder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> generateInsertWfEltGrpPreparedStatement(connection, dto), holder);
        Number key = holder.getKey();
        if (key != null && key.longValue() > 0) {
            return key.longValue();
        } else {
            throw new RuntimeException("WORKFLOW ELEMENT COULD NOT BE INSERTED. GENERATED SEQUENCE NOT AVAILABLE. CHILD TABLE >" + dto.getChildTable() + "<, CHILD TABLE PK >" + dto.getPk() + "<");
        }
    }
}