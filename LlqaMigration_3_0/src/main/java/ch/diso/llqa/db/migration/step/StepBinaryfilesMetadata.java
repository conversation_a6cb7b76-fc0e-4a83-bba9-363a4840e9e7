package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.BinaryfilesMetadataDto;
import ch.diso.llqa.db.migration.processor.BinaryfileMetadataItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.BinaryfilesMetadataItemWriter;
import ch.diso.llqa.db.migration.writer.ListUnpackingItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StepBinaryfilesMetadata extends JsonMigrationStep {

    public static final String TABLE_NAME = "binaryfiles";

    public static final String COLUMN_NAME = "metadata";

    public static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

    @Value("${binaryfiles.metadata.fetch.size}")
    private int fetchSize;

    @Value("${binaryfiles.metadata.chunk.size}")
    private int chunkSize;

    public static final int MIN_ID = 1;

    @Autowired
    private BinaryfileMetadataItemProcessor processor;

    @Autowired
    private BinaryfilesMetadataItemWriter writer;

    private ListUnpackingItemWriter writerWrapper;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));

        writerWrapper = new ListUnpackingItemWriter<BinaryfilesMetadataDto>();
        writerWrapper.setDelegate(writer);

        return stepBuilderFactory.get("stepBinaryfilesMetadata").listener(stepCompletionListener)
                .<JsonNode, List<BinaryfilesMetadataDto>> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writerWrapper)
                .build();
    }
}
