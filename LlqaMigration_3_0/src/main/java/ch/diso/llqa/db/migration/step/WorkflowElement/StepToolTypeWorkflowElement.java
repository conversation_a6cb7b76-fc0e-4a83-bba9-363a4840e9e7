package ch.diso.llqa.db.migration.step.WorkflowElement;

import ch.diso.llqa.db.migration.writer.WorkflowElement.ToolTypeWorkflowElementWriter;
import ch.diso.llqa.db.migration.writer.WorkflowElement.WorkflowElementWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepToolTypeWorkflowElement extends StepWorkflowElement {

   private static final String SELECT_SQL = "SELECT id AS %s, 'tool_type' AS %s, NULL AS %s, NULL AS %s FROM tool_types ORDER BY id";

   @Autowired
   private ToolTypeWorkflowElementWriter toolTypeWorkflowElementWriter;

   @Value("${tooltype.workflowelement.fetch.size}")
   private int fetchSize;

   @Value("${tooltype.workflowelement.chunk.size}")
   private int chunkSize;

   @Override
   protected WorkflowElementWriter getWorkflowElementWriter() {
      return toolTypeWorkflowElementWriter;
   }

   @Override
   protected String generateSelectSql() {
      return String.format(SELECT_SQL, PK_COLOUMN_NAME, TABLE_COLUMN_NAME, REAL_ID_COLUMN_NAME, CODE_COLUMN_NAME);
   }

   @Override
   protected int getChunkSize() {
      return this.chunkSize;
   }

   @Override
   protected int getFetchSize() {
      return this.fetchSize;
   }

   @Override
   protected String getStepName() {
      return "stepToolTypeWorkflowElement";
   }
}