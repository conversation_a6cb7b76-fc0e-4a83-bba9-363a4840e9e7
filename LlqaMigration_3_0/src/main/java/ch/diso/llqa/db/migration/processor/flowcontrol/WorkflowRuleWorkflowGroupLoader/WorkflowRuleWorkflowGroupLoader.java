package ch.diso.llqa.db.migration.processor.flowcontrol.WorkflowRuleWorkflowGroupLoader;

import java.util.Collection;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * functional interface to load a workflow element from a child tables code
 *
 * <AUTHOR>
 */
public interface WorkflowRuleWorkflowGroupLoader {

    /**
     * load a workflow element group primary key by its code
     *
     * @param jdbcTemplate    the JDBC template to perform the DB select
     * @param selectRealIdSql sql query to select the current real id from the group
     * @param code            the child tables code. format dependes on its implementation.
     * @return the workflow group primary key load from the database
     */
    Collection<Long> loadWorkflowElementGroupPks(JdbcTemplate jdbcTemplate, String selectRealIdSql, String code);
}