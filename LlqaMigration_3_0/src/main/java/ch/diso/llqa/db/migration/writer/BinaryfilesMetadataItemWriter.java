package ch.diso.llqa.db.migration.writer;

import ch.diso.llqa.db.migration.dto.BinaryfilesMetadataDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;

@Component
public class BinaryfilesMetadataItemWriter extends JdbcBatchItemWriter<List<BinaryfilesMetadataDto>> {

    public static final String SQL = "INSERT INTO binaryfile_metadata (binaryfile_id, type, value,  created_at, created_by, updated_at, updated_by) VALUES (:binaryfileId, :type, :value, :createdAt, :createdBy, :updatedAt, :updatedBy)";

    @Autowired
    public BinaryfilesMetadataItemWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL);
        setDataSource(dataSource);
    }
}