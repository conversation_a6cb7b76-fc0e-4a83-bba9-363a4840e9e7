package ch.diso.llqa.db.migration.step.WorkflowElement;

import ch.diso.llqa.db.migration.writer.WorkflowElement.StepWorkflowElementWriter;
import ch.diso.llqa.db.migration.writer.WorkflowElement.WorkflowElementWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepStepsWorkflowElement extends StepWorkflowElement {

    private static final String SELECT_SQL =
            "SELECT s.id AS %s, 'step' AS %s, real_id AS %s, s.code AS %s " +
            "FROM steps s INNER JOIN procedures p ON (s.procedure_id = p.id) " +
            "ORDER BY s.id";

    @Autowired
    private StepWorkflowElementWriter stepWorkflowElementWriter;

    @Value("${steps.workflowelement.fetch.size}")
    private int fetchSize;

    @Value("${steps.workflowelement.chunk.size}")
    private int chunkSize;

    @Override
    protected WorkflowElementWriter getWorkflowElementWriter() {
        return this.stepWorkflowElementWriter;
    }

    @Override
    protected String generateSelectSql() {
        return String.format(SELECT_SQL, PK_COLOUMN_NAME, TABLE_COLUMN_NAME, REAL_ID_COLUMN_NAME, CODE_COLUMN_NAME);
    }

    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    @Override
    protected String getStepName() { return "stepStepsWorkflowElement"; }
}