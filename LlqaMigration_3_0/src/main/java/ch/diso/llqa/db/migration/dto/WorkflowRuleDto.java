package ch.diso.llqa.db.migration.dto;

/**
 * <AUTHOR>
 */
public class WorkflowRuleDto extends LlqaDto {

    private long ownerWorkflowElementId;
    private long targetWorkflowElementGroupId;
    private String result;
    private boolean inverse;
    private String trigger;

    public long getOwnerWorkflowElementId() {
        return ownerWorkflowElementId;
    }

    public void setOwnerWorkflowElementId(long ownerWorkflowElementId) {
        this.ownerWorkflowElementId = ownerWorkflowElementId;
    }

    public long getTargetWorkflowElementGroupId() {
        return targetWorkflowElementGroupId;
    }

    public void setTargetWorkflowElementGroupId(long targetWorkflowElementGroupId) {
        this.targetWorkflowElementGroupId = targetWorkflowElementGroupId;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public boolean isInverse() {
        return inverse;
    }

    public void setInverse(boolean inverse) {
        this.inverse = inverse;
    }

    public String getTrigger() {
        return trigger;
    }

    public void setTrigger(String trigger) {
        this.trigger = trigger;
    }
}