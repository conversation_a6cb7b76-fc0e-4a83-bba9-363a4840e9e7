package ch.diso.llqa.db.migration.writer.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsMetadataDto;
import org.springframework.batch.item.database.BeanPropertyItemSqlParameterSourceProvider;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class MeasurementsMetadataItemWriter extends JdbcBatchItemWriter<MeasurementsMetadataDto> {

    public static final String SQL = "UPDATE measurements SET comment_by = :commentBy WHERE ID = :pk";

    @Autowired
    public MeasurementsMetadataItemWriter(@Qualifier("dataSource") DataSource dataSource) {
        setItemSqlParameterSourceProvider(new BeanPropertyItemSqlParameterSourceProvider());
        setSql(SQL);
        setDataSource(dataSource);
    }
}