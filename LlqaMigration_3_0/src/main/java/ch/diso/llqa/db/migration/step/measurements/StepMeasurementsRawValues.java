package ch.diso.llqa.db.migration.step.measurements;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.measurements.MeasurementsRawValueDto;
import ch.diso.llqa.db.migration.processor.measurements.MeasurementsRawValuesItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.step.JsonMigrationStep;
import ch.diso.llqa.db.migration.writer.measurements.MeasurementsRawValuesItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StepMeasurementsRawValues extends JsonMigrationStep {

    public static final String TABLE_NAME = "measurements";
    public static final String COLUMN_NAME = "rawvalues";
    public static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

    public static final char KEY_SPLITTER_CHAR = ':';

    @Value("${measurements.rawvalue.fetch.size}")
    private int fetchSize;

    @Value("${measurements.rawvalue.chunk.size}")
    private int chunkSize;

    public static final int MIN_ID = 1;

    @Autowired
    private MeasurementsRawValuesItemProcessor processor;

    @Autowired
    private MeasurementsRawValuesItemWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));

        return stepBuilderFactory.get("stepMeasurementsRawValues").listener(stepCompletionListener)
                .<JsonNode, List<MeasurementsRawValueDto>> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
}
