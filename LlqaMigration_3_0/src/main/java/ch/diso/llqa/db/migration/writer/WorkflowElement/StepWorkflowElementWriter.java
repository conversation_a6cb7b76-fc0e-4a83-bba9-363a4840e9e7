package ch.diso.llqa.db.migration.writer.WorkflowElement;


import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
public class StepWorkflowElementWriter extends WorkflowElementWriter {

    /**
     * {@inheritDoc}
     */
    @Override
    protected String generateSelectWorkflowElementGroupIdSqlQuery(Long realId, String code, String idKey) {
        String sqlFormat =
              "SELECT DISTINCT(wfEltGrp.id) AS %s " +
                    "FROM steps s INNER JOIN procedures p ON s.procedure_id = p.id" +
                    "             INNER JOIN workflow_elements wfElt ON s.workflow_element_id = wfElt.id " +
                    "             INNER JOIN workflow_element_groups wfEltGrp ON wfElt.workflow_element_group_id = wfEltGrp.id " +
                    "WHERE p.real_id = %d" +
                    "  AND s.code = '%s'" +
                    "  AND wfEltGrp.child_table = 'step'";
        return String.format(sqlFormat, idKey, realId, code);
    }
}