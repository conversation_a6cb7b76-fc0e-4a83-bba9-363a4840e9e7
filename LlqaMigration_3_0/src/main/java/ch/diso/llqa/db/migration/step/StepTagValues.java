package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.TagValuesValuesDto;
import ch.diso.llqa.db.migration.processor.TagValuesItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.reader.TagValuesReader;
import ch.diso.llqa.db.migration.writer.ListUnpackingItemWriter;
import ch.diso.llqa.db.migration.writer.TagValuesItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepTagValues extends JsonMigrationStep {

    public static final String TABLE_NAME = "tags";

    public static final String COLUMN_NAME = "sub_values";

    public static final ColumnType COLUMN_TYPE = ColumnType.ARRAY;

    @Value("${workflow.element.tag.values.sub.values.fetch.size}")
    private int fetchSize;

    @Value("${workflow.element.tag.values.sub.values.chunk.size}")
    private int chunkSize;

    public static final int MIN_ID = 1;

    @Autowired
    protected TagValuesReader workflowElementTagValuesSubValuesReader;

    @Autowired
    private TagValuesItemProcessor processor;

    @Autowired
    private TagValuesItemWriter writer;

    private ListUnpackingItemWriter writerWrapper;

    @Override
    public Step createStep() {
        workflowElementTagValuesSubValuesReader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));
        writerWrapper = new ListUnpackingItemWriter<TagValuesValuesDto>();
        writerWrapper.setDelegate(writer);
        return stepBuilderFactory.get("stepTagValues").listener(stepCompletionListener)
                .<JsonNode, List<TagValuesValuesDto>> chunk(chunkSize)
                .reader(workflowElementTagValuesSubValuesReader)
                .processor(processor)
                .writer(writerWrapper)
                .build();
    }
}
