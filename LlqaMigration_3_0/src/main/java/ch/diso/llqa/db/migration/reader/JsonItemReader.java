package ch.diso.llqa.db.migration.reader;

import ch.diso.llqa.db.migration.common.JsonCreater;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;

@Component
@Scope("prototype")
public class JsonItemReader implements ItemReader<JsonNode> {

    private static final String ID = "id";

    @Autowired
    private JsonCreater jsonCreater;

    private JsonNode tree;

    private int treeIndex;

    private long lastMinId;

    private JsonItemReaderConfiguration configuration;

    public void configure(JsonItemReaderConfiguration configuration) {
        this.configuration = configuration;
        lastMinId = this.configuration.getMinId() - 1;
        instantiateTreeAsNullNode();
    }

    private void init(long lastMinId) throws IOException, InterruptedException {
        treeIndex = 0;
        configuration.updateMinId(lastMinId);
        String json = jsonCreater.getJson(configuration);
        readJsonTree(json);
    }

    @Override
    public JsonNode read() throws IOException, InterruptedException {
        JsonNode node = null;
        if (isNotNullNode() && isTreeIndexLessThanTreeSize()) {
            node = getNodeFromTree();
        } else {
            init(++lastMinId);
            if (tree.size() > 0) {
                node = getNodeFromTree();
            }
        }
        return node;
    }

    private JsonNode getNodeFromTree() {
        JsonNode aNode = tree.get(treeIndex);
        lastMinId = aNode.path(ID).asLong();
        treeIndex++;
        return aNode;
    }

    private void readJsonTree(String json) throws IOException {
        if (StringUtils.isEmpty(json)) {
            instantiateTreeAsNullNode();
        } else {
            tree = new ObjectMapper().readTree(json);
        }
    }

    private void instantiateTreeAsNullNode() {
        tree = JsonNodeFactory.instance.nullNode();
    }

    private boolean isNotNullNode() {
        return tree.getNodeType() != JsonNodeType.NULL;
    }

    private boolean isTreeIndexLessThanTreeSize() {
        return treeIndex < tree.size();
    }
}
