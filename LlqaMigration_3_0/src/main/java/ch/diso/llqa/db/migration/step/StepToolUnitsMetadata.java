package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.ToolUnitMetadataDto;
import ch.diso.llqa.db.migration.processor.ToolUnitsMetadataItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.ToolUnitsMetadataItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepToolUnitsMetadata extends JsonMigrationStep {

   private static final String TABLE_NAME = "tool_units";

   private static final String COLUMN_NAME = "metadata";

   private static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

   @Value("${toolunits.metadata.fetch.size}")
   private int fetchSize;

   @Value("${toolunits.metadata.chunk.size}")
   private int chunkSize;

   public static final int MIN_ID = 1;

   @Autowired
   private ToolUnitsMetadataItemProcessor processor;

   @Autowired
   private ToolUnitsMetadataItemWriter writer;

   @Override
   public Step createStep() {
      reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));

      return stepBuilderFactory.get("stepToolUnitsMetadata").listener(stepCompletionListener)
            .<JsonNode, ToolUnitMetadataDto> chunk(chunkSize)
            .reader(reader)
            .processor(processor)
            .writer(writer)
            .build();
   }
}