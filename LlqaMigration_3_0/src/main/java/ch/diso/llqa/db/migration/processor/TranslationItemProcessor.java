package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.TranslationDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Iterator;

@Component
@Scope("prototype")
public class TranslationItemProcessor extends JsonContentProcessor<TranslationDto> {

    @Override
    protected TranslationDto generateDto(long pk, JsonNode yamlNode) {

        if (this.containsTranslation(yamlNode)) {
            TranslationDto dto = new TranslationDto();
            dto.setPk(pk);
            dto.setContent(yamlNode.toString());
            return dto;
        } else {
            return null;
        }
    }

    private boolean containsTranslation(JsonNode yamlNode) {
        Iterator<String> fieldIter = yamlNode.fieldNames();
        while (fieldIter.hasNext()) {
            final String field = fieldIter.next();
            JsonNode currentTrans = yamlNode.path(field);
            if (currentTrans.isTextual() && !StringUtils.isEmpty(currentTrans.asText())) {
                return true;
            }
        }
        return false;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}