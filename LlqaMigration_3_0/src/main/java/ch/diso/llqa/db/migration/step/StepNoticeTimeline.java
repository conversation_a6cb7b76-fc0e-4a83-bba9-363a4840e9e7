package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.BinaryfilesMetadataDto;
import ch.diso.llqa.db.migration.dto.NoticeTimelineDto;
import ch.diso.llqa.db.migration.processor.NoticeTimelineProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.ListUnpackingItemWriter;
import ch.diso.llqa.db.migration.writer.NoticeTimelineItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StepNoticeTimeline extends JsonMigrationStep {

    public static final String TABLE_NAME = "notices";
    public static final String COLUMN_NAME = "timeline";
    public static final ColumnType COLUMN_TYPE = ColumnType.ARRAY;
    public static final int MIN_ID = 1;

    @Value("${notices.timeline.fetch.size}")
    private int fetchSize;

    @Value("${notices.timeline.chunk.size}")
    private int chunkSize;

    @Autowired
    private NoticeTimelineProcessor processor;

    @Autowired
    private NoticeTimelineItemWriter writer;

    private ListUnpackingItemWriter writerWrapper;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));

        writerWrapper = new ListUnpackingItemWriter<BinaryfilesMetadataDto>();
        writerWrapper.setDelegate(writer);

        return stepBuilderFactory.get("stepNoticeTimeline").listener(stepCompletionListener)
                .<JsonNode, List<NoticeTimelineDto>> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writerWrapper)
                .build();
    }
}