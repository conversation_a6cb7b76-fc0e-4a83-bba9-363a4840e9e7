package ch.diso.llqa.db.migration.dto.measurements;

import ch.diso.llqa.db.migration.dto.LlqaDto;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

@Setter
@Getter
public class MeasurementsRawValueDto extends LlqaDto implements Cloneable {

    public enum ValueType {
        UNKNOWN,
        TIME_STAMP_REDUCE
    }

    public static final String MATRIX_MAX_DIMENSION_PATTERN = "^[a-i]?[1-9]?";

    private boolean skip;
    private String value;
    private Long matricesCellId;
    private Long measurementId;
    private Long reduce;
    private ValueType valueType;

    @JsonIgnore
    private String tempMatricesCellId;

    public String getValue() { return value; }

    @JsonProperty("val")
    public void setValue(String value) { this.value = value; }

    public Long getReduce() { return reduce; }

    @JsonProperty("reduce")
    public void setReduce(Long reduce) { this.reduce = reduce; }

    @Setter(AccessLevel.NONE)
    private LinkedHashMap<String, String> cellValues = new LinkedHashMap<>();

    @JsonAnySetter
    public void setCellValues(String key, String value){

        if(key.matches(MATRIX_MAX_DIMENSION_PATTERN)){
            cellValues.put(key, value);
        }
    }

    @Override
    public MeasurementsRawValueDto clone() throws CloneNotSupportedException {
        MeasurementsRawValueDto clonedRawValue = (MeasurementsRawValueDto) super.clone();

        clonedRawValue.setSkip(this.isSkip());
        clonedRawValue.setValue(this.getValue());
        clonedRawValue.setMatricesCellId(this.getMatricesCellId());
        clonedRawValue.setMeasurementId(this.getMeasurementId());
        clonedRawValue.setReduce(this.getReduce());

        return clonedRawValue;
    }
}
