package ch.diso.llqa.db.migration.writer.WorkflowElement;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
public class MeasureWorkflowElementWriter extends WorkflowElementWriter {

    /**
     * {@inheritDoc}
     */
    @Override
    protected String generateSelectWorkflowElementGroupIdSqlQuery(Long realId, String code, String idKey) {
        String sqlFormat =
              "SELECT DISTINCT(wfEltGrp.id) AS %s " +
                    "FROM measures m INNER JOIN steps s ON m.step_id = s.id" +
                    "                INNER JOIN procedures p ON s.procedure_id = p.id" +
                    "                INNER JOIN workflow_elements wfElt ON m.workflow_element_id = wfElt.id " +
                    "                INNER JOIN workflow_element_groups wfEltGrp ON wfElt.workflow_element_group_id = wfEltGrp.id " +
                    "WHERE p.real_id = %d" +
                    "  AND m.code = '%s'" +
                    "  AND wfEltGrp.child_table = 'measure'";
        return String.format(sqlFormat, idKey, realId, code);
    }
}