package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.CheckMetadataDto;
import ch.diso.llqa.db.migration.dto.LlqaDto;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class CheckMetadataItemProcessor extends JsonContentProcessor<CheckMetadataDto> {

    public static final String TEST_RUN_NODE_NAME = "testrun";
    public static final String COMMENT_BY_NODE_NAME = "_commentby";
    public static final String TYPE_NODE_NAME = "type";
    public static final String ITEM_NODE_NAME = "item";

    @Override
    protected CheckMetadataDto generateDto(long pk, JsonNode yamlNode) {
        CheckMetadataDto checkMetadataDto = new CheckMetadataDto();
        checkMetadataDto.setPk(pk);
        JsonNode testRunNode = yamlNode.get(TEST_RUN_NODE_NAME);
        if (testRunNode != null) {
            JsonNode typeNode = testRunNode.get(TYPE_NODE_NAME);
            JsonNode itemNode = testRunNode.get(ITEM_NODE_NAME);
            if (typeNode != null && itemNode != null) {
                LlqaDto dto = new LlqaDto();
                dto.setPk(pk);
                checkMetadataDto.setTestRunDto(dto);
            }
        }
        JsonNode commentByNode = yamlNode.get(COMMENT_BY_NODE_NAME);
        if (commentByNode != null && commentByNode.isNumber()) {
            LlqaDto dto = new LlqaDto();
            dto.setPk(commentByNode.longValue());
            checkMetadataDto.setCommentByDto(dto);
        }
        return Objects.nonNull(checkMetadataDto.getCommentByDto()) || Objects.nonNull(checkMetadataDto.getTestRunDto()) ? checkMetadataDto : null;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}