package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.ProcedureProcessingTimeDto;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

@Component
public class ProcedureProcessingTimeProcessor extends JsonContentProcessor<ProcedureProcessingTimeDto> {

   private static final String HOURS_NODE_NAME = "hours";
   private static final String MINUTES_NODE_NAME = "minutes";

   @Override
   protected ProcedureProcessingTimeDto generateDto(long pk, JsonNode yamlNode) {
      JsonNode hoursNode = yamlNode.get(HOURS_NODE_NAME);
      JsonNode minutesNode = yamlNode.get(MINUTES_NODE_NAME);
      long processintTimeSec = 0l;
      if (minutesNode != null && minutesNode.isNumber()) {
         processintTimeSec += minutesNode.asLong() * 60;
      }
      if (hoursNode != null && hoursNode.isNumber()) {
         processintTimeSec += hoursNode.asLong() * 3600;
      }
      if (processintTimeSec > 0l) {
         ProcedureProcessingTimeDto dto = new ProcedureProcessingTimeDto();
         dto.setPk(pk);
         dto.setProcessingTime(processintTimeSec);
         return dto;
      } else {
         return null;
      }
   }

   @Override
   protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
      return jsonNode.isObject();
   }
}