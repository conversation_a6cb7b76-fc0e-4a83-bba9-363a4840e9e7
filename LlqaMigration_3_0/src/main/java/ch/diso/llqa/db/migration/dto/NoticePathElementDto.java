package ch.diso.llqa.db.migration.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.Optional;

@Getter
@Setter
public class NoticePathElementDto extends LlqaDto {

    @Getter
    public enum Type {
        MODEL("Model", "models"),
        UNIT("Unit", "units"),
        CHECK("Check", "checks"),
        PROCEDURE("Procedure", "procedures"),
        STEP("Step", "steps");

        private final String type;
        private final String tableName;

        Type(String type, String tableName) {
            this.type = type;
            this.tableName = tableName;
        }

        public static Type getType(String type) {
            if (type == null)
                return null;
            Optional<Type> typeOptional = Arrays.stream(Type.values())
                    .filter(t -> type.equals(t.getType()))
                    .findAny();
            if (typeOptional.isPresent()) {
                return typeOptional.get();
            } else {
                throw new RuntimeException("UNKNOWN URL TYPE >" + type + "<");
            }
        }
    }

    private long noticeId;
    private Type type;
    private Long childId;
    private int seq;
}