package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.UserTypeAheadUnit;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

@Component
public class UserTypeAheadUnitItemProcessor extends JsonContentProcessor<UserTypeAheadUnit> {

    private static final String TYPEAHEAD_UNIT = "typeahead_unit";

    @Override
    protected UserTypeAheadUnit generateDto(long pk, JsonNode yamlNode) {
        if (yamlNode.has(TYPEAHEAD_UNIT)) {
            JsonNode typeAheadUnit = yamlNode.get(TYPEAHEAD_UNIT);
            if (typeAheadUnit.isArray()) {
                return new UserTypeAheadUnit(pk, typeAheadUnit.toString());
            }
        }
        return null;
    }

    @Override
    protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
        return jsonNode.isObject();
    }
}