package ch.diso.llqa.db.migration.processor;

import ch.diso.llqa.db.migration.dto.UnitMetadataDto;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class UnitsMetadataItemProcessor extends JsonContentProcessor<UnitMetadataDto> {
   private static final String NODE_FIELD_COMMENT_BY = "_commentby";

   @Override
   protected UnitMetadataDto generateDto(long pk, JsonNode yamlNode) {
      JsonNode commentByNode = yamlNode.get(NODE_FIELD_COMMENT_BY);
      if (Objects.nonNull(commentByNode) && commentByNode.isNumber()) {
         UnitMetadataDto dto = new UnitMetadataDto();
         dto.setPk(pk);
         dto.setCommentedBy(commentByNode.asLong());
         return dto;
      } else {
         return null;
      }
   }

   @Override
   protected boolean isYamlNodeOfExpectedType(JsonNode jsonNode) {
      return jsonNode.isObject();
   }
}
