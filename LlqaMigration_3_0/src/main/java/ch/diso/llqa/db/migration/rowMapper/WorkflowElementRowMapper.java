package ch.diso.llqa.db.migration.rowMapper;

import ch.diso.llqa.db.migration.dto.WorkflowElementDto;
import ch.diso.llqa.db.migration.step.WorkflowElement.StepWorkflowElement;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;

@Component
public class WorkflowElementRowMapper implements RowMapper<WorkflowElementDto> {

    @Override
    public WorkflowElementDto mapRow(ResultSet resultSet, int i) throws SQLException {
        WorkflowElementDto dto = new WorkflowElementDto();

        dto.setPk(resultSet.getLong(StepWorkflowElement.PK_COLOUMN_NAME));
        dto.setRealId(resultSet.getLong(StepWorkflowElement.REAL_ID_COLUMN_NAME));
        dto.setChildTable(resultSet.getString(StepWorkflowElement.TABLE_COLUMN_NAME));
        dto.setCode(resultSet.getString(StepWorkflowElement.CODE_COLUMN_NAME));

        return dto;
    }
}