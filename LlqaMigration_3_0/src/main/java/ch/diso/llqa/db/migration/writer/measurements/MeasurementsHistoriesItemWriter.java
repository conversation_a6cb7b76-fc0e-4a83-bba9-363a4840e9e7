package ch.diso.llqa.db.migration.writer.measurements;

import ch.diso.llqa.db.migration.dto.measurements.MeasurementsHistoriesDto;
import java.sql.PreparedStatement;
import java.sql.Types;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class MeasurementsHistoriesItemWriter implements ItemWriter<List<MeasurementsHistoriesDto>> {

    public static final String INSERT_SQL = "INSERT INTO measurements (measure_id," +                       // 1
                                            "                          check_id," +                         // 2
                                            "                          last_measurement_measurement_id," +  // 3
                                            "                          toolunit_id," +                      // NULL
                                            "                          value," +                            // 4
                                            "                          comment," +                          // 5
                                            "                          status," +                           // 6
                                            "                          saved_by," +                         // 7
                                            "                          comment_by," +                       // NULL
                                            "                          skip," +                             // 8
                                            "                          workflow_element_check_id," +        // 9
                                            "                          saved_on," +                         // NULL
                                            "                          measurement_error_category_id," +    // 10
                                            "                          created_at," +                       // 11
                                            "                          updated_at," +                       // 12
                                            "                          created_by," +                       // 13
                                            "                          updated_by) " +                      // 14
                                            "VALUES ((SELECT measure_id FROM measurements WHERE id = ?)," +                 // 1...: measure_id
                                            "        (SELECT check_id FROM measurements WHERE id = ?)," +                   // 2...: check_id
                                            "        NULL," +                                                               // NULL: last_measurement_measurement_id
                                            "        (SELECT toolunit_id FROM measurements WHERE id = ?)," +                // 3...: toolunit_id
                                            "        ?," +                                                                  // 4...: value
                                            "        ?," +                                                                  // 5...: comment
                                            "        ?," +                                                                  // 6...: status
                                            "        ?," +                                                                  // 7...: saved_by
                                            "        NULL," +                                                               // NULL: comment_by
                                            "        (SELECT skip FROM measurements WHERE id = ?)," +                       // 8...: skip
                                            "        (SELECT workflow_element_check_id FROM measurements WHERE id = ?)," +  // 9...: workflow_element_check_id
                                            "        ?," +                                                                  // 10..: saved_on
                                            "        ?," +                                                                  // 11..: measurement_error_category_id
                                            "        ?," +                                                                  // 12..: created_at
                                            "        ?," +                                                                  // 13..: updated_at
                                            "        ?," +                                                                  // 14..: created_by
                                            "        ?)";                                                                   // 15..: updated_by
    public static final String UPDATE_PREVIOUS_SQL = "UPDATE measurements "
                                                   + "SET last_measurement_measurement_id = ? "
                                                   + "WHERE ID = ?";
    private static final String[] COLUMN_NAMES = {"id"};

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final static Logger logger = LoggerFactory.getLogger(MeasurementsHistoriesItemWriter.class);

    @Override
    public void write(List<? extends List<MeasurementsHistoriesDto>> list) {
        list.forEach(this::writeSubList);
    }

    public void writeSubList(List<MeasurementsHistoriesDto> list) {
        Set<Long> parentMeasurementIds = list.stream()
                                             .map(MeasurementsHistoriesDto::getMeasurementId)
                                             .filter(Objects::nonNull)
                                             .collect(Collectors.toSet());
        if (parentMeasurementIds.size() == 1) {
            long parentMeasurementId = parentMeasurementIds.iterator().next();
            List<MeasurementsHistoriesDto> measurementsHistories = list.stream()
                                                                       .sorted(Comparator.comparing(MeasurementsHistoriesDto::getUpdatedAt))
                                                                       .collect(Collectors.toList());
            for (MeasurementsHistoriesDto historyItem : measurementsHistories) {
                final long insertedMeasurementId = this.insertHistoryItem(parentMeasurementId, historyItem);
                this.updatePrevious(parentMeasurementId, insertedMeasurementId);
                parentMeasurementId = insertedMeasurementId;
            }
        } else {
            this.logger.error("Invalid number of parent measurement IDs >{}<", parentMeasurementIds.size());
        }
    }

    private long insertHistoryItem(long parentMeasurementId, MeasurementsHistoriesDto dto) {
        KeyHolder keyHolder = new GeneratedKeyHolder();

        jdbcTemplate.update(connection -> {
            PreparedStatement insertStatement = connection.prepareStatement(INSERT_SQL, COLUMN_NAMES);
            insertStatement.setLong(1, parentMeasurementId);
            insertStatement.setLong(2, parentMeasurementId);
            insertStatement.setLong(3, parentMeasurementId);
            if (!StringUtils.isEmpty(dto.getValue())) {
                insertStatement.setString(4, dto.getValue());
            } else {
                insertStatement.setNull(4, Types.VARCHAR);
            }
            if (!StringUtils.isEmpty(dto.getComment())) {
                insertStatement.setString(5, dto.getComment().substring(0, Math.min(dto.getComment().length(), 255)));
            } else {
                insertStatement.setNull(5, Types.VARCHAR);
            }
            insertStatement.setLong(6, dto.getStatus());
            insertStatement.setLong(7, dto.getSavedBy());
            insertStatement.setLong(8, parentMeasurementId);
            insertStatement.setLong(9, parentMeasurementId);
            insertStatement.setTimestamp(10, dto.getUpdatedAt());
            if (Objects.nonNull(dto.getMeasurementErrorCategoryId())) {
                insertStatement.setLong(11, dto.getMeasurementErrorCategoryId());
            } else {
                insertStatement.setNull(11, Types.BIGINT);
            }
            insertStatement.setTimestamp(12, dto.getCreatedAt());
            insertStatement.setTimestamp(13, dto.getUpdatedAt());
            insertStatement.setString(14, dto.getUpdatedBy());
            insertStatement.setString(15, dto.getUpdatedBy());
            return insertStatement;
        }, keyHolder);

        return (long) keyHolder.getKey();
    }

    private void updatePrevious(long measurementIdToUpdate, long previousMeasurementId) {
        jdbcTemplate.update(connection -> {
            PreparedStatement insertStatement = connection.prepareStatement(UPDATE_PREVIOUS_SQL);
            insertStatement.setLong(1, previousMeasurementId);
            insertStatement.setLong(2, measurementIdToUpdate);
            return insertStatement;
        });
    }
}