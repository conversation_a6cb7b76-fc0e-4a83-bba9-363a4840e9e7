package ch.diso.llqa.db.migration.step.flowcontrol;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepMeasuresFlowcontrol extends StepWorkflowRule {
    private static final String TABLE_NAME = "measures";

    @Value("${measures.flow.control.fetch.size}")
    private int fetchSize;

    @Value("${measures.flow.control.chunk.size}")
    private int chunkSize;

    /**
     * {@inheritDoc}
     */
    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    /**
     * get the name from the table to do the flow control migration on it
     *
     * @return the name from the table to do the flow control migration on it. always 'measures'
     */
    @Override
    protected String getTableName() {
        return TABLE_NAME;
    }

    @Override
    protected String getSelectRealIdSql() {
        return "SELECT p2.real_id " +
                "FROM measures m2 INNER JOIN STEPS s2 ON m2.step_id = s2.id" +
                "                 INNER JOIN procedures p2 ON s2.procedure_id = p2.id " +
                "WHERE m2.id = %d";
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected String getStepName() {
        return "stepMeasuresFlowcontrol";
    }
}