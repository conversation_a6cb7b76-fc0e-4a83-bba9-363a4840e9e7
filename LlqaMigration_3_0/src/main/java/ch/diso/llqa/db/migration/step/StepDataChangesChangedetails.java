package ch.diso.llqa.db.migration.step;

import ch.diso.llqa.db.migration.common.ColumnType;
import ch.diso.llqa.db.migration.dto.DataChangesChangedetailsDto;
import ch.diso.llqa.db.migration.processor.DataChangesChangedetailsItemProcessor;
import ch.diso.llqa.db.migration.reader.JsonItemReaderConfiguration;
import ch.diso.llqa.db.migration.writer.DataChangesChangeDetailsItemWriter;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepDataChangesChangedetails extends JsonMigrationStep {

    public static final String TABLE_NAME = "data_changes";

    public static final String COLUMN_NAME = "changedetails";

    public static final ColumnType COLUMN_TYPE = ColumnType.OBJECT;

    @Value("${datachanges.changedetails.fetch.size}")
    private int fetchSize;

    @Value("${datachanges.changedetails.chunk.size}")
    private int chunkSize;

    public static final int MIN_ID = 1;

    @Autowired
    private DataChangesChangedetailsItemProcessor processor;

    @Autowired
    private DataChangesChangeDetailsItemWriter writer;

    @Override
    public Step createStep() {
        reader.configure(new JsonItemReaderConfiguration(this.databaseName, TABLE_NAME, COLUMN_NAME, COLUMN_TYPE, fetchSize, MIN_ID, this.password));
        return stepBuilderFactory.get("stepDataChangesChangedetails").listener(stepCompletionListener)
                .<JsonNode, DataChangesChangedetailsDto> chunk(chunkSize)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
}
