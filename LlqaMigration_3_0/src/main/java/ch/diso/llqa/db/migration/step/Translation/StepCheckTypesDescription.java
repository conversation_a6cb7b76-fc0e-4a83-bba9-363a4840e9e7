package ch.diso.llqa.db.migration.step.Translation;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class StepCheckTypesDescription extends StepWorkflowElementTranslation {

    private static final String TABLE_NAME = "check_types";
    private static final String COLUMN_NAME = "description";
    public static final int MIN_ID = 1;

    @Value("${checktypes.description.translation.fetch.size}")
    private int fetchSize;

    @Value("${checktypes.description.translation.chunk.size}")
    private int chunkSize;

    @Override
    protected String getSrcColumnName() {
        return COLUMN_NAME;
    }

    @Override
    protected String getDestColumnName() {
        return COLUMN_NAME;
    }

    @Override
    protected String getTableName() {
        return TABLE_NAME;
    }

    @Override
    protected int getChunkSize() {
        return this.chunkSize;
    }

    @Override
    protected int getFetchSize() {
        return this.fetchSize;
    }

    @Override
    protected int getMinId() {
        return MIN_ID;
    }

    @Override
    protected String getStepName() { return "stepCheckTypesDescription"; }
}