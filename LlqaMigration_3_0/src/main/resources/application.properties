logging.level.root=info
logging.level.org.springframework.web=error
logging.level.org.hibernate=error
log4j.category.org.springframework.jdbc.core=error

# db connection
spring.datasource.platform=postgres
spring.datasource.connectionProperties=useUnicode=true;characterEncoding=utf-8;
spring.datasource.url=
spring.datasource.username=postgres
spring.datasource.password=admin

database.name=

pre.migrate.scripts=
#pre.migrate.scripts=preMigrateScripts.json
#pre.migrate.scripts=preMigrateScriptsGeorgFischerTest.json
#pre.migrate.scripts=preMigrateScriptsOsterwalder.json
#pre.migrate.scripts=preMigrateScriptsHpTest.json

post.migrate.scripts=
#post.migrate.scripts=postMigrateScripts.json
#post.migrate.scripts=postMigrateScriptsOsterwalder.json

# steps
users.userinfo.fetch.size=500
users.userinfo.chunk.size=200

users.typeaheadunit.fetch.size=500
users.typeaheadunit.chunk.size=200

binaryfiles.metadata.fetch.size=500
binaryfiles.metadata.chunk.size=200

workflowelement.tags.fetch.size=500
workflowelement.tags.chunk.size=200

workflow.element.tag.values.sub.values.fetch.size=500
workflow.element.tag.values.sub.values.chunk.size=200

config.columns.fetch.size=500
config.columns.chunk.size=200

measures.calculation.fetch.size=500
measures.calculation.chunk.size=200

measures.metadata.fetch.size=500
measures.metadata.chunk.size=200

models.metadata.fetch.size=500
models.metadata.chunk.size=200

units.metadata.fetch.size=500
units.metadata.chunk.size=200

toolunits.metadata.fetch.size=500
toolunits.metadata.chunk.size=200

procedures.flow.control.fetch.size=500
procedures.flow.control.chunk.size=200

steps.flow.control.fetch.size=500
steps.flow.control.chunk.size=200

measures.flow.control.fetch.size=500
measures.flow.control.chunk.size=200

checks.metadata.fetch.size=500
checks.metadata.chunk.size=200

notices.timeline.fetch.size=500
notices.timeline.chunk.size=200

notices.path.fetch.size=500
notices.path.chunk.size=200
notice.path.bulk.size=200

configentries.workflowelement.fetch.size=500
configentries.workflowelement.chunk.size=200

checks.checkdata.fetch.size=20
checks.checkdata.chunk.size=20
checks.checkdata.bulk.size=200

datachanges.changedetails.fetch.size=500
datachanges.changedetails.chunk.size=200

measurements.metadata.fetch.size=500
measurements.metadata.chunk.size=200

measurements.history.fetch.size=500
measurements.history.chunk.size=200

measurements.rawvalue.fetch.size=500
measurements.rawvalue.chunk.size=200

procedures.processing.time.fetch.size=500
procedures.processing.time.chunk.size=200

## workflow element steps
procedures.workflowelement.fetch.size=500
procedures.workflowelement.chunk.size=200

steps.workflowelement.fetch.size=500
steps.workflowelement.chunk.size=200

checktypes.workflowelement.fetch.size=500
checktypes.workflowelement.chunk.size=200

measures.workflowelement.fetch.size=500
measures.workflowelement.chunk.size=200

devicetypes.workflowelement.fetch.size=500
devicetypes.workflowelement.chunk.size=200

models.workflowelement.fetch.size=500
models.workflowelement.chunk.size=200

tooltype.workflowelement.fetch.size=500
tooltype.workflowelement.chunk.size=200

## translation steps
translation.bulk.size=200

procedures.title.translation.fetch.size=500
procedures.title.translation.chunk.size=200
procedures.description.translation.fetch.size=500
procedures.description.translation.chunk.size=200

steps.title.translation.fetch.size=500
steps.title.translation.chunk.size=200
steps.description.translation.fetch.size=500
steps.description.translation.chunk.size=200

measures.title.translation.fetch.size=500
measures.title.translation.chunk.size=200
measures.description.translation.fetch.size=500
measures.description.translation.chunk.size=200

models.title.translation.fetch.size=500
models.title.translation.chunk.size=200
models.description.translation.fetch.size=500
models.description.translation.chunk.size=200

checktypes.title.translation.fetch.size=500
checktypes.title.translation.chunk.size=200
checktypes.description.translation.fetch.size=500
checktypes.description.translation.chunk.size=200

devicetypes.title.translation.fetch.size=500
devicetypes.title.translation.chunk.size=200
devicetypes.description.translation.fetch.size=500
devicetypes.description.translation.chunk.size=200

tooltypes.title.translation.fetch.size=500
tooltypes.title.translation.chunk.size=200
tooltypes.description.translation.fetch.size=500
tooltypes.description.translation.chunk.size=200

workflow.element.tag.values.valuetranslation.fetch.size=500
workflow.element.tag.values.valuetranslation.chunk.size=200

measurementerrorcategories.value.translation.fetch.size=500
measurementerrorcategories.value.translation.chunk.size=200

workflow.element.tag.values.value.translation.fetch.size=500
workflow.element.tag.values.value.translation.chunk.size=200
