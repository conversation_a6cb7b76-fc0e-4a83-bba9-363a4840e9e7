CREATE SEQUENCE sq_config_entrie_col_contents;
CREATE TABLE config_entrie_col_contents
(
  id                          BIGINT DEFAULT nextval('sq_config_entrie_col_contents' :: REGCLASS) NOT NULL
                              CONSTRAINT pk_config_entrie_col_contents
                              PRIMARY KEY,
  config_entry_id             BIGINT NOT NULL,
  config_table_col_header_id  BIGINT NOT NULL,
  content                     VARCHAR(1024) NOT NULL,
  seqnum                      SMALLINT NOT NULL,
  created_at                  TIMESTAMP,
  updated_at                  TIMESTAMP,
  created_by                  VA<PERSON>HAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by                  VA<PERSON>HAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_config_entrie_col_contents_config_entrie_id ON config_entrie_col_contents (config_entry_id);
CREATE INDEX fki_config_entrie_col_contents_config_table_col_header_id ON config_entrie_col_contents (config_table_col_header_id);
ALTER TABLE config_entrie_col_contents ADD CONSTRAINT fk_config_entrie_col_contents_config_entrie_id FOREIGN KEY (config_entry_id) REFERENCES configentries (id) ON DELETE CASCADE;
ALTER TABLE config_entrie_col_contents ADD CONSTRAINT fk_config_entrie_col_contents_config_table_col_header_id FOREIGN KEY (config_table_col_header_id) REFERENCES config_table_col_headers (id) ON DELETE CASCADE;