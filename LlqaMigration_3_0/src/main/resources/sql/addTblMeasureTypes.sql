CREATE SEQUENCE sq_measure_types;
CREATE TABLE measure_types
(
  id              BIGINT DEFAULT nextval('sq_measure_types' :: REGCLASS) NOT NULL
                  CONSTRAINT pk_measure_types
                  PRIMARY KEY,
  measure_id      BIGINT NOT NULL,
  measure_type    SMALLINT NOT NULL,
  optional        BO<PERSON>EAN NOT NULL DEFAULT FALSE,
  internal        BOOLEAN NOT NULL DEFAULT FALSE,
  number_decimals SMALLINT,
  target_value    NUMERIC,
  target_unit     VARCHAR(10),
  threshold_value NUMERIC,
  threshold_unit  VARCHAR(10),
  min_value       NUMERIC,
  max_value       NUMERIC,
  unit            VARCHAR(10),
  value           NUMERIC,
  min_length      BIGINT,
  expected        NUMERIC,
  comparator      SMALLINT,
  grouping        VARCHAR(50),
  list_content    JSON,
  reg_exp         VARCHAR(4000),
  created_at      TIMESTAMP,
  updated_at      TIMESTAMP,
  created_by      <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by      VARCHAR(255) DEFAULT 'system' :: <PERSON><PERSON><PERSON><PERSON>R VARYING NOT NULL
);
CREATE INDEX fki_measure_types_measure_id ON measure_types (measure_id);
ALTER TABLE measure_types ADD CONSTRAINT fk_measure_types_measure_id FOREIGN KEY (measure_id) REFERENCES measures (id) ON DELETE CASCADE;
ALTER TABLE measure_types ADD CONSTRAINT uc_measure_types_measure_id_measure_type UNIQUE (measure_id, measure_type);
