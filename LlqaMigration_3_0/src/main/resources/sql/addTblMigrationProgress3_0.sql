CREATE SEQUENCE sq_migrationprogess;
CREATE TABLE migrationprogess
(
  id            BIGINT DEFAULT nextval('sq_migrationprogess' :: REGCLASS) NOT NULL
                CONSTRAINT pk_migrationprogess
                PRIMARY KEY,
  migrationstep VARCHAR(50) NOT NULL,
  min_id        BIGINT NOT NULL,
  errorcode     INTEGER DEFAULT NULL,
  created_at    TIMESTAMP NOT NULL DEFAULT current_timestamp,
  updated_at    TIMESTAMP DEFAULT NULL,
  created_by    VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by    VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
ALTER TABLE migrationprogess ADD CONSTRAINT uk_migrationprogess_migrationstep UNIQUE (migrationstep);

CREATE SEQUENCE sq_migrationprogessskips;
CREATE TABLE migrationprogessskips
(
  id                  BIGINT DEFAULT nextval('sq_migrationprogessskips' :: REGCLASS) NOT NULL
                      CONSTRAINT pk_migrationprogessskips
                      PRIMARY KEY,
  migrationprogess_id BIGINT NOT NULL,
  errorrecord_id      BIGINT NOT NULL,
  errorcode           INTEGER DEFAULT NULL,
  solved              BOOLEAN DEFAULT FALSE,
  created_at          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at          TIMESTAMP DEFAULT NULL,
  created_by          VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by          VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_migrationprogessskips_migrationprogess_id ON migrationprogessskips (migrationprogess_id);
ALTER TABLE  migrationprogessskips ADD CONSTRAINT fk_migrationprogessskips_migrationprogess_id FOREIGN KEY (migrationprogess_id) REFERENCES migrationprogess (id) ON DELETE CASCADE;

INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('active_procedures_workflow', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('binaryfiles_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('checks_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('checks_checkdata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('checks_checktypes', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('check_types_title', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('check_types_description', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('check_types_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('config_entries_blocked_by', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('data_changes_changedetails', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('device_types', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('device_types_title', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('device_types_description', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('device_types_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measurement_error_categories_name', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measurements_rawvalues', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measurements_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measurements_previous_measurements', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measures_title', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measures_description', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measures_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measures_calculation', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('measures_workflow_elements', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('models_title', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('models_description', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('models_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('notices_path', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('notices_timeline', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('procedures', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('procedures_title', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('procedures_description', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('procedures_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('procedures_workflow_elements', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('settings_value', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('settings_sub_values', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('steps_title', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('steps_description', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('steps_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('steps_workflow_elements', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('tool_types', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('tool_types_title', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('tool_types_description', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('tool_types_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('tool_units_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('units', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('units_metadata', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('users_dashboardinfo', 1);
INSERT INTO migrationprogess (migrationstep, min_id) VALUES ('users_metadata', 1);