UPDATE data_changes SET owner_workflow_element_id = workflow_element_id
                       ,subobjtype = 'workflow_element_tag_value'
                       ,subobject_id = (
                                          SELECT MIN(wfEltTagValues.id)
                                          FROM tags tag INNER JOIN workflow_element_tags wfEltTag ON (tag.id = wfEltTag.tag_id)
                                                        INNER JOIN workflow_element_tag_values wfEltTagValues ON (wfEltTag.id = wfEltTagValues.workflow_element_tag_id)
                                          WHERE seqnum = CAST(SPLIT_PART(data_changes.field, '_', 2) AS INTEGER)
                                            AND wfEltTag.workflow_element_id = data_changes.workflow_element_id
                                       )
                       ,updated_at = CURRENT_TIMESTAMP
                       ,updated_by = 'migration_3.0'
WHERE field LIKE 'tag_%';