if [ -z $1 ] ; then
    echo "ERROR - 1. argument must be set: DB host"
    exit 1
fi

if [ -z $2 ] ; then
    echo "ERROR - 2. argument must be set: Path to DB dump"
    exit 1
fi

if [ -z $3 ] ; then
    echo "ERROR - 3. argument must be set: DB user"
    exit 1
fi

if [ -z $4 ] ; then
    echo "ERROR - 4. argument must be set: DB name"
    exit 1
fi

if [ -z $5 ] ; then
    echo "ERROR - 5. argument must be set: DB password"
    exit 1
fi

echo "You passed the following parameters:"
echo "  1. DB host         = $1"
echo "  2. Path to DB dump = $2"
echo "  3. DB user         = $3"
echo "  4. DB name         = $4"
echo "  5. DB password     = $5"

echo "Drop DB..."
dropdb --if-exist -p 5432 -h $1 -U $3 $4
if [ $? -ne 0 ] ; then
  echo "...drop DB failed, canceling migration"
  exit
fi

echo "Create DB..."
createdb -p 5432 -h $1 -U $3 $4
if [ $? -ne 0 ] ; then
  echo "...create DB failed, canceling migration"
  exit
fi

echo "Restore DB..."
pg_restore -x -O --schema=public -p 5432 -h $1 -U $3 -d $4 $2
if [ $? -ne 0 ] ; then
  echo "...restore DB failed, canceling migration"
  exit
fi

echo "+-------------+"
echo "| DB restored |"
echo "+-------------+"