CREATE SEQUENCE sq_user_metadata;
CREATE TABLE user_metadata
(
  id          BIGINT DEFAULT nextval('sq_user_metadata' :: REGCLASS) NOT NULL
              CONSTRAINT pk_user_metadata
              PRIMARY KEY,
  user_id     BIGINT NOT NULL,
  key         VARCHAR(25) NOT NULL,
  value       VARCHAR(1024),
  created_at  TIMESTAMP,
  updated_at  TIMESTAMP,
  created_by  <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by  VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_user_metadata_user_id ON user_metadata (user_id);
ALTER TABLE user_metadata ADD CONSTRAINT fk_user_metadata_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE user_metadata ADD CONSTRAINT uc_user_metadata_key UNIQUE (user_id, key);