ALTER TABLE activechecktypes RENAME TO active_check_types;
ALTER TABLE active_check_types ALTER COLUMN model_id SET NOT NULL;
ALTER TABLE active_check_types ALTER COLUMN checktype_id SET NOT NULL;
ALTER TABLE active_check_types ADD CONSTRAINT uc_active_check_types_model_id_checktype_id UNIQUE (model_id, checktype_id);
ALTER TABLE active_check_types DROP CONSTRAINT IF EXISTS fk_activechecktype_model_id, ADD CONSTRAINT fk_activechecktype_model_id FOREIGN KEY (model_id) REFERENCES models(id) ON DELETE CASCADE;
ALTER TABLE active_check_types DROP CONSTRAINT IF EXISTS fk_activechecktype_checktype_id, ADD CONSTRAINT fk_activechecktype_checktype_id FOREIGN KEY (checktype_id) REFERENCES check_types(id) ON DELETE CASCADE;