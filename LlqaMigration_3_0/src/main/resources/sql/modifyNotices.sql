ALTER TABLE notices ADD COLUMN check_id BIGINT;
ALTER TABLE notices ADD COLUMN step_id BIGINT;
CREATE INDEX fki_notices_check_id ON notices (check_id);
CREATE INDEX fki_notices_step_id ON notices (step_id);
CREATE INDEX fki_notices_category_id ON notices (category_id);
ALTER TABLE notices ADD CONSTRAINT fk_notices_check_id FOREIGN KEY (check_id) REFERENCES checks (id) ON DELETE CASCADE;
ALTER TABLE notices ADD CONSTRAINT fk_notices_steps_id FOREIGN KEY (step_id) REFERENCES steps (id) ON DELETE CASCADE;
ALTER TABLE notices ADD CONSTRAINT fk_notices_category_id FOREIGN KEY (category_id) REFERENCES snippets (id) ON DELETE RESTRICT;
ALTER TABLE notices ALTER COLUMN status SET NOT NULL;
ALTER TABLE notices ALTER COLUMN text SET NOT NULL;