create sequence if not exists sq_tag_values;
create table if not exists tag_values
(
  id          BIGINT DEFAULT NEXTVAL('sq_tag_values'::regclass) NOT NULL PRIMARY KEY,
  tag_id      BIGINT NOT NULL,
  name        <PERSON><PERSON><PERSON> NOT NULL,
  tmp_name    TEXT NOT NULL,
  deleted     BOOLEAN DEFAULT FALSE,
  created_at  TIMESTAMP,
  updated_at  TIMESTAMP,
  created_by  VA<PERSON>HAR(255) DEFAULT 'system'::character varying not null,
  updated_by  VARCHAR(255) DEFAULT 'system'::character varying not null
);

CREATE INDEX fki_tag_values_tag_id ON tag_values (tag_id);
ALTER TABLE tag_values ADD CONSTRAINT fk_tags_tag_id FOREIGN KEY (tag_id) REFERENCES settings (id) ON DELETE CASCADE;
CREATE UNIQUE INDEX ukidx_tag_values_tag_id_name ON tag_values (tag_id, (name::text));