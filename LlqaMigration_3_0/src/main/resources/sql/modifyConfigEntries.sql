ALTER TABLE configentries RENAME TO config_entries;
ALTER TABLE config_entries ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE config_entries ADD CONSTRAINT uc_config_entries_workflow_element_id UNIQUE (workflow_element_id);
CREATE INDEX fki_config_entries_workflow_element_id ON config_entries (workflow_element_id);
ALTER TABLE config_entries ADD CONSTRAINT fk_config_entries_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE config_entries DROP CONSTRAINT IF EXISTS fk_config_entries_config_table_id;
ALTER TABLE config_entries ADD CONSTRAINT fk_config_entries_config_table_id FOREIGN KEY (configtable_id) REFERENCES configtables (id) ON DELETE CASCADE;
ALTER TABLE config_entries ADD CONSTRAINT chk_seqnum CHECK (code_id >= 0);