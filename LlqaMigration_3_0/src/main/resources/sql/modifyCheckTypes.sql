ALTER TABLE checktypes RENAME TO check_types;
ALTER TABLE check_types ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE check_types ADD CONSTRAINT uc_check_types_workflow_element_id UNIQUE (workflow_element_id);
CREATE INDEX fki_check_types_workflow_element_id ON check_types (workflow_element_id);
ALTER TABLE check_types ADD CONSTRAINT fk_check_types_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE check_types ADD CONSTRAINT chk_seqnum CHECK (seqnum >= 0);