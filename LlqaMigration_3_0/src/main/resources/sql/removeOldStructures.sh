if [ "$#" -ne 4 ]; then
    echo "ERROR - Illegal number of parameters"
    echo "  1.: DB name"
    echo "  2.: DB user"
    echo "  3.: host"
    echo "  4.: port"
    exit
fi

echo "connect to DB >$1< with user >$2< host >$3< port >$4<"

echo "+-------------------------+"
echo "|  REMOVE OLD STRUCTURES  |"
echo "+-------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f removeOldStructures.sql