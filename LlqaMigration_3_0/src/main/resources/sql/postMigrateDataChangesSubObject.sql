CREATE OR <PERSON><PERSON>LACE FUNCTION llqa3Mig__getSubObjIdCheckType(checkTypeCode VARCHAR)
  RETURNS BIGINT AS
'
DECLARE
  foundCheckTypeIds CONSTANT BIGINT[] := ARRAY(SELECT id FROM check_types WHERE code = checkTypeCode);
BEGIN
  IF ARRAY_LENGTH(foundCheckTypeIds, 1) = 1 THEN
    RETURN (foundCheckTypeIds)[1];
  ELSE
    RETURN NULL;
  END IF;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqa3Mig__getSubObjIdProcedure(checkTypeCode VARCHAR, dataChangeNewData JSON, wfEltId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  procedureRow procedures%ROWTYPE;
BEGIN
  SELECT p.*
  INTO procedureRow
  FROM procedures p
  WHERE p.workflow_element_id = wfEltId;

  IF FOUND THEN
    -- THE DATA CHANGE ROW REFERENCES TO PROCEDURE
    RETURN procedureRow.id;
  ELSE
    -- SEARCH FOR THE PROCEDURE ID IN THE DATA CHANGES ''NEW DATA''
    -- ==> used if a model assigned a procedure over the table active procedures
    DECLARE
      procedureId BIGINT;
    BEGIN
      SELECT p.id
      INTO procedureId
      FROM procedures p
      WHERE p.id = (json_extract_path_text(dataChangeNewData, ''procedure_id'')::BIGINT);
      IF procedureId IS NOT NULL THEN
        RETURN procedureId;
      END IF;
    END;

    -- THE DATA CHANGE ROW REFERENCES NOT TO A PROCEDURE ==> WE TRY TO FIND THE PROCEDURE BY ITS CODE
    DECLARE
      procedureIds CONSTANT BIGINT[] := ARRAY(SELECT p.id FROM procedures p WHERE p.code = checkTypeCode);
    BEGIN
      IF ARRAY_LENGTH(procedureIds, 1) = 1 THEN
        RETURN (procedureIds)[1];
      END IF;
    END;
    -- SORRY, THE REFERENCED CODE IS NOT UNIQUE ON PROCEDURES
    RETURN NULL;
  END IF;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqa3Mig__getSubObjIdStep(subObj VARCHAR, wfEltId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  stepIds CONSTANT BIGINT[] := ARRAY(SELECT s.id
                                     FROM steps s INNER JOIN procedures p ON s.procedure_id = p.id
                                     WHERE p.workflow_element_id = wfEltId
                                       AND s.code = subObj
                                    );
BEGIN
  IF ARRAY_LENGTH(stepIds, 1) = 1 THEN
    RETURN (stepIds)[1];
  ELSE
    RETURN NULL;
  END IF;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqa3Mig__getSubObjIdMeasure(subObj VARCHAR, wfEltId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  measureIds CONSTANT BIGINT[] := ARRAY(SELECT m.workflow_element_id
                                        FROM measures m INNER JOIN steps s ON m.step_id = s.id
                                                        INNER JOIN procedures p ON s.procedure_id = p.id
                                        WHERE p.workflow_element_id = wfEltId
                                          AND s.code = SPLIT_PART(subObj, ''.'', 1)
                                          AND m.code = SPLIT_PART(subObj, ''.'', 2)
                                       );
BEGIN
  IF ARRAY_LENGTH(measureIds, 1) = 1 THEN
    RETURN (measureIds)[1];
  ELSE
    RETURN NULL;
  END IF;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqa3Mig__getSubObjIdModel(subObj VARCHAR, wfEltId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  modelRow models%ROWTYPE;
BEGIN
  SELECT m.*
  INTO modelRow
  FROM models m
  WHERE m.workflow_element_id = wfEltId;

  IF FOUND THEN
    -- THE DATA CHANGE ROW REFERENCES TO PROCEDURE
    RETURN modelRow.id;
  ELSE
    DECLARE
      foundModelIds CONSTANT BIGINT[] := ARRAY(SELECT id FROM check_types WHERE code = subObj);
    BEGIN
      IF ARRAY_LENGTH(foundModelIds, 1) = 1 THEN
        RETURN (foundModelIds)[1];
      ELSE
        RETURN NULL;
      END IF;
    END;
  END IF;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqa3Mig__getSubObjIdDocument(subObj VARCHAR)
  RETURNS BIGINT AS
'
  SELECT doc.id
  FROM documents doc INNER JOIN binaryfiles file ON doc.binaryfile_id = file.id
  WHERE file.original_filename = subObj;
'
LANGUAGE SQL;

CREATE OR REPLACE FUNCTION llqa3Mig__getSubObjIdGalleryItem(subObj VARCHAR)
  RETURNS BIGINT AS
'
  SELECT MIN(galleryItem.id)
  FROM gallery_items galleryItem LEFT JOIN binaryfiles fileFullImg ON galleryItem.fullimage_id = fileFullImg.id
                                 LEFT JOIN binaryfiles fileThumbnail ON galleryItem.fullimage_id = fileThumbnail.id
  WHERE fileFullImg.original_filename = subObj
     OR fileThumbnail.original_filename = subObj;
'
LANGUAGE SQL;

CREATE OR REPLACE FUNCTION llqa3Mig__getSubObjId(dataChangeId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  dataChangeRow data_changes%ROWTYPE;
BEGIN
  SELECT dc.*
  INTO dataChangeRow
  FROM data_changes dc
  WHERE dc.id = dataChangeId;
  RAISE NOTICE ''data change id >%<, sub object type >%<'', dataChangeId, dataChangeRow.subobjtype;

  CASE dataChangeRow.subobjtype
    WHEN ''checktype'' THEN RETURN llqa3Mig__getSubObjIdCheckType(dataChangeRow.subobject);
    WHEN ''document'' THEN RETURN llqa3Mig__getSubObjIdDocument(dataChangeRow.subobject);
    WHEN ''gallery_item'' THEN RETURN llqa3Mig__getSubObjIdGalleryItem(dataChangeRow.subobject);
    WHEN ''model'' THEN RETURN llqa3Mig__getSubObjIdModel(dataChangeRow.subobject, dataChangeRow.workflow_element_id);
    WHEN ''procedure'' THEN RETURN llqa3Mig__getSubObjIdProcedure(dataChangeRow.subobject, dataChangeRow.new_data, dataChangeRow.workflow_element_id);
    WHEN ''step'' THEN RETURN llqa3Mig__getSubObjIdStep(dataChangeRow.subobject, dataChangeRow.workflow_element_id);
    WHEN ''measure'' THEN RETURN llqa3Mig__getSubObjIdMeasure(dataChangeRow.subobject, dataChangeRow.workflow_element_id);
    ELSE RETURN NULL;
  END CASE;
END;
'
LANGUAGE plpgsql;

CREATE INDEX i_procedure_code_id ON procedures (code);
CREATE INDEX i_step_code_id ON steps (code);
CREATE INDEX i_measure_code_id ON measures (code);
CREATE INDEX i_models_code_id ON models (code);

UPDATE data_changes SET subobject_id = llqa3Mig__getSubObjId(id)
                       ,updated_at = CURRENT_TIMESTAMP
                       ,updated_by = 'migration_3.0';

DROP INDEX i_procedure_code_id;
DROP INDEX i_step_code_id;
DROP INDEX i_measure_code_id;
DROP INDEX i_models_code_id;

DROP FUNCTION llqa3Mig__getSubObjId(BIGINT);
DROP FUNCTION llqa3Mig__getSubObjIdCheckType(VARCHAR);
DROP FUNCTION llqa3Mig__getSubObjIdGalleryItem(VARCHAR);
DROP FUNCTION llqa3Mig__getSubObjIdDocument(VARCHAR);
DROP FUNCTION llqa3Mig__getSubObjIdModel(VARCHAR, BIGINT);
DROP FUNCTION llqa3Mig__getSubObjIdMeasure(VARCHAR, BIGINT);
DROP FUNCTION llqa3Mig__getSubObjIdStep(VARCHAR, BIGINT);
DROP FUNCTION llqa3Mig__getSubObjIdProcedure(VARCHAR, JSON, BIGINT);