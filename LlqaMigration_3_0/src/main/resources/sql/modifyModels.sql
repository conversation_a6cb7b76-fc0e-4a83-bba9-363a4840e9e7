ALTER TABLE models ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE models ADD CONSTRAINT uc_models_workflow_element_id UNIQUE (workflow_element_id);
ALTER TABLE models RENAME COLUMN realid TO real_id;
ALTER TABLE models RENAME COLUMN devicetype_id TO device_type_id;
CREATE INDEX fki_models_workflow_element_id ON models (workflow_element_id);
ALTER TABLE models ADD CONSTRAINT fk_models_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE models DROP CONSTRAINT IF EXISTS fk_models_device_type_id;
ALTER TABLE models DROP CONSTRAINT IF EXISTS fk_models_devicetype_id;
ALTER TABLE models ADD CONSTRAINT fk_models_device_type_id FOREIGN KEY (device_type_id) REFERENCES devicetypes (id) ON DELETE CASCADE;