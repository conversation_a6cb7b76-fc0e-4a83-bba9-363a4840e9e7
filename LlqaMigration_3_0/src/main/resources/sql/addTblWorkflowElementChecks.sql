CREATE SEQUENCE sq_workflow_element_checks;
CREATE TABLE workflow_element_checks
(
  id                  BIGINT DEFAULT nextval('sq_workflow_element_checks' :: REGCLASS) NOT NULL
                      CONSTRAINT pk_workflow_element_checks
                      PRIMARY KEY,
  check_id            BIGINT NOT NULL,
  workflow_element_id BIGINT NOT NULL,
  assignee_id         BIGINT,
  committed_by        BIGIN<PERSON>,
  status              SMALLINT NOT NULL,
  locked              BOOLEAN NOT NULL,
  remaining_time      BIGINT,
  enforce             SMALLINT DEFAULT '1'::SMALLINT NOT NULL,
  procedure_seqnum    INTEGER DEFAULT 0 NOT NULL,
  step_seqnum         INTEGER,
  measure_seqnum      INTEGER,
  created_at          TIMESTAMP,
  updated_at          TIMESTAMP,
  created_by          VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by          VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_workflow_element_checks_check_id ON workflow_element_checks (check_id);
CREATE INDEX fki_workflow_element_checks_workflow_element_id ON workflow_element_checks (workflow_element_id);
CREATE INDEX fki_workflow_element_checks_assignee_id ON workflow_element_checks (assignee_id);
CREATE INDEX fki_workflow_element_checks_committed_by ON workflow_element_checks (committed_by);
ALTER TABLE workflow_element_checks ADD CONSTRAINT fk_workflow_element_checks_check_id FOREIGN KEY (check_id) REFERENCES checks (id) ON DELETE CASCADE;
ALTER TABLE workflow_element_checks ADD CONSTRAINT fk_workflow_element_checks_workflow_element_group_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE workflow_element_checks ADD CONSTRAINT fk_workflow_element_checks_assignee_id FOREIGN KEY (assignee_id) REFERENCES assignees (id) ON DELETE SET NULL;
ALTER TABLE workflow_element_checks ADD CONSTRAINT fk_workflow_element_checks_committed_by FOREIGN KEY (committed_by) REFERENCES users (id) ON DELETE RESTRICT;
