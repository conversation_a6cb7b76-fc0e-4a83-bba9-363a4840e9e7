CREATE SEQUENCE sq_binaryfile_metadata;
CREATE TABLE binaryfile_metadata
(
  id            BIGINT DEFAULT nextval('sq_binaryfile_metadata' :: REGCLASS) NOT NULL
                CONSTRAINT pk_binaryfile_metadata
                PRIMARY KEY,
  binaryfile_id BIGINT NOT NULL,
  type          VA<PERSON><PERSON><PERSON>(30) NOT NULL,
  value         VARCHAR(30) NOT NULL,
  created_at    TIMESTAMP,
  updated_at    TIMESTAMP,
  created_by    <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING    NOT NULL,
  updated_by    VA<PERSON><PERSON>R(255) DEFAULT 'system' :: CHARACTER VARYING    NOT NULL
);
CREATE INDEX fki_binaryfile_metadata_binaryfile_id ON binaryfile_metadata (binaryfile_id);
ALTER TABLE binaryfile_metadata ADD CONSTRAINT fk_binaryfile_metadata_binaryfile_id FOREIGN KEY (binaryfile_id) REFERENCES binaryfiles (id) ON DELETE CASCADE;