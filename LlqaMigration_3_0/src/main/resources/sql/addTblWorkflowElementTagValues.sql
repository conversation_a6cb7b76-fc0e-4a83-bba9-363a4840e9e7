create sequence if not exists sq_workflow_element_tag_values;
create table if not exists workflow_element_tag_values
(
  id                      bigint default nextval('sq_workflow_element_tag_values'::regclass) not null primary key,
  workflow_element_tag_id bigint not null,
  tag_value_id            bigint not null,
  created_at              timestamp,
  updated_at              timestamp,
  created_by              varchar(255) default 'system'::character varying not null,
  updated_by              varchar(255) default 'system'::character varying not null
);

CREATE INDEX fki_workflow_element_tag_values_workflow_element_tag_id ON workflow_element_tag_values (workflow_element_tag_id);
ALTER TABLE workflow_element_tag_values ADD CONSTRAINT fk_workflow_element_tag_values_workflow_element_tag_id FOREIGN KEY (workflow_element_tag_id) REFERENCES workflow_element_tags (id) ON DELETE CASCADE;

CREATE INDEX fki_workflow_element_tag_value_tag_value_id ON workflow_element_tag_values (tag_value_id);
ALTER TABLE workflow_element_tag_values ADD CONSTRAINT fk_workflow_element_tags_values_tag_value_id FOREIGN KEY (tag_value_id) REFERENCES tag_values (id) ON DELETE CASCADE;

ALTER TABLE workflow_element_tag_values ADD CONSTRAINT uk_workflow_element_tags_subvalue_workflow_element_tag_id_tag_value_id UNIQUE (workflow_element_tag_id, tag_value_id);