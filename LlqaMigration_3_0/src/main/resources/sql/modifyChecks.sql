ALTER TABLE checks ADD COLUMN testrun_workflow_id BIGINT;
<PERSON><PERSON><PERSON> TABLE checks ADD COLUMN closed_by BIGINT;
ALTER TABLE checks ADD COLUMN comment_by BIGINT;
<PERSON>TER TABLE checks ADD COLUMN assignment_mode SMALLINT;
<PERSON><PERSON><PERSON> TABLE checks ADD COLUMN registration_mode SMALLINT;
ALTER TABLE checks ADD COLUMN last_finished_procedure_workflow_element_check_id BIGINT;
ALTER TABLE checks RENAME COLUMN dueby TO due_by;
CREATE INDEX fki_checks_testrun_workflow_id ON checks (testrun_workflow_id);
ALTER TABLE checks ADD CONSTRAINT fk_checks_testrun_workflow_id FOREIGN KEY (testrun_workflow_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
CREATE INDEX fki_checks_closed_by ON checks (closed_by);
CREATE INDEX fki_checks_comment_by ON checks (comment_by);
CREATE INDEX fki_checks_last_finished_procedure_workflow_element_id ON checks (last_finished_procedure_workflow_element_check_id);
<PERSON><PERSON><PERSON> TABLE checks ADD CONSTRAINT fk_checks_closed_by <PERSON><PERSON><PERSON><PERSON><PERSON>EY (closed_by) REFERENCES users (id) ON DELETE SET NULL;
ALTER TABLE checks ADD CONSTRAINT fk_checks_comment_by FOREIGN KEY (comment_by) REFERENCES users (id) ON DELETE SET NULL;
ALTER TABLE checks ADD CONSTRAINT fk_checks_last_finished_procedure_workflow_element_id FOREIGN KEY (last_finished_procedure_workflow_element_check_id) REFERENCES workflow_element_checks (id) ON DELETE SET NULL;
