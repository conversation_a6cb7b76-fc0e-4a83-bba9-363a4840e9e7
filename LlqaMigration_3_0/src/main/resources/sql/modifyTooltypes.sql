ALTER TABLE tooltypes RENAME TO tool_types;
ALTER TABLE tool_types ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE tool_types ADD CONSTRAINT uc_tool_types_workflow_element_id UNIQUE (workflow_element_id);
CREATE INDEX fki_tool_types_workflow_element_id ON tool_types (workflow_element_id);
ALTER TABLE tool_types ADD CONSTRAINT fk_tool_types_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;