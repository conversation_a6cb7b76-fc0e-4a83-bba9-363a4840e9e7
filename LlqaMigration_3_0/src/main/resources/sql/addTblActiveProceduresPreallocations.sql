CREATE SEQUENCE sq_active_procedures_preallocations;
CREATE TABLE active_procedures_preallocations
(
  id                  BIGINT DEFAULT nextval('sq_active_procedures_preallocations' :: REGCLASS) NOT NULL
                      CONSTRAINT pk_active_procedures_preallocations
                      PRIMARY KEY,
  active_procedure_id BIGINT NOT NULL,
  user_group_id       BIGINT,
  user_id             BIGINT,
  check_types_id      BIGINT NOT NULL,
  created_at          TIMESTAMP,
  updated_at          TIMESTAMP,
  created_by          VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING    NOT NULL,
  updated_by          VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING    NOT NULL
);
CREATE INDEX fki_active_procedures_preallocations_active_procedure_id ON active_procedures_preallocations (active_procedure_id);
CREATE INDEX fki_active_procedures_preallocations_user_group_id ON active_procedures_preallocations (user_group_id);
CREATE INDEX fki_active_procedures_preallocations_user_id ON active_procedures_preallocations (user_id);
CREATE INDEX fki_active_procedures_preallocations_checktypes_id ON active_procedures_preallocations (check_types_id);
ALTER TABLE active_procedures_preallocations ADD CONSTRAINT fk_active_procedures_preallocations_active_procedure_id FOREIGN KEY (active_procedure_id) REFERENCES activeprocedures (id) ON DELETE CASCADE;
ALTER TABLE active_procedures_preallocations ADD CONSTRAINT fk_active_procedures_preallocations_user_group_id FOREIGN KEY (user_group_id) REFERENCES usergroups (id) ON DELETE RESTRICT;
ALTER TABLE active_procedures_preallocations ADD CONSTRAINT fk_active_procedures_preallocations_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE RESTRICT;
ALTER TABLE active_procedures_preallocations ADD CONSTRAINT fk_active_procedures_preallocations_checktypes_id FOREIGN KEY (check_types_id) REFERENCES checktypes (id) ON DELETE RESTRICT;