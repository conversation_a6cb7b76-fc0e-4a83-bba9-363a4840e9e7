--
-- DROP MEASURES COLUMNS
--
ALTER TABLE measures DROP COLUMN title;
ALTER TABLE measures DROP COLUMN description;
ALTER TABLE measures DROP COLUMN metadata;
ALTER TABLE measures DROP COLUMN calculation;
ALTER TABLE measures DROP COLUMN flowcontrol;

--
-- DROP TOOL TYPES COLUMNS
--
ALTER TABLE tool_types DROP COLUMN title;
ALTER TABLE tool_types DROP COLUMN description;
ALTER TABLE tool_types DROP COLUMN metadata;

--
-- DROP MEASUREMENT ERROR CATEGORIES COLUMNS
--
ALTER TABLE measurement_error_categories DROP COLUMN old_name;

--
-- DROP SETTINGS COLUMNS
--
<PERSON><PERSON><PERSON> TABLE tags DROP COLUMN value;
ALTER TABLE tags DROP COLUMN sub_values;

--
-- DROP TAG VALUE COLUMNS
--
ALTER TABLE tag_values DROP COLUMN tmp_name;

--
-- DROP ACTIVE PROCEDURES COLUMNS
--
ALTER TABLE active_procedures DROP COLUMN flowcontrol;

--
-- DROP CHECK TYPES COLUMNS
--
<PERSON><PERSON><PERSON> TABLE check_types DROP COLUMN title;
ALTER TABLE check_types DROP COLUMN description;
ALTER TABLE check_types DROP COLUMN metadata;

--
-- DROP DEVICE TYPES COLUMNS
--
ALTER TABLE device_types DROP COLUMN title;
ALTER TABLE device_types DROP COLUMN description;
ALTER TABLE device_types DROP COLUMN metadata;

--
-- DROP MEASUREMENTS COLUMNS
--
ALTER TABLE measurements DROP COLUMN rawvalues;
ALTER TABLE measurements DROP COLUMN metadata;
ALTER TABLE measurements DROP COLUMN previous_measurements;
ALTER TABLE measurements DROP COLUMN check_id;
ALTER TABLE measurements DROP COLUMN saved_on_old;

--
-- DROP MODELS COLUMNS
--
ALTER TABLE models DROP COLUMN title;
ALTER TABLE models DROP COLUMN description;
ALTER TABLE models DROP COLUMN metadata;
ALTER TABLE models DROP COLUMN real_id;

--
-- DROP STEPS COLUMNS
--
ALTER TABLE steps DROP COLUMN title;
ALTER TABLE steps DROP COLUMN description;
ALTER TABLE steps DROP COLUMN metadata;
ALTER TABLE steps DROP COLUMN flowcontrol;

--
-- DROP UNITS COLUMNS
--
ALTER TABLE units DROP COLUMN metadata;

--
-- DROP PROCEDURES COLUMNS
--
ALTER TABLE procedures DROP COLUMN title;
ALTER TABLE procedures DROP COLUMN description;
ALTER TABLE procedures DROP COLUMN metadata;
ALTER TABLE procedures DROP COLUMN flowcontrol;
ALTER TABLE procedures DROP COLUMN tag_1;
ALTER TABLE procedures DROP COLUMN tag_2;
ALTER TABLE procedures DROP COLUMN tag_3;
ALTER TABLE procedures DROP COLUMN tag_4;
ALTER TABLE procedures DROP COLUMN tag_5;
ALTER TABLE procedures DROP COLUMN tag_6;
ALTER TABLE procedures DROP COLUMN real_id;
ALTER TABLE procedures DROP COLUMN processing_time_old;

--
-- DROP CHECKS COLUMNS
--
ALTER TABLE checks DROP COLUMN checkdata;
ALTER TABLE checks DROP COLUMN metadata;
ALTER TABLE checks DROP COLUMN assign_data;

--
-- DROP BINARY FILES COLUMNS
--
ALTER TABLE binaryfiles DROP COLUMN metadata;

--
-- DROP IMAGES COLUMNS
--
ALTER TABLE gallery_items DROP COLUMN preview_id;

--
-- DROP NOTICES COLUMNS
--
ALTER TABLE notices DROP COLUMN path;
ALTER TABLE notices DROP COLUMN timeline;

--
-- DROP USERS COLUMNS
--
ALTER TABLE users DROP COLUMN userinfo;
ALTER TABLE users DROP COLUMN dashboardinfo;
ALTER TABLE users DROP COLUMN metadata;

--
-- DROP CONFIG ENTRIES COLUMNS
--
ALTER TABLE config_entries DROP COLUMN blocked_by;
ALTER TABLE config_entries DROP COLUMN blocked;
ALTER TABLE config_entries DROP COLUMN col1;
ALTER TABLE config_entries DROP COLUMN col2;
ALTER TABLE config_entries DROP COLUMN col3;
ALTER TABLE config_entries DROP COLUMN col4;
ALTER TABLE config_entries DROP COLUMN col5;
ALTER TABLE config_entries DROP COLUMN col6;

--
-- DROP CONFIG TABLES COLUMNS
--
ALTER TABLE config_tables DROP COLUMN colheader_1;
ALTER TABLE config_tables DROP COLUMN colheader_2;
ALTER TABLE config_tables DROP COLUMN colheader_3;
ALTER TABLE config_tables DROP COLUMN colheader_4;
ALTER TABLE config_tables DROP COLUMN colheader_5;
ALTER TABLE config_tables DROP COLUMN colheader_6;

--
-- DROP DATA CHANGES COLUMNS
--
ALTER TABLE data_changes DROP COLUMN source_type;
ALTER TABLE data_changes DROP COLUMN source_id;
ALTER TABLE data_changes DROP COLUMN timestamp;
ALTER TABLE data_changes DROP COLUMN subobject;
ALTER TABLE data_changes DROP COLUMN tmp_tgt;

--
-- DROP CONFIG ENTRIE COL CONTENTS COLUMNS
--
ALTER TABLE config_entrie_col_contents DROP COLUMN IF EXISTS seqnum;

--
-- DROP TABLE SCHEMA MIGRATIONS
--
DROP TABLE schema_migrations;

--
-- DROP TABLE MIGRATION PROGRESS
--
-- TRUNCATE TABLE migrationprogess;
-- DROP TABLE migrationprogess;
-- DROP SEQUENCE sq_migrationprogess;