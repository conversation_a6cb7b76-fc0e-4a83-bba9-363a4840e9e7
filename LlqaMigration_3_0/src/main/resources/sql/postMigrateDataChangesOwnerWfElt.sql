CREATE OR REPLACE FUNCTION llqa3Mig__getModelDataChangeOwnerWorkflowElementId(dataChangeId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  workflowElementId BIGINT;
  ownerTypeFromJson VARCHAR(255);
  ownerIdFrom<PERSON>son BIGINT;
BEGIN
  -- 1) SEARCH THE OWNER BY ''owner_type'' AND ''owner_id''
  SELECT JSON_EXTRACT_PATH_TEXT(dc.new_data, ''owner_type'')
        ,CAST(JSON_EXTRACT_PATH_TEXT(dc.new_data, ''owner_id'') AS BIGINT)
  INTO ownerTypeFromJson
      ,ownerIdFromJson
  FROM data_changes dc
  WHERE dc.id = dataChangeId;

  IF ownerTypeFromJson = ''Model'' AND ownerIdFromJson IS NOT NULL THEN
    SELECT wfElt.id
    INTO workflowElementId
    FROM models m INNER JOIN workflow_elements wfElt ON m.workflow_element_id = wfElt.id
    WHERE m.id = ownerIdFromJson;
  END IF;

  IF workflowElementId IS NOT NULL THEN
    RETURN workflowElementId;
  END IF;

  -- 2) SEARCH BY OLD TGT (CODE)
  SELECT dc.workflow_element_id
  INTO workflowElementId
  FROM data_changes dc INNER JOIN workflow_elements wfElt ON dc.workflow_element_id = wfElt.id
                       INNER JOIN models md ON wfElt.id = md.workflow_element_id
  WHERE dc.id = dataChangeId
    AND dc.tmp_tgt IS NOT NULL
    AND dc.tmp_tgt = md.code;

  IF workflowElementId IS NOT NULL THEN
    RETURN workflowElementId;
  END IF;

  -- 3) IF SUB OBJECT TYPE IS OF >model< TAKE THIS AS OWNER
  SELECT dc.workflow_element_id
  INTO workflowElementId
  FROM data_changes dc INNER JOIN models md ON (dc.subobject_id = md.id)
  WHERE dc.id = dataChangeId
    AND dc.subobjtype = ''model'';

  RETURN workflowElementId;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqa3Mig__getProcedureDataChangeOwnerWorkflowElementId(dataChangeId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  dataChangeRow data_changes%ROWTYPE;
  procedureRow procedures%ROWTYPE;
  workflowElementId BIGINT;
  ownerTypeFromJson VARCHAR(255);
  ownerIdFromJson BIGINT;
BEGIN
  -- 1) SEARCH THE OWNER BY ''owner_type'' AND ''owner_type''
  SELECT JSON_EXTRACT_PATH_TEXT(dc.new_data, ''owner_type'')
        ,CAST(JSON_EXTRACT_PATH_TEXT(dc.new_data, ''owner_id'') AS BIGINT)
  INTO ownerTypeFromJson
      ,ownerIdFromJson
  FROM data_changes dc
  WHERE dc.id = dataChangeId;

  IF ownerTypeFromJson = ''Procedure'' AND ownerIdFromJson IS NOT NULL THEN
    SELECT wfElt.id
    INTO workflowElementId
    FROM procedures p INNER JOIN workflow_elements wfElt ON p.workflow_element_id = wfElt.id
    WHERE p.id = ownerIdFromJson;
  ELSEIF ownerTypeFromJson = ''Step'' AND ownerIdFromJson IS NOT NULL THEN
    SELECT wfElt.id
    INTO workflowElementId
    FROM steps s INNER JOIN workflow_elements wfElt ON s.workflow_element_id = wfElt.id
    WHERE s.id = ownerIdFromJson;
  END IF;

  IF workflowElementId IS NOT NULL THEN
    RETURN workflowElementId;
  END IF;

  -- 2) SEARCH BY OLD TGT (CODE)
  SELECT dc.*
  INTO dataChangeRow
  FROM data_changes dc
  WHERE dc.id = dataChangeId;

  IF dataChangeRow IS NULL THEN
    RETURN NULL;
  END IF;

  SELECT p.*
  INTO procedureRow
  FROM data_changes dc INNER JOIN workflow_elements wfElt ON dc.workflow_element_id = wfElt.id
                       INNER JOIN procedures p ON wfElt.id = p.workflow_element_id
  WHERE dc.id = dataChangeId;

  -- 2.1) SEARCH TGT CODE ON STEPS
  SELECT MIN(wfElt.id)
  INTO workflowElementId
  FROM steps s INNER JOIN workflow_elements wfElt ON s.workflow_element_id = wfElt.id
  WHERE s.procedure_id = procedureRow.id
    AND s.code = dataChangeRow.tmp_tgt;

  IF workflowElementId IS NOT NULL THEN
    RETURN workflowElementId;
  END IF;

  -- 2.1) SEARCH TGT CODE ON PROCEDURES
  IF procedureRow.code = dataChangeRow.tmp_tgt THEN
    RETURN procedureRow.workflow_element_id;
  END IF;

  -- 3) SEARCH ON SUB OBJECT
  IF dataChangeRow.subobjtype = ''procedure'' THEN
    -- 3.1) SEARCH ON SUB OBJECT ON PROCEDURES
    DECLARE
      numberFoundProcedures INTEGER;
    BEGIN
      SELECT COUNT(*)
      INTO numberFoundProcedures
      FROM procedures p
      WHERE p.workflow_element_id = dataChangeRow.workflow_element_id
        AND p.code = dataChangeRow.subobject;
      IF numberFoundProcedures = 1 THEN
        SELECT p.workflow_element_id
        INTO workflowElementId
        FROM procedures p
        WHERE p.workflow_element_id = dataChangeRow.workflow_element_id
          AND p.code = dataChangeRow.subobject;
        RETURN workflowElementId;
      END IF;
    END;
  ELSIF dataChangeRow.subobjtype = ''step'' THEN
    -- 3.2) SEARCH ON SUB OBJECT ON STEPS
    DECLARE
      numberFoundSteps INTEGER;
    BEGIN
      SELECT COUNT(*)
      INTO numberFoundSteps
      FROM steps s INNER JOIN procedures p ON (s.procedure_id = p.id)
      WHERE p.workflow_element_id = dataChangeRow.workflow_element_id
        AND s.code = dataChangeRow.subobject;
      IF numberFoundSteps = 1 THEN
        SELECT s.workflow_element_id
        INTO workflowElementId
        FROM steps s INNER JOIN procedures p ON (s.procedure_id = p.id)
        WHERE p.workflow_element_id = dataChangeRow.workflow_element_id
          AND s.code = dataChangeRow.subobject;
        RETURN workflowElementId;
      END IF;
    END;
  ELSIF dataChangeRow.subobjtype = ''measure'' THEN
    -- 3.3) SEARCH ON SUB OBJECT ON MEASURES
    DECLARE
      numberFoundMeasures INTEGER;
    BEGIN
      SELECT COUNT(*)
      INTO numberFoundMeasures
      FROM measures m INNER JOIN steps s ON (m.step_id = s.id)
                      INNER JOIN procedures p ON (s.procedure_id = p.id)
      WHERE p.workflow_element_id = dataChangeRow.workflow_element_id
        AND s.code = SPLIT_PART(dataChangeRow.subobject, ''.'', 1)
        AND m.code = SPLIT_PART(dataChangeRow.subobject, ''.'', 2);
      IF numberFoundMeasures = 1 THEN
        SELECT m.workflow_element_id
        INTO workflowElementId
        FROM measures m INNER JOIN steps s ON (m.step_id = s.id)
                        INNER JOIN procedures p ON (s.procedure_id = p.id)
        WHERE p.workflow_element_id = dataChangeRow.workflow_element_id
          AND s.code = SPLIT_PART(dataChangeRow.subobject, ''.'', 1)
          AND m.code = SPLIT_PART(dataChangeRow.subobject, ''.'', 2);
        RETURN workflowElementId;
      END IF;
    END;
  END IF;

  -- 4) IF SUB OBJECT TYPE IS OF >procedure< TAKE THIS AS OWNER
  SELECT dc.workflow_element_id
  INTO workflowElementId
  FROM data_changes dc INNER JOIN procedures p ON (dc.subobject_id = p.id)
  WHERE dc.id = dataChangeId
    AND dc.subobjtype = ''procedure'';

  RETURN workflowElementId;
END
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqa3Mig__getDataChangeOwnerWorkflowElementId(dataChangeId BIGINT)
  RETURNS BIGINT AS
'
DECLARE
  childTable CONSTANT VARCHAR(50) := (SELECT source_type FROM data_changes WHERE id = dataChangeId);
BEGIN
  CASE childTable
    WHEN ''Model'' THEN RETURN llqa3Mig__getModelDataChangeOwnerWorkflowElementId(dataChangeId);
    WHEN ''Procedure'' THEN RETURN llqa3Mig__getProcedureDataChangeOwnerWorkflowElementId(dataChangeId);
    ELSE RETURN NULL;
  END CASE;
END;
'
LANGUAGE plpgsql;

CREATE INDEX i_procedure_code_id ON procedures (code);
CREATE INDEX i_step_code_id ON steps (code);
CREATE INDEX i_measure_code_id ON measures (code);
CREATE INDEX i_models_code_id ON models (code);

UPDATE data_changes SET owner_workflow_element_id = llqa3Mig__getDataChangeOwnerWorkflowElementId(id)
                       ,updated_at = CURRENT_TIMESTAMP
                       ,updated_by = 'migration_3.0';

DROP INDEX i_procedure_code_id;
DROP INDEX i_step_code_id;
DROP INDEX i_measure_code_id;
DROP INDEX i_models_code_id;

DROP FUNCTION llqa3Mig__getDataChangeOwnerWorkflowElementId(BIGINT);
DROP FUNCTION llqa3Mig__getProcedureDataChangeOwnerWorkflowElementId(BIGINT);
DROP FUNCTION llqa3Mig__getModelDataChangeOwnerWorkflowElementId(BIGINT);