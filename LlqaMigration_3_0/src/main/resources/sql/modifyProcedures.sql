ALTER TABLE procedures ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE procedures ADD CONSTRAINT uc_procedures_workflow_element_id UNIQUE (workflow_element_id);
ALTER TABLE procedures RENAME COLUMN realid TO real_id;
ALTER TABLE procedures ADD COLUMN processing_time_old TEXT;
UPDATE procedures SET processing_time_old = processing_time;
UPDATE procedures SET processing_time = NULL;
UPDATE procedures SET disabled = FALSE WHERE disabled IS NULL;
ALTER TABLE procedures ALTER COLUMN disabled SET NOT NULL;
ALTER TABLE procedures ALTER COLUMN processing_time SET DATA TYPE BIGINT USING processing_time::BIGINT;
CREATE INDEX fki_procedures_workflow_element_id ON procedures (workflow_element_id);
ALTER TABLE procedures ADD CONSTRAINT fk_procedures_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;