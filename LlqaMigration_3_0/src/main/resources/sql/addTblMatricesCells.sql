CREATE SEQUENCE sq_matrices_cells;
CREATE TABLE matrices_cells
(
  id                     BIGINT DEFAULT nextval('sq_matrices_cells' :: REGCLASS) NOT NULL
                         CONSTRAINT pk_matrices_cells
                         PRIMARY KEY,
  matrices_row_header_id BIGINT NOT NULL,
  matrices_col_header_id BIGINT NOT NULL,
  cell_header            VARCHAR(255),
  cell_content           VARCHAR(255),
  created_at             TIMESTAMP,
  updated_at             TIMESTAMP,
  created_by             <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by             VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_matrices_cells_row_header_id ON matrices_cells (matrices_row_header_id);
ALTER TABLE matrices_cells ADD CONSTRAINT fk_matrices_cells_row_header_id FOREIGN KEY (matrices_row_header_id) REFERENCES matrices_row_headers (id) ON DELETE CASCADE;
CREATE INDEX fki_matrices_cells_col_header_id ON matrices_cells (matrices_col_header_id);
ALTER TABLE matrices_cells ADD CONSTRAINT fk_matrices_cells_col_header_id FOREIGN KEY (matrices_col_header_id) REFERENCES matrices_column_headers (id) ON DELETE CASCADE;
ALTER TABLE matrices_cells ADD CONSTRAINT uc_matrices_cells_row_col_header_id UNIQUE (matrices_row_header_id, matrices_col_header_id);