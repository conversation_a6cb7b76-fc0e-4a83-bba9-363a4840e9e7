UPDATE measurements
SET value = (CAST(REGEXP_REPLACE(value, '^([(].*[)]) ([[]SPAN:)(\d*)([]])$', '\3') AS BIGINT) / 3600) || ':' ||
            TRIM(TO_CHAR((CAST(REGEXP_REPLACE(value, '^([(].*[)]) ([[]SPAN:)(\d*)([]])$', '\3') AS BIGINT) % 3600) / 60, '00')) || ':' ||
            TRIM(TO_CHAR((CAST(REGEXP_REPLACE(value, '^([(].*[)]) ([[]SPAN:)(\d*)([]])$', '\3') AS BIGINT) % 60), '00'))
WHERE id IN (
              SELECT meas.id
              FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
              WHERE m.measure_type IN (13, 14, 15, 16)
                AND meas.value IS NOT NULL
                AND meas.value ~ '^([(].*[)]) ([[]SPAN:)(\d*)([]])$'
            );

UPDATE measurements
SET value = NULL
WHERE id IN (
              SELECT meas.id
              FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
              WHERE m.measure_type IN (13, 14, 15, 16)
                AND meas.value IS NOT NULL
                AND meas.value ~ '^[(][[]TIMS:(\d*)[]]'
            );