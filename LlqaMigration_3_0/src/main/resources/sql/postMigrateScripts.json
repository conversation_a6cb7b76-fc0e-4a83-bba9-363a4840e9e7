["postMigrate.sql", "postMigrateDataChangesOwnerWfElt.sql", "postMigrateDataChangesSubObject.sql", "postMigrateDataChangesTags.sql", "postMigrateDataChangesMeasureType.sql", "postMigrateDataChangesWorkflowRule.sql", "postMigrateDataChangesResetOldDataNewData.sql", "postMigrateModels.sql", "removeOldStructures.sql", "createCodeSequences.sql", "insertApplicationProperties.sql", "reorganizeDocumentTypes.sql"]