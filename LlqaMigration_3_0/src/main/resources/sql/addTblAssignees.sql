CREATE SEQUENCE sq_assignees;
CREATE TABLE assignees
(
  id              BIGINT DEFAULT nextval('sq_assignees' :: REGCLASS) NOT NULL
                  CONSTRAINT pk_assignees
                  PRIMARY KEY,
  assignee_id     BIGINT,
  old_index       SMALLINT NOT NULL,
  user_group_id   BIGINT,
  user_id         BIGINT,
  check_id        BIGINT NOT NULL,
  created_at      TIMESTAMP,
  updated_at      TIMESTAMP,
  created_by      VARCHA<PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING    NOT NULL,
  updated_by      VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING    NOT NULL
);
CREATE INDEX fki_assignees_assignee_id ON assignees (assignee_id);
CREATE INDEX fki_assignees_user_group_id ON assignees (user_group_id);
CREATE INDEX fki_assignees_user_id ON assignees (user_id);
CREATE INDEX fki_assignees_check_id ON assignees (check_id);
ALTER TABLE assignees ADD CONSTRAINT fk_assignees_assignee_id FOREIGN KEY (assignee_id) REFERENCES assignees (id) ON DELETE SET NULL;
ALTER TABLE assignees ADD CONSTRAINT fk_assignees_user_group_id FOREIGN KEY (user_group_id) REFERENCES usergroups (id) ON DELETE RESTRICT;
ALTER TABLE assignees ADD CONSTRAINT fk_assignees_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE RESTRICT;
ALTER TABLE assignees ADD CONSTRAINT uc_assignees_01 UNIQUE (user_group_id, user_id, check_id);