CREATE SEQUENCE sq_user_dashboards;
CREATE TABLE user_dashboards
(
  id          BIGINT DEFAULT nextval('sq_user_dashboards' :: REGCLASS) NOT NULL
              CONSTRAINT pk_user_dashboards
              PRIMARY KEY,
  user_id     BIGINT NOT NULL,
  seqnum      SMALLINT NOT NULL,
  type        <PERSON><PERSON><PERSON><PERSON>(32) NOT NULL,
  name        VA<PERSON><PERSON><PERSON>(128) NOT NULL,
  created_at  TIMESTAMP,
  updated_at  TIMESTAMP,
  created_by  VA<PERSON>HAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by  VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_user_dashboards_user_id ON user_dashboards (user_id);
ALTER TABLE user_dashboards ADD CONSTRAINT fk_user_dashboards_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
