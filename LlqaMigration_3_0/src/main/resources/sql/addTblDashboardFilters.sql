CREATE SEQUENCE sq_user_dashboard_filters;
CREATE TABLE user_dashboard_filters
(
  id                    BIGINT DEFAULT nextval('sq_user_dashboard_filters' :: REGCLASS) NOT NULL
                        CONSTRAINT pk_user_dashboard_filters
                        PRIMARY KEY,
  user_dashboard_id     BIGINT NOT NULL,
  self_assigned         B<PERSON><PERSON><PERSON><PERSON>,
  self_edited           <PERSON><PERSON><PERSON><PERSON><PERSON>,
  for_audit             BOOLEA<PERSON>,
  check_state           VARCHAR(32) ,
  unit_condition        VA<PERSON>HAR,
  edit_version_type     VA<PERSON><PERSON><PERSON>(32) ,
  created_at            TIMESTAMP,
  updated_at            TIMESTAMP,
  created_by            VA<PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by            VA<PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  CONSTRAINT uc_user_dashboard_filters_user_dashboard_type
    UNIQUE (user_dashboard_id)
);
CREATE INDEX fki_user_dashboard_filters_user_dashboard_id ON user_dashboard_filters (user_dashboard_id);
ALTER TABLE user_dashboard_filters ADD CONSTRAINT fk_user_dashboard_filters_user_dashboard_id FOREIGN KEY (user_dashboard_id) REFERENCES user_dashboards (id) ON DELETE CASCADE;
