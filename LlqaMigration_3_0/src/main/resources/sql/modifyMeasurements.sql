ALTER TABLE measurements ADD COLUMN comment_by BIGINT;
ALTER TABLE measurements ADD COLUMN skip BOOLEAN;
ALTER TABLE measurements ADD COLUMN workflow_element_check_id BIGINT;
ALTER TABLE measurements RENAME COLUMN savedon TO saved_on_old;
ALTER TABLE measurements ADD COLUMN saved_on timestamp;
UPDATE measurements SET saved_on = TO_TIMESTAMP(saved_on_old) AT TIME ZONE 'Europe/Zurich' AT TIME ZONE 'GMT';
ALTER TABLE measurements RENAME COLUMN savedby TO saved_by;
UPDATE measurements SET saved_by = NULL WHERE saved_by NOT IN (SELECT id FROM users);
UPDATE measurements SET skip = FALSE;
ALTER TABLE measurements ALTER COLUMN skip SET NOT NULL;
CREATE INDEX fki_measurements_comment_by ON measurements (comment_by);
CREATE INDEX fki_measurements_saved_by ON measurements (saved_by);
CREATE INDEX fki_measurements_workflow_element_check_id ON measurements (workflow_element_check_id);
ALTER TABLE measurements ADD CONSTRAINT fk_mesurements_comment_by FOREIGN KEY (comment_by) REFERENCES users (id) ON DELETE SET NULL;
ALTER TABLE measurements ADD CONSTRAINT fk_mesurements_saved_by FOREIGN KEY (saved_by) REFERENCES users (id) ON DELETE SET NULL;
ALTER TABLE measurements ADD CONSTRAINT fk_mesurements_workflow_element_check_id FOREIGN KEY (workflow_element_check_id) REFERENCES workflow_element_checks (id) ON DELETE RESTRICT;
ALTER TABLE measurements DROP CONSTRAINT IF EXISTS fk_measurements_measure_id;
ALTER TABLE measurements ADD CONSTRAINT fk_measurements_measure_id FOREIGN KEY (measure_id) REFERENCES measures (id) ON DELETE CASCADE;
ALTER TABLE measurements ADD COLUMN measurement_error_category_id BIGINT;
CREATE INDEX fki_measurement_measurement_error_category_id ON measurements (measurement_error_category_id);
ALTER TABLE  measurements ADD CONSTRAINT fk_measurement_measurement_error_category_id FOREIGN KEY (measurement_error_category_id) REFERENCES measurement_error_categories (id) ON DELETE SET NULL;
ALTER TABLE measurements ADD COLUMN last_measurement_measurement_id BIGINT;
CREATE INDEX fki_measurement_last_measurement_measurement_id ON measurements (last_measurement_measurement_id);
ALTER TABLE measurements ADD CONSTRAINT fk_measurement_last_measurement_measurement_id FOREIGN KEY (last_measurement_measurement_id) REFERENCES measurements (id) ON DELETE CASCADE;