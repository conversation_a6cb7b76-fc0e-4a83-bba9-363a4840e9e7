UPDATE data_changes SET subobjtype = 'measure_type'
                       ,subobject_id = (
                                          CASE (SELECT COUNT (*) FROM workflow_elements_measures_view m INNER JOIN measure_types mt ON (m.id = mt.measure_id) WHERE m.workflow_element_id = owner_workflow_element_id)
                                            WHEN 1 THEN (SELECT mt.id FROM workflow_elements_measures_view m INNER JOIN measure_types mt ON (m.id = mt.measure_id) WHERE m.workflow_element_id = owner_workflow_element_id)
                                            ELSE NULL
                                          END
                                       )
                       ,updated_at = CURRENT_TIMESTAMP
                       ,updated_by = 'migration_3.0'
WHERE field = 'calculation';