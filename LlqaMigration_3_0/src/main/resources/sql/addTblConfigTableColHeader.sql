CREATE SEQUENCE sq_config_table_col_headers;
CREATE TABLE config_table_col_headers
(
  id              BIGINT DEFAULT nextval('sq_config_table_col_headers' :: REGCLASS) NOT NULL
                  CONSTRAINT pk_config_table_col_headers
                  PRIMARY KEY,
  config_table_id BIGINT NOT NULL,
  col_header      VARCHAR(512) NOT NULL,
  seqnum          SMALLINT NOT NULL,
  created_at      TIMESTAMP,
  updated_at      TIMESTAMP,
  created_by      VARCHA<PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by      VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_config_table_col_headers_config_table_id ON config_table_col_headers (config_table_id);
ALTER TABLE config_table_col_headers ADD CONSTRAINT fk_config_table_col_headers_procedure_id FOREIGN KEY (config_table_id) REFERENCES configtables (id) ON DELETE CASCADE;
ALTER TABLE config_table_col_headers ADD CONSTRAINT uk_config_table_seqnum UNIQUE (config_table_id, seqnum);