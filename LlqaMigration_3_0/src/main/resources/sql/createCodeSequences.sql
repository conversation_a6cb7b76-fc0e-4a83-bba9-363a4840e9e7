--
-- MODELS
--
CREATE SEQUENCE sq_models_code;
SELECT setval('sq_models_code', COALESCE(MAX(CAST(code AS BIGINT)), 1))
FROM models
WHERE code ~ '^\d*$';
ALTER TABLE models ALTER COLUMN code SET DEFAULT NEXTVAL('sq_models_code')::varchar;

--
-- PROCEDURES
--
CREATE SEQUENCE sq_procedures_code;
SELECT setval('sq_procedures_code', COALESCE(MAX(CAST(code AS BIGINT)), 1))
FROM procedures
WHERE code ~ '^\d*$';
ALTER TABLE procedures ALTER COLUMN code SET DEFAULT NEXTVAL('sq_procedures_code')::varchar;

--
-- STEPS
--
CREATE SEQUENCE sq_steps_code;
SELECT setval('sq_steps_code', COALESCE(MAX(CAST(code AS BIGINT)), 1))
FROM steps
WHERE code ~ '^\d*$';
ALTER TABLE steps ALTER COLUMN code SET DEFAULT NEXTVAL('sq_steps_code')::varchar;

--
-- MEASURES
--
CREATE SEQUENCE sq_measures_code;
SELECT setval('sq_measures_code', COALESCE(MAX(CAST(code AS BIGINT)), 1))
FROM measures
WHERE code ~ '^\d*$';
ALTER TABLE measures ALTER COLUMN code SET DEFAULT NEXTVAL('sq_measures_code')::varchar;

--
-- DEVICE TYPES
--
CREATE SEQUENCE sq_device_types_code;
SELECT setval('sq_device_types_code', COALESCE(MAX(CAST(code AS BIGINT)), 1))
FROM device_types
WHERE code ~ '^\d*$';
ALTER TABLE device_types ALTER COLUMN code SET DEFAULT NEXTVAL('sq_device_types_code')::varchar;

--
-- CHECK TYPES
--
CREATE SEQUENCE sq_check_types_code;
SELECT setval('sq_check_types_code', COALESCE(MAX(CAST(code AS BIGINT)), 1))
FROM check_types
WHERE code ~ '^\d*$';
ALTER TABLE check_types ALTER COLUMN code SET DEFAULT NEXTVAL('sq_check_types_code')::varchar;

--
-- CHECK TYPES
--
CREATE SEQUENCE sq_tool_types_code;
SELECT setval('sq_tool_types_code', COALESCE(MAX(CAST(code AS BIGINT)), 1))
FROM tool_types
WHERE code ~ '^\d*$';
ALTER TABLE tool_types ALTER COLUMN code SET DEFAULT NEXTVAL('sq_tool_types_code')::varchar;