ALTER TABLE datachanges <PERSON><PERSON><PERSON><PERSON> TO data_changes;
ALTER TABLE data_changes ALTER COLUMN id TYPE BIGINT;
ALTER TABLE data_changes RENAME COLUMN "user" TO user_id;
ALTER TABLE data_changes ALTER COLUMN user_id TYPE BIGINT;
ALTER TABLE data_changes ADD COLUMN owner_workflow_element_id BIGINT;
ALTER TABLE data_changes ADD COLUMN subobject_id BIGINT;
ALTER TABLE data_changes ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE data_changes ADD COLUMN tmp_tgt VARCHAR(20);
ALTER TABLE data_changes ADD COLUMN old_data JSON;
ALTER TABLE data_changes ADD COLUMN new_data JSON;
ALTER TABLE data_changes ADD COLUMN field VARCHAR(255);
ALTER TABLE data_changes ADD COLUMN created_at TIMESTAMP;
ALTER TABLE data_changes ADD COLUMN updated_at TIMESTAMP;
ALTER TABLE data_changes ADD COLUMN created_by VARCHAR(255) DEFAULT 'system' :: CHAR<PERSON>TER VARYING NOT NULL;
ALTER TABLE data_changes ADD COLUMN updated_by VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL;
UPDATE data_changes SET updated_at = TO_TIMESTAMP(timestamp) AT TIME ZONE 'Europe/Zurich' AT TIME ZONE 'GMT'
                       ,created_at = TO_TIMESTAMP(timestamp) AT TIME ZONE 'Europe/Zurich' AT TIME ZONE 'GMT';
UPDATE data_changes SET subobjtype = 'gallery_item' WHERE subobjtype = 'image';
CREATE INDEX fki_data_changes_workflow_element_id ON data_changes (workflow_element_id);
CREATE INDEX fki_data_changes_owner_workflow_element_id ON data_changes (owner_workflow_element_id);
CREATE INDEX IF NOT EXISTS fki_datachanges_subobjtype_subobject_id ON data_changes (subobjtype, subobject_id);
ALTER TABLE data_changes ADD CONSTRAINT fk_data_changes_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE data_changes ADD CONSTRAINT fk_data_changes_owner_workflow_element_id FOREIGN KEY (owner_workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
UPDATE data_changes SET post_finalize = FALSE WHERE post_finalize IS NULL;
ALTER TABLE data_changes ALTER COLUMN post_finalize SET NOT NULL;
UPDATE data_changes SET dirty = FALSE WHERE dirty IS NULL;
ALTER TABLE data_changes ALTER COLUMN dirty SET NOT NULL;
ALTER TABLE data_changes DROP CONSTRAINT IF EXISTS fk_datachanges_user_id;
ALTER TABLE data_changes ADD CONSTRAINT fk_data_changes_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL;