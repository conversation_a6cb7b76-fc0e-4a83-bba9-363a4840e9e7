--
-- <PERSON><PERSON><PERSON><PERSON> PROCEDURES
--
INSERT INTO procedures
(
  id
 ,code
 ,title
 ,description
 ,metadata
 ,flowcontrol
 ,realid
 ,version
 ,disabled
 ,tag_1
 ,tag_2
 ,tag_3
 ,tag_4
 ,tag_5
 ,tag_6
 ,processing_time
 ,created_at
 ,updated_at
 ,created_by
 ,updated_by
)
VALUES
(
  53
 ,'INB_P08.1'
 ,'---
de: dummy
'
 ,NULL
 ,NULL
 ,NULL
 ,16
 ,1
 ,FALSE
 ,NULL
 ,NULL
 ,NULL
 ,NULL
 ,NULL
 ,NULL
 ,NULL
 ,current_timestamp
 ,current_timestamp
 ,'migration manual'
 ,'migration manual'
);

--
-- DELETE ZOMBIE MEASURES
--
DELETE
FROM measures m
WHERE m.step_id IN
      (
        SELECT s.id
        FROM steps s
        WHERE s.procedure_id NOT IN
              (
                SELECT id
                FROM procedures
              )
      );

--
-- DELETE ZOMBIE STEPS
--
DELETE
FROM steps s
WHERE s.procedure_id NOT IN
      (
        SELECT id
        FROM procedures
      );

--
-- DELETE ZOMBIE ACTIVE PROCEDURES
--
DELETE
FROM activeprocedures ap
WHERE ap.procedure_id NOT IN
      (
        SELECT id
        FROM procedures
      );

--
-- ADD MISSING STEPS ==> PROCEDURE FOREIGN KEY
--
ALTER TABLE steps ADD CONSTRAINT fk_steps_procedure_id FOREIGN KEY (procedure_id) REFERENCES procedures (id);

--
-- ADD ACTIVE PROCEDURES ==> PROCEDURE FOREIGN KEY
--
ALTER TABLE activeprocedures ADD CONSTRAINT fk_active_procedures_procedure_id FOREIGN KEY (procedure_id) REFERENCES procedures (id);

--
-- ADD GRANTS ==> PRIVILEGES FOREIGN KEY
--
ALTER TABLE grants ADD CONSTRAINT fk_grants_privilege_id FOREIGN KEY (privilege_id) REFERENCES privileges (id);