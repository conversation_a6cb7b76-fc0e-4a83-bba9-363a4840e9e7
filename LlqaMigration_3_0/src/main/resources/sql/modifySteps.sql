ALTER TABLE steps ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE steps RENAME COLUMN steptype to step_type;
ALTER TABLE steps ADD CONSTRAINT uc_steps_workflow_element_id UNIQUE (workflow_element_id);
ALTER TABLE steps ALTER COLUMN enforce TYPE SMALLINT;
ALTER TABLE steps ALTER COLUMN step_type TYPE SMALLINT;
CREATE INDEX fki_steps_workflow_element_id ON steps (workflow_element_id);
ALTER TABLE steps ADD CONSTRAINT fk_steps_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE steps ADD CONSTRAINT chk_seqnum CHECK (seqnum >= 0);
ALTER TABLE steps ALTER COLUMN seqnum SET DATA TYPE INTEGER USING seqnum::INTEGER;