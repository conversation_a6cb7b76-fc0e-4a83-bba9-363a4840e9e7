ALTER TABLE documents ADD COLUMN doctype_old BIGINT;
UPDATE documents SET doctype_old = doctype;
UPDATE documents SET doctype = NULL;
ALTER TABLE documents ALTER COLUMN doctype TYPE VARCHAR(128);
UPDATE documents SET doctype = 'pdf' WHERE doctype_old = 1;
UPDATE documents SET doctype = 'movie' WHERE doctype_old = 2;
UPDATE documents SET doctype = 'compressed' WHERE doctype_old = 3;
ALTER TABLE documents ALTER COLUMN doctype SET NOT NULL;
ALTER TABLE documents ADD CONSTRAINT doctype_exists CHECK (doctype in ('compressed','pdf'));
ALTER TABLE documents DROP COLUMN doctype_old;