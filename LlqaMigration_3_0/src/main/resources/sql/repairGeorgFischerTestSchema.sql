--
-- DELETE CONFIG ENTRIES WITH DUPLICATED CODES FROM THE SAME CONFIG TABLE
--
DELETE FROM configentries ce1
WHERE ce1.id NOT IN
      (
        SELECT MAX(ce2.id)
        FROM configentries ce2
        GROUP BY ce2.configtable_id, ce2.code_id
        HAVING COUNT (*) > 1
      )
      AND (ce1.configtable_id, ce1.code_id) IN
      (
        SELECT ce2.configtable_id
              ,ce2.code_id
        FROM configentries ce2
        GROUP BY ce2.configtable_id, ce2.code_id
        HAVING COUNT (*) > 1
      );