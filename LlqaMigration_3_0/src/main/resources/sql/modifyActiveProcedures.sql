ALTER TABLE activeprocedures RENAME TO active_procedures;
ALTER TABLE active_procedures ALTER COLUMN enforce TYPE SMALLINT;
ALTER TABLE active_procedures ALTER COLUMN model_id SET NOT NULL;
ALTER TABLE active_procedures ALTER COLUMN procedure_id SET NOT NULL;
ALTER TABLE active_procedures ADD CONSTRAINT uc_active_procedures_model_id_checktype_id UNIQUE (model_id, procedure_id);
ALTER TABLE active_procedures DROP CONSTRAINT IF EXISTS fk_activeprocedures_model_id, ADD CONSTRAINT fk_activeprocedures_model_id FOREIGN KEY (model_id) REFERENCES models(id) ON DELETE CASCADE;
ALTER TABLE active_procedures DROP CONSTRAINT IF EXISTS fk_activeprocedures_procedure_id, ADD CONSTRAINT fk_activeprocedures_procedure_id FOREIGN KEY (procedure_id) REFERENCES procedures(id) ON DELETE CASCADE;
ALTER TABLE active_procedures ALTER COLUMN seqnum SET DATA TYPE INTEGER USING seqnum::INTEGER;