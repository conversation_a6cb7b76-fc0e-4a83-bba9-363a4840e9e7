ALTER TABLE toolunits RENAME TO tool_units;
ALTER TABLE tool_units RENAME COLUMN tooltype_id TO tool_type_id;
ALTER TABLE tool_units RENAME COLUMN code TO serial;
ALTER TABLE tool_units ADD COLUMN comment_by BIGINT;
CREATE INDEX fki_tool_units_comment_by ON tool_units (comment_by);
ALTER TABLE tool_units ADD CONSTRAINT fk_tool_units_comment_by FOREIGN KEY (comment_by) REFERENCES users (id) ON DELETE SET NULL;