ALTER TABLE settings ADD COLUMN name JSON;
UPDATE settings SET name = json_build_object() WHERE name IS NULL;
ALTER TABLE settings ALTER COLUMN name SET NOT NULL;
ALTER TABLE settings ALTER COLUMN seqnum TYPE INTEGER;
ALTER TABLE settings ADD COLUMN deleted boolean default false;
ALTER TABLE settings RENAME TO tags;
ALTER SEQUENCE sq_settings RENAME TO sq_tags;
ALTER TABLE tags ALTER id SET DEFAULT NEXTVAL('sq_tags');
ALTER TABLE tags ALTER COLUMN key SET NOT NULL;
ALTER TABLE tags ALTER COLUMN seqnum SET DEFAULT 0;
UPDATE tags SET seqnum = 0 WHERE seqnum IS NULL;
ALTER TABLE tags ALTER COLUMN seqnum SET NOT NULL;
ALTER TABLE tags ADD CONSTRAINT chk_seqnum CHECK (seqnum >= 0);