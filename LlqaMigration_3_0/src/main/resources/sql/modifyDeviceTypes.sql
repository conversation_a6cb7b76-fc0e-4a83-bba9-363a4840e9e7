ALTER TABLE devicetypes RENAME TO device_types;
ALTER TABLE device_types ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE device_types ADD CONSTRAINT uc_devicetypes_workflow_element_id UNIQUE (workflow_element_id);
CREATE INDEX fki_devicetypes_workflow_element_id ON device_types (workflow_element_id);
ALTER TABLE device_types ADD CONSTRAINT fk_device_types_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;