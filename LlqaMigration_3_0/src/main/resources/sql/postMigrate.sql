SELECT SETVAL('sq_measurement_error_categories', COALESCE(MAX(id), 1))
FROM measurement_error_categories;

--
-- MODIFY CHECKS
--
<PERSON><PERSON><PERSON> checks SET status = 0
    WHERE status ISNULL;
UP<PERSON><PERSON> checks SET progress_finished = 0
    WHERE status = 0;
<PERSON><PERSON><PERSON> checks SET progress_error = 0
    WHERE status = 0;
ALTE<PERSON> TABLE checks ALTER COLUMN status SET NOT NULL;
ALTER TABLE checks ALTER COLUMN progress_finished SET NOT NULL;
ALTER TABLE checks ALTER COLUMN progress_error SET NOT NULL;

--
-- MODIFY CONFIG ENTRIES
--
ALTER TABLE config_entries ALTER COLUMN workflow_element_id SET NOT NULL;
ALTER TABLE config_entries RENAME COLUMN code_id TO seqnum;
ALTER TABLE config_entries ALTER COLUMN seqnum TYPE INTEGER;
UPDATE workflow_element_groups
SET deleted = TRUE
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE id IN (
               SELECT wfElt.workflow_element_group_id
               FROM config_entries ce INNER JOIN workflow_elements wfElt ON (ce.workflow_element_id = wfElt.id)
               WHERE ce.deleted = TRUE
            );
ALTER TABLE config_entries DROP COLUMN deleted;

--
-- MODIFY CONFIG TABLES ==> SET PARENT ID TO MODEL GROUP ID
--
DO
'
DECLARE
  rec RECORD;
  current_group_id BIGINT := -1;
BEGIN
  FOR rec IN SELECT grp.id AS grp_id
                   ,ct.id  AS table_id
             FROM config_tables ct INNER JOIN models md ON (ct.parent_id = md.id)
                                   INNER JOIN workflow_elements we ON (md.workflow_element_id = we.id)
                                   INNER JOIN workflow_element_groups grp ON (we.workflow_element_group_id = grp.id)
             WHERE ct.parent = ''model''
             ORDER BY grp.id
                     ,md.version DESC NULLS FIRST
  LOOP
      -- there should be only one config table per model
      IF current_group_id <> rec.grp_id THEN
      UPDATE config_tables SET parent_id = rec.grp_id WHERE config_tables.id = rec.table_id;
    ELSE
      UPDATE config_tables SET parent_id = NULL WHERE config_tables.id = rec.table_id;
    END IF;
    current_group_id := rec.grp_id;
  END LOOP;
END
';

--
-- FIX CORRUPT CONFIG TABLES WITH INVALID PARENT REFERENCE
--
UPDATE config_tables
SET parent_id = NULL, parent = NULL
WHERE (parent_id IS NULL AND parent IS NOT NULL)
   OR (parent_id IS NOT NULL AND (parent IS NULL OR parent = ''));

DO
'
DECLARE
  configEntryRow config_entries%ROWTYPE;
BEGIN
  FOR configEntryRow IN SELECT ce.*
                        FROM config_entries ce INNER JOIN config_tables ct ON (ce.configtable_id = ct.id)
                        WHERE ct.parent = ''check''
                          AND ct.parent_id IN (
                                                SELECT chk.id
                                                FROM checks chk
                                              )
                        ORDER BY ce.id
    LOOP
      DECLARE
        rec RECORD;
      BEGIN
         SELECT wfEltCeChk.workflow_element_group_id AS wf_elt_grp_id_chk
               ,wfEltCeChk.id                        AS wf_elt_id_chk
               ,wfEltCeMd.workflow_element_group_id  AS wf_elt_grp_id_md
         INTO rec
         FROM config_entries ceChk INNER JOIN config_tables           ctChk      ON (ceChk.configtable_id              = ctChk.id)
                                   INNER JOIN checks                  chk        ON (ctChk.parent_id                   = chk.id)
                                   INNER JOIN models                  md         ON (chk.model_id                      = md.id)
                                   INNER JOIN workflow_elements       mdWfElt    ON (md.workflow_element_id            = mdWfElt.id)
                                   INNER JOIN workflow_element_groups mdWfEltGrp ON (mdWfElt.workflow_element_group_id = mdWfEltGrp.id)
                                   INNER JOIN config_tables           ctMd       ON (mdWfEltGrp.id                     = ctMd.parent_id)
                                   INNER JOIN config_entries          ceMd       ON (ctMd.id                           = ceMd.configtable_id)
                                   INNER JOIN workflow_elements       wfEltCeChk ON (ceChk.workflow_element_id         = wfEltCeChk.id)
                                   INNER JOIN workflow_elements       wfEltCeMd  ON (ceMd.workflow_element_id          = wfEltCeMd.id)
         WHERE ceChk.id = configEntryRow.id
           AND ctMd.parent = ''model''
           AND ceChk.seqnum = ceMd.seqnum;
        IF rec.wf_elt_grp_id_chk IS NOT NULL THEN

          UPDATE workflow_rules
          SET workflow_element_group_id = rec.wf_elt_grp_id_md
          WHERE workflow_element_group_id = rec.wf_elt_grp_id_chk;

          UPDATE workflow_elements
          SET workflow_element_group_id = rec.wf_elt_grp_id_md
          WHERE id = rec.wf_elt_id_chk;

          DELETE FROM workflow_element_groups
          WHERE id = rec.wf_elt_grp_id_chk;
        END IF;
      END;
    END LOOP;
END
';

--
-- MODIFY CONFIG ENTRIES COLUMN CONTENTS
--
ALTER TABLE config_entrie_col_contents DROP COLUMN IF EXISTS seqnum;

--
-- MODIFY PROCEDURES
--
ALTER TABLE procedures ALTER COLUMN workflow_element_id SET NOT NULL;
UPDATE workflow_element_groups
SET deleted = TRUE
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE id IN (
               SELECT wfElt.workflow_element_group_id
               FROM procedures p INNER JOIN workflow_elements wfElt ON (p.workflow_element_id = wfElt.id)
               WHERE p.disabled = TRUE
            );
ALTER TABLE procedures DROP COLUMN disabled;

--
-- MODIFY STEPS
--
ALTER TABLE steps ALTER COLUMN workflow_element_id SET NOT NULL;
UPDATE steps s
SET step_type = '1'
WHERE (SELECT count(m.*)
       FROM measures m
       WHERE m.step_id = s.id
      ) = 0
  AND s.step_type = '0';

--
-- MODIFY MEASURES
--
ALTER TABLE measures ALTER COLUMN workflow_element_id SET NOT NULL;

--
-- MODIFY MEASUREMENTS
--
-- Osterwalder
-- ALTER TABLE measurements ALTER COLUMN workflow_element_check_id SET NOT NULL;
UPDATE measurements SET status = 0 WHERE status = 4;  -- INVALID ==> UNPROC
UPDATE measurements SET status = 0 WHERE status = 5;  -- ERROR ==> UNPROC

--
-- MODIFY MODELS
--
ALTER TABLE models ALTER COLUMN workflow_element_id SET NOT NULL;
UPDATE workflow_element_groups
SET deleted = TRUE
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE id IN (
               SELECT wfElt.workflow_element_group_id
               FROM models md INNER JOIN workflow_elements wfElt ON (md.workflow_element_id = wfElt.id)
               WHERE md.disabled = TRUE
            );
ALTER TABLE models DROP COLUMN disabled;

--
-- MODIFY UNITS
--
UPDATE units SET status = 10 WHERE status = 110;
UPDATE units SET status = 20 WHERE status = 120;
UPDATE units SET status = 10 WHERE status >= 100;

--
-- MODIFY DEVICE TYPES
--
ALTER TABLE device_types ALTER COLUMN workflow_element_id SET NOT NULL;
UPDATE workflow_element_groups
SET deleted = TRUE
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE id IN (
               SELECT wfElt.workflow_element_group_id
               FROM device_types dt INNER JOIN workflow_elements wfElt ON (dt.workflow_element_id = wfElt.id)
               WHERE dt.deleted = TRUE
            );
ALTER TABLE device_types DROP COLUMN deleted;

--
-- MODIFY CHECK TYPES
--
ALTER TABLE check_types ALTER COLUMN workflow_element_id SET NOT NULL;
UPDATE workflow_element_groups
SET deleted = TRUE
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE id IN (
               SELECT wfElt.workflow_element_group_id
               FROM check_types ct INNER JOIN workflow_elements wfElt ON (ct.workflow_element_id = wfElt.id)
               WHERE ct.deleted = TRUE
            );
ALTER TABLE check_types DROP COLUMN deleted;

--
-- MODIFY TOOL TYPES
--
ALTER TABLE tool_types ALTER COLUMN workflow_element_id SET NOT NULL;
UPDATE workflow_element_groups
SET deleted = TRUE
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE id IN (
               SELECT wfElt.workflow_element_group_id
               FROM tool_types tt INNER JOIN workflow_elements wfElt ON (tt.workflow_element_id = wfElt.id)
               WHERE tt.deleted = TRUE
            );
ALTER TABLE tool_types DROP COLUMN deleted;

--
-- MODIFY WORKFLOW RULES
--
ALTER TABLE workflow_rules ALTER COLUMN workflow_element_id SET NOT NULL;

--
-- MODIFY WORKFLOW ELEMENTS CHECK
--
ALTER TABLE workflow_element_checks ALTER COLUMN workflow_element_id SET NOT NULL;

--
-- MODIFY ASSIGNEES
--
ALTER TABLE assignees DROP COLUMN old_index;

--
-- MODIFY DATA CHANGES
--
UPDATE data_changes dc
SET workflow_element_id = (
                             SELECT p.workflow_element_id
                             FROM procedures p
                             WHERE p.id = dc.source_id
                          )
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE dc.source_type = 'Procedure';
UPDATE data_changes dc
SET workflow_element_id = (
                             SELECT m.workflow_element_id
                             FROM models m
                             WHERE m.id = dc.source_id
                          )
   ,updated_at = CURRENT_TIMESTAMP
   ,updated_by = 'migration_3.0'
WHERE dc.source_type = 'Model';
UPDATE data_changes
SET field = NULL
  ,updated_at = CURRENT_TIMESTAMP
  ,updated_by = 'migration_3.0'
WHERE field IN ('calculation', 'flowcontrol')
   OR field LIKE 'tag_%';
DELETE FROM data_changes WHERE workflow_element_id IS NULL;
ALTER TABLE data_changes ALTER COLUMN workflow_element_id SET NOT NULL;

--
-- MODIFY MEASUREMENT ERROR CATEGORIES
--
ALTER TABLE measurement_error_categories ALTER COLUMN seqnum SET NOT NULL;

-- Osterwalder
-- ALTER TABLE measurement_error_categories ALTER COLUMN name SET NOT NULL;

--
-- workflow element check states migrate all OMITTED SOFT (21) ==> OMITTED (20)
--
UPDATE workflow_element_checks SET status = 20 WHERE status = 21;

--
-- workflow element check states migrate all WARNINGS (13) ==> FAILED (15)
-- status WARNINGS won't exist anymore
--
UPDATE workflow_element_checks SET status = 15 WHERE status = 13;

--
-- REFACTORE PRIVILEGES
--
ALTER TABLE grants ADD COLUMN privilege VARCHAR(32);
UPDATE grants
SET privilege = subquery.code
FROM (
       SELECT id, code
       FROM privileges
     ) AS subquery
WHERE subquery.id = grants.privilege_id;
ALTER TABLE grants ALTER COLUMN privilege SET NOT NULL;
ALTER TABLE grants DROP CONSTRAINT fk_grants_privilege_id;
ALTER TABLE grants DROP COLUMN privilege_id;
DROP TABLE privileges;

ALTER TABLE grants ADD CONSTRAINT uc_grants_grantee_id_grantee_type_privilege
  UNIQUE (grantee_id, grantee_type, privilege);

--
-- WORKFLOW ELEMENT VIEWS
--,
ALTER TABLE affiliations ADD CONSTRAINT uc_affiliations_user_id_usergroup_id
UNIQUE (user_id, usergroup_id);

--
-- DELETE TEST MODELS
--
-- Osterwalder
-- DELETE FROM units
-- WHERE model_id IN (
--                     SELECT id
--                     FROM models
--                     WHERE code LIKE '%(TR)'
--                   );
--
-- DELETE FROM workflow_elements
-- WHERE id IN (
--               SELECT workflow_element_id
--               FROM models
--               WHERE code LIKE '%(TR)'
--             );
--
-- DELETE FROM models
-- WHERE code LIKE '%(TR)';

DELETE FROM workflow_element_groups
WHERE child_table = 'model'
  AND NOT EXISTS (
                  SELECT 1
                  FROM workflow_elements
                  WHERE workflow_element_groups.id = workflow_elements.workflow_element_group_id
                 );

--
-- update timestamp measurement values to format
-- drop temporary column >value type< from >measurement values<
--
UPDATE measurements
SET value = TO_CHAR(TO_TIMESTAMP(value, 'DD.MM.YYYY HH24:MI'), 'YYYY-MM-DD HH24:MI:SS')
WHERE id IN (
              SELECT meas.id
              FROM measurements meas INNER JOIN measures m ON (meas.measure_id = m.id)
              WHERE m.measure_type IN (13, 14, 15, 16)
                AND meas.value ~ '^(\d){2}.(\d){2}.(\d){4} (\d){2}:(\d){2}$'
                AND last_measurement_measurement_id IS NOT NULL
            );
UPDATE measurement_values
SET value = TO_CHAR(TO_TIMESTAMP(CAST(value AS BIGINT)), 'YYYY-MM-DD HH24:MI:SS')
WHERE id IN (
              SELECT mv.id
              FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
                              INNER JOIN measurement_values mv ON (meas.id = mv.measurement_id)
              WHERE m.measure_type IN (13, 14, 15, 16)
                AND mv.value IS NOT NULL
                AND mv.value ~ '^\d*$'
                AND mv.value_type != 'TIME_STAMP_REDUCE'
            );
ALTER TABLE measurement_values DROP COLUMN value_type;

--
-- update yes/no measurement values to format
--
UPDATE measurement_values
SET value = 'NO'
WHERE id IN (
              SELECT mv.id
              FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
                              INNER JOIN measurement_values mv ON (meas.id = mv.measurement_id)
              WHERE m.measure_type IN (5, 10)
                AND mv.value = '0'
            );
UPDATE measurement_values
SET value = 'YES'
WHERE id IN (
              SELECT mv.id
              FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
                              INNER JOIN measurement_values mv ON (meas.id = mv.measurement_id)
              WHERE m.measure_type IN (5, 10)
                AND mv.value = '1'
            );

--
-- migrate measurement for time recording starting to the LLQA 3.0 format
--
UPDATE measurements
SET value = (
                SELECT SUBSTRING(meas.value, 1, 10) ||
                       'T' ||
                       SUBSTRING(meas.value, 12, 10) ||
                       '.000Z'
                FROM measurements meas
                WHERE meas.id = measurements.id
            )
WHERE id IN (
                SELECT meas.id
                FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
                WHERE m.measure_type = 13
                  AND value ~ '^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
            );

UPDATE measurements
SET value = (
                SELECT SUBSTRING(meas.value, 7, 4) ||
                       '-' ||
                       SUBSTRING(meas.value, 4, 2) ||
                       '-' ||
                       SUBSTRING(meas.value, 1, 2) ||
                       'T' ||
                       SUBSTRING(meas.value, 12, 5) ||
                       ':00.000Z'
                FROM measurements meas
                WHERE meas.id = measurements.id
            )
WHERE id IN (
                SELECT meas.id
                FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
                WHERE m.measure_type = 13
                  AND value ~ '^\d{2}.\d{2}.\d{4} \d{2}:\d{2}$'
            );

UPDATE measurements
SET value = (
                SELECT TO_CHAR(meas.saved_on, 'YYYY-MM-DD') ||
                       'T' ||
                       TO_CHAR(meas.saved_on, 'HH24:MI:SS.MS') ||
                       'Z'
                FROM measurements meas
                WHERE meas.id = measurements.id
            )
WHERE id IN (
                SELECT meas.id
                FROM measures m INNER JOIN measurements meas ON (m.id = meas.measure_id)
                WHERE m.measure_type = 13
                  AND value ~ '^\d+:\d{2}:\d{2}$'
            );

--
-- delete active_procedure_preallocations which are not assigned to any user or group
--
DELETE FROM active_procedures_preallocations WHERE user_group_id IS NULL AND user_id IS NULL;

--
-- WORKFLOW ELEMENT VIEWS
--
CREATE OR REPLACE VIEW workflow_elements_procedures_view AS
  SELECT p.id,
         p.code,
         p.version,
         wfeltgrp.deleted,
         p.processing_time,
         p.created_at,
         p.updated_at,
         p.created_by,
         p.updated_by,
         wfelt.id AS workflow_element_id,
         wfelt.title,
         wfelt.description,
         wfeltgrp.id AS grp_id
  FROM ((workflow_elements wfelt
      JOIN procedures p ON ((wfelt.id = p.workflow_element_id)))
      JOIN workflow_element_groups wfeltgrp ON ((wfelt.workflow_element_group_id = wfeltgrp.id)))
  WHERE ((wfeltgrp.child_table)::text = 'procedure'::text);

CREATE OR REPLACE VIEW workflow_elements_steps_view AS
  SELECT s.id,
         s.procedure_id,
         s.code,
         s.seqnum,
         s.enforce,
         s.step_type,
         wfeltgrp.deleted,
         s.created_at,
         s.updated_at,
         s.created_by,
         s.updated_by,
         wfelt.id AS workflow_element_id,
         wfelt.title,
         wfelt.description,
         wfeltgrp.id AS grp_id,
         p.version
  FROM ((workflow_elements wfelt
      JOIN steps s ON ((wfelt.id = s.workflow_element_id)))
      JOIN workflow_element_groups wfeltgrp ON ((wfelt.workflow_element_group_id = wfeltgrp.id)))
      LEFT JOIN procedures p ON (s.procedure_id = p.id)
  WHERE ((wfeltgrp.child_table)::text = 'step'::text);

CREATE OR REPLACE VIEW workflow_elements_measures_view as
  SELECT m.id,
         m.step_id,
         s.procedure_id,
         m.tooltype_id,
         m.code,
         m.measure_type,
         m.seqnum,
         m.enforce,
         wfeltgrp.deleted,
         m.created_at,
         m.updated_at,
         m.created_by,
         m.updated_by,
         wfelt.id AS workflow_element_id,
         wfelt.title,
         wfelt.description,
         wfeltgrp.id AS grp_id,
         p.version
  FROM ((workflow_elements wfelt
      JOIN measures m ON ((wfelt.id = m.workflow_element_id)))
      JOIN workflow_element_groups wfeltgrp ON ((wfelt.workflow_element_group_id = wfeltgrp.id)))
         LEFT JOIN steps s ON (m.step_id = s.id)
         LEFT JOIN procedures p ON (s.procedure_id = p.id)
  WHERE ((wfeltgrp.child_table)::text = 'measure'::text);

CREATE OR REPLACE VIEW workflow_elements_models_view AS
  SELECT m.id,
         m.device_type_id,
         m.code,
         m.version,
         wfeltgrp.deleted,
         m.created_at,
         m.updated_at,
         m.created_by,
         m.updated_by,
         wfelt.id AS workflow_element_id,
         wfelt.title,
         wfelt.description,
         wfeltgrp.id AS grp_id
  FROM ((workflow_elements wfelt
      JOIN models m ON ((wfelt.id = m.workflow_element_id)))
      JOIN workflow_element_groups wfeltgrp ON ((wfelt.workflow_element_group_id = wfeltgrp.id)))
  WHERE ((wfeltgrp.child_table)::text = 'model'::text);

CREATE OR REPLACE VIEW workflow_elements_device_types_view AS
  SELECT dt.id,
         dt.code,
         wfeltgrp.deleted,
         dt.created_at,
         dt.updated_at,
         dt.created_by,
         dt.updated_by,
         wfelt.id AS workflow_element_id,
         wfelt.title,
         wfelt.description,
         wfeltgrp.id AS grp_id
  FROM ((workflow_elements wfelt
      JOIN device_types dt ON ((wfelt.id = dt.workflow_element_id)))
      JOIN workflow_element_groups wfeltgrp ON ((wfelt.workflow_element_group_id = wfeltgrp.id)))
  WHERE ((wfeltgrp.child_table)::text = 'device_type'::text);

CREATE OR REPLACE VIEW workflow_elements_check_types_view AS
  SELECT ct.id,
         ct.code,
         wfeltgrp.deleted,
         ct.seqnum,
         ct.created_at,
         ct.updated_at,
         ct.created_by,
         ct.updated_by,
         wfelt.id AS workflow_element_id,
         wfelt.title,
         wfelt.description,
         wfeltgrp.id AS grp_id
  FROM ((workflow_elements wfelt
      JOIN check_types ct ON ((wfelt.id = ct.workflow_element_id)))
      JOIN workflow_element_groups wfeltgrp ON ((wfelt.workflow_element_group_id = wfeltgrp.id)))
  WHERE ((wfeltgrp.child_table)::text = 'check_type'::text);

CREATE OR REPLACE VIEW workflow_elements_config_entries_view AS
  SELECT c.id,
         c.seqnum,
         c.configtable_id,
         c.active,
         wfeltgrp.deleted,
         c.timeline,
         c.created_at,
         c.updated_at,
         c.created_by,
         c.updated_by,
         wfelt.id AS workflow_element_id,
         wfelt.title,
         wfelt.description,
         wfeltgrp.id AS grp_id
  FROM ((workflow_elements wfelt
      JOIN config_entries c ON ((wfelt.id = c.workflow_element_id)))
      JOIN workflow_element_groups wfeltgrp ON ((wfelt.workflow_element_group_id = wfeltgrp.id)))
  WHERE ((wfeltgrp.child_table)::text = 'config_entry'::text);

--
-- handle step assignments ==> LLQA 3.0 is only allowed to assign items for procedures
--
UPDATE workflow_element_checks
SET assignee_id = (
                      SELECT stepItem1.assignee_id
                      FROM workflow_element_checks procedureItem1 INNER JOIN workflow_elements wfEltP1 ON (procedureItem1.workflow_element_id = wfEltP1.id)
                                                                  INNER JOIN procedures p1 ON (wfEltP1.id = p1.workflow_element_id)
                                                                  INNER JOIN
                           (
                               SELECT wfEltP2.id                 AS procedure_wf_elt_id
                                     ,stepItem2.check_id
                                     ,MAX(stepItem2.assignee_id) AS assignee_id
                               FROM workflow_element_checks stepItem2 INNER JOIN workflow_elements wfEltS ON (stepItem2.workflow_element_id = wfEltS.id)
                                                                      INNER JOIN steps s2 ON (wfEltS.id = s2.workflow_element_id)
                                                                      INNER JOIN procedures p2 ON (s2.procedure_id = p2.id)
                                                                      INNER JOIN workflow_elements wfEltP2 ON (p2.workflow_element_id = wfEltP2.id)
                               WHERE stepItem2.assignee_id IS NOT NULL
                               GROUP BY procedure_wf_elt_id
                                      ,stepItem2.check_id
                           ) stepItem1 ON (procedureItem1.check_id = stepItem1.check_id AND procedureItem1.workflow_element_id = stepItem1.procedure_wf_elt_id)
                      WHERE procedureItem1.id = workflow_element_checks.id
                  )
WHERE id IN (
                SELECT procedureItem1.id
                FROM workflow_element_checks procedureItem1 INNER JOIN workflow_elements wfEltP1 ON (procedureItem1.workflow_element_id = wfEltP1.id)
                                                            INNER JOIN procedures p1 ON (wfEltP1.id = p1.workflow_element_id)
                                                            INNER JOIN
                     (
                         SELECT wfEltP2.id                 AS procedure_wf_elt_id
                               ,stepItem2.check_id
                               ,MAX(stepItem2.assignee_id) AS assignee_id
                         FROM workflow_element_checks stepItem2 INNER JOIN workflow_elements wfEltS2 ON (stepItem2.workflow_element_id = wfEltS2.id)
                                                                INNER JOIN steps s2 ON (wfEltS2.id = s2.workflow_element_id)
                                                                INNER JOIN procedures p2 ON (s2.procedure_id = p2.id)
                                                                INNER JOIN workflow_elements wfEltP2 ON (p2.workflow_element_id = wfEltP2.id)
                         WHERE stepItem2.assignee_id IS NOT NULL
                         GROUP BY procedure_wf_elt_id
                                 ,stepItem2.check_id
                     ) stepItem1 ON (procedureItem1.check_id = stepItem1.check_id AND procedureItem1.workflow_element_id = stepItem1.procedure_wf_elt_id)
                WHERE procedureItem1.assignee_id IS NULL
                   OR procedureItem1.assignee_id != stepItem1.assignee_id
            );

UPDATE workflow_element_checks
SET assignee_id = NULL
WHERE id IN (
                 SELECT item.id
                 FROM workflow_element_checks item INNER JOIN workflow_elements wfElt ON (item.workflow_element_id = wfElt.id)
                                                   INNER JOIN workflow_element_groups wfEltGrp ON (wfElt.workflow_element_group_id = wfEltGrp.id)
                 WHERE wfEltGrp.child_table != 'procedure'
            )
  AND assignee_id IS NOT NULL;

--
-- MODIFY USERS: UPDATE PASSHASH TO THE DEFINED STANDARD
--
UPDATE USERS SET passhash = '$2a$10$VLKfwiPcf1ArW/EUrvalC.SiW0JFofhjGh.iqI0Tsgp0ZMhrkOZue';

--
-- MODIFY PRIVILEGES: SIMPLYFY
--
-- remove unique key since following updates may result in duplicates
-- those will be deleted after adding the constraint back
ALTER TABLE grants
    DROP CONSTRAINT uc_grants_grantee_id_grantee_type_privilege;

-- XXXALL
UPDATE grants g
SET privilege = 'ALL001'
    WHERE g.privilege LIKE 'EDTALL'
       OR g.privilege LIKE 'CRTALL';

UPDATE grants g
SET privilege = 'ALL002'
    WHERE g.privilege LIKE 'DELALL';

-- MODELS
UPDATE grants g
SET privilege = 'MOD001'
    WHERE g.privilege LIKE 'EDTMOD'
       OR g.privilege LIKE 'CRTMOD';
UPDATE grants g
SET privilege = 'MOD002'
    WHERE g.privilege LIKE 'DELMOD';
UPDATE grants g
SET privilege = 'MOD003'
    WHERE g.privilege LIKE 'FINALZ';
UPDATE grants g
SET privilege = 'MOD004'
    WHERE g.privilege LIKE 'MODVRS';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'MOD___')
      AND g.privilege LIKE 'MOD___';

-- UNITS
UPDATE grants g
SET privilege = 'UNT001'
    WHERE g.privilege LIKE 'EDTUNT'
       OR g.privilege LIKE 'CRTUNT';
UPDATE grants g
SET privilege = 'UNT002'
    WHERE g.privilege LIKE 'DELUNT';
UPDATE grants g
SET privilege = 'UNT003'
    WHERE g.privilege LIKE 'FINALZ';
UPDATE grants g
SET privilege = 'UNT004'
    WHERE g.privilege LIKE 'UNTVRS';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'UNT___')
      AND g.privilege LIKE 'UNT___';

-- CHECKS
UPDATE grants g
SET privilege = 'CHK001'
    WHERE g.privilege LIKE 'EDTCHK'
       OR g.privilege LIKE 'CRTCHK';
UPDATE grants g
SET privilege = 'CHK002'
    WHERE g.privilege LIKE 'DELCHK';
UPDATE grants g
SET privilege = 'CHK003'
    WHERE g.privilege LIKE 'FINALZ';
UPDATE grants g
SET privilege = 'CHK004'
    WHERE g.privilege LIKE 'CHKVRS';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'CHK___')
      AND g.privilege LIKE 'CHK___';

-- PROCEDURES STEPS MEASURES
UPDATE grants g
SET privilege = 'PSM001'
    WHERE g.privilege LIKE 'EDTPSM'
       OR g.privilege LIKE 'CRTPSM';
UPDATE grants g
SET privilege = 'PSM002'
    WHERE g.privilege LIKE 'DELPSM'
       OR g.privilege LIKE 'DELPRC';
UPDATE grants g
SET privilege = 'PSM003'
    WHERE g.privilege LIKE 'FINALZ';
UPDATE grants g
SET privilege = 'PSM004'
    WHERE g.privilege LIKE 'PSMVRS';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'PSM___')
      AND g.privilege LIKE 'PSM___';

-- TOOL TYPES
UPDATE grants g
SET privilege = 'TTY001'
    WHERE g.privilege LIKE 'EDTTTY'
       OR g.privilege LIKE 'CRTTTY';
UPDATE grants g
SET privilege = 'TTY002'
    WHERE g.privilege LIKE 'DELTTY';
UPDATE grants g
SET privilege = 'TTY003'
    WHERE g.privilege LIKE 'FINALZ';
UPDATE grants g
SET privilege = 'TTY004'
    WHERE g.privilege LIKE 'TTYVRS';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'TTY___')
      AND g.privilege LIKE 'TTY___';

-- TOOL UNITS
UPDATE grants g
SET privilege = 'TOL001'
    WHERE g.privilege LIKE 'EDTTOL'
       OR g.privilege LIKE 'CRTTOL';
UPDATE grants g
SET privilege = 'TOL002'
    WHERE g.privilege LIKE 'DELTOL';
UPDATE grants g
SET privilege = 'TOL003'
    WHERE g.privilege LIKE 'FINALZ';
UPDATE grants g
SET privilege = 'TOL004'
    WHERE g.privilege LIKE 'TOLVRS';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'TOL___')
      AND g.privilege LIKE 'TOL___';

-- CONFIG TABLES
UPDATE grants g
SET privilege = 'CFE002'
    WHERE g.privilege LIKE 'MNGCFE';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'CFE___')
      AND g.privilege LIKE 'CFE___';

-- DEVICE TYPES & CHECK TYPES
UPDATE grants g
SET privilege = 'PAR002'
    WHERE g.privilege LIKE 'MNGPAR';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'PAR___')
      AND g.privilege LIKE 'PAR___';

-- ERROR REPORTS MANAGEMENT
UPDATE grants g
SET privilege = 'ERR002'
    WHERE g.privilege LIKE 'MNGNTC';

-- a user or user group may now have a privilege MOD001 and MOD002 or something like that
-- only keep the highest since the privileges are now hierarchical
DELETE
    FROM grants g
    WHERE substring(g.privilege FROM 4 FOR 3)::INTEGER <> (SELECT max(substring(privilege FROM 4 FOR 3)::INTEGER)
                                                               FROM grants g2
                                                               WHERE g2.grantee_id = g.grantee_id
                                                                 AND g2.grantee_type = g.grantee_type
                                                                 AND g2.privilege LIKE 'ERR___')
      AND g.privilege LIKE 'ERR___';

-- DELETE MNGXXX (important that it comes after config tables and error report!
DELETE from grants g WHERE g.privilege LIKE 'MNG___';

-- DELETE unneeded
DELETE from grants g WHERE g.privilege LIKE 'GLSRCH';
DELETE from grants g WHERE g.privilege LIKE 'TRUSER';
DELETE from grants g WHERE g.privilege LIKE 'CHGCOD';

-- DELETE duplicates which now may occur
DELETE FROM grants WHERE grants.id NOT IN
                         (SELECT dupls.id FROM (
                                                   SELECT DISTINCT ON (grantee_id, grantee_type, privilege) *
                                                   FROM grants) as dupls);
-- add back unique key after removing duplicates
ALTER TABLE grants
    ADD CONSTRAINT uc_grants_grantee_id_grantee_type_privilege UNIQUE (grantee_id, grantee_type, privilege);



--
-- USER PRIVILEGES VIEW
--
CREATE OR REPLACE VIEW user_privileges_view AS
SELECT DISTINCT g.id,
                g.privilege,
                COALESCE(a.user_id, u.id) AS user_id
FROM grants g LEFT JOIN affiliations a ON ((g.grantee_id = a.user_id AND g.grantee_type = 'User') OR (g.grantee_id = a.usergroup_id AND g.grantee_type = 'Usergroup'))
              LEFT JOIN users u ON (g.grantee_id = u.id AND g.grantee_type = 'User')
              LEFT JOIN user_groups grp ON (g.grantee_id= grp.id AND g.grantee_type = 'Usergroup')
WHERE grp.id IS NULL
   OR grp.deleted = FALSE;

--
-- FUNCTIONS
--
CREATE OR REPLACE FUNCTION countFilterTagsByUserAndType(usrname character varying, tagtype character varying)
    RETURNS bigint
    IMMUTABLE
    LANGUAGE sql
AS
$$
SELECT count(*)
    FROM (SELECT distinct t.id
              FROM users u
                       JOIN user_tag_value_filters utvf ON u.id = utvf.user_id
                       JOIN tag_values tv ON tv.id = utvf.tag_value_id
                       JOIN tags t ON t.id = tv.tag_id
              WHERE t.key = tagtype
                AND u.username = usrname
         ) tagIds
$$;

CREATE OR REPLACE FUNCTION llqaUtil__generateLocalisedText(txt TEXT)
  RETURNS JSON AS
'
  SELECT to_json(''{''||string_agg(trans.lang, '','')||''}'') AS localisation
  FROM
  (
    SELECT ''"''||SPLIT_PART(json_array_elements_text(json_extract_path(prop_value, ''applicationLocales'')), ''-'', 1)||''","''||txt||''"'' AS lang
    FROM application_properties
    WHERE prop_key = ''LOCALES''
  ) trans;
'
LANGUAGE SQL;

CREATE OR REPLACE FUNCTION llqautil__generateDefaultLocalisedText(txt text)
    RETURNS JSON AS
$$
SELECT cast('{"'||SPLIT_PART(json_extract_path_text(prop_value, 'defaultLocale'), '-', 1)||'":"'||txt||'"}' as json)
    FROM application_properties
    WHERE prop_key = 'LOCALES'
$$
LANGUAGE sql;

CREATE OR REPLACE FUNCTION llqaDataChange__getSubObjectTitle(subobjType VARCHAR(255), subobjectId BIGINT)
  RETURNS JSON AS
'
DECLARE
  title JSON;
BEGIN
  CASE subobjType
    WHEN ''checktype'' THEN
      SELECT wfElt.title
      INTO title
      FROM workflow_elements wfElt INNER JOIN check_types ct ON (wfElt.id = ct.workflow_element_id)
      WHERE ct.id = subobjectId;
    WHEN ''document'' THEN
      SELECT llqaUtil__generateLocalisedText(d.caption)
      INTO title
      FROM documents d
      WHERE d.id = subobjectId;
    WHEN ''gallery_item'' THEN
      SELECT llqaUtil__generateLocalisedText(g.caption)
      INTO title
      FROM gallery_items g
      WHERE g.id = subobjectId;
    WHEN ''measure'' THEN
      SELECT wfElt.title
      INTO title
      FROM workflow_elements wfElt INNER JOIN measures m ON (wfElt.id = m.workflow_element_id)
      WHERE m.id = subobjectId;
    WHEN ''measure_type'' THEN
      SELECT wfElt.title
      INTO title
      FROM workflow_elements wfElt INNER JOIN measures m ON (wfElt.id = m.workflow_element_id)
                                   INNER JOIN measure_types mt ON (m.id = mt.measure_id)
      WHERE mt.id = subobjectId;
    WHEN ''model'' THEN
      SELECT wfElt.title
      INTO title
      FROM workflow_elements wfElt INNER JOIN models m ON (wfElt.id = m.workflow_element_id)
      WHERE m.id = subobjectId;
    WHEN ''procedure'' THEN
      SELECT wfElt.title
      INTO title
      FROM workflow_elements wfElt INNER JOIN procedures p ON (wfElt.id = p.workflow_element_id)
      WHERE p.id = subobjectId;
    WHEN ''step'' THEN
      SELECT wfElt.title
      INTO title
      FROM steps s INNER JOIN workflow_elements wfElt ON (s.workflow_element_id = wfElt.id)
      WHERE s.id = subobjectId;
    WHEN ''workflow_rule'' THEN
      title := NULL;
    WHEN ''matrix'' THEN
      DECLARE
        matrixRow matrices%ROWTYPE;
      BEGIN
        SELECT mx.*
        INTO matrixRow
        FROM matrices mx
        WHERE mx.id = subobjectId;
        IF matrixRow != NULL THEN
          IF matrixRow.x_top IS NOT NULL AND matrixRow.x_top <> '''' THEN
            title := llqaUtil__generateLocalisedText(matrixRow.x_top);
          ELSIF matrixRow.y_left IS NOT NULL AND matrixRow.y_left <> '''' THEN
            title := llqaUtil__generateLocalisedText(matrixRow.y_left);
          ELSE
            title := NULL;
          END IF;
        END IF;
      END;
    WHEN ''matrix_column_header'' THEN
      DECLARE
        colHead matrices_column_headers%ROWTYPE;
      BEGIN
        SELECT head.*
        INTO colHead
        FROM matrices_column_headers head
        WHERE head.id = subobjectId;
        IF colHead != NULL
        THEN
          IF colHead.value_top IS NOT NULL AND colHead.value_top <> '''' THEN
            title := llqaUtil__generateLocalisedText(colHead.value_top);
          ELSE
            title := NULL;
          END IF;
        END IF;
      END;
    WHEN ''matrix_row_header'' THEN
      DECLARE
        rowHead matrices_row_headers%ROWTYPE;
      BEGIN
        SELECT head.*
        INTO rowHead
        FROM matrices_row_headers head
        WHERE head.id = subobjectId;
        IF rowHead != NULL
        THEN
          IF rowHead.value_left IS NOT NULL AND rowHead.value_left <> '''' THEN
            title := llqaUtil__generateLocalisedText(rowHead.value_left);
          ELSE
            title := NULL;
          END IF;
        END IF;
      END;
    WHEN ''matrix_cell'' THEN
      DECLARE
        cell matrices_cells%ROWTYPE;
      BEGIN
        SELECT c.*
        INTO cell
        FROM matrices_cells c
        WHERE c.id = subobjectId;
        IF cell != NULL
        THEN
          IF cell.cell_header IS NOT NULL AND cell.cell_header <> '''' THEN
            title := llqaUtil__generateLocalisedText(cell.cell_header);
          ELSIF cell.cell_content IS NOT NULL AND cell.cell_content <> '''' THEN
              title := llqaUtil__generateLocalisedText(cell.cell_content);
          ELSE
            title := NULL;
          END IF;
        END IF;
      END;
    WHEN ''active_procedure'' THEN
      title := NULL;
    WHEN ''workflow_element_tag_value'' THEN
      SELECT tag.name
      INTO title
      FROM tags tag INNER JOIN workflow_element_tags wfEltTag ON (tag.id = wfEltTag.tag_id)
                    INNER JOIN workflow_element_tag_values wfEltTagVal ON (wfEltTag.id = wfEltTagVal.workflow_element_tag_id)
      WHERE wfEltTagVal.id = subobjectId;
    ELSE
      title := NULL;
  END CASE;
  RETURN title;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqaDataChange__getSubObjectCode(subobjType VARCHAR(255), subobjectId BIGINT)
  RETURNS VARCHAR(1024) AS
'
DECLARE
  code VARCHAR(1024);
BEGIN
  CASE subobjType
    WHEN ''checktype'' THEN
      SELECT ct.code
      INTO code
      FROM check_types ct
      WHERE ct.id = subobjectId;
    WHEN ''document'' THEN
      SELECT file.original_filename
      INTO code
      FROM documents d INNER JOIN binaryfiles file ON (d.binaryfile_id = file.id)
      WHERE d.id = subobjectId;
    WHEN ''gallery_item'' THEN
      SELECT CASE
                 WHEN fileFullImg.original_filename IS NOT NULL THEN fileFullImg.original_filename
                 ELSE fileThumbNail.original_filename
             END
      INTO code
      FROM gallery_items g LEFT JOIN binaryfiles fileFullImg ON (g.fullimage_id = fileFullImg.id)
                           LEFT JOIN binaryfiles fileThumbNail ON (g.thumbnail_id = fileThumbNail.id)
      WHERE g.id = subobjectId;
    WHEN ''measure'' THEN
      SELECT m.code
      INTO code
      FROM measures m INNER JOIN steps s ON (m.step_id = s.id)
      WHERE m.id = subobjectId;
    WHEN ''measure_type'' THEN
      SELECT m.code
      INTO code
      FROM measures m INNER JOIN steps s ON (m.step_id = s.id)
                      INNER JOIN measure_types mt ON (m.id = mt.measure_id)
      WHERE mt.id = subobjectId;
    WHEN ''model'' THEN
      SELECT m.code
      INTO code
      FROM models m
      WHERE m.id = subobjectId;
    WHEN ''procedure'' THEN
      SELECT p.code
      INTO code
      FROM procedures p
      WHERE p.id = subobjectId;
    WHEN ''step'' THEN
      SELECT s.code
      INTO code
      FROM steps s
      WHERE s.id = subobjectId;
    WHEN ''workflow_rule'' THEN
      code := NULL;
    WHEN ''matrix'' THEN
      code := NULL;
    WHEN ''matrix_column_header'' THEN
      code := NULL;
    WHEN ''matrix_row_header'' THEN
      code := NULL;
    WHEN ''matrix_cell'' THEN
      code := NULL;
    WHEN ''active_procedure'' THEN
      code := NULL;
    WHEN ''workflow_element_tag_value'' THEN
      code := NULL;
    ELSE
      code := NULL;
  END CASE;
  RETURN code;
END;
'
LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION llqaDataChange__getOwnerCode(workflowElementId BIGINT)
  RETURNS VARCHAR(1024) AS
'
DECLARE
  child_table CONSTANT workflow_element_groups.child_table%TYPE := (
                                                                     SELECT grp.child_table
                                                                     FROM workflow_elements wfElt INNER JOIN workflow_element_groups grp ON (wfElt.workflow_element_group_id = grp.id)
                                                                     WHERE wfElt.id = workflowElementId
                                                                   );
  code VARCHAR(1024);
BEGIN
  CASE child_table
    WHEN ''check_type'' THEN
      SELECT ct.code
      INTO code
      FROM workflow_elements wfElt INNER JOIN check_types ct ON (wfElt.id = ct.workflow_element_id)
      WHERE wfElt.id = workflowElementId;
    WHEN ''tool_type'' THEN
      SELECT tt.code
      INTO code
      FROM workflow_elements wfElt INNER JOIN tool_types tt ON (wfElt.id = tt.workflow_element_id)
      WHERE wfElt.id = workflowElementId;
    WHEN ''device_type'' THEN
      SELECT dt.code
      INTO code
      FROM workflow_elements wfElt INNER JOIN device_types dt ON (wfElt.id = dt.workflow_element_id)
      WHERE wfElt.id = workflowElementId;
    WHEN ''config_entry'' THEN
      SELECT CAST(ce.seqnum AS VARCHAR(50))
      INTO code
      FROM workflow_elements wfElt INNER JOIN config_entries ce ON (wfElt.id = ce.workflow_element_id)
      WHERE wfElt.id = workflowElementId;
    WHEN ''procedure'' THEN
      SELECT p.code
      INTO code
      FROM workflow_elements wfElt INNER JOIN procedures p ON (wfElt.id = p.workflow_element_id)
      WHERE wfElt.id = workflowElementId;
    WHEN ''step'' THEN
      SELECT s.code
      INTO code
      FROM workflow_elements wfElt INNER JOIN steps s ON (wfElt.id = s.workflow_element_id)
      WHERE wfElt.id = workflowElementId;
    WHEN ''measure'' THEN
      SELECT m.code
      INTO code
      FROM workflow_elements wfElt INNER JOIN measures m ON (wfElt.id = m.workflow_element_id)
                                   INNER JOIN steps s ON (m.step_id = s.id)
                                   INNER JOIN measure_types mt ON (m.id = mt.measure_id)
      WHERE wfElt.id = workflowElementId;
    WHEN ''model'' THEN
      SELECT m.code
      INTO code
      FROM workflow_elements wfElt INNER JOIN models m ON (wfElt.id = m.workflow_element_id)
      WHERE wfElt.id = workflowElementId;
    ELSE
      code := NULL;
  END CASE;
  RETURN code;
END;
'
LANGUAGE plpgsql;

-- THIS VIEW DEPENDS ON POSTGRESQL FUNCTIONS THEREFORE WE DEFINE IT IN THE POSTGRESQL SPECIFIC SCRIPT
CREATE OR REPLACE VIEW data_changes_view AS
  SELECT dc.id
        ,dc.changetype
        ,dc.subobjtype
        ,llqaDataChange__getSubObjectTitle(dc.subobjtype, dc.subobject_id) AS sub_object_title
        ,llqaDataChange__getSubObjectCode(dc.subobjtype, dc.subobject_id)  AS sub_object_code
        ,user_id
        ,usr.username                                                      AS user_name
        ,usr.realname                                                      AS user_real_name
        ,dc.post_finalize
        ,dc.dirty
        ,owner.title                                                       AS owner_title
        ,llqaDataChange__getOwnerCode(dc.owner_workflow_element_id)        AS owner_code
        ,ownerGrp.child_table                                              AS owner_type
        ,dc.workflow_element_id
        ,dc.old_data
        ,dc.new_data
        ,dc.field
        ,dc.created_at                                                     AS timestamp
  FROM data_changes dc LEFT JOIN users usr ON (dc.user_id = usr.id)
                       LEFT JOIN workflow_elements owner ON (dc.owner_workflow_element_id = owner.id)
                       LEFT JOIN workflow_element_groups ownerGrp ON (owner.workflow_element_group_id = ownerGrp.id);

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getLatestProcedure(groupId BIGINT)
  RETURNS SETOF workflow_elements_procedures_view
LANGUAGE SQL
AS
'
  SELECT p1.*
  FROM workflow_elements_procedures_view p1
  WHERE p1.grp_id = groupId
    AND (
              p1.version = (
              SELECT MAX(p2.version)
                  FROM workflow_elements_measures_view p2
                  WHERE p2.grp_id = groupId
          )
          OR
              p1.version IS NULL
      );
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getLatestStep(groupId BIGINT)
  RETURNS SETOF workflow_elements_steps_view
LANGUAGE SQL
AS
'
  SELECT s1.*
  FROM workflow_elements_steps_view s1
  WHERE s1.grp_id = groupId
    AND (
              s1.version = (
              SELECT MAX(s2.version)
                  FROM workflow_elements_measures_view s2
                  WHERE s2.grp_id = groupId
          )
          OR
              s1.version IS NULL
      );
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getLatestMeasure(groupId BIGINT)
  RETURNS SETOF workflow_elements_measures_view
LANGUAGE SQL
AS
'
  SELECT m1.*
  FROM workflow_elements_measures_view m1
  WHERE m1.grp_id = groupId
    AND (
              m1.version = (
              SELECT MAX(m2.version)
                  FROM workflow_elements_measures_view m2
                  WHERE m2.grp_id = groupId
          )
          OR
              m1.version IS NULL
      );
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getLatestFinalizedModel(groupId BIGINT)
  RETURNS SETOF workflow_elements_models_view
LANGUAGE SQL
AS
'
  SELECT md1.*
  FROM workflow_elements_models_view md1
  WHERE md1.grp_id = groupId
    AND md1.version = (
                        SELECT MAX(md2.version)
                        FROM workflow_elements_models_view md2
                        WHERE md2.grp_id = groupId
                          AND md2.version IS NOT NULL
                      );
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getLatestFinalizedCheckType(groupId BIGINT)
  RETURNS SETOF workflow_elements_check_types_view
LANGUAGE SQL
AS
'
  SELECT ct.*
  FROM workflow_elements_check_types_view ct
  WHERE ct.grp_id = groupId;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getLatestFinalizedDeviceType(groupId BIGINT)
  RETURNS SETOF workflow_elements_device_types_view
LANGUAGE SQL
AS
'
  SELECT dt.*
  FROM workflow_elements_device_types_view dt
  WHERE dt.grp_id = groupId;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getLatestFinalizedConfigEntry(groupId BIGINT)
  RETURNS SETOF workflow_elements_config_entries_view
LANGUAGE SQL
AS
'
  SELECT ce.*
  FROM workflow_elements_config_entries_view ce
  WHERE ce.grp_id = groupId;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getElementCodeFromWorkflowElementGroup(groupId BIGINT, child_table VARCHAR(255))
  RETURNS CHARACTER VARYING
LANGUAGE plpgsql
AS
'
DECLARE
  code VARCHAR(1024);
BEGIN
  CASE child_table
    WHEN ''procedure'' THEN
      code := (
                SELECT p1.code
                FROM procedures p1
                WHERE p1.id = (
                                SELECT MAX(p2.id)
                                FROM llqaWorkflowRule__getLatestProcedure(groupId) p2
                              )
              );
    WHEN ''step'' THEN
      code := (
                SELECT s1.code
                FROM steps s1
                WHERE s1.id = (
                                SELECT MAX(s2.id)
                                FROM llqaWorkflowRule__getLatestStep(groupId) s2
                              )
              );
    WHEN ''measure'' THEN
      code := (
                SELECT m1.code
                FROM measures m1
                WHERE m1.id = (
                                SELECT MAX(m2.id)
                                FROM llqaWorkflowRule__getLatestMeasure(groupId) m2
                              )
              );
    WHEN ''model'' THEN
      code := (
                SELECT md1.code
                FROM models md1
                WHERE md1.id = (
                                 SELECT MAX(md2.id)
                                 FROM llqaWorkflowRule__getLatestFinalizedModel(groupId) md2
                               )
              );
    WHEN ''check_type'' THEN
      code := (
                SELECT ct1.code
                FROM check_types ct1
                WHERE ct1.id = (
                                 SELECT MAX(ct2.id)
                                 FROM llqaWorkflowRule__getLatestFinalizedCheckType(groupId) ct2
                               )
              );
    WHEN ''device_type'' THEN
      code := (
                SELECT dt1.code
                FROM device_types dt1
                WHERE dt1.id = (
                                 SELECT MAX(dt2.id)
                                 FROM llqaWorkflowRule__getLatestFinalizedDeviceType(groupId) dt2
                               )
              );
    WHEN ''config_entry'' THEN
      code := (
                SELECT CAST(ce1.seqnum AS VARCHAR(50))
                FROM config_entries ce1
                WHERE ce1.id = (
                                 SELECT MAX(ce2.id)
                                 FROM llqaWorkflowRule__getLatestFinalizedConfigEntry(groupId) ce2
                               )
              );
  END CASE;
  RETURN code;
END;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getElementIdFromWorkflowElementGroup(groupId BIGINT, child_table VARCHAR(255))
  RETURNS BIGINT
LANGUAGE plpgsql
AS
'
DECLARE
  id BIGINT;
BEGIN
  CASE child_table
    WHEN ''procedure'' THEN
      id := (SELECT MAX(p.id) FROM llqaWorkflowRule__getLatestProcedure(groupId) p);
    WHEN ''step'' THEN
      id := (SELECT MAX(s.id) FROM llqaWorkflowRule__getLatestStep(groupId) s);
    WHEN ''measure'' THEN
      id := (SELECT MAX(m.id) FROM llqaWorkflowRule__getLatestMeasure(groupId) m);
    WHEN ''model'' THEN
      id := (SELECT MAX(md.id) FROM llqaWorkflowRule__getLatestFinalizedModel(groupId) md);
    WHEN ''check_type'' THEN
      id := (SELECT MAX(ct.id) FROM llqaWorkflowRule__getLatestFinalizedCheckType(groupId) ct);
    WHEN ''device_type'' THEN
      id := (SELECT MAX(dt.id) FROM llqaWorkflowRule__getLatestFinalizedDeviceType(groupId) dt);
    WHEN ''config_entry'' THEN
      id := (SELECT MAX(ce.id) FROM llqaWorkflowRule__getLatestFinalizedConfigEntry(groupId) ce);
  END CASE;
  RETURN id;
END;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getElementIdFromWorkflowElement(wfEltId BIGINT, child_table VARCHAR(255))
  RETURNS BIGINT
LANGUAGE plpgsql
AS
'
DECLARE
  id BIGINT;
BEGIN
  CASE child_table
    WHEN ''procedure'' THEN
      id := (
              SELECT MAX(p.id)
              FROM workflow_elements wfElt INNER JOIN procedures p ON wfElt.id = p.workflow_element_id
              WHERE wfElt.id = wfEltId
            );
    WHEN ''step'' THEN
      id := (
              SELECT MAX(s.id)
              FROM workflow_elements wfElt INNER JOIN steps s ON wfElt.id = s.workflow_element_id
              WHERE wfElt.id = wfEltId
            );
    WHEN ''measure'' THEN
      id := (
              SELECT MAX(m.id)
              FROM workflow_elements wfElt INNER JOIN measures m ON wfElt.id = m.workflow_element_id
              WHERE wfElt.id = wfEltId
            );
    WHEN ''model'' THEN
      id := (
              SELECT MAX(md.id)
              FROM workflow_elements wfElt INNER JOIN models md ON wfElt.id = md.workflow_element_id
              WHERE wfElt.id = wfEltId
            );
    WHEN ''check_type'' THEN
      id := (
              SELECT MAX(ct.id)
              FROM workflow_elements wfElt INNER JOIN check_types ct ON wfElt.id = ct.workflow_element_id
              WHERE wfElt.id = wfEltId
            );
    WHEN ''device_type'' THEN
      id := (
              SELECT MAX(dt.id)
              FROM workflow_elements wfElt INNER JOIN device_types dt ON wfElt.id = dt.workflow_element_id
              WHERE wfElt.id = wfEltId
            );
    WHEN ''config_entry'' THEN
      id := (
              SELECT MAX(ce.id)
              FROM workflow_elements wfElt INNER JOIN config_entries ce ON wfElt.id = ce.workflow_element_id
              WHERE wfElt.id = wfEltId
            );
  END CASE;
  RETURN id;
END;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getElementCodeFromWorkflowElement(wfEltId BIGINT, child_table VARCHAR(255))
  RETURNS CHARACTER VARYING
LANGUAGE plpgsql
AS
'
DECLARE
  code VARCHAR(255);
BEGIN
  CASE child_table
    WHEN ''procedure'' THEN
      code := (
                SELECT MAX(p.code)
                FROM workflow_elements wfElt INNER JOIN procedures p ON wfElt.id = p.workflow_element_id
                WHERE wfElt.id = wfEltId
              );
    WHEN ''step'' THEN
      code := (
                SELECT MAX(s.code)
                FROM workflow_elements wfElt INNER JOIN steps s ON wfElt.id = s.workflow_element_id
                WHERE wfElt.id = wfEltId
              );
    WHEN ''measure'' THEN
      code := (
                SELECT MAX(m.code)
                FROM workflow_elements wfElt INNER JOIN measures m ON wfElt.id = m.workflow_element_id
                WHERE wfElt.id = wfEltId
              );
    WHEN ''model'' THEN
      code := (
                SELECT MAX(md.code)
                FROM workflow_elements wfElt INNER JOIN models md ON wfElt.id = md.workflow_element_id
                WHERE wfElt.id = wfEltId
              );
    WHEN ''check_type'' THEN
      code := (
                SELECT MAX(ct.code)
                FROM workflow_elements wfElt INNER JOIN check_types ct ON wfElt.id = ct.workflow_element_id
                WHERE wfElt.id = wfEltId
              );
    WHEN ''device_type'' THEN
      code := (
                SELECT MAX(dt.code)
                FROM workflow_elements wfElt INNER JOIN device_types dt ON wfElt.id = dt.workflow_element_id
                WHERE wfElt.id = wfEltId
              );
    WHEN ''config_entry'' THEN
      code := (
                SELECT MAX(CAST(ce.seqnum AS VARCHAR(50)))
                FROM workflow_elements wfElt INNER JOIN config_entries ce ON wfElt.id = ce.workflow_element_id
                WHERE wfElt.id = wfEltId
              );
  END CASE;
  RETURN code;
END;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getWorkflowEltTitleFromWorkflowElementGroup(groupId BIGINT, child_table VARCHAR(255))
  RETURNS JSON
LANGUAGE plpgsql
AS
'
DECLARE
  title JSON;
BEGIN
  CASE child_table
    WHEN ''procedure'' THEN
      title := (
                 SELECT wfElt.title
                 FROM workflow_elements wfElt
                 WHERE wfElt.id = (
                                    SELECT MAX(p.workflow_element_id)
                                    FROM llqaWorkflowRule__getLatestProcedure(groupId) p
                                  )
               );
    WHEN ''step'' THEN
      title := (
                 SELECT wfElt.title
                 FROM workflow_elements wfElt
                 WHERE wfElt.id = (
                                    SELECT MAX(s.workflow_element_id)
                                    FROM llqaWorkflowRule__getLatestStep(groupId) s
                                  )
               );
    WHEN ''measure'' THEN
      title := (
                 SELECT wfElt.title
                 FROM workflow_elements wfElt
                 WHERE wfElt.id = (
                                    SELECT MAX(m.workflow_element_id)
                                    FROM llqaWorkflowRule__getLatestMeasure(groupId) m
                                  )
               );
    WHEN ''model'' THEN
      title := (
                 SELECT wfElt.title
                 FROM workflow_elements wfElt
                 WHERE wfElt.id = (
                                    SELECT MAX(m.workflow_element_id)
                                    FROM llqaWorkflowRule__getLatestFinalizedModel(groupId) m
                                  )
               );
    WHEN ''check_type'' THEN
      title := (
                 SELECT wfElt.title
                 FROM workflow_elements wfElt
                 WHERE wfElt.id = (
                                    SELECT MAX(ct.workflow_element_id)
                                    FROM llqaWorkflowRule__getLatestFinalizedCheckType(groupId) ct
                                  )
               );
    WHEN ''device_type'' THEN
      title := (
                 SELECT wfElt.title
                 FROM workflow_elements wfElt
                 WHERE wfElt.id = (
                                    SELECT MAX(dt.workflow_element_id)
                                    FROM llqaWorkflowRule__getLatestFinalizedDeviceType(groupId) dt
                                  )
               );
    WHEN ''config_entry'' THEN
      title := (
                 SELECT llqautil__generatedefaultlocalisedtext(ct.title)
                 FROM workflow_elements wfElt INNER JOIN config_entries ce on wfElt.id = ce.workflow_element_id
                                              INNER JOIN config_tables ct on ce.configtable_id = ct.id
                 WHERE wfElt.id = (
                                    SELECT MAX(ce.workflow_element_id)
                                    FROM llqaWorkflowRule__getLatestFinalizedConfigEntry(groupId) ce
                                  )
               );
  END CASE;
  return title;
END;
';

CREATE OR REPLACE FUNCTION llqaWorkflowRule__getElementVersionFromWorkflowElement(wfEltId BIGINT, child_table VARCHAR(255))
    RETURNS BIGINT
    LANGUAGE plpgsql
AS
'
    DECLARE
        version BIGINT;
    BEGIN
        CASE child_table
            WHEN ''procedure'' THEN
                version := (
                    SELECT MAX(p.version)
                        FROM workflow_elements wfElt INNER JOIN procedures p ON wfElt.id = p.workflow_element_id
                        WHERE wfElt.id = wfEltId
                );
            WHEN ''step'' THEN
                version := (
                    SELECT MAX(p.version)
                        FROM workflow_elements wfElt INNER JOIN steps s ON wfElt.id = s.workflow_element_id
                                                     INNER JOIN procedures p ON s.procedure_id = p.id
                        WHERE wfElt.id = wfEltId
                );
            WHEN ''measure'' THEN version := (
                SELECT MAX(p.version)
                FROM workflow_elements wfElt
                         INNER JOIN measures m ON wfElt.id = m.workflow_element_id
                         INNER JOIN steps s ON m.step_id = s.id
                         INNER JOIN procedures p ON s.procedure_id = p.id
                WHERE wfElt.id = wfEltId
            );
            WHEN ''model'' THEN
                version := (
                    SELECT MAX(md.version)
                        FROM workflow_elements wfElt INNER JOIN models md ON wfElt.id = md.workflow_element_id
                        WHERE wfElt.id = wfEltId
                );
            END CASE;
        RETURN version;
    END;
';

CREATE OR REPLACE VIEW workflow_rules_owner_view AS
  SELECT wfRule.id
        ,wfRule.workflow_element_id
        ,wfRule.workflow_element_group_id
        ,wfRule.inverse
        ,wfRule.trigger
        ,wfEltGrpSrc.child_table                                                                   AS owner_type
        ,llqaWorkflowRule__getElementIdFromWorkflowElement(wfEltSrc.id, wfEltGrpSrc.child_table)   AS owner_id
        ,llqaWorkflowRule__getElementCodeFromWorkflowElement(wfEltSrc.id, wfEltGrpSrc.child_table) AS owner_code
        ,wfEltSrc.title                                                                            AS owner_title
       ,llqaWorkflowRule__getElementVersionFromWorkflowElement(wfEltSrc.id, wfEltGrpSrc.child_table) AS owner_version
        ,wfRule.created_at
        ,wfRule.updated_at
        ,wfRule.created_by
        ,wfRule.updated_by
  FROM workflow_rules wfRule INNER JOIN workflow_elements wfEltSrc ON (wfRule.workflow_element_id = wfEltSrc.id)
                             INNER JOIN workflow_element_groups wfEltGrpSrc ON (wfEltSrc.workflow_element_group_id = wfEltGrpSrc.id);

CREATE OR REPLACE FUNCTION llqaCheck__getParent(item workflow_element_checks)
  RETURNS workflow_element_checks
  LANGUAGE plpgsql
AS
'
DECLARE
  child_table CONSTANT workflow_element_groups.child_table%TYPE := (
                                                                     SELECT grp.child_table
                                                                     FROM workflow_elements wfElt INNER JOIN workflow_element_groups grp ON (wfElt.workflow_element_group_id = grp.id)
                                                                     WHERE wfElt.id = item.workflow_element_id
                                                                   );
  parentItem workflow_element_checks%ROWTYPE;
BEGIN
  CASE child_table
    WHEN ''step'' THEN
      SELECT wfeltchk.*
      INTO parentItem
      FROM workflow_element_checks wfeltchk INNER JOIN workflow_elements wfelt ON (wfeltchk.workflow_element_id = wfelt.id)
                                            INNER JOIN procedures p ON (wfelt.id = p.workflow_element_id)
      WHERE wfeltchk.check_id = item.check_id
        AND p.id = (
                     SELECT sp.id
                     FROM workflow_element_checks wfeltchks INNER JOIN workflow_elements wfelts ON (wfeltchks.workflow_element_id = wfelts.id)
                                                            INNER JOIN steps ss ON (ss.workflow_element_id = wfelts.id)
                                                            INNER JOIN procedures sp ON (ss.procedure_id = sp.id)
                     WHERE wfeltchks.id = item.id
                  );
    WHEN ''measure'' THEN
      SELECT wfeltchk.*
      INTO parentItem
      FROM workflow_element_checks wfeltchk INNER JOIN workflow_elements wfelt ON (wfeltchk.workflow_element_id = wfelt.id)
                                            INNER JOIN steps s ON (wfelt.id = s.workflow_element_id)
      WHERE wfeltchk.check_id = item.check_id
        AND s.id = (
                     SELECT sm.id
                     FROM workflow_element_checks wfeltchkm INNER JOIN workflow_elements wfeltm ON (wfeltchkm.workflow_element_id = wfeltm.id)
                                                            INNER JOIN measures mm ON (mm.workflow_element_id = wfeltm.id)
                                                            INNER JOIN steps sm ON (mm.step_id = sm.id)
                     WHERE wfeltchkm.id = item.id
                  );
    ELSE
      parentItem := NULL;
  END CASE;
  RETURN parentItem;
END;
';

CREATE OR REPLACE FUNCTION llqaCheck__getWorkflowElementCheckState(itemId BIGINT)
  RETURNS workflow_element_checks.status%TYPE
  LANGUAGE plpgsql
AS
'
DECLARE
  item workflow_element_checks%ROWTYPE;
  parentItem workflow_element_checks%ROWTYPE;
  state_omitted_by_rule CONSTANT workflow_element_checks.status%TYPE := 20;
  state_omitted_by_user CONSTANT workflow_element_checks.status%TYPE := 30;
BEGIN
  SELECT *
  INTO item
  FROM workflow_element_checks wfEltChk
  WHERE wfEltChk.id = itemId;
  parentItem := item;
  WHILE parentItem.id IS NOT NULL LOOP
    IF parentItem.status IN (state_omitted_by_rule, state_omitted_by_user)
    THEN
      RETURN parentItem.status;
    END IF;
    parentItem := llqaCheck__getParent(parentItem);
  END LOOP;
  RETURN item.status;
END;
';

UPDATE workflow_element_checks SET status = 15 WHERE status = 13;

CREATE OR REPLACE VIEW workflow_rules_target_view AS
  SELECT wfRule.id
        ,wfRule.workflow_element_id
        ,wfRule.workflow_element_group_id
        ,wfRule.inverse
        ,wfRule.trigger
        ,wfEltGrpTarget.child_table                                                                                   AS target_type
        ,llqaWorkflowRule__getElementIdFromWorkflowElementGroup(wfEltGrpTarget.id, wfEltGrpTarget.child_table)        AS target_id
        ,llqaWorkflowRule__getElementCodeFromWorkflowElementGroup(wfEltGrpTarget.id, wfEltGrpTarget.child_table)      AS target_code
        ,llqaWorkflowRule__getWorkflowEltTitleFromWorkflowElementGroup(wfEltGrpTarget.id, wfEltGrpTarget.child_table) AS target_title
        ,wfRule.created_at
        ,wfRule.updated_at
        ,wfRule.created_by
        ,wfRule.updated_by
  FROM workflow_rules wfRule INNER JOIN workflow_element_groups wfEltGrpTarget ON (wfRule.workflow_element_group_id = wfEltGrpTarget.id);

CREATE OR REPLACE VIEW workflow_elements_checks_view AS
  SELECT full_checks.id,
         full_checks.check_id,
         full_checks.workflow_element_id,
         full_checks.assignee_id,
         full_checks.committed_by,
         full_checks.status,
         full_checks.locked,
         full_checks.remaining_time,
         full_checks.enforce,
         full_checks.procedure_seqnum,
         full_checks.step_seqnum,
         full_checks.measure_seqnum,
         full_checks.model_id,
         full_checks.unit_id,
         full_checks.testrun_workflow_id,
         full_checks.procedure_id,
         full_checks.step_id,
         full_checks.measure_id,
         full_checks.procedure_code,
         full_checks.step_code,
         full_checks.measure_code,
         full_checks.instruction,
         full_checks.created_at,
         full_checks.updated_at,
         full_checks.created_by,
         full_checks.updated_by
  FROM (
       SELECT wfeltchkp.id,
              wfeltchkp.check_id,
              wfeltchkp.workflow_element_id,
              wfeltchkp.assignee_id,
              wfeltchkp.committed_by,
              wfeltchkp.status,
              wfeltchkp.locked,
              wfeltchkp.remaining_time,
              wfeltchkp.enforce,
              wfeltchkp.procedure_seqnum,
              wfeltchkp.step_seqnum,
              wfeltchkp.measure_seqnum,
              chkp.model_id,
              chkp.unit_id,
              chkp.testrun_workflow_id,
              pp.id                            AS procedure_id,
              NULL::BIGINT                     AS step_id,
              NULL::BIGINT                     AS measure_id,
              pp.code                          AS procedure_code,
              NULL::VARCHAR(255)               AS step_code,
              NULL::VARCHAR(255)               AS measure_code,
              (
              SELECT STRING_AGG(DISTINCT s.step_type::TEXT,',') = '1'
              FROM steps s
              WHERE s.procedure_id = pp.id
              )                                AS instruction,
              wfeltchkp.created_at,
              wfeltchkp.updated_at,
              wfeltchkp.created_by,
              wfeltchkp.updated_by
       FROM workflow_element_checks wfeltchkp INNER JOIN checks chkp ON (wfeltchkp.check_id = chkp.id)
                                              INNER JOIN workflow_elements wfeltp ON (wfeltchkp.workflow_element_id = wfeltp.id)
                                              INNER JOIN procedures pp ON (wfeltchkp.workflow_element_id = pp.workflow_element_id)
       UNION ALL
       SELECT wfeltchks.id,
              wfeltchks.check_id,
              wfeltchks.workflow_element_id,
              wfeltchks.assignee_id,
              wfeltchks.committed_by,
              wfeltchks.status,
              wfeltchks.locked,
              wfeltchks.remaining_time,
              wfeltchks.enforce,
              wfeltchks.procedure_seqnum,
              wfeltchks.step_seqnum,
              wfeltchks.measure_seqnum,
              chks.model_id,
              chks.unit_id,
              chks.testrun_workflow_id,
              ps.id                            AS procedure_id,
              ss.id                            AS step_id,
              NULL::BIGINT                     AS measure_id,
              ps.code                          AS procedure_code,
              ss.code                          AS step_code,
              NULL::VARCHAR(255)               AS measure_code,
              CASE ss.step_type
                WHEN 1 THEN TRUE
                ELSE FALSE
                  END                              AS instruction,
              wfeltchks.created_at,
              wfeltchks.updated_at,
              wfeltchks.created_by,
              wfeltchks.updated_by
       FROM workflow_element_checks wfeltchks INNER JOIN checks chks ON (wfeltchks.check_id = chks.id)
                                              INNER JOIN workflow_elements wfelts ON (wfeltchks.workflow_element_id = wfelts.id)
                                              INNER JOIN steps ss ON (wfEltChks.workflow_element_id = ss.workflow_element_id)
                                              INNER JOIN procedures ps ON (ss.procedure_id = ps.id)
       UNION ALL
       SELECT wfeltchkm.id,
              wfeltchkm.check_id,
              wfeltchkm.workflow_element_id,
              wfeltchkm.assignee_id,
              wfeltchkm.committed_by,
              wfeltchkm.status,
              wfeltchkm.locked,
              wfeltchkm.remaining_time,
              wfeltchkm.enforce,
              wfeltchkm.procedure_seqnum,
              wfeltchkm.step_seqnum,
              wfeltchkm.measure_seqnum,
              chkm.model_id,
              chkm.unit_id,
              chkm.testrun_workflow_id,
              pm.id                            AS procedure_id,
              sm.id                            AS step_id,
              m.id                             AS measure_id,
              pm.code                          AS procedure_code,
              sm.code                          AS step_code,
              m.code                           AS measure_code,
              FALSE                            AS instruction,
              wfeltchkm.created_at,
              wfeltchkm.updated_at,
              wfeltchkm.created_by,
              wfeltchkm.updated_by
       FROM workflow_element_checks wfeltchkm INNER JOIN checks chkm ON (wfeltchkm.check_id = chkm.id)
                                              INNER JOIN workflow_elements wfeltm ON (wfeltchkm.workflow_element_id = wfeltm.id)
                                              INNER JOIN measures m ON (wfEltChkm.workflow_element_id = m.workflow_element_id)
                                              INNER JOIN steps sm ON (m.step_id = sm.id)
                                              INNER JOIN procedures pm ON (sm.procedure_id = pm.id)
       ) full_checks;

CREATE OR REPLACE FUNCTION f_llqa_max_version(BIGINT, BIGINT)
    RETURNS BIGINT
    IMMUTABLE
    LANGUAGE SQL
AS
'
    SELECT CASE WHEN $1 IS NULL THEN NULL
                WHEN $2 IS NULL THEN NULL
                WHEN $1 > $2    THEN $1
                ELSE                 $2
               END;
';

CREATE AGGREGATE llqa_max_version(BIGINT)
(
    SFUNC    = f_llqa_max_version,
    STYPE    = BIGINT,
    INITCOND = -999999
);

CREATE OR REPLACE FUNCTION llqaactiveprocedure__getpreallocationstate(act_proc_id bigint) RETURNS character varying
    LANGUAGE sql
AS
$$
SELECT appState.state
    FROM
        (SELECT 'NONE' as state, 1 as sort
             WHERE NOT EXISTS(
                     SELECT 1
                         FROM active_procedures_preallocations app
                         WHERE app.active_procedure_id = act_proc_id)
         UNION
         SELECT 'INVALID' as state, 2 as sort
             FROM active_procedures_preallocations app
                      LEFT OUTER JOIN user_groups ug ON ug.id = app.user_group_id
                      LEFT OUTER JOIN users u ON u.id = app.user_id
             WHERE app.active_procedure_id = act_proc_id
               AND (   (    app.user_id IS NOT NULL
                 AND app.user_group_id IS NOT NULL) -- BOTH GIVEN
                 OR (    app.user_id IS NULL
                     AND app.user_group_id IS NULL) -- NONE GIVEN
                 OR u.status = 0                    -- DEACTIVATED
                 OR ug.deleted = true)              -- DELETED
         UNION
         SELECT 'PARTIAL' as state, 3 as sort
             FROM active_check_types act
                      JOIN active_procedures ap ON act.model_id = ap.model_id
             WHERE ap.id = act_proc_id
               AND NOT EXISTS(
                     SELECT 1
                         FROM active_procedures_preallocations app
                         WHERE app.active_procedure_id = ap.id
                           AND app.check_types_id = act.checktype_id)
         UNION
         SELECT 'FULL' as state, 4 as sort
         ORDER BY sort LIMIT 1) appState
$$;


CREATE VIEW procedures_tree_view AS
SELECT p1.id,
       p1.code,
       p1.version,
       p1.processing_time,
       p1.workflow_element_id,
       p1.created_at,
       p1.updated_at,
       p1.created_by,
       p1.updated_by
FROM procedures p1 INNER JOIN workflow_elements wfElt1 ON (p1.workflow_element_id = wfElt1.id)
                   INNER JOIN workflow_element_groups wfEltGrp1 ON (wfElt1.workflow_element_group_id = wfEltGrp1.id)
                   INNER JOIN (
                                  SELECT wfEltGrp2.id                 AS group_id,
                                         llqa_max_version(p2.version) AS max_version
                                  FROM procedures p2 INNER JOIN workflow_elements wfElt2 ON (p2.workflow_element_id = wfElt2.id)
                                                     INNER JOIN workflow_element_groups wfEltGrp2 ON (wfElt2.workflow_element_group_id = wfEltGrp2.id)
                                  GROUP BY wfEltGrp2.id
                              ) latesVersion ON (COALESCE(p1.version, -999999) = COALESCE(latesVersion.max_version, -999999) AND wfEltGrp1.id = latesVersion.group_id);

CREATE VIEW steps_tree_view AS
SELECT s.id
      ,s.procedure_id
      ,s.code
      ,s.seqnum
      ,s.enforce
      ,s.step_type
      ,s.workflow_element_id
      ,s.created_at
      ,s.updated_at
      ,s.created_by
      ,s.updated_by
FROM steps s INNER JOIN procedures p1 ON (s.procedure_id = p1.id)
             INNER JOIN workflow_elements pWfElt1 ON (p1.workflow_element_id = pWfElt1.id)
             INNER JOIN workflow_element_groups pWfEltGrp1 ON (pWfElt1.workflow_element_group_id = pWfEltGrp1.id)
             INNER JOIN (
                            SELECT wfEltGrp2.id                 AS group_id,
                                   llqa_max_version(p2.version) AS max_version
                            FROM procedures p2 INNER JOIN workflow_elements wfElt2 ON (p2.workflow_element_id = wfElt2.id)
                                               INNER JOIN workflow_element_groups wfEltGrp2 ON (wfElt2.workflow_element_group_id = wfEltGrp2.id)
                            GROUP BY wfEltGrp2.id
                        ) latesVersion ON (COALESCE(p1.version, -999999) = COALESCE(latesVersion.max_version, -999999) AND pWfEltGrp1.id = latesVersion.group_id);

CREATE VIEW measures_tree_view AS
SELECT m.id
      ,m.step_id
      ,m.tooltype_id
      ,m.code
      ,m.measure_type
      ,m.seqnum
      ,m.enforce
      ,m.workflow_element_id
      ,m.created_at
      ,m.updated_at
      ,m.created_by
      ,m.updated_by
FROM measures m INNER JOIN steps s ON (m.step_id = s.id)
                INNER JOIN procedures p1 ON (s.procedure_id = p1.id)
                INNER JOIN workflow_elements pWfElt1 ON (p1.workflow_element_id = pWfElt1.id)
                INNER JOIN workflow_element_groups pWfEltGrp1 ON (pWfElt1.workflow_element_group_id = pWfEltGrp1.id)
                INNER JOIN (
                               SELECT wfEltGrp2.id                 AS group_id,
                                      llqa_max_version(p2.version) AS max_version
                               FROM procedures p2 INNER JOIN workflow_elements wfElt2 ON (p2.workflow_element_id = wfElt2.id)
                                                  INNER JOIN workflow_element_groups wfEltGrp2 ON (wfElt2.workflow_element_group_id = wfEltGrp2.id)
                               GROUP BY wfEltGrp2.id
                           ) latesVersion ON (COALESCE(p1.version, -999999) = COALESCE(latesVersion.max_version, -999999) AND pWfEltGrp1.id = latesVersion.group_id);

CREATE VIEW measure_types_tree_view AS
SELECT mt.id
      ,mt.measure_id
      ,mt.measure_type
      ,mt.optional
      ,mt.internal
      ,mt.number_decimals
      ,mt.target_value
      ,mt.target_unit
      ,mt.threshold_value
      ,mt.threshold_unit
      ,mt.min_value
      ,mt.max_value
      ,mt.unit
      ,mt.value
      ,mt.min_length
      ,mt.expected
      ,mt.comparator
      ,mt.grouping
      ,mt.list_content
      ,mt.reg_exp
      ,mt.created_at
      ,mt.updated_at
      ,mt.created_by
      ,mt.updated_by
FROM measure_types mt INNER JOIN measures m ON (mt.measure_id = m.id AND mt.measure_type = m.measure_type)
                      INNER JOIN steps s ON (m.step_id = s.id)
                      INNER JOIN procedures p1 ON (s.procedure_id = p1.id)
                      INNER JOIN workflow_elements pWfElt1 ON (p1.workflow_element_id = pWfElt1.id)
                      INNER JOIN workflow_element_groups pWfEltGrp1 ON (pWfElt1.workflow_element_group_id = pWfEltGrp1.id)
                      INNER JOIN (
                                     SELECT wfEltGrp2.id                 AS group_id,
                                            llqa_max_version(p2.version) AS max_version
                                     FROM procedures p2 INNER JOIN workflow_elements wfElt2 ON (p2.workflow_element_id = wfElt2.id)
                                                        INNER JOIN workflow_element_groups wfEltGrp2 ON (wfElt2.workflow_element_group_id = wfEltGrp2.id)
                                     GROUP BY wfEltGrp2.id
                                 ) latesVersion ON (COALESCE(p1.version, -999999) = COALESCE(latesVersion.max_version, -999999) AND pWfEltGrp1.id = latesVersion.group_id);

--
-- add last_finished_procedure_workflow_element_id to checks
--
DO
'
DECLARE
    checkRow checks%ROWTYPE;
    lastFinishedProcWec workflow_element_checks%ROWTYPE;
BEGIN
    FOR checkRow IN SELECT chk.* FROM checks chk ORDER BY chk.id
        LOOP
            DECLARE
                workflowElementCheckId BIGINT;
            BEGIN


                FOR lastFinishedProcWec IN
                    SELECT wec.* FROM workflow_element_checks wec
                                          INNER JOIN workflow_elements we ON wec.workflow_element_id = we.id
                                          INNER JOIN workflow_element_groups weg ON we.workflow_element_group_id = weg.id
                                 WHERE wec.check_id = checkRow.id AND
                                         weg.child_table = ''procedure''
                                 ORDER BY wec.procedure_seqnum
                    LOOP
                        IF (lastFinishedProcWec.status != 0) THEN
                            workflowElementCheckId := lastFinishedProcWec.id;
                        ELSE
                            EXIT;
                        END IF;
                    END LOOP;

                UPDATE checks
                SET last_finished_procedure_workflow_element_check_id = workflowElementCheckId
                    WHERE id = checkRow.id;
            END;
        END LOOP;
END
';

UPDATE checks SET status = 30 WHERE status = 33;
UPDATE checks SET status = 40 WHERE status = 43;
