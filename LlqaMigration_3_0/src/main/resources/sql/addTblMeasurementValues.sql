CREATE SEQUENCE sq_measurement_values;
CREATE TABLE measurement_values
(
  id             BIGINT DEFAULT nextval('sq_measurement_values' :: REGCLASS) NOT NULL
                 CONSTRAINT pk_measurement_values
                 PRIMARY KEY,
  measurement_id BIGINT NOT NULL,
  matrices_cell_id BIGINT,
  value          VARCHAR(128),
  value_type     VA<PERSON>HAR(128),
  created_at     TIMESTAMP,
  updated_at     TIMESTAMP,
  created_by     VA<PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by     VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_measurement_values_measurement_id ON measurement_values (measurement_id);
CREATE INDEX fki_measurement_values_matrices_cell_id ON measurement_values (matrices_cell_id);
ALTER TABLE  measurement_values ADD CONSTRAINT fk_measurement_values_measurement_id FOREIGN KEY (measurement_id) REFERENCES measurements (id) ON DELETE CASCADE;
ALTER TABLE  measurement_values ADD CONSTRAINT fk_measurement_values_matrices_cell_id FOREIGN KEY (matrices_cell_id) REFERENCES matrices_cells (id) ON DELETE SET NULL;