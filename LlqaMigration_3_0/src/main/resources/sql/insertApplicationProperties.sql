--
-- INSERT DEFAULT ENTRIES INTO APPLICATION_PROPERTIES
--
INSERT INTO application_properties (prop_key, prop_value, created_at, updated_at) VALUES ('LOCALES', json_build_object('applicationLocales', json_build_array('de-CH'), 'defaultLocale', 'de-CH', 'timeZone' , 'Europe/Paris'), current_timestamp , current_timestamp);
INSERT INTO application_properties (prop_key, prop_value, created_at, updated_at) VALUES ('OPTIONAL_FEATURES', json_build_array(json_build_object('name', 'configtables', 'enabled', true),json_build_object('name', 'measurementerrorreport', 'enabled', true), json_build_object('name', 'enforceworkflow', 'enabled', true)), current_timestamp , current_timestamp);
INSERT INTO application_properties (prop_key, prop_value, created_at, updated_at) VALUES ('CHECK',  json_build_object('skipSchedule', false, 'limitProcedureToRegister', 3, 'precisionNumberOfDecimals', 3), current_timestamp , current_timestamp);
INSERT INTO application_properties (prop_key, prop_value, created_at, updated_at) VALUES ('JWT_EXPIRATION', json_build_object('seconds', 3600), current_timestamp , current_timestamp);
INSERT INTO application_properties (prop_key, prop_value, created_at, updated_at) VALUES ('REPORT',            JSON_BUILD_OBJECT('headerFooterColor', '#006699', 'dateFormat', 'dd.MM.yyyy', 'timeFormat', 'dd.MM.yyyy HH:mm:ss'), CURRENT_TIMESTAMP , CURRENT_TIMESTAMP);
