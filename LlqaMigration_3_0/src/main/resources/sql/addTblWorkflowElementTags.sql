CREATE SEQUENCE sq_workflow_element_tags;
CREATE TABLE workflow_element_tags
(
  id                  BIGINT DEFAULT nextval('sq_workflow_element_tags' :: REGCLASS) NOT NULL
                      CONSTRAINT pk_workflow_element_tags
                      PRIMARY KEY,
  workflow_element_id BIGINT NOT NULL,
  tag_id              BIGINT NOT NULL,
  created_at          TIMESTAMP,
  updated_at          TIMESTAMP,
  created_by          VA<PERSON>HAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by          VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_workflow_element_tags_workflow_element_id ON workflow_element_tags (workflow_element_id);
CREATE INDEX fki_workflow_element_tags_workflow_element_tag_id ON workflow_element_tags (tag_id);
ALTER TABLE workflow_element_tags ADD CONSTRAINT fk_workflow_element_tags_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE workflow_element_tags ADD CONSTRAINT fk_workflow_element_tags_tag_id FOREIGN KEY (tag_id) REFERENCES settings (id) ON DELETE CASCADE;
ALTER TABLE workflow_element_tags ADD CONSTRAINT uk_workflow_element_tags_workflow_element_id_tag_id UNIQUE (workflow_element_id, tag_id);