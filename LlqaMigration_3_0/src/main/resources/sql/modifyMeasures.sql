ALTER TABLE measures RENAME COLUMN measuretype TO measure_type;
ALTER TABLE measures ADD COLUMN workflow_element_id BIGINT;
ALTER TABLE measures ADD CONSTRAINT uc_measures_workflow_element_id UNIQUE (workflow_element_id);
ALTER TABLE measures ALTER COLUMN enforce TYPE SMALLINT;
CREATE INDEX fki_measures_workflow_element_id ON measures (workflow_element_id);
ALTER TABLE measures ADD CONSTRAINT fk_measures_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE measures ADD CONSTRAINT chk_seqnum CHECK (seqnum >= 0);
ALTER TABLE measures ALTER COLUMN seqnum SET DATA TYPE INTEGER USING seqnum::INTEGER;