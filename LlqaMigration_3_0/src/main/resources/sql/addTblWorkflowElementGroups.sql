CREATE SEQUENCE sq_workflow_element_groups;
CREATE TABLE workflow_element_groups
(
  id          BIGINT DEFAULT nextval('sq_workflow_element_groups' :: REGCLASS) NOT NULL
              CONSTRAINT pk_workflow_element_groups
              PRIMARY KEY,
  child_table VARCHAR(50) NOT NULL,
  deleted     B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT FALSE,
  created_at  TIMESTAMP,
  updated_at  TIMESTAMP,
  created_by  VA<PERSON>HA<PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by  VA<PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);