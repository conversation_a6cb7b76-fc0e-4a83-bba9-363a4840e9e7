CREATE SEQUENCE sq_notice_timelines;
CREATE TABLE notice_timelines
(
  id          BIGINT DEFAULT nextval('sq_notice_timelines' :: REGCLASS) NOT NULL
              CONSTRAINT pk_notice_timelines
              PRIMARY KEY,
  notice_id   BIGINT NOT NULL,
  user_id     BIGINT,
  status      BIGINT,
  timestamp   TIMESTAMP,
  comment     VARCHAR(255),
  created_at  TIMESTAMP,
  updated_at  TIMESTAMP,
  created_by  VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by  VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_notice_timelines_notice_id ON notice_timelines (notice_id);
CREATE INDEX fki_notice_timelines_user_id ON notice_timelines (user_id);
ALTER TABLE notice_timelines ADD CONSTRAINT fk_notice_timelines_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE notice_timelines ADD CONSTRAINT fk_notice_timelines_notice_id FOREIGN KEY (notice_id) REFERENCES notices (id) ON DELETE CASCADE;