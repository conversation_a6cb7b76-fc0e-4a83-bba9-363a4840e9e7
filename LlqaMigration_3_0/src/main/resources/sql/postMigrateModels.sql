-- `deleted` is not set correctly
UPDATE workflow_element_groups
set deleted = true
where child_table = 'model';

-- simply set all workflow element groups with no model edit version to deleted = true
UPDATE workflow_element_groups
SET deleted = false
where id in
      (SELECT DISTINCT weg.id
       FROM models m
                inner join workflow_elements we on m.workflow_element_id = we.id
                inner join workflow_element_groups weg on we.workflow_element_group_id = weg.id
       where m.version IS NULL)
