CREATE SEQUENCE sq_user_tag_value_filters;
CREATE TABLE IF NOT EXISTS user_tag_value_filters
(
  id BIGINT DEFAULT NEXTVAL('sq_user_tag_value_filters'::REGCLASS) NOT NULL
    CONSTRAINT pk_user_tag_value_filters PRIMARY KEY,
  user_id BIGINT NOT NULL
    CONSTRAINT fk_user_tag_value_filter_user_id
      REFERENCES users,
  tag_value_id BIGINT NOT NULL
    CONSTRAINT fk_user_tag_value_filter_tag_value_id
      REFERENCES tag_values,
  updated_at TIMESTAMP,
  created_at TIMESTAMP,
  created_by <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system'::CHARACTER VARYING NOT NULL,
  updated_by <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system'::CHARACTER VARYING NOT NULL,
  CONSTRAINT uk_user_tag_value_filter_user_id_tag_value_id
    UNIQUE (user_id, tag_value_id)
);

CREATE INDEX IF NOT EXISTS fki_user_tag_value_filters_user_id
  ON user_tag_value_filters (user_id);

CREATE INDEX IF NOT EXISTS fki_user_tag_value_filters_tag_value_id
  ON user_tag_value_filters (tag_value_id);