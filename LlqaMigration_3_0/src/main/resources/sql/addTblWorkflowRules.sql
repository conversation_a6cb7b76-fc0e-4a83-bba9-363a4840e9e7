CREATE SEQUENCE sq_workflow_rules;
CREATE TABLE workflow_rules
(
  id                        BIGINT DEFAULT nextval('sq_workflow_rules' :: REGCLASS) NOT NULL
                            CONSTRAINT pk_workflow_rules
                            PRIMARY KEY,
  workflow_element_id       BIGINT       NOT NULL,
  workflow_element_group_id BIGINT       NOT NULL,
  inverse                   BOOLEAN      NOT NULL,
  trigger                   VARCHAR(10)  NOT NULL,
  created_at                TIMESTAMP,
  updated_at                TIMESTAMP,
  created_by                VA<PERSON>HAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by                VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_workflow_rules_workflow_element_id ON workflow_rules (workflow_element_id);
CREATE INDEX fki_workflow_rules_workflow_element_group_id ON workflow_rules (workflow_element_group_id);
ALTER TABLE  workflow_rules ADD CONSTRAINT fk_workflow_rules_workflow_element_id FOREIGN KEY (workflow_element_id) REFERENCES workflow_elements (id) ON DELETE CASCADE;
ALTER TABLE  workflow_rules ADD CONSTRAINT fk_workflow_rules_workflow_element_group_id FOREIGN KEY (workflow_element_group_id) REFERENCES workflow_element_groups (id) ON DELETE RESTRICT;
