CREATE SEQUENCE sq_user_dashboard_filter_models;
CREATE TABLE user_dashboard_filter_models
(
  id                       BIGINT DEFAULT nextval('sq_user_dashboard_filter_models' :: REGCLASS) NOT NULL
                           CONSTRAINT pk_user_dashboard_filter_models
                           PRIMARY KEY,
  user_dashboard_filter_id BIGINT NOT NULL,
  model_id                 BIGINT NOT NULL,
  created_at               TIMESTAMP,
  updated_at               TIMESTAMP,
  created_by               VA<PERSON>HAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by               VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  CONSTRAINT uc_user_dashboard_filter_models_model_id
    UNIQUE (user_dashboard_filter_id, model_id)
);
CREATE INDEX fki_user_dashboard_filter_models_user_dashboard_filter_id ON user_dashboard_filter_models (user_dashboard_filter_id);
ALTER TABLE user_dashboard_filter_models ADD CONSTRAINT fk_user_dashboard_filter_models_user_dashboard_filter_id FOREIGN KEY (user_dashboard_filter_id) REFERENCES user_dashboard_filters (id) ON DELETE CASCADE;
ALTER TABLE user_dashboard_filter_models ADD CONSTRAINT fk_user_dashboard_filter_models_model_id FOREIGN KEY (model_id) REFERENCES models (id) ON DELETE CASCADE;
