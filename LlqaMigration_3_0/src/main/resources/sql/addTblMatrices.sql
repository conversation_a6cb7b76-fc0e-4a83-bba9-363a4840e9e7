CREATE SEQUENCE sq_matrices;
CREATE TABLE matrices
(
  id              BIGINT DEFAULT nextval('sq_matrices' :: REGCLASS) NOT NULL
                  CONSTRAINT pk_matrices
                  PRIMARY KEY,
  measure_type_id BIGINT NOT NULL,
  x_size          SMALLINT NOT NULL,
  y_size          SMALLINT NOT NULL,
  formula         VARCHAR(4000) NOT NULL,
  x_top           VARCHAR(255),
  y_left          VARCHAR(255),
  created_at      TIMESTAMP,
  updated_at      TIMESTAMP,
  created_by      VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by      <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_matrices_measure_type_id ON matrices (measure_type_id);
ALTER TABLE matrices ADD CONSTRAINT fk_matrices_measure_type_id FOREIGN KEY (measure_type_id) REFERENCES measure_types (id) ON DELETE CASCADE;
ALTER TABLE matrices ADD CONSTRAINT uc_matrices_measure_type_id UNIQUE (measure_type_id);