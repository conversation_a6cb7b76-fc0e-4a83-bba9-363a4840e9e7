CREATE SEQUENCE sq_workflow_elements;
CREATE TABLE workflow_elements
(
  id                        BIGINT DEFAULT nextval('sq_workflow_elements' :: REGCLASS) NOT NULL
                            CONSTRAINT pk_workflow_elements
                            PRIMARY KEY,
  title                     J<PERSON><PERSON> NOT NULL,
  description               JSON,
  workflow_element_group_id BIGINT NOT NULL,
  rule_type                 VARCHAR(128) NOT NULL DEFAULT 'EXECUTE',
  created_at                TIMESTAMP,
  updated_at                TIMESTAMP,
  created_by                VA<PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by                VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_workflow_elements_workflow_element_group_id ON workflow_elements (workflow_element_group_id);
ALTER TABLE workflow_elements ADD CONSTRAINT fk_workflow_elements_workflow_element_group_id FOREIGN KEY (workflow_element_group_id) REFERENCES workflow_element_groups (id) ON DELETE CASCADE;