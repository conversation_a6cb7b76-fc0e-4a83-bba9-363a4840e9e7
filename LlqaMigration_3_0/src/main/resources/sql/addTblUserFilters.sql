CREATE SEQUENCE sq_user_filters;
CREATE TABLE user_filters
(
    id             BIGINT DEFAULT nextval('sq_user_filters' :: REGCLASS) NOT NULL
        CONSTRAINT pk_user_filters
            PRIMARY KEY,
    user_id        BIGINT NOT NULL,
    key            VARCHAR(50) NOT NULL,
    value          JSONB,
    created_at     TIMESTAMP,
    updated_at     TIMESTAMP,
    created_by     <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
    updated_by     VARCHAR(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_user_filters_user_id_value ON user_filters (user_id, value);
ALTER TABLE user_filters ADD CONSTRAINT fk_user_filters_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE user_filters ADD CONSTRAINT uc_user_filters_user_id_key UNIQUE (user_id, key);
