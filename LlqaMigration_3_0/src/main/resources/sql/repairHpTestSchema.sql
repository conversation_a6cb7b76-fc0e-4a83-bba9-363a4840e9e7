--
-- <PERSON><PERSON><PERSON> CHECKS WITH INVALID REFERENCES
--
DELETE FROM measurements WHERE check_id = 342;
<PERSON>LE<PERSON> FROM checks WHERE id = 342;

--
-- FLAG CONFIG ENTRIES >DELETED< WHERE ITS CONFIG TABLES ARE DELETED
--
UPDATE configentries SET deleted = TRUE
WHERE deleted = FALSE
  AND configtable_id IN
      (
        SELECT id
        FROM configtables
        WHERE configtables.deleted = TRUE
      );