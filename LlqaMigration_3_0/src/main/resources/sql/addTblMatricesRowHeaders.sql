CREATE SEQUENCE sq_matrices_row_headers;
CREATE TABLE matrices_row_headers
(
  id           BIGINT DEFAULT nextval('sq_matrices_row_headers' :: REGCLASS) NOT NULL
               CONSTRAINT pk_matrices_row_headers
               PRIMARY KEY,
  matrices_id  BIGINT NOT NULL,
  row_index    SMALLINT NOT NULL,
  value_left   VARCHAR(255),
  created_at   TIMESTAMP,
  updated_at   TIMESTAMP,
  created_by   <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL,
  updated_by   VA<PERSON><PERSON>R(255) DEFAULT 'system' :: CHARACTER VARYING NOT NULL
);
CREATE INDEX fki_matrices_row_headers_matrices_id ON matrices_row_headers (matrices_id);
ALTER TABLE matrices_row_headers ADD CONSTRAINT fk_matrices_row_headers_matrices_id FOREIGN KEY (matrices_id) REFERENCES matrices (id) ON DELETE CASCADE;
ALTER TABLE matrices_row_headers ADD CONSTRAINT uc_matrices_row_headers_matrices_id_row_index UNIQUE (matrices_id, row_index);