ALTER TABLE images ADD COLUMN gallery_item_type VARCHAR(128)
  CONSTRAINT gallery_item_type_exists CHECK ((gallery_item_type) :: text = ANY (ARRAY [('image' :: character varying) :: text, ('video' :: character varying) :: text]));
UPDATE images SET gallery_item_type = 'image';
ALTER TABLE images ALTER COLUMN gallery_item_type SET NOT NULL;

INSERT INTO images
(
   owner_id
  ,owner_type
  ,fullimage_id
  ,thumbnail_id
  ,caption
  ,seqnum
  ,gallery_item_type
  ,created_at
  ,updated_at
  ,created_by
  ,updated_by
)
SELECT doc.owner_id
      ,doc.owner_type
      ,doc.binaryfile_id
      ,NULL
      ,doc.caption
      ,COALESCE(doc.seqnum, 0) -
       (SELECT COALESCE(MIN(docSecNum.seqnum), 0) FROM documents docSecNum WHERE docSecNum.owner_id = doc.owner_id AND docSecNum.owner_type = doc.owner_type) +
       (SELECT COALESCE(MAX(img.seqnum), 0) FROM images img WHERE img.owner_id = doc.owner_id AND img.owner_type = doc.owner_type) +
       1
      ,'video'
      ,doc.created_at
      ,CURRENT_TIMESTAMP
      ,doc.created_by
      ,'llqa-3-migration'
FROM documents doc
WHERE doc.doctype = 2
ORDER BY doc.seqnum;

DELETE FROM documents WHERE doctype = 2;

ALTER TABLE images RENAME TO gallery_items;
ALTER SEQUENCE sq_images RENAME TO sq_gallery_items;
ALTER TABLE gallery_items ALTER id SET DEFAULT NEXTVAL('sq_gallery_items');

UPDATE data_changes SET subobjtype = 'gallery_item' WHERE subobjtype = 'document' AND LOWER(subobject) LIKE '%.mp4';