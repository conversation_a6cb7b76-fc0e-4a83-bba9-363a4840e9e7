if [ "$#" -ne 4 ]; then
    echo "ERROR - Illegal number of parameters"
    echo "  1.: DB name"
    echo "  2.: DB user"
    echo "  3.: host"
    echo "  4.: port"
    exit
fi

echo "connect to DB >$1< with user >$2< host >$3< port >$4<"

echo "+-------------------------------------+"
echo "|  ADD TABLE WORKFLOW ELEMENT GROUPS  |"
echo "+-------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblWorkflowElementGroups.sql

echo "+--------------------------+"
echo "|  ADD TABLE MEASURE_TYPES  |"
echo "+--------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblMeasureTypes.sql

echo "+----------------------+"
echo "|  ADD TABLE MATRICES  |"
echo "+----------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblMatrices.sql

echo "+----------------------------------+"
echo "|  ADD TABLE MATRICES ROW HEADERS  |"
echo "+----------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblMatricesRowHeaders.sql

echo "+-------------------------------------+"
echo "|  ADD TABLE MATRICES COLUMN HEADERS  |"
echo "+-------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblMatricesColumnHeaders.sql

echo "+----------------------------+"
echo "|  ADD TABLE MATRICES CELLS  |"
echo "+----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblMatricesCells.sql

echo "+-----------------------+"
echo "|  ADD TABLE ASSIGNEES  |"
echo "+-----------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblAssignees.sql

echo "+----------------------------------------------+"
echo "|  ADD TABLE ACTIVE PROCEDURES PREALLOCATIONS  |"
echo "+----------------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblActiveProceduresPreallocations.sql

echo "+-----------------------------+"
echo "|  ADD TABLE ASSIGNEE BLOCKS  |"
echo "+-----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblAssigneeBlocks.sql

echo "+-------------------------------+"
echo "|  ADD TABLE WORKFLOW ELEMENTS  |"
echo "+-------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblWorkflowElements.sql

echo "+----------------------------+"
echo "|  ADD TABLE WORKFLOW RULES  |"
echo "+----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblWorkflowRules.sql

echo "+-------------------------------------+"
echo "|  ADD TABLE WORKFLOW ELEMENT CHECKS  |"
echo "+-------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblWorkflowElementChecks.sql

echo "+--------------------------------+"
echo "|  ADD TABLE MEASUREMENT VALUES  |"
echo "+--------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblMeasurementValues.sql

echo "+----------------------------+"
echo "|  ADD TABLE USER DASHBAORD  |"
echo "+----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblUserDashboards.sql

echo "+-------------------------------+"
echo "|  ADD TABLE DASHBOARD FILTERS  |"
echo "+-------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblDashboardFilters.sql

echo "+-------------------------------------+"
echo "|  ADD TABLE DASHBOARD FILTER MODELS  |"
echo "+-------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblDashboardFilterModels.sql

echo "+---------------------------+"
echo "|  ADD TABLE USER METADATA  |"
echo "+---------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblUserMetaData.sql

echo "+-------------------------+"
echo "|  ADD TABLE USER_FILTERS  |"
echo "+-------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblUserFilters.sql

echo "+------------------------------+"
echo "|  ADD TABLE NOTICE TIMELINES  |"
echo "+------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblNoticeTimelines.sql

echo "+-----------------------------+"
echo "|  ADD TABLE BINARYFILESMETA  |"
echo "+-----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblBinaryFilesMeta.sql

echo "+----------------------------+"
echo "|  ADD TABLE WORFLOWKELEMENT TAGS  |"
echo "+----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblWorkflowElementTags.sql

echo "+-------------------------------------+"
echo "|  ADD TABLE CONFIG TABLE COL HEADER  |"
echo "+-------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblConfigTableColHeader.sql

echo "+--------------------------------------+"
echo "|  ADD TABLE CONFIG ENTRY COL CONTENT  |"
echo "+--------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblConfigEntryColContent.sql

echo "+------------------------------------+"
echo "|  ADD TABLE APPLICATION PROPERTIES  |"
echo "+------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblApplicationProperties.sql;

echo "+------------------------+"
echo "|  MODIFY CHECHS TABLE   |"
echo "+------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyChecks.sql

echo "+-----------------------------+"
echo "|  MODIFY CONFIGTABLES TABLE  |"
echo "+-----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyConfigEntries.sql

echo "+--------------------------+"
echo "|  MODIFY PROCEDURE TABLE  |"
echo "+--------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyProcedures.sql

echo "+----------------------+"
echo "|  MODIFY STEPS TABLE  |"
echo "+----------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifySteps.sql

echo "+-------------------------+"
echo "|  MODIFY MEASURES TABLE  |"
echo "+-------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyMeasures.sql

echo "+-----------------------+"
echo "|  MODIFY MODELS TABLE  |"
echo "+-----------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyModels.sql

echo "+-----------------------------+"
echo "|  MODIFY MEASUREMENTS TABLE  |"
echo "+-----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyMeasurements.sql

echo "+----------------------------+"
echo "|  MODIFY DEVICETYPES TABLE  |"
echo "+----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyDeviceTypes.sql

echo "+---------------------------+"
echo "|  MODIFY CHECKTYPES TABLE  |"
echo "+---------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyCheckTypes.sql

echo "+---------------------------------+"
echo "|  MODIFY ACRIVEPROCEDURES TABLE  |"
echo "+---------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyActiveProcedures.sql

echo "+-------------------------+"
echo "|  MODIFY SETTINGS TABLE  |"
echo "+-------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifySettings.sql

echo "+--------------------------+"
echo "|  MODIFY TOOLTYPES TABLE  |"
echo "+--------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyTooltypes.sql

echo "+------------------------+"
echo "|  MODIFY NOTICES TABLE  |"
echo "+------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyNotices.sql

echo "+----------------------------+"
echo "|  MODIFY DATACHANGES TABLE  |"
echo "+----------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyDataChanges.sql

echo "+---------------------------------+"
echo "|  MODIFY ACTIVECHECKTYPES TABLE  |"
echo "+---------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyActiveCheckTypes.sql

echo "+--------------------------+"
echo "|  MODIFY TOOLUNITS TABLE  |"
echo "+--------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyToolUnits.sql

echo "+---------------------------+"
echo "|  MODIFY USERGROUPS TABLE  |"
echo "+---------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyUserGroups.sql

echo "+---------------------------------------+"
echo "|  MODIFY MEASUREMENT ERROR CATEGORIES  |"
echo "+---------------------------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyMeasurementErrorCategories.sql

echo "+----------------------+"
echo "|  MODIFY CONFIGTABLES |"
echo "+----------------------+"
psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f modifyConfigTables.sql

# NOT USED ANY MORE
# echo "+--------------------------------+"
# echo "|  ADD TABLE MIGRATION PROGRESS  |"
# echo "+--------------------------------+"
# psql --host=$3 --dbname=$1 --username=$2 --port=$4 -a -f addTblMigrationProgress3_0.sql