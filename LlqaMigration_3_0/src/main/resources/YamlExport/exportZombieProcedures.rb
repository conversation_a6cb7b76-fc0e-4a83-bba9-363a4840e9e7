require 'pg'
require 'yaml'
require 'json'
require 'active_record'
require_relative 'models/yaml_object_record'
require_relative 'models/yaml_array_record'

config = YAML.load_file('config/main_config.yml')
db_config = config['database']

# select database
begin
  ActiveRecord::Base.establish_connection(
      adapter:            db_config['adapter'],
      host:               db_config['host'],
      database:           db_config['name'],
      username:           db_config['user'],
      password:           db_config['password'],
      port:               db_config['port'],
      schema_search_path: db_config['schema_search_path'],
      encoding:           db_config['encoding']
  )

  ActiveRecord::Base.connection.execute "CREATE OR REPLACE TEMPORARY VIEW yaml_object_records
                                         AS
                                         SELECT id, checkdata AS yaml_row FROM checks"
  results = YamlObjectRecord.find_by_sql("SELECT id, yaml_row
                                          FROM yaml_object_records
                                          WHERE yaml_row IS NOT NULL
                                            AND yaml_row NOT LIKE '--- {}%'
                                          ORDER BY id")

  json_result = []
  # compose the JSON output to a JSON array
  results.each do |result|
    id = result['id']
    yaml = result['yaml_row']
    # puts "check id >#{id}<"
    yaml['procedures'].each do |p|
      result_num_procedures = ActiveRecord::Base.connection.execute("SELECT COUNT(*) AS num_procedures FROM procedures WHERE id = #{p['id']}")
      num_procedures = result_num_procedures[0]['num_procedures']
      # puts "procedure >#{p['id']}<, number of procedures >#{num_procedures}<"
      if num_procedures <= 0 then
        json_result << {'check_id' => id, 'level' => 'procedure', 'procedure_id' => p['id'], 'code' => p['code'],  'ap_id' => p['ap_id'], 'real_id' => p['rid']}
      end
      p['steps'].each do |s|
        result_num_steps = ActiveRecord::Base.connection.execute("SELECT COUNT(*) AS num_steps FROM steps WHERE id = #{s['id']}")
        num_steps = result_num_steps[0]['num_steps']
        # puts "step >#{s['id']}<, number of steps >#{num_steps}<"
        if num_steps <= 0 then
          json_result << {'check_id' => id, 'level' => 'steps', 'step_id' => s['id'], 'code' => s['code']}
        end
        s['measures'].each do |m|
          result_num_measures = ActiveRecord::Base.connection.execute("SELECT COUNT(*) AS num_measures FROM measures WHERE id = #{m['id']}")
          num_measures = result_num_measures[0]['num_measures']
          # puts "measure >#{m['id']}<, number of measures >#{num_measures}<"
          if num_measures <= 0 then
            json_result << {'check_id' => id, 'level' => 'measures', 'measure_id' => m['id'], 'code' => m['code'], 'mtype' => m['mtype'], 'needstool' => m['needstool']}
          end
        end
      end
    end
  end

  puts json_result.to_json

  rescue ActiveRecord::RecordInvalid => err
    raise("postgresql ERROR >#{err}<")
    fail
  ensure
    ActiveRecord::Base.connection.close
    exit(true)
end
