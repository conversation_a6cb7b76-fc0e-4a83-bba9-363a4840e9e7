# Export YAML DB attributes from LEAN LOGIG QA to JSON:

simple application to export LLQA rows with YAML content to JSON.

## Input

the input parameter are the following:

* table name: "table_name". the name rom the table
* column name: "column_name". the column name from the table
* column type: column_type" ("object" or "array"). depending on the YAML source.
* fetch size: "fetch_size" (number). the maximum number of fetched records 
* start primary key: "min_id" (number). the minimum primary key that is written from the source table

## OUTPUT

tha application will print the result to the standard output.

JSON array with requested records has the following attributes.

* "id": the records primary key
* "yaml": YAML content as JSON. either array or object

### Examle output
```
[
  {
    "id": 1,
    "yaml": [
      {
        "type": "CO",
        "code": "1.28",
        "res": "omit",
        "trigger": "Select"
      },
      {
        "type": "CO",
        "code": "1.29",
        "res": "omit",
        "trigger": "Select"
      }
    ]
  },
  {
    ...
  },
  ...
]
```
## Usage

a simple commandline application that writes its result to the standard output.

you can write the standard output to a file or parse it from in the calling application.

### Exaple (write JSON to the std out)
    $ ruby main.rb table_name=datachanges column_name=changedetails column_type=object fetch_size=500 min_id=1
    
### Exaple (write JSON to a file)
    $ ruby main.rb table_name=datachanges column_name=changedetails column_type=object fetch_size=500 min_id=1 > export.json