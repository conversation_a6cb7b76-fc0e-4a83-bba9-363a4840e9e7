require 'pg'
require 'yaml'
require 'json'
require 'active_record'
require_relative 'models/yaml_object_record'
require_relative 'models/yaml_array_record'

config = YAML.load_file('config/main_config.yml')
db_config = config['database']

KEY_DATABASE_NAME = "database_name"
KEY_TABLE_NAME = "table_name"
KEY_COLUMN_NAME = "column_name"
KEY_COLUMN_TYPE = "column_type"
KEY_MIN_ID = "min_id"
KEY_FETCH_SIZE = "fetch_size"
KEY_PASSWORD = "password"

COLUMN_TYPE_OBJECT = "object"
COLUMN_TYPE_ARRAY = "array"

# parse and initialize arguments
def check_param(args, key)
  if !args.has_key?(key)
    raise("#{key} not set")
  end
end

def check_params(args)
  check_param(args, KEY_DATABASE_NAME)
  check_param(args, KEY_TABLE_NAME)
  check_param(args, KEY_COLUMN_NAME)
  check_param(args, KEY_COLUMN_TYPE)
  check_param(args, KEY_MIN_ID)
  check_param(args, KEY_FETCH_SIZE)
  check_param(args, KEY_PASSWORD)
end

args = Hash.new
ARGV.each do|arg|
  conf = arg.split(/=/)
  if conf.length == 2 then
    param_name = conf[0].strip
    param_value = conf[1].strip
    if param_name.length > 0 and param_value.length > 0 then
      args[param_name] = param_value
    end
  end
end

check_params(args)

# select database
begin
  ActiveRecord::Base.establish_connection(
      adapter:            db_config['adapter'],
      host:               db_config['host'],
      database:           args[KEY_DATABASE_NAME],
      username:           db_config['user'],
      password:           args[KEY_PASSWORD],
      port:               db_config['port'],
      schema_search_path: db_config['schema_search_path'],
      encoding:           db_config['encoding']
  )

  case args[KEY_COLUMN_TYPE]
    when COLUMN_TYPE_OBJECT
      ActiveRecord::Base.connection.execute "CREATE OR REPLACE TEMPORARY VIEW yaml_object_records
                                             AS
                                             SELECT id, #{args[KEY_COLUMN_NAME]} AS yaml_row FROM #{args[KEY_TABLE_NAME]}"
      results = YamlObjectRecord.find_by_sql("SELECT id, yaml_row
                                              FROM yaml_object_records
                                              WHERE id >= #{args[KEY_MIN_ID]}
                                                AND yaml_row IS NOT NULL
                                                AND yaml_row NOT LIKE '--- {}%'
                                              ORDER BY id
                                              LIMIT #{args[KEY_FETCH_SIZE]}")
    when COLUMN_TYPE_ARRAY
      ActiveRecord::Base.connection.execute "CREATE OR REPLACE TEMPORARY VIEW yaml_array_records
                                             AS
                                             SELECT id, #{args[KEY_COLUMN_NAME]} AS yaml_row FROM #{args[KEY_TABLE_NAME]}"
      results = YamlArrayRecord.find_by_sql("SELECT id, yaml_row
                                             FROM yaml_array_records
                                             WHERE id >= #{args[KEY_MIN_ID]}
                                               AND yaml_row IS NOT NULL
                                               AND yaml_row NOT LIKE '--- []%'
                                             ORDER BY id
                                             LIMIT #{args[KEY_FETCH_SIZE]}")
  end

  json_result = []

  # compose the JSON output to a JSON array
  results.each do |result|
    id = result['id']
    yaml = result['yaml_row']
    json_result << {'id' => id, 'yaml' => yaml}
  end

  puts json_result.to_json

  rescue ActiveRecord::RecordInvalid => err
    raise("postgresql ERROR >#{err}<")
    fail
  ensure
    ActiveRecord::Base.connection.close
    exit(true)
end
