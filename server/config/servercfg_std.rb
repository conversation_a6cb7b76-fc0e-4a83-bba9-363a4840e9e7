set :environment, :development
set :port, 4567

$system_languages = []

ENV['LLQA_LANGUAGES'].split(',').each do |lang|
  if lang === 'en'
    $system_languages << {code: 'en', name: 'English', dformat: 'MM/dd/yyyy', tformat: 'MM/dd/yyyy hh:mm:ss a', dformatr: '%m/%d/%Y', tformatr: '%m/%d/%Y %I:%M:%S %p' }
  end
  if lang === 'de'
    $system_languages << {code: 'de', name: 'Deutsch', dformat: 'dd.MM.yyyy', tformat: 'dd.MM.yyyy HH:mm:ss',   dformatr: '%d.%m.%Y', tformatr: '%d.%m.%Y %H:%M:%S'    }
  end
  if lang === 'cn'
    $system_languages << {code: 'cn', name: '中文',     dformat: 'MM/dd/yyyy', tformat: 'MM/dd/yyyy hh:mm:ss a', dformatr: '%m/%d/%Y', tformatr: '%m/%d/%Y %I:%M:%S %p' }
  end
  if lang === 'fr'
    $system_languages << {code: 'fr', name: 'Français', dformat: 'dd.MM.yyyy', tformat: 'dd.MM.yyyy HH:mm:ss',   dformatr: '%d.%m.%Y', tformatr: '%d.%m.%Y %H:%M:%S'    }
  end
end

if $system_languages == []
  $system_languages << {code: 'en', name: 'English', dformat: 'MM/dd/yyyy', tformat: 'MM/dd/yyyy hh:mm:ss a', dformatr: '%m/%d/%Y', tformatr: '%m/%d/%Y %I:%M:%S %p' }
end


$debugging_api = get_boolean_value(ENV['LLQA_DEVELOPMENT_ENVIRONMENT'])

# expiration time in seconds
$expiry_time = ENV['LLQA_EXPIRATION_TIME'].to_i

# available customer features
$features = {
    customTags: get_boolean_value(ENV['LLQA_OPTION_CUSTOMTAGS']),
    autocode: get_boolean_value(ENV['LLQA_OPTION_AUTOCODE']),
    configtable: get_boolean_value(ENV['LLQA_OPTION_CONFIGTABLE']),
    measurementErrorReport: get_boolean_value(ENV['LLQA_OPTION_MEASUREMENTERRORREPORT'])
}