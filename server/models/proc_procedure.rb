class Procedure < ActiveRecord::Base
  include ModelBase
  include Versioning
  include Searching

  # -----
  # Table setup
  # -----

  before_validation { complete_languageblock(false, :title, :description) }

  validates :title, language: { complete: :all, minlength: 3 }
  validates :description, language: { complete: :none }

  serialize :title, Hash
  serialize :description, Hash
  serialize :metadata, Hash
  serialize :flowcontrol, Array
  serialize :processing_time, Hash

  has_many :steps, -> { order('seqnum ASC') }, dependent: :destroy
  has_many :activeprocedures, dependent: :destroy

  has_many :datachanges, -> { order('timestamp ASC') }, as: :source

  augment_class_usertag

  def self.create_table(m,tn)
    m.create_table tn do |t|
      t.string :code, null:false
      t.text :title, null:false
      t.text :description
      t.text :metadata
      t.text :flowcontrol
      augment_table_usertag t
      augment_table_version t
    end
  end
  # LLQA-85: add custom model attributes
  def self.add_custom_attrs(m,tn)
    m.change_table tn do |t|
      t.text :tag_1
      t.text :tag_2
      t.text :tag_3
      t.text :tag_4
      t.text :tag_5
      t.text :tag_6
    end
  end

  def self.add_processing_time(m,tn)
    m.change_table tn do |t|
      t.text :processing_time
    end
  end
  # -----
  # Global search
  # -----

  def self.searchable_fields
    {
        code: :text,
        title: :localized_text,
        description: :localized_html
    }
  end

  def self.get_entity_array(fields, limitation = :none)
    fields += %w(realid version)
    return self.order(updated_at: :desc).select(fields).all if limitation == :none
    return self.order(updated_at: :desc).where(version: nil).select(fields).all if limitation == :edit_version
    return self.order(updated_at: :desc).where.not(version: nil).select(fields).all if limitation == :finalized
    ret = {}
    self.order(updated_at: :desc).where.not(version: nil).select(fields).all.each do |entity|
      ret[entity.realid] = entity if(!ret[entity.realid] || (entity.version > ret[entity.realid].version))
    end
    ret.values
  end

  # -----
  # DB hooks
  # -----

  def has_been_created(init)
    log_change(self.code, 'procedure', 'create', { initdata: init }, true)
  end

  def has_been_updated(changes)
    changes.each do |k,v|
      if k == 'code'
        log_change(self.code, 'procedure', 'changecode', { oldcode: v[0], newcode: v[1]}, true)
      elsif (%w(title description flowcontrol realid tag_1 tag_2 tag_3 tag_4 tag_5 tag_6 processing_time).index(k))
        log_change(self.code, 'procedure', 'changefield', { field: k, olddata: v[0] ? v[0].to_json : nil, newdata: v[1] ? v[1].to_json : v[1] }, true)
      elsif k == 'version'
        log_change(self.code, 'procedure', 'finalize', { version: v[1] }, false)
      elsif k == 'disabled'
        log_change(self.code, 'procedure', 'delete', { type: 'disabled' }, false)
      end
    end
  end

  def has_been_deleted
    log_change(self.code, 'procedure', 'delete', { type: 'deleted' }, false)
  end

  # -----
  # Versioning
  # -----

  def log_change(obj, objtype, type, details, dirty)
    Datachange.log_change(self, obj, objtype, type, details, (self.version != nil) && dirty, (self.version == nil) && dirty)
  end

  def is_dirty?
    Datachange.where(source: self, dirty: true).size > 0
  end

  def recurse_finalize
    [[:steps,:procedure_id]]
  end

  def effective_version
    self.version
  end

  def effective_realid
    self.realid
  end

  # -----
  # Import/Export
  # -----

  def self.import_from_hash(hash,changecode = true)
    steps = hash.delete 'steps'
    blobs = hash.delete 'blobs' if !blobs

    # Validate title (languages)
    check_for_languages(hash)

    procedure = Procedure.new hash
    stamp_realid(procedure)
    procedure.save!

    # always set code to generated id, no matter what 'changecode' is
    procedure['code'] = procedure.id.to_s

    procedure.save
    procedure.has_been_created(hash)
    steps.each { |step| step['procedure_id'] = procedure.id; Step.import_from_hash(step,false,blobs) }
    procedure
  end

  def self.export_to_hash(id, forexport=true, kindOfExport='1', dataToExport='')

    # kindOfExport:
    #
    # 1 = Export für selbes System
    #     - Bilder und Dokumente  Nur Referenzen werden exportiert
    #     - Werkzeuge             Nur Referenzen werden exportiert
    #     - Regeln
    #     - Ablaufzwang
    #
    # 2 = Export für anderes System
    #     - Bilder und Dokumente: Dateien werden mitexportiert
    #

    # dataToExport
    #
    # 1 = Bilder und Dokumente
    # 2 = Werkzeuge TODO for now, only in same system included!
    # 3 = Regeln TODO for now, only in same system included!
    # 4 = Ablaufzwang TODO for now, only in same system included!

    dataToExport = dataToExport.split(",")
    get_single(id, forexport).serialize(%w(steps steps.measures steps.images steps.documents)) do |h,o|

      if kindOfExport == '1'
          if (!dataToExport.include? '1') || (!dataToExport.include? '2') || (!dataToExport.include? '3')  || (!dataToExport.include? '4')
            h['flowcontrol'] = [] if !dataToExport.include? '3'
            h['steps'].each do |step|
              step['flowcontrol'] = [] if !dataToExport.include? '3'
              step['images'] = [] if !dataToExport.include? '1'
              step['documents'] = [] if !dataToExport.include? '1'
              step['enforce'] = 0 if !dataToExport.include? '4'
              step['measures'].each do |measure|
                measure['flowcontrol'] = [] if !dataToExport.include? '3'
                measure['tooltype_id'] = nil if !dataToExport.include? '2'
                measure['enforce'] = 0 if !dataToExport.include? '4'
              end
            end
          end
      end

      if kindOfExport == '2'

        h['tag_1'] = nil
        h['tag_2'] = nil
        h['tag_3'] = nil
        h['tag_4'] = nil
        h['tag_5'] = nil
        h['tag_6'] = nil

        if dataToExport.include? '1'
          h['blobs'] = {}
          o.steps.each do |step|
            step.images.each { |img| img.export_blob(h['blobs']) }
            step.documents.each { |doc| doc.export_blob(h['blobs']) }
          end
        end
        h['flowcontrol'] = [] # if !dataToExport.include? '3' TODO for now, always cleared!
        h['steps'].each do |step|
          step['flowcontrol'] = [] # if !dataToExport.include? '3' TODO for now, always cleared!
          step['images'] = [] if !dataToExport.include? '1'
          step['documents'] = [] if !dataToExport.include? '1'
          step['enforce'] = 0 if # !dataToExport.include? '4' TODO for now, always cleared!
          step['measures'].each_with_index do |measure, i|
            measure['flowcontrol'] = [] # if !dataToExport.include? '3' TODO for now, always cleared!
            measure['tooltype_id'] = nil

            measure['enforce'] = 0 # if !dataToExport.include? '4' TODO for now, always cleared!
          end
        end
      end

      h.clean_export_hash
    end
  end

  # Setting missing languages for title, setting the title the same as a previous one from another
  # language. If there is noprevious title, set questionmarks
  def self.check_for_languages(obj)
    alllang = $system_languages.map { |a| a[:code] }

    default_title = nil
    obj['title'].each do |lang, title|
      default_title = title
    end

    alllang.each do |lang, title|
      if !obj['title'].include?(lang)
        if default_title
          obj['title'][lang] = default_title
        else
          obj['title'][lang] = '?????'
        end
      end
    end
  end

end

