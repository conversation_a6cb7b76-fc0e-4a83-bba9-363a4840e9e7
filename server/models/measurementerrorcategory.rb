class MeasurementErrorCategory < ActiveRecord::Base
  include ModelBase
  include Ordering

  default_scope { order(seqnum: :asc) }

  # -----
  # Table setup
  # -----
  before_validation { complete_languageblock(false, :name) }

  validates :name, language: { complete: :all, minlength: 3 }

  # serialize json object (value) as newline delimited string in the database
  serialize :name

  augment_class_usertag

  def self.create_table(m,tn)
    m.create_table tn do |t|
      t.string :name
      t.boolean :deleted, null: false, default: false
      t.integer :seqnum
      augment_table_usertag t
    end
  end

  # -----
  # Ordering
  # -----
  def reorder_parentset
    MeasurementErrorCategory.where(deleted: false)
  end
end