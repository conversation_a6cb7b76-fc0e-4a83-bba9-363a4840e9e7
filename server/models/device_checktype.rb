class Checktype < ActiveRecord::Base
  include ModelBase
  include Searching
  include Ordering

  # -----
  # Table setup
  # -----

  default_scope { order(seqnum: :asc) }

  before_validation { complete_languageblock(false, :title, :description) }

  validates :code, codevalid: true
  validates :title, language: { complete: :all, minlength: 3 }
  validates :description, language: { complete: :none }

  serialize :title
  serialize :description
  serialize :metadata, Hash

  has_many :checks, dependent: :destroy
  has_many :activechecktypes, dependent: :destroy

  augment_class_usertag

  def self.create_table(m,tn)
    m.create_table tn do |t|
      t.string :code, null:false
      t.text :title, null:false
      t.text :description
      t.text :metadata
      t.boolean :deleted, null:false, default:false
      augment_table_usertag t
    end
  end

  # -----
  # Global search
  # -----

  def self.searchable_fields
    {
        code: :text,
        title: :localized_text,
        description: :localized_text
    }
  end

  # -----
  # Reordering
  # -----
  def reorder_parentset
    Checktype.all
  end

  # -----
  # Migrations
  # -----
  def self.update_table_01(m,tn)
    m.change_table tn do |t|
      t.integer :seqnum, null: false, default: 0
    end

    Checktype.all.each_with_index do |ct, idx|
      ct.seqnum = idx + 1
      ct.save!
    end
  end
end
