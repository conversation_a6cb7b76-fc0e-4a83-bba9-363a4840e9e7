# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `rails
# db:schema:load`. When creating a new database, `rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2020_06_18_083011) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "activechecktypes", id: :bigint, default: -> { "nextval('sq_activechecktypes'::regclass)" }, force: :cascade do |t|
    t.bigint "model_id"
    t.bigint "checktype_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.index ["checktype_id"], name: "fki_fk_activechecktype_checktype_id"
    t.index ["model_id"], name: "fki_fk_activechecktype_model_id"
  end

  create_table "activeprocedures", id: :bigint, default: -> { "nextval('sq_activeprocedures'::regclass)" }, force: :cascade do |t|
    t.bigint "model_id"
    t.bigint "procedure_id"
    t.text "flowcontrol"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.bigint "seqnum", default: -1, null: false
    t.bigint "enforce", default: 0, null: false
    t.index ["model_id"], name: "fki_fk_activeprocedures_model_id"
    t.index ["procedure_id"], name: "fki_fk_activeprocedures_procedure_id"
  end

  create_table "affiliations", id: :bigint, default: -> { "nextval('sq_affiliations'::regclass)" }, force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "usergroup_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.index ["user_id"], name: "fki_fk_affiliations_user_id"
    t.index ["usergroup_id"], name: "fki_fk_affiliations_usergroup_id"
  end

  create_table "binaryfiles", id: :bigint, default: -> { "nextval('sq_binaryfiles'::regclass)" }, force: :cascade do |t|
    t.string "filename", limit: 255
    t.string "original_filename", limit: 255
    t.string "hexhash", limit: 255
    t.string "mimetype", limit: 255
    t.bigint "size"
    t.text "metadata"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
  end

  create_table "checks", id: :bigint, default: -> { "nextval('sq_checks'::regclass)" }, force: :cascade do |t|
    t.bigint "unit_id", null: false
    t.bigint "model_id", null: false
    t.bigint "checktype_id", null: false
    t.text "comment"
    t.date "scheduled"
    t.date "dueby"
    t.date "started"
    t.date "finished"
    t.bigint "status"
    t.text "metadata"
    t.text "checkdata"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.decimal "progress_finished"
    t.decimal "progress_error"
    t.text "assign_data"
    t.index ["checktype_id"], name: "fki_fk_checks_checktype_id"
    t.index ["model_id"], name: "fki_fk_checks_model_id"
    t.index ["unit_id"], name: "fki_fk_checks_unit_id"
  end

  create_table "checktypes", id: :bigint, default: -> { "nextval('sq_checktypes'::regclass)" }, force: :cascade do |t|
    t.string "code", limit: 255, null: false
    t.text "title", null: false
    t.text "description"
    t.text "metadata"
    t.boolean "deleted", default: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.integer "seqnum", default: 0, null: false
  end

  create_table "configentries", id: :bigint, default: -> { "nextval('sq_configentries'::regclass)" }, force: :cascade do |t|
    t.bigint "code_id"
    t.bigint "configtable_id"
    t.boolean "active", default: false
    t.boolean "deleted", default: false
    t.string "col1", limit: 255
    t.string "col2", limit: 255
    t.string "col3", limit: 255
    t.string "col4", limit: 255
    t.string "col5", limit: 255
    t.string "col6", limit: 255
    t.text "blocked_by"
    t.boolean "blocked", default: false
    t.text "timeline"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.index ["configtable_id"], name: "fki_fk_configentries_configtable_id"
  end

  create_table "configtables", id: :bigint, default: -> { "nextval('sq_configtables'::regclass)" }, force: :cascade do |t|
    t.string "title", limit: 255
    t.string "parent", limit: 255
    t.bigint "parentid"
    t.string "parentcode", limit: 255
    t.string "colheader1", limit: 255
    t.string "colheader2", limit: 255
    t.string "colheader3", limit: 255
    t.string "colheader4", limit: 255
    t.string "colheader5", limit: 255
    t.string "colheader6", limit: 255
    t.text "timeline"
    t.boolean "editable", default: true
    t.boolean "deleted", default: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
  end

  create_table "datachanges", id: :integer, default: -> { "nextval('sq_datachanges'::regclass)" }, force: :cascade do |t|
    t.integer "source_id"
    t.string "source_type", limit: 255
    t.string "subobject", limit: 255, null: false
    t.string "subobjtype", limit: 255, null: false
    t.string "changetype", limit: 255, null: false
    t.text "changedetails"
    t.integer "user"
    t.integer "timestamp"
    t.boolean "post_finalize"
    t.boolean "dirty"
    t.index ["user"], name: "fki_fk_datachanges_user_id"
  end

  create_table "devicetypes", id: :bigint, default: -> { "nextval('sq_devicetypes'::regclass)" }, force: :cascade do |t|
    t.string "code", limit: 255, null: false
    t.text "title", null: false
    t.text "description"
    t.text "metadata"
    t.boolean "deleted", default: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
  end

  create_table "documents", id: :bigint, default: -> { "nextval('sq_documents'::regclass)" }, force: :cascade do |t|
    t.bigint "owner_id"
    t.string "owner_type", limit: 255
    t.bigint "binaryfile_id"
    t.string "caption", limit: 255
    t.bigint "doctype"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.bigint "seqnum", default: -1, null: false
    t.index ["binaryfile_id"], name: "fki_fk_documents_binaryfile_id"
  end

  create_table "grants", id: :bigint, default: -> { "nextval('sq_grants'::regclass)" }, force: :cascade do |t|
    t.bigint "grantee_id"
    t.string "grantee_type", limit: 255
    t.bigint "privilege_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.index ["privilege_id"], name: "fki_fk_grants_privilege_id"
  end

  create_table "images", id: :bigint, default: -> { "nextval('sq_images'::regclass)" }, force: :cascade do |t|
    t.bigint "owner_id"
    t.string "owner_type", limit: 255
    t.bigint "fullimage_id"
    t.bigint "preview_id"
    t.bigint "thumbnail_id"
    t.string "caption", limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.bigint "seqnum", default: -1, null: false
    t.index ["fullimage_id"], name: "fki_fk_images_fullimage_id"
    t.index ["preview_id"], name: "fki_fk_images_preview_id"
    t.index ["thumbnail_id"], name: "fki_fk_images_thumbnail_id"
  end

  create_table "measurement_error_categories", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.boolean "deleted", default: false, null: false
    t.integer "seqnum"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
  end

  create_table "measurements", id: :bigint, default: -> { "nextval('sq_measurements'::regclass)" }, force: :cascade do |t|
    t.bigint "check_id", null: false
    t.bigint "measure_id", null: false
    t.bigint "toolunit_id"
    t.text "rawvalues"
    t.string "value", limit: 255
    t.string "comment", limit: 255
    t.bigint "status", default: 0, null: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.text "metadata"
    t.bigint "savedon"
    t.bigint "savedby"
    t.text "previous_measurements"
    t.index ["check_id"], name: "fki_fk_measurements_check_id"
    t.index ["measure_id"], name: "fki_fk_measurements_measure_id"
    t.index ["toolunit_id"], name: "fki_fk_measurements_toolunit_id"
  end

  create_table "measures", id: :bigint, default: -> { "nextval('sq_measures'::regclass)" }, force: :cascade do |t|
    t.bigint "step_id", null: false
    t.bigint "tooltype_id"
    t.string "code", limit: 255, null: false
    t.text "title", null: false
    t.text "description"
    t.bigint "measuretype", null: false
    t.text "calculation", null: false
    t.text "metadata"
    t.text "flowcontrol"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.bigint "seqnum", default: -1, null: false
    t.bigint "enforce", default: 0, null: false
    t.index ["step_id"], name: "fki_fk_measures_step_id"
    t.index ["tooltype_id"], name: "fki_fk_measures_tooltype_id"
  end

  create_table "models", id: :bigint, default: -> { "nextval('sq_models'::regclass)" }, force: :cascade do |t|
    t.bigint "devicetype_id", null: false
    t.string "code", limit: 255, null: false
    t.text "title", null: false
    t.text "description"
    t.text "metadata"
    t.bigint "realid", null: false
    t.bigint "version"
    t.boolean "disabled", default: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.index ["devicetype_id"], name: "fki_fk_models_devicetype_id"
  end

  create_table "notices", id: :bigint, default: -> { "nextval('sq_notices'::regclass)" }, force: :cascade do |t|
    t.string "text", limit: 500
    t.bigint "category_id", null: false
    t.text "path"
    t.string "artno", limit: 255
    t.string "artdesc", limit: 255
    t.bigint "timeloss"
    t.bigint "status"
    t.text "timeline"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
  end

  create_table "privileges", id: :bigint, default: -> { "nextval('sq_privileges'::regclass)" }, force: :cascade do |t|
    t.string "name", limit: 255, null: false
    t.text "description"
    t.string "code", limit: 255, null: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
  end

  create_table "procedures", id: :bigint, default: -> { "nextval('sq_procedures'::regclass)" }, force: :cascade do |t|
    t.string "code", limit: 255, null: false
    t.text "title", null: false
    t.text "description"
    t.text "metadata"
    t.text "flowcontrol"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.bigint "realid", null: false
    t.bigint "version"
    t.boolean "disabled", default: false
    t.text "tag_1"
    t.text "tag_2"
    t.text "tag_3"
    t.text "tag_4"
    t.text "tag_5"
    t.text "tag_6"
    t.text "processing_time"
  end

  create_table "settings", id: :bigint, default: -> { "nextval('sq_settings'::regclass)" }, force: :cascade do |t|
    t.string "key", limit: 255
    t.string "value", limit: 255
    t.bigint "seqnum"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.string "sub_values", limit: 255
  end

  create_table "snippets", id: :bigint, default: -> { "nextval('sq_snippets'::regclass)" }, force: :cascade do |t|
    t.string "category", limit: 255
    t.string "text", limit: 255
    t.bigint "status"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.bigint "seqnum", default: -1, null: false
  end

  create_table "steps", id: :bigint, default: -> { "nextval('sq_steps'::regclass)" }, force: :cascade do |t|
    t.bigint "procedure_id", null: false
    t.string "code", limit: 255, null: false
    t.text "title", null: false
    t.text "description"
    t.text "metadata"
    t.text "flowcontrol"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.bigint "seqnum", default: -1, null: false
    t.bigint "enforce", default: 0, null: false
    t.bigint "steptype", default: 0
    t.index ["procedure_id"], name: "fki_fk_steps_procedure_id"
  end

  create_table "tooltypes", id: :bigint, default: -> { "nextval('sq_tooltypes'::regclass)" }, force: :cascade do |t|
    t.string "code", limit: 255, null: false
    t.text "title", null: false
    t.text "description"
    t.text "metadata"
    t.boolean "deleted", default: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
  end

  create_table "toolunits", id: :bigint, default: -> { "nextval('sq_toolunits'::regclass)" }, force: :cascade do |t|
    t.bigint "tooltype_id", null: false
    t.string "code", limit: 255, null: false
    t.text "comment"
    t.text "metadata"
    t.boolean "deleted", default: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.index ["tooltype_id"], name: "fki_fk_toolunits_tooltype_id"
  end

  create_table "units", id: :bigint, default: -> { "nextval('sq_units'::regclass)" }, force: :cascade do |t|
    t.bigint "model_id", null: false
    t.string "code", limit: 255, null: false
    t.string "customer", limit: 255
    t.text "comment"
    t.date "commissioned"
    t.date "finished"
    t.date "delivered"
    t.date "approved"
    t.bigint "status"
    t.text "metadata"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.index ["model_id"], name: "fki_fk_units_model_id"
  end

  create_table "usergroups", id: :bigint, default: -> { "nextval('sq_usergroups'::regclass)" }, force: :cascade do |t|
    t.string "name", limit: 255, null: false
    t.bigint "level"
    t.text "description"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.boolean "deleted", default: false
  end

  create_table "users", id: :bigint, default: -> { "nextval('sq_users'::regclass)" }, force: :cascade do |t|
    t.string "username", limit: 255, null: false
    t.string "passhash", limit: 255, null: false
    t.text "realname", null: false
    t.text "comment"
    t.text "userinfo"
    t.bigint "status", default: 1, null: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "created_by", limit: 255, default: "system", null: false
    t.string "updated_by", limit: 255, default: "system", null: false
    t.text "metadata"
    t.text "dashboardinfo"
  end

  add_foreign_key "activechecktypes", "checktypes", name: "fk_activechecktype_checktype_id"
  add_foreign_key "activechecktypes", "models", name: "fk_activechecktype_model_id"
  add_foreign_key "activeprocedures", "models", name: "fk_activeprocedures_model_id"
  add_foreign_key "activeprocedures", "procedures", name: "fk_activeprocedures_procedure_id"
  add_foreign_key "affiliations", "usergroups", name: "fk_affiliations_usergroup_id"
  add_foreign_key "affiliations", "users", name: "fk_affiliations_user_id"
  add_foreign_key "checks", "checktypes", name: "fk_checks_checktype_id"
  add_foreign_key "checks", "models", name: "fk_checks_model_id"
  add_foreign_key "checks", "units", name: "fk_checks_unit_id"
  add_foreign_key "configentries", "configtables", name: "fk_configentries_configtable_id"
  add_foreign_key "datachanges", "users", column: "user", name: "fk_datachanges_user_id"
  add_foreign_key "documents", "binaryfiles", name: "fk_documents_binaryfile_id"
  add_foreign_key "grants", "privileges", name: "fk_grants_privilege_id"
  add_foreign_key "images", "binaryfiles", column: "fullimage_id", name: "fk_images_fullimage_id"
  add_foreign_key "images", "binaryfiles", column: "preview_id", name: "fk_images_preview_id"
  add_foreign_key "images", "binaryfiles", column: "thumbnail_id", name: "fk_images_thumbnail_id"
  add_foreign_key "measurements", "checks", name: "fk_measurements_check_id"
  add_foreign_key "measurements", "measures", name: "fk_measurements_measure_id"
  add_foreign_key "measurements", "toolunits", name: "fk_measurements_toolunit_id"
  add_foreign_key "measures", "steps", name: "fk_measures_step_id"
  add_foreign_key "measures", "tooltypes", name: "fk_measures_tooltype_id"
  add_foreign_key "models", "devicetypes"
  add_foreign_key "steps", "procedures", name: "fk_steps_procedure_id"
  add_foreign_key "toolunits", "tooltypes", name: "fk_toolunits_tooltype_id"
  add_foreign_key "units", "models", name: "fk_units_model_id"
end
