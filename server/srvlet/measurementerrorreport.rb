module MeasurementErrorReport

  @page_number = nil
  @language = nil
  @pdf = nil
  @params = nil
  @sort = nil
  @header_pos_y = nil
  @header_height = nil
  @footer_pos_y = nil
  @footer_height = nil
  @page_content_start = nil
  @page_content_width = nil
  @page_content_height = nil
  @pos_x = nil
  @previous_measurement_counter = nil
  @current_fill_color = nil


  # generate PDF
  def self.gen_pdf(params)

    # Header
    @header_pos_y = 10
    @header_height = 15

    # Footer
    @footer_pos_y = -20
    @footer_height = 10

    # Page Content
    @page_content_start = 297
    @page_content_width = 170
    @page_content_height = 32
    @pos_x = 15

    # Sorting
    #   Passed numeric  =>   key in hash to sort by
    #   (@sort_num)          (@sort_by)
    #   -------------------------------------------
    #   1               =>   'measure_code'
    #   2               =>   'previous_measurement_errorcategoryname'
    #   3               =>   'measurement_saved_by'
    #   4               =>   'unit_code'
    @sort_num = -1
    @sort_by = ''

    # other
    @current_fill_color = '000000'
    @previous_measurement_counter = 1
    @page_number = 1
    @language = nil
    @pdf = nil
    @params = nil

    @params = params

    # PDF data
    @report_data = []
    @report_record = {}

    # instanciate PDF
    Prawn::Document.new(page_size: 'A4', margin: 0, page_layout: :portrait, compress: true, print_scalign: :none) do |pdf|
      @pdf = pdf
      pdf_setup_fonts(@pdf, 'os')

      @language = pdf_read_language(@params['lang'] || $system_languages[0][:code])

      @period_start = Date.parse @params['periodStart']
      @period_end = Date.parse @params['periodEnd']

      @sort_num = @params['sort'].to_i

      insert_header_and_footer()

      @params['measures'].split(',').each do |measure_id|
        @report_record = {}

        measure = Measure.find(measure_id)
        step = Step.find(measure.step_id)
        procedure = Procedure.find(step.procedure_id)

        #
        # set up record
        #
        @report_record['measure_code'] = measure.code
        @report_record['measure_title'] = "#{measure.title[@params['lang']]}"
        case measure.measuretype
          when 1, 6
            # thold
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_target} #{measure.calculation['t1_targetv']} #{measure.calculation['t1_targetu']} #{@language.pdf_measurementerrorreport_expinput_thresh} #{measure.calculation['t1_tholdv']} #{measure.calculation['t1_tholdu']}"
          when 2, 7
            # absrng
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_valuerng1} #{measure.calculation['t2_minv']} #{measure.calculation['t2_unit']} #{@language.pdf_measurementerrorreport_expinput_valuerng2} #{measure.calculation['t2_maxv']} #{measure.calculation['t2_unit']}"
          when 8, 9
            # abs
            @report_record['measure_value_title'] = "#{@language.send("pdf_measurementerrorreport_expinput_valuecmp"+measure.calculation['t8_comparator'].to_s)}"
            if measure.calculation['t8_value'] == nil
              @report_record['measure_value_title'] += ' ----'
            else
              @report_record['measure_value_title'] += " #{ measure.calculation['t8_value']}#{measure.calculation['t8_unit']}"
            end
            @report_record['measure_value_title'].slice! '{{ val }}'
          when 11, 12
            # statst
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_statistic}"
          when 10
            # reschk
            @report_record['measure_value_title'] = "#{@language.send("pdf_measurementerrorreport_expinput_checkcmp"+measure.calculation['t10_comparator'].to_s)}"
            if measure.calculation['t10_value'] == nil
              @report_record['measure_value_title'] += ' ----'
            else
              @report_record['measure_value_title'] += " #{ measure.calculation['t10_value']}#{measure.calculation['t10_unit']}"
            end
            @report_record['measure_value_title'].slice! '{{ val }}'
          when 3
            # frtext
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_textlen1}: #{measure.calculation['t3_minlen']} #{@language.pdf_measurementerrorreport_expinput_textlen2}"
          when 4
            # regexp
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_regexp} #{measure.calculation['t4_regexp']}"
          when 5
            # flag
            if measure.calculation['t5_expected'] == 0
              @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_expno}"
            end
            if measure.calculation['t5_expected'] == 1
              @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_expyes}"
            end
            if measure.calculation['t5_expected'] == 2
              @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_expboth}"
            end
          when 13
            # timera
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_texttimerstart}"
          when 14
            # timers
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_texttimerstop}"
          when 15, 16
            # timerq || timerc
            if measure.calculation['t15_comparator'] === COMP_LESSTHAN
              @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_texttimerstoplt}"
            elsif measure.calculation['t15_comparator'] === COMP_MORETHAN
              @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_texttimerstopgt}"
            end
            if @report_record['measure_value_title']
              @report_record['measure_value_title'] = @report_record['measure_value_title'].gsub('{{ val }}', measure.calculation['t15_value'].to_s)
            end

          when 17
            # timerq || timerc
            @report_record['measure_value_title'] = "#{@language.pdf_measurementerrorreport_expinput_choicelist}"
          else
            @report_record['measure_value_title'] = '???'
        end
        @report_record['measure_value_title'] = '??' if @report_record['measure_value_title'] == nil || @report_record['measure_value_title'] == ''
        @report_record['procedure_code'] = procedure.code
        @report_record['procedure_title'] = procedure.title[@params['lang']]
        @report_record['step_code'] = step.code
        @report_record['step_title'] = step.title[@params['lang']]

        measure.measurements.reverse_each do |measurement|
          next if (measurement.previous_measurements.empty?)

          user = User.find(measurement.savedby)
          check = Check.find(measurement.check_id)
          model = Model.find(check.model_id)
          unit = Unit.find(check.unit_id)

          @report_record['model_code'] = model.code
          @report_record['model_title'] = model.title[@params['lang']]
          @report_record['unit_code'] = unit.code

          status = [@language.pdf_measurementerrorreport_status_unproc, @language.pdf_measurementerrorreport_status_passed, @language.pdf_measurementerrorreport_status_failed, @language.pdf_measurementerrorreport_status_skipped, @language.pdf_measurementerrorreport_status_invalid, @language.pdf_measurementerrorreport_status_error]
          @report_record['measurement_id'] = measurement.id
          @report_record['measurement_status_raw'] = measurement.status
          @report_record['measurement_status'] = status[measurement.status]

          measurement_value = nil
          if measurement.value && measurement.value.include?('([TIMS:')
            measurement_value = measurement.value[measurement.value.index('([TIMS:')+7, 10]

            # if somehow the timestamp's length is not 10, write ??? instead of throwing an error
            unless measurement_value.include? ')]'
              measurement_value = Time.at(measurement_value.to_i).strftime('%d.%m.%Y %H:%M')
            else
              measurement_value = '???'
            end
          end

          @report_record['measurement_value'] = measurement_value ? measurement_value : measurement.value
          @report_record['measurement_saved_by_username'] = user.username
          @report_record['measurement_saved_by_realname'] = user.realname
          @report_record['measurement_saved_by_timestamp'] = measurement.updated_at.strftime('%d.%m.%Y %H:%M')

          measurement[:previous_measurements].each do |previous_measurement|
            next if (previous_measurement['updated_at'].to_date < @period_start)
            next if (previous_measurement['updated_at'].to_date > @period_end)

            previous_measurement['errorcategoryname'] = MeasurementErrorCategory.find(previous_measurement['measurementerrorcategoryid']).name[@params['lang']]
            user = User.find(previous_measurement['savedby'])

            @report_record['previous_measurement_errorcategoryname'] = previous_measurement['errorcategoryname']
            status = [@language.pdf_measurementerrorreport_status_unproc, @language.pdf_measurementerrorreport_status_passed, @language.pdf_measurementerrorreport_status_failed, @language.pdf_measurementerrorreport_status_skipped, @language.pdf_measurementerrorreport_status_invalid, @language.pdf_measurementerrorreport_status_error]
            @report_record['previous_measurement_status_raw'] = previous_measurement['status']
            @report_record['previous_measurement_status'] = status[previous_measurement['status']]
            @report_record['previous_measurement_value'] = previous_measurement['value']
            @report_record['previous_measurement_saved_by_username'] = user.username
            @report_record['previous_measurement_saved_by_realname'] = user.realname
            @report_record['previous_measurement_saved_by_timestamp'] = previous_measurement['updated_at'].strftime('%d.%m.%Y %H:%M')


            @report_data << @report_record.clone
          end
        end
      end

      case @sort_num
        when 1
          @sort_by = 'measure_code'
          @report_data = @report_data.sort_by { |k| [k[@sort_by], k['unit_code'], k['previous_measurement_saved_by_timestamp']] }
        when 2
          @sort_by = 'previous_measurement_errorcategoryname'
          @report_data = @report_data.sort_by { |k| [k[@sort_by], k['measure_code'], k['previous_measurement_saved_by_timestamp']] }
        when 3
          @sort_by = 'previous_measurement_saved_by_realname'
          @report_data = @report_data.sort_by { |k| [k[@sort_by], k['measure_code'], k['previous_measurement_saved_by_timestamp']] }
        when 4
          @sort_by = 'unit_code'
          @report_data = @report_data.sort_by { |k| [k[@sort_by], k['measure_code'], k['previous_measurement_saved_by_timestamp']] }
        else
          @sort_by = 'measure_code'
          @report_data = @report_data.sort_by { |k| [k[@sort_by], k['unit_code'], k['previous_measurement_saved_by_timestamp']] }
      end

      puts @report_data

      @report_data.each_with_index do |record, idx|
        sort_header_inserted = false
        if idx == 0 || record[@sort_by] != @report_data[idx-1][@sort_by]
          if idx > 0
            page_break()
          end
          insert_sort_header(record)
          sort_header_inserted = true
        end
        if idx == 0 || record['measurement_id'] != @report_data[idx-1]['measurement_id'] || sort_header_inserted
          insert_recent_measurement(record)
        end
        insert_previous_measurement(record)
      end
    end.render
  end

  def self.insert_sort_header(record)
    height = 0
    @current_fill_color = 'B8D4EB'

    case @sort_by
      when 'measure_code'
        height = 33
        draw_rectangle(height)

        pdf_text @pdf, @pos_x+5, @page_content_height+3, @page_content_width, 8, "#{@language.pdf_measurementerrorreport_procedure} #{record['procedure_code']}: #{@language.pdf_measurementerrorreport_procedure} #{record['procedure_title']}, #{@language.pdf_measurementerrorreport_step} #{record['step_code']}: #{@language.pdf_measurementerrorreport_step} #{record['step_title']}", false, valign: :top, fontsize: 9, style: :bold
        pdf_text @pdf, @pos_x+5, @page_content_height+8, @page_content_width, 8, "#{@language.pdf_measurementerrorreport_measure} #{record['measure_code']}:", false, valign: :top, fontsize: 18, style: :bold, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+16, @page_content_width, 8, record['measure_title'], false, valign: :top, fontsize: 16
        pdf_text @pdf, @pos_x+5, @page_content_height+26, @page_content_width, 8, record['measure_value_title'], false, valign: :top, fontsize: 11

      when 'previous_measurement_errorcategoryname'
        height = 15
        draw_rectangle(height)

        pdf_text @pdf, @pos_x+5, @page_content_height+5, @page_content_width, 8, record['previous_measurement_errorcategoryname'], false, valign: :top, fontsize: 18, style: :bold, inline_format: true

      when 'previous_measurement_saved_by_realname'
        height = 22
        draw_rectangle(height)

        pdf_text @pdf, @pos_x+5, @page_content_height+5, @page_content_width, 8, "#{record['previous_measurement_saved_by_realname']}", false, valign: :top, fontsize: 18, style: :bold, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+13, @page_content_width, 8, "#{record['previous_measurement_saved_by_username']}", false, valign: :top, fontsize: 14, inline_format: true

      when 'unit_code'
        height = 22
        draw_rectangle(height)

        pdf_text @pdf, @pos_x+5, @page_content_height+5, @page_content_width, 8, "<b>#{@language.pdf_measurementerrorreport_unit}:</b> #{record['unit_code']}", false, valign: :top, fontsize: 18, style: :bold, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+13, @page_content_width, 8, "#{@language.pdf_measurementerrorreport_model}: #{record['model_code']}  -  #{record['model_title']}", false, valign: :top, fontsize: 14, inline_format: true
    end
    @page_content_height += height-1
  end

  def self.insert_recent_measurement(record)
    height = 0

    @pdf.font 'FontHdr'
    if record['measurement_status_raw'] == 1
      @current_fill_color = 'BED6C7'
    else
      @current_fill_color = 'D4A5A7'
    end

    case @sort_by
      when 'measure_code'
        height = 31
        @page_content_height += 5
        page_break?(height)
        draw_rectangle(height)

        @page_content_height += 3

        pdf_text @pdf, @pos_x+5, @page_content_height, @page_content_width, 8, "<b>#{@language.pdf_measurementerrorreport_model}:</b> #{record['model_code']}  -  #{record['model_title']}", true, valign: :top, fontsize: 14, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+6, @page_content_width, 5, "<b>#{@language.pdf_measurementerrorreport_unit}:</b> #{record['unit_code']}", true, valign: :top, fontsize: 12, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+13, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_status_title}</b> #{record['measurement_status']}", true, valign: :top, fontsize: 10, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+17, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_value}</b> #{record['measurement_value']}", true, valign: :top, fontsize: 10, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+21, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_savedby}</b> #{record['measurement_saved_by_realname']} (#{record['measurement_saved_by_username']}) - #{record['measurement_saved_by_timestamp']}", true, valign: :top, fontsize: 10, inline_format: true

      when 'previous_measurement_errorcategoryname', 'previous_measurement_saved_by_realname'
        height = 53
        @page_content_height += 5
        page_break?(height)

        draw_rectangle(height)

        @page_content_height += 3

        pdf_text @pdf, @pos_x+5, @page_content_height, @page_content_width, 8, "#{@language.pdf_measurementerrorreport_procedure} #{record['procedure_code']}: #{@language.pdf_measurementerrorreport_procedure} #{record['procedure_title']}, #{@language.pdf_measurementerrorreport_step} #{record['step_code']}: #{@language.pdf_measurementerrorreport_step} #{record['step_title']}", false, valign: :top, fontsize: 9, style: :bold
        pdf_text @pdf, @pos_x+5, @page_content_height+5, @page_content_width, 8, "#{@language.pdf_measurementerrorreport_measure} #{record['measure_code']}:", false, valign: :top, fontsize: 18, style: :bold, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+13, @page_content_width, 8, record['measure_title'], false, valign: :top, fontsize: 16
        pdf_text @pdf, @pos_x+5, @page_content_height+23, @page_content_width, 8, "<b>#{@language.pdf_measurementerrorreport_model}:</b> #{record['model_code']}  -  #{record['model_title']}", true, valign: :top, fontsize: 14, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+29, @page_content_width, 5, "<b>#{@language.pdf_measurementerrorreport_unit}:</b> #{record['unit_code']}", true, valign: :top, fontsize: 12, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+36, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_status_title}</b> #{record['measurement_status']}", true, valign: :top, fontsize: 10, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+40, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_value}</b> #{record['measurement_value']}", true, valign: :top, fontsize: 10, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+44, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_savedby}</b> #{record['measurement_saved_by_realname']} (#{record['measurement_saved_by_username']}) - #{record['measurement_saved_by_timestamp']}", true, valign: :top, fontsize: 10, inline_format: true

      when 'unit_code'
        height = 38
        @page_content_height += 5
        page_break?(height)

        draw_rectangle(height)

        @page_content_height += 3

        pdf_text @pdf, @pos_x+5, @page_content_height, @page_content_width, 8, "#{@language.pdf_measurementerrorreport_procedure} #{record['procedure_code']}: #{@language.pdf_measurementerrorreport_procedure} #{record['procedure_title']}, #{@language.pdf_measurementerrorreport_step} #{record['step_code']}: #{@language.pdf_measurementerrorreport_step} #{record['step_title']}", false, valign: :top, fontsize: 9, style: :bold
        pdf_text @pdf, @pos_x+5, @page_content_height+5, @page_content_width, 8, "#{@language.pdf_measurementerrorreport_measure} #{record['measure_code']}:", false, valign: :top, fontsize: 18, style: :bold, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+13, @page_content_width, 8, record['measure_title'], false, valign: :top, fontsize: 16
        pdf_text @pdf, @pos_x+5, @page_content_height+21, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_status_title}</b> #{record['measurement_status']}", true, valign: :top, fontsize: 10, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+25, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_value}</b> #{record['measurement_value']}", true, valign: :top, fontsize: 10, inline_format: true
        pdf_text @pdf, @pos_x+5, @page_content_height+29, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_savedby}</b> #{record['measurement_saved_by_realname']} (#{record['measurement_saved_by_username']}) - #{record['measurement_saved_by_timestamp']}", true, valign: :top, fontsize: 10, inline_format: true
    end

    @page_content_height += height-1
    @previous_measurement_counter = 0
  end

  def self.insert_previous_measurement(record)
    height = 22
    page_break?(height)

    unless @previous_measurement_counter & 1 > 0
      @pdf.font 'FontHdr'
      @current_fill_color = 'e0e0e0'
      draw_rectangle(height)
    end

    @page_content_height += 3

    pdf_text @pdf, @pos_x+5, @page_content_height, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_errorcategory}</b> #{record['previous_measurement_errorcategoryname']}", true, valign: :top, fontsize: 10, inline_format: true
    pdf_text @pdf, @pos_x+5, @page_content_height+4, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_status_title}</b> #{record['previous_measurement_status']}", true, valign: :top, fontsize: 10, inline_format: true
    pdf_text @pdf, @pos_x+5, @page_content_height+8, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_value}</b> #{record['previous_measurement_value']}", true, valign: :top, fontsize: 10, inline_format: true
    pdf_text @pdf, @pos_x+5, @page_content_height+12, @page_content_width, 4, "<b>#{@language.pdf_measurementerrorreport_savedby}</b> #{record['previous_measurement_saved_by_realname']} (#{record['previous_measurement_saved_by_username']}) - #{record['previous_measurement_saved_by_timestamp']}", true, valign: :top, fontsize: 10, inline_format: true

    @previous_measurement_counter += 1
    @page_content_height += height-1
  end

  def self.draw_rectangle(height)
    @pdf.fill_color @current_fill_color
    @pdf.fill {
      @pdf.rectangle [@pos_x.mm, (@page_content_start-@page_content_height).mm], 180.mm, height.mm
    }
    @current_fill_color = '000000'
    @pdf.fill_color @current_fill_color
  end

  def self.page_break?(height)
    if @page_content_start-@page_content_height-@footer_height <= height+10
      page_break()
    end
  end

  def self.page_break()
    @pdf.start_new_page
    @page_number += 1
    @page_content_height = 32

    # insert header and footer
    insert_header_and_footer()
  end

  def self.insert_header_and_footer
    @pdf.font 'FontHdr'
    @pdf.fill_color '005499'
    @pdf.fill {
      @pdf.rectangle [@pos_x.mm, 287.mm], 180.mm, 15.mm
      @pdf.rectangle [@pos_x.mm, 20.mm], 180.mm, 10.mm
    }
    @pdf.fill_color 'ffffff'

    # header
    text = @language.pdf_measurementerrorreport_title
    pdf_text @pdf, @pos_x+5, @header_pos_y, @page_content_width, @header_height, text, true, valign: :center, fontsize: 24

    # footer
    # text = (@language.pdf_tureport_footer % [unit.tooltype.code, unit.code])
    text = @language.formatd(Time.now)
    pdf_text @pdf, @pos_x+5, @header_pos_y, @page_content_width, @header_height, text, true, style: :bold, fontsize: 16, valign: :center, halign: :right
    pdf_text @pdf, @pos_x+5, @footer_pos_y, @page_content_width, @footer_height, text, true, fontsize: 14, valign: :center, style: :bold, autofit: true, inline_format: true
    pdf_text @pdf, @pos_x+5, @footer_pos_y, @page_content_width, @footer_height, "#{@page_number}", true, fontsize: 16, valign: :center, halign: :right

    @pdf.fill_color @current_fill_color
  end
end