{"name": "qa-protoyp", "version": "0.1.0", "private": true, "dependencies": {"bookshelf": "0.5.7", "easyimage": "0.1.3", "express": "3.2.6", "jade": "0.31.2", "less-middleware": "0.1.12", "moment": "2.2.1", "underscore.string": "2.3.3", "when": "2.4.0", "@bower_components/CryptoJS": "karolmajta/crypto-js#3.1.12", "@bower_components/angular": "angular/bower-angular#1.3.15", "@bower_components/angular-bootstrap": "angular-ui/bootstrap-bower#~0.13.0", "@bower_components/angular-cookies": "angular/bower-angular-cookies#1.3.15", "@bower_components/angular-feature-flags": "mjt01/angular-feature-flags#~1.1.0", "@bower_components/angular-file-upload": "nervgh/angular-file-upload#1.1.5", "@bower_components/angular-mocks": "angular/bower-angular-mocks#1.3.15", "@bower_components/angular-sanitize": "angular/bower-angular-sanitize#1.3.15", "@bower_components/angular-spinner": "Attrash-Islam/bower-angular-spinner#0.6.1", "@bower_components/angular-timer": "siddii/angular-timer#1.3.5", "@bower_components/angular-touch": "angular/bower-angular-touch#1.3.15", "@bower_components/angular-translate": "PascalPrecht/bower-angular-translate#2.7.0", "@bower_components/angular-translate-loader-static-files": "PascalPrecht/bower-angular-translate-loader-static-files#2.7.0", "@bower_components/angular-ui-router": "angular-ui/angular-ui-router-bower#0.2.15", "@bower_components/angular-ui-select": "angular-ui/ui-select#~0.18.0", "@bower_components/angular-ui-sortable": "angular-ui/ui-sortable#0.13.3", "@bower_components/angular-ui-utils": "angular-ui/ui-utils#0.2.3", "@bower_components/bootstrap": "twbs/bootstrap#3.1.1", "@bower_components/classie": "desandro/classie#~1.0.1", "@bower_components/draggabilly": "desandro/draggabilly#1.2.4", "@bower_components/es5-shim": "es-shims/es5-shim#>=3.4.0", "@bower_components/eventEmitter": "Olical/EventEmitter#~4.2.11", "@bower_components/eventie": "desandro/eventie#~1.0.6", "@bower_components/fastclick": "ftlabs/fastclick#1.0.6", "@bower_components/font-awesome": "FortAwesome/Font-Awesome#>=4.0.x", "@bower_components/get-size": "desandro/get-size#~1.2.2", "@bower_components/get-style-property": "desandro/get-style-property#1.x", "@bower_components/humanize-duration": "EvanHahn/HumanizeDuration.js#~3.10.0", "@bower_components/jquery": "jquery/jquery-dist#2.1.4", "@bower_components/jquery-ui": "components/jqueryui#1.11.4", "@bower_components/lodash": "lodash/lodash#4.17.4", "@bower_components/moment": "moment/moment#~2.9.0", "@bower_components/rangy": "timdown/rangy-release#1.3.0", "@bower_components/restangular": "mgonto/restangular#1.6.1", "@bower_components/spin.js": "fgnass/spin.js#2.3.1", "@bower_components/sprintf": "alexei/sprintf.js#1.0.2", "@bower_components/textAngular": "fraywing/textAngular#1.5.16", "@bower_components/unidragger": "metafizzy/unidragger#~1.1.0", "@bower_components/unipointer": "metafizzy/unipointer#~1.1.0", "@bower_components/videogular": "2fdevs/bower-videogular#0.4.2", "@bower_components/videogular-buffering": "2fdevs/bower-videogular-buffering#0.4.2", "@bower_components/videogular-controls": "2fdevs/bower-videogular-controls#0.4.2", "@bower_components/videogular-themes-default": "2fdevs/bower-videogular-themes-default#0.4.2"}, "devDependencies": {"grunt-eslint": "^19.0.0", "install": "^0.8.2", "karma": "^1.3.0", "karma-coverage": "^1.1.1", "karma-jasmine": "^1.1.0", "karma-phantomjs-launcher": "^1.0.2", "load-grunt-tasks": "^3.5.2", "npm": "^3.10.9"}, "engines": {"yarn": ">= 1.0.0"}, "scripts": {"postinstall": "node -e \"try { require('fs').symlinkSync(require('path').resolve('node_modules/@bower_components'), 'components', 'junction') } catch (e) { }\""}}