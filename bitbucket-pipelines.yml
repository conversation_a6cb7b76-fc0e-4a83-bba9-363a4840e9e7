# This is a sample build configuration for <PERSON>.
# Check our guides at https://confluence.atlassian.com/x/8r-5Mw for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: ruby:2.3.8

pipelines:

  # triggered by pushes / merges
  default:
    - step:
        name: Migration Tests (Java)
        image: maven:3.5.2-jdk-8-alpine
        script:

        # run tests with maven
        - cd LlqaMigration_3_0/
        - mvn test
        - cd ..

    - step:
        name: Tests
        script:

          # update OS
          - apt-get update -y
          - apt-get install curl software-properties-common -y
          - curl -sL https://deb.nodesource.com/setup_11.x | bash -
          - apt-get install nodejs

          # install dependencies
          - npm install -g yarn
          - yarn
          - cd ./server/
          - bundle install

          # test db by running migration
          - bundle exec rake db:migrate

          # run application and wait 10 seconds, if no error was thrown, app runs successfully. Need to kill it
          - ruby server.rb &
          - sleep 10
          - kill $!
          - cd ..

        services:
        - postgres

  # manually triggered pipelines
  branches:

    # to testserver (demo.llqa.ch)
    development:
      - step:
          name: SonarQube Analysis
          image: node:latest
          script:
            - npm install -g sonarqube-scanner
            - sonar-scanner -X -Dsonar.host.url=$SONARQUBE_SERVER -Dsonar.login=$SONARQUBE_TOKEN -Dsonar.projectKey=llqa -Dsonar.projectName=LLQA-2 -Dsonar.sources=./ -Dsonar.test.inclusions=**/*.spec.ts -Dsonar.java.binaries=LlqaMigration_3_0/src/ -Dsonar.exclusions=**/node_modules/**,**/tests/**,**/postgres-data/**,**/doc/**,**/public/less/**,**/css/**,**/public/lib/**

      - step:
          name: Migration Tests (Java)
          image: maven:3.5.2-jdk-8-alpine
          script:

          # run tests with maven
          - cd LlqaMigration_3_0/
          - mvn test
          - cd ..

      - step:
          name: Tests
          script:

            # update OS
            - apt-get update -y
            - apt-get install curl software-properties-common -y
            - curl -sL https://deb.nodesource.com/setup_11.x | bash -
            - apt-get install nodejs

              # install dependencies
            - npm install -g yarn
            - yarn
            - cd ./server/
            - bundle install

            # test db by running migration
            - bundle exec rake db:migrate

            # run application and wait 10 seconds, if no error was thrown, app runs successfully. Need to kill it
            - ruby server.rb &
            - sleep 10
            - kill $!
            - cd ..

          services:
          - postgres

      - step:
          name: Deployment Testserver

          # only deploy when triggered manually
          trigger: manual
          script:
            # get app version from git and remove the leading character 'v' in order to have the plain
            # version number. Use to version for the docker image deployment
            - version=$(git describe --tags --abbrev=0)
            - version=${version:1}

            # build docker image and push to repo
            - docker login $NEXUS_SERVER -u $NEXUS_USER -p $NEXUS_PASSWORD
            - docker build -t $NEXUS_SERVER/diso/llqa:$version .
            - docker push $NEXUS_SERVER/diso/llqa:$version
            - docker tag $NEXUS_SERVER/diso/llqa:$version $NEXUS_SERVER/diso/llqa:latest
            - docker push $NEXUS_SERVER/diso/llqa:latest

            # prepare connection to server over ssh
            - mkdir -p ~/.ssh
            - cat known_hosts >> ~/.ssh/known_hosts
            - (umask  077 ; echo $SSH_KEY | base64 --decode > ~/.ssh/ssh_key)

            # connect to server, pull previously built docker image and run it
            - version="$version-$BITBUCKET_BUILD_NUMBER"
            - ssh -i ~/.ssh/ssh_key -t <EMAIL> "sudo sh /usr/src/llqa/deliver.sh $version"

    master:
      - step:
          name: SonarQube Analysis
          image: node:latest
          script:
            - npm install -g sonarqube-scanner
            - sonar-scanner -X -Dsonar.host.url=$SONARQUBE_SERVER -Dsonar.login=$SONARQUBE_TOKEN -Dsonar.projectKey=llqa -Dsonar.projectName=LLQA-2 -Dsonar.sources=./ -Dsonar.test.inclusions=**/*.spec.ts -Dsonar.java.binaries=LlqaMigration_3_0/src/ -Dsonar.exclusions=**/node_modules/**,**/tests/**,**/postgres-data/**,**/doc/**,**/public/less/**,**/css/**,**/public/lib/**

      - step:
          name: Migration Tests (Java)
          image: maven:3.5.2-jdk-8-alpine
          script:

          # run tests with maven
          - cd LlqaMigration_3_0/
          - mvn test
          - cd ..

      - step:
          name: Test
          script:

            # update OS
            - apt-get update -y
            - apt-get install curl software-properties-common -y
            - curl -sL https://deb.nodesource.com/setup_11.x | bash -
            - apt-get install nodejs

              # install dependencies
            - npm install -g yarn
            - yarn
            - cd ./server/
            - bundle install

            # test db by running migration
            - bundle exec rake db:migrate

            # run application and wait 10 seconds, if no error was thrown, app runs successfully. Need to kill it
            - ruby server.rb &
            - sleep 10
            - kill $!
            - cd ..

          services:
          - postgres


#      - step:
#          name: Deployment Production
#
#          # only deploy when triggered manually
#          trigger: manual
#          script:
#
#            # get app version from git and remove the leading character 'v' in order to have the plain
#            # version number. Use to version for the docker image deployment
#            - version=$(git describe --tags --abbrev=0)
#            - version=${version:1}
#
#            # build docker image and push to repo
#            - docker login $NEXUS_SERVER -u $NEXUS_USER -p $NEXUS_PASSWORD
#            - docker build -t $NEXUS_SERVER/diso/llqa:$version .
#            - docker push $NEXUS_SERVER/diso/llqa:$version
#            - docker tag $NEXUS_SERVER/diso/llqa:$version $NEXUS_SERVER/diso/llqa:latest
#            - docker push $NEXUS_SERVER/diso/llqa:latest
#
#            # prepare connection to server over ssh
#            - mkdir -p ~/.ssh
#            - cat known_hosts >> ~/.ssh/known_hosts
#            - (umask  077 ; echo $SSH_KEY | base64 --decode > ~/.ssh/ssh_key)
#
#            # connect to server, pull previously built docker image and run it
#            - ssh -i ~/.ssh/ssh_key -t ubuntu@************* "sudo sh /usr/src/llqa/deliver.sh $version"

  custom:
    Migrate-Dumps:
      - step:
          name: Migrate Dumps (Java)
          image: maven:3.6.1-jdk-8
          # only deploy when triggered manually
          script:
            # setup postgres cli tools
            - apt-get update
            - wget -O - http://apt.postgresql.org/pub/repos/apt/ACCC4CF8.asc | apt-key add -
            - sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt/ stretch-pgdg main" >> /etc/apt/sources.list.d/pgdg.list'
            - apt-get update && apt-get -y install postgresql-client-11

            # setup ruby environment
            - apt-get update && apt-get install libpq-dev -y
            - apt-get update && apt-get install ruby2.3 -y
            - apt-get update && apt-get install ruby-dev build-essential -y
            - gem install activerecord
            - gem install pg -v 1.1.4

            # setup SSH key environment
            - mkdir -p ~/.ssh
            - cat known_hosts >> ~/.ssh/known_hosts
            - (umask  077 ; echo $LLQA3_SSH_KEY | base64 --decode > ~/.ssh/id_rsa)

            # copy LLQA2 dumps which will be migrated
            - mkdir -p llqa2_dumps
            - scp -i ~/.ssh/id_rsa $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA2_DB_DUMP_PATH/$DB_DUMP_SCHL llqa2_dumps/$DB_DUMP_SCHL
            - scp -i ~/.ssh/id_rsa $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA2_DB_DUMP_PATH/$DB_DUMP_GF llqa2_dumps/$DB_DUMP_GF
            - scp -i ~/.ssh/id_rsa $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA2_DB_DUMP_PATH/$DB_DUMP_GF_TEST llqa2_dumps/$DB_DUMP_GF_TEST
            - scp -i ~/.ssh/id_rsa $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA2_DB_DUMP_PATH/$DB_DUMP_HPL llqa2_dumps/$DB_DUMP_HPL
            - scp -i ~/.ssh/id_rsa $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA2_DB_DUMP_PATH/$DB_DUMP_OW llqa2_dumps/$DB_DUMP_OW

            # migrate schleuniger
            - sh LlqaMigration_3_0/src/main/resources/sql/restoreAndMigrateDbSchleuniger.sh $LLQA_POSTGRES_HOST llqa2_dumps/$DB_DUMP_SCHL $LLQA_POSTGRES_USERNAME $LLQA_POSTGRES_DB $LLQA_POSTGRES_PASSWORD
            - rm llqa2_dumps/$DB_DUMP_SCHL
            - pg_dump -h $LLQA_POSTGRES_HOST -U $LLQA_POSTGRES_USERNAME -Ft $LLQA_POSTGRES_DB > llqa2_dumps/$DB_DUMP_SCHL

            # migrate georgfischer
            - sh LlqaMigration_3_0/src/main/resources/sql/restoreAndMigrateDbGeorgfischer.sh $LLQA_POSTGRES_HOST llqa2_dumps/$DB_DUMP_GF $LLQA_POSTGRES_USERNAME $LLQA_POSTGRES_DB $LLQA_POSTGRES_PASSWORD
            - rm llqa2_dumps/$DB_DUMP_GF
            - pg_dump -h $LLQA_POSTGRES_HOST -U $LLQA_POSTGRES_USERNAME -Ft $LLQA_POSTGRES_DB > llqa2_dumps/$DB_DUMP_GF

            # migrate georgfischertest
            - sh LlqaMigration_3_0/src/main/resources/sql/restoreAndMigrateDbGeorgfischertest.sh $LLQA_POSTGRES_HOST llqa2_dumps/$DB_DUMP_GF_TEST $LLQA_POSTGRES_USERNAME $LLQA_POSTGRES_DB $LLQA_POSTGRES_PASSWORD
            - rm llqa2_dumps/$DB_DUMP_GF_TEST
            - pg_dump -h $LLQA_POSTGRES_HOST -U $LLQA_POSTGRES_USERNAME -Ft $LLQA_POSTGRES_DB > llqa2_dumps/$DB_DUMP_GF_TEST

            # migrate hptest
            - sh LlqaMigration_3_0/src/main/resources/sql/restoreAndMigrateDbHptest.sh $LLQA_POSTGRES_HOST llqa2_dumps/$DB_DUMP_HPL $LLQA_POSTGRES_USERNAME $LLQA_POSTGRES_DB $LLQA_POSTGRES_PASSWORD
            - rm llqa2_dumps/$DB_DUMP_HPL
            - pg_dump -h $LLQA_POSTGRES_HOST -U $LLQA_POSTGRES_USERNAME -Ft $LLQA_POSTGRES_DB > llqa2_dumps/$DB_DUMP_HPL

            # migrate osterwalder
            - sh LlqaMigration_3_0/src/main/resources/sql/restoreAndMigrateDbOsterwalder.sh $LLQA_POSTGRES_HOST llqa2_dumps/$DB_DUMP_OW $LLQA_POSTGRES_USERNAME $LLQA_POSTGRES_DB $LLQA_POSTGRES_PASSWORD
            - rm llqa2_dumps/$DB_DUMP_OW
            - pg_dump -h $LLQA_POSTGRES_HOST -U $LLQA_POSTGRES_USERNAME -Ft $LLQA_POSTGRES_DB > llqa2_dumps/$DB_DUMP_OW

            # copy migrated LLQA3 dumps to DEV and STG
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_SCHL $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA3_DB_DUMP_PATH/$DB_DUMP_SCHL
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_SCHL $DELIVERY_USER_STG@$DELIVERY_SERVER_STG:$LLQA3_DB_DUMP_PATH/$DB_DUMP_SCHL
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_GF $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA3_DB_DUMP_PATH/$DB_DUMP_GF
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_GF $DELIVERY_USER_STG@$DELIVERY_SERVER_STG:$LLQA3_DB_DUMP_PATH/$DB_DUMP_GF
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_GF_TEST $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA3_DB_DUMP_PATH/$DB_DUMP_GF_TEST
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_GF_TEST $DELIVERY_USER_STG@$DELIVERY_SERVER_STG:$LLQA3_DB_DUMP_PATH/$DB_DUMP_GF_TEST
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_HPL $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA3_DB_DUMP_PATH/$DB_DUMP_HPL
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_HPL $DELIVERY_USER_STG@$DELIVERY_SERVER_STG:$LLQA3_DB_DUMP_PATH/$DB_DUMP_HPL
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_OW $DELIVERY_USER_DEV@$DELIVERY_SERVER_DEV:$LLQA3_DB_DUMP_PATH/$DB_DUMP_OW
            - scp -i ~/.ssh/id_rsa llqa2_dumps/$DB_DUMP_OW $DELIVERY_USER_STG@$DELIVERY_SERVER_STG:$LLQA3_DB_DUMP_PATH/$DB_DUMP_OW

          services:
            - postgres
definitions:
  services:
      postgres:
        image: postgres:11
        environment:
          POSTGRES_DB: llqa
          POSTGRES_USER: llqa
          POSTGRES_PASSWORD: access4llqa
options:
  docker: true