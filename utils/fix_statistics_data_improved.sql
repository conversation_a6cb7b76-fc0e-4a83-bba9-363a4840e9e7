-- LLQA v3.0 Verbesserte Statistikdaten Reparatur
-- Behebt das Problem fehlender Statistikdaten nach der Migration
-- 
-- Verbesserungen gegenüber der ursprünglichen Lösung:
-- - Robustere Fehlerbehandlung
-- - Bessere Performance durch optimierte Abfragen
-- - Umfassendere Diagnose und Verifikation
-- - Berücksichtigung von Edge Cases
--
-- Autor: Augment Agent (basierend auf Ana<PERSON><PERSON> von <PERSON>'s Lösung)
-- Datum: 2025-08-13
-- Ticket: LLQA-2

-- =====================================================
-- SCHRITT 1: UMFASSENDE DIAGNOSE
-- =====================================================

-- Backup-Tabelle erstellen (Sicherheit)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'checks_checkdata_backup_improved') THEN
        CREATE TABLE checks_checkdata_backup_improved AS 
        SELECT id, checkdata, updated_at FROM checks 
        WHERE checkdata IS NOT NULL AND checkdata != '{}';
        
        RAISE NOTICE 'Backup-Tabelle checks_checkdata_backup_improved erstellt mit % Einträgen', 
                     (SELECT COUNT(*) FROM checks_checkdata_backup_improved);
    ELSE
        RAISE NOTICE 'Backup-Tabelle checks_checkdata_backup_improved existiert bereits';
    END IF;
END $$;

-- Detaillierte Diagnose vor der Reparatur
WITH diagnosis AS (
    SELECT 
        c.id,
        c.checkdata,
        COALESCE(jsonb_array_length(c.checkdata->'procedures'), 0) as procedure_count,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc
            WHERE (proc->>'status')::int > 0
        ) as procedures_with_status,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
            WHERE (measure->>'status')::int > 0
        ) as measures_with_status,
        (
            SELECT COUNT(*)
            FROM measurements m
            WHERE m.check_id = c.id AND m.status > 0
        ) as measurements_with_data
    FROM checks c
    WHERE c.checkdata IS NOT NULL 
      AND c.checkdata != '{}' 
      AND c.checkdata->'procedures' IS NOT NULL
)
SELECT 
    'Diagnose vor Reparatur' as info,
    COUNT(*) as total_checks,
    SUM(procedure_count) as total_procedures,
    SUM(procedures_with_status) as procedures_with_status,
    SUM(measures_with_status) as measures_with_status,
    SUM(measurements_with_data) as measurements_with_data,
    COUNT(CASE WHEN measurements_with_data > 0 AND measures_with_status = 0 THEN 1 END) as problematic_checks
FROM diagnosis;

-- =====================================================
-- SCHRITT 2: VERBESSERTE REPARATUR-LOGIK
-- =====================================================

-- Funktion zur Status-Berechnung erstellen
CREATE OR REPLACE FUNCTION calculate_measure_status_improved(p_check_id BIGINT, p_measure_id BIGINT)
RETURNS INTEGER AS $$
DECLARE
    measurement_status INTEGER;
BEGIN
    -- Neueste Messung für diese Measure holen
    SELECT m.status INTO measurement_status
    FROM measurements m
    WHERE m.check_id = p_check_id 
      AND m.measure_id = p_measure_id
    ORDER BY m.updated_at DESC, m.id DESC
    LIMIT 1;
    
    -- Status-Mapping: measurements.status → checkdata.status
    RETURN CASE 
        WHEN measurement_status = 1 THEN 10  -- PASSED
        WHEN measurement_status = 2 THEN 15  -- FAILED
        WHEN measurement_status = 3 THEN 30  -- SKIPPED
        WHEN measurement_status = 4 THEN 15  -- INVALID → FAILED
        WHEN measurement_status = 5 THEN 15  -- ERROR → FAILED
        ELSE 0  -- TODO (default)
    END;
END;
$$ LANGUAGE plpgsql;

-- Hauptreparatur mit verbesserter Logik
UPDATE checks 
SET checkdata = (
    WITH updated_procedures AS (
        SELECT jsonb_agg(
            jsonb_set(
                jsonb_set(
                    proc,
                    '{status}',
                    to_jsonb(
                        CASE 
                            -- Alle Steps abgeschlossen?
                            WHEN (
                                SELECT COUNT(*)
                                FROM jsonb_array_elements(proc->'steps') as step
                                WHERE (step->>'status')::int IN (10, 13, 15)
                            ) = COALESCE(jsonb_array_length(proc->'steps'), 0) 
                            AND COALESCE(jsonb_array_length(proc->'steps'), 0) > 0 THEN
                                CASE 
                                    -- Mindestens ein Step failed?
                                    WHEN (
                                        SELECT COUNT(*)
                                        FROM jsonb_array_elements(proc->'steps') as step
                                        WHERE (step->>'status')::int = 15
                                    ) > 0 THEN 15  -- FAILED
                                    -- Mindestens ein Step mit Warnings?
                                    WHEN (
                                        SELECT COUNT(*)
                                        FROM jsonb_array_elements(proc->'steps') as step
                                        WHERE (step->>'status')::int = 13
                                    ) > 0 THEN 13  -- WARNINGS
                                    ELSE 10  -- PASSED
                                END
                            -- Teilweise abgeschlossen?
                            WHEN (
                                SELECT COUNT(*)
                                FROM jsonb_array_elements(proc->'steps') as step
                                WHERE (step->>'status')::int > 0
                            ) > 0 THEN 10  -- PASSED (partial)
                            ELSE 0  -- TODO
                        END
                    )
                ),
                '{steps}',
                (
                    SELECT jsonb_agg(
                        jsonb_set(
                            jsonb_set(
                                step,
                                '{status}',
                                to_jsonb(
                                    CASE 
                                        -- Alle Measures abgeschlossen?
                                        WHEN (
                                            SELECT COUNT(*)
                                            FROM jsonb_array_elements(step->'measures') as measure
                                            WHERE (measure->>'status')::int IN (10, 13, 15)
                                        ) = COALESCE(jsonb_array_length(step->'measures'), 0)
                                        AND COALESCE(jsonb_array_length(step->'measures'), 0) > 0 THEN
                                            CASE 
                                                -- Mindestens eine Measure failed?
                                                WHEN (
                                                    SELECT COUNT(*)
                                                    FROM jsonb_array_elements(step->'measures') as measure
                                                    WHERE (measure->>'status')::int = 15
                                                ) > 0 THEN 15  -- FAILED
                                                -- Mindestens eine Measure mit Warnings?
                                                WHEN (
                                                    SELECT COUNT(*)
                                                    FROM jsonb_array_elements(step->'measures') as measure
                                                    WHERE (measure->>'status')::int = 13
                                                ) > 0 THEN 13  -- WARNINGS
                                                ELSE 10  -- PASSED
                                            END
                                        -- Teilweise abgeschlossen?
                                        WHEN (
                                            SELECT COUNT(*)
                                            FROM jsonb_array_elements(step->'measures') as measure
                                            WHERE (measure->>'status')::int > 0
                                        ) > 0 THEN 10  -- PASSED (partial)
                                        ELSE 0  -- TODO
                                    END
                                )
                            ),
                            '{measures}',
                            (
                                SELECT jsonb_agg(
                                    jsonb_set(
                                        measure,
                                        '{status}',
                                        to_jsonb(
                                            COALESCE(
                                                calculate_measure_status_improved(
                                                    checks.id, 
                                                    (measure->>'id')::bigint
                                                ),
                                                0
                                            )
                                        )
                                    )
                                )
                                FROM jsonb_array_elements(step->'measures') as measure
                            )
                        )
                    )
                    FROM jsonb_array_elements(proc->'steps') as step
                )
            )
        ) as updated_procedures
        FROM jsonb_array_elements(checks.checkdata->'procedures') as proc
    )
    SELECT jsonb_set(
        jsonb_set(
            checks.checkdata,
            '{procedures}',
            updated_procedures.updated_procedures
        ),
        '{updated_at}',
        to_jsonb(NOW()::text)
    )
    FROM updated_procedures
),
updated_at = NOW()
WHERE checkdata IS NOT NULL 
  AND checkdata != '{}' 
  AND checkdata->'procedures' IS NOT NULL
  AND EXISTS (
      SELECT 1 FROM measurements m 
      WHERE m.check_id = checks.id AND m.status > 0
  );

-- Hilfsfunktion wieder entfernen
DROP FUNCTION IF EXISTS calculate_measure_status_improved(BIGINT, BIGINT);

-- =====================================================
-- SCHRITT 3: UMFASSENDE VERIFIKATION
-- =====================================================

-- Verifikation nach der Reparatur
WITH verification AS (
    SELECT 
        c.id,
        COALESCE(jsonb_array_length(c.checkdata->'procedures'), 0) as procedure_count,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc
            WHERE (proc->>'status')::int > 0
        ) as procedures_with_status,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
            WHERE (measure->>'status')::int > 0
        ) as measures_with_status,
        (
            SELECT COUNT(*)
            FROM measurements m
            WHERE m.check_id = c.id AND m.status > 0
        ) as measurements_with_data
    FROM checks c
    WHERE c.checkdata IS NOT NULL 
      AND c.checkdata != '{}' 
      AND c.checkdata->'procedures' IS NOT NULL
)
SELECT 
    'Verifikation nach Reparatur' as info,
    COUNT(*) as total_checks,
    SUM(procedure_count) as total_procedures,
    SUM(procedures_with_status) as procedures_with_status,
    SUM(measures_with_status) as measures_with_status,
    SUM(measurements_with_data) as measurements_with_data,
    ROUND(
        CASE 
            WHEN SUM(procedure_count) > 0 
            THEN (SUM(procedures_with_status)::decimal / SUM(procedure_count)) * 100 
            ELSE 0 
        END, 2
    ) as percentage_procedures_repaired,
    COUNT(CASE WHEN measurements_with_data > 0 AND measures_with_status = 0 THEN 1 END) as remaining_problematic_checks
FROM verification;

-- Beispiel der reparierten Daten anzeigen
SELECT 
    'Reparierte Daten Beispiel' as info,
    c.id as check_id,
    jsonb_pretty(c.checkdata->'procedures'->0->'steps'->0->'measures'->0) as first_measure_example
FROM checks c
WHERE c.checkdata IS NOT NULL 
  AND c.checkdata != '{}' 
  AND c.checkdata->'procedures' IS NOT NULL
  AND jsonb_array_length(c.checkdata->'procedures') > 0
  AND (c.checkdata->'procedures'->0->'steps'->0->'measures'->0->>'status')::int > 0
LIMIT 1;

-- =====================================================
-- SCHRITT 4: PERFORMANCE-OPTIMIERUNG
-- =====================================================

-- Index für bessere Performance erstellen (falls nicht vorhanden)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checks_checkdata_gin_improved 
ON checks USING gin (checkdata);

-- Index für measurements Abfragen
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_measurements_check_measure_status 
ON measurements (check_id, measure_id, status, updated_at);

-- Tabellenstatistiken aktualisieren
ANALYZE checks;
ANALYZE measurements;

-- =====================================================
-- ABSCHLUSS
-- =====================================================

SELECT 
    'Verbesserte Statistikdaten-Reparatur abgeschlossen!' as status,
    NOW() as timestamp,
    'Backup verfügbar in: checks_checkdata_backup_improved' as backup_info;
