-- LLQA v3.0 Statistikdaten Reparatur-Skript
-- Behebt das Problem fehlender Statistikdaten nach der Migration
-- 
-- Problem: Die checkdata-Struktur in der checks Tabelle enthält keine gültigen Status-Werte
-- für procedures, steps und measures, was zu leeren Statistiken im Frontend führt.
--
-- Autor: <PERSON>
-- Datum: 2025-08-13
-- Ticket: LLQA-2

-- =====================================================
-- SCHRITT 1: DIAGNOSE - Aktuellen Zustand prüfen
-- =====================================================

-- Anzahl Checks mit checkdata
SELECT 
    'Checks mit checkdata' as info,
    COUNT(*) as anzahl
FROM checks 
WHERE checkdata IS NOT NULL AND checkdata != '{}';

-- Beispiel einer checkdata-Struktur anzeigen
SELECT 
    'Beispiel checkdata' as info,
    id,
    LEFT(checkdata::text, 200) as checkdata_preview
FROM checks 
WHERE checkdata IS NOT NULL AND checkdata != '{}' 
LIMIT 1;

-- Prüfen ob Status-Werte in checkdata vorhanden sind
WITH status_check AS (
    SELECT 
        id,
        checkdata,
        jsonb_array_length(checkdata->'procedures') as proc_count,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(checkdata->'procedures') as proc
            WHERE (proc->>'status')::int > 0
        ) as proc_with_status
    FROM checks 
    WHERE checkdata IS NOT NULL 
      AND checkdata != '{}' 
      AND checkdata->'procedures' IS NOT NULL
)
SELECT 
    'Status-Verteilung in checkdata' as info,
    COUNT(*) as total_checks,
    SUM(proc_count) as total_procedures,
    SUM(proc_with_status) as procedures_with_status,
    ROUND(
        CASE 
            WHEN SUM(proc_count) > 0 
            THEN (SUM(proc_with_status)::decimal / SUM(proc_count)) * 100 
            ELSE 0 
        END, 2
    ) as percentage_with_status
FROM status_check;

-- =====================================================
-- SCHRITT 2: REPARATUR - Status-Werte korrigieren
-- =====================================================

-- Backup der aktuellen checkdata erstellen (optional)
-- CREATE TABLE checks_checkdata_backup AS 
-- SELECT id, checkdata FROM checks WHERE checkdata IS NOT NULL;

-- Status-Werte basierend auf measurements korrigieren
UPDATE checks 
SET checkdata = (
    SELECT jsonb_set(
        jsonb_set(
            checkdata,
            '{procedures}',
            (
                SELECT jsonb_agg(
                    jsonb_set(
                        jsonb_set(
                            proc,
                            '{status}',
                            to_jsonb(
                                CASE 
                                    -- Procedure Status basierend auf Steps berechnen
                                    WHEN (
                                        SELECT COUNT(*)
                                        FROM jsonb_array_elements(proc->'steps') as step
                                        WHERE (step->>'status')::int IN (10, 13, 15) -- FINISHED
                                    ) = jsonb_array_length(proc->'steps') THEN 10 -- PASSED
                                    WHEN (
                                        SELECT COUNT(*)
                                        FROM jsonb_array_elements(proc->'steps') as step
                                        WHERE (step->>'status')::int = 15 -- FAILED
                                    ) > 0 THEN 15 -- FAILED
                                    WHEN (
                                        SELECT COUNT(*)
                                        FROM jsonb_array_elements(proc->'steps') as step
                                        WHERE (step->>'status')::int = 13 -- WARNINGS
                                    ) > 0 THEN 13 -- WARNINGS
                                    WHEN (
                                        SELECT COUNT(*)
                                        FROM jsonb_array_elements(proc->'steps') as step
                                        WHERE (step->>'status')::int > 0
                                    ) > 0 THEN 10 -- PASSED (partial)
                                    ELSE 0 -- TODO
                                END
                            )
                        ),
                        '{steps}',
                        (
                            SELECT jsonb_agg(
                                jsonb_set(
                                    jsonb_set(
                                        step,
                                        '{status}',
                                        to_jsonb(
                                            CASE 
                                                -- Step Status basierend auf Measures berechnen
                                                WHEN (
                                                    SELECT COUNT(*)
                                                    FROM jsonb_array_elements(step->'measures') as measure
                                                    WHERE (measure->>'status')::int IN (10, 13, 15) -- FINISHED
                                                ) = jsonb_array_length(step->'measures') THEN 
                                                    CASE 
                                                        WHEN (
                                                            SELECT COUNT(*)
                                                            FROM jsonb_array_elements(step->'measures') as measure
                                                            WHERE (measure->>'status')::int = 15 -- FAILED
                                                        ) > 0 THEN 15 -- FAILED
                                                        WHEN (
                                                            SELECT COUNT(*)
                                                            FROM jsonb_array_elements(step->'measures') as measure
                                                            WHERE (measure->>'status')::int = 13 -- WARNINGS
                                                        ) > 0 THEN 13 -- WARNINGS
                                                        ELSE 10 -- PASSED
                                                    END
                                                WHEN (
                                                    SELECT COUNT(*)
                                                    FROM jsonb_array_elements(step->'measures') as measure
                                                    WHERE (measure->>'status')::int > 0
                                                ) > 0 THEN 10 -- PASSED (partial)
                                                ELSE 0 -- TODO
                                            END
                                        )
                                    ),
                                    '{measures}',
                                    (
                                        SELECT jsonb_agg(
                                            jsonb_set(
                                                measure,
                                                '{status}',
                                                to_jsonb(
                                                    CASE 
                                                        -- Measure Status basierend auf measurements Tabelle
                                                        WHEN EXISTS (
                                                            SELECT 1 FROM measurements m 
                                                            WHERE m.check_id = checks.id 
                                                              AND m.measure_id = (measure->>'id')::bigint
                                                              AND m.status IN (1, 2) -- PASSED or FAILED
                                                        ) THEN (
                                                            SELECT 
                                                                CASE 
                                                                    WHEN m.status = 1 THEN 10 -- PASSED
                                                                    WHEN m.status = 2 THEN 15 -- FAILED
                                                                    ELSE 0 -- TODO
                                                                END
                                                            FROM measurements m 
                                                            WHERE m.check_id = checks.id 
                                                              AND m.measure_id = (measure->>'id')::bigint
                                                            ORDER BY m.updated_at DESC 
                                                            LIMIT 1
                                                        )
                                                        ELSE 0 -- TODO
                                                    END
                                                )
                                            )
                                        )
                                        FROM jsonb_array_elements(step->'measures') as measure
                                    )
                                )
                            )
                            FROM jsonb_array_elements(proc->'steps') as step
                        )
                    )
                )
                FROM jsonb_array_elements(checkdata->'procedures') as proc
            )
        ),
        '{updated_at}',
        to_jsonb(NOW()::text)
    )
    FROM checks c2 
    WHERE c2.id = checks.id
)
WHERE checkdata IS NOT NULL 
  AND checkdata != '{}' 
  AND checkdata->'procedures' IS NOT NULL;

-- =====================================================
-- SCHRITT 3: VERIFIKATION - Reparatur prüfen
-- =====================================================

-- Status-Verteilung nach der Reparatur prüfen
WITH status_check_after AS (
    SELECT 
        id,
        checkdata,
        jsonb_array_length(checkdata->'procedures') as proc_count,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(checkdata->'procedures') as proc
            WHERE (proc->>'status')::int > 0
        ) as proc_with_status,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step
            WHERE (step->>'status')::int > 0
        ) as steps_with_status,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
            WHERE (measure->>'status')::int > 0
        ) as measures_with_status
    FROM checks 
    WHERE checkdata IS NOT NULL 
      AND checkdata != '{}' 
      AND checkdata->'procedures' IS NOT NULL
)
SELECT 
    'Status nach Reparatur' as info,
    COUNT(*) as total_checks,
    SUM(proc_count) as total_procedures,
    SUM(proc_with_status) as procedures_with_status,
    SUM(steps_with_status) as steps_with_status,
    SUM(measures_with_status) as measures_with_status,
    ROUND(
        CASE 
            WHEN SUM(proc_count) > 0 
            THEN (SUM(proc_with_status)::decimal / SUM(proc_count)) * 100 
            ELSE 0 
        END, 2
    ) as percentage_procedures_with_status
FROM status_check_after;

-- Beispiel der reparierten checkdata anzeigen
SELECT 
    'Reparierte checkdata Beispiel' as info,
    id,
    jsonb_pretty(checkdata->'procedures'->0) as first_procedure_example
FROM checks 
WHERE checkdata IS NOT NULL 
  AND checkdata != '{}' 
  AND checkdata->'procedures' IS NOT NULL
  AND jsonb_array_length(checkdata->'procedures') > 0
LIMIT 1;

-- =====================================================
-- SCHRITT 4: ZUSÄTZLICHE OPTIMIERUNGEN
-- =====================================================

-- Index für bessere Performance bei checkdata-Abfragen erstellen (falls nicht vorhanden)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checks_checkdata_gin 
ON checks USING gin (checkdata);

-- Statistiken der Tabelle aktualisieren
ANALYZE checks;

-- =====================================================
-- FERTIG
-- =====================================================

SELECT 'Statistikdaten-Reparatur abgeschlossen!' as status,
       NOW() as timestamp;
