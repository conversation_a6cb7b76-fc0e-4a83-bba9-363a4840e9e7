#!/usr/bin/env ruby
# LLQA v3.0 Verbesserte Statistikdaten Reparatur (Ruby Version)
# Behebt das Problem fehlender Statistikdaten nach der Migration
#
# Verbesserungen gegenüber der ursprünglichen Lösung:
# - Robustere Fehlerbehandlung und Validierung
# - Bessere Logging und Fortschrittsanzeige
# - Transaktionale Sicherheit
# - Umfassendere Status-Logik
# - Performance-Optimierungen
#
# Autor: Augment Agent (basierend auf Analy<PERSON> von <PERSON>'s Lösung)
# Datum: 2025-08-13
# Ticket: LLQA-2
#
# Verwendung:
#   cd /path/to/llqa2-monolith/server
#   ruby ../utils/fix_statistics_data_improved.rb

require 'bundler/setup'
require 'active_record'
require 'json'
require 'logger'
require 'benchmark'

# Erweiterte Logging-Konfiguration
logger = Logger.new(STDOUT)
logger.level = Logger::INFO
logger.formatter = proc do |severity, datetime, progname, msg|
  "[#{datetime.strftime('%Y-%m-%d %H:%M:%S')}] #{severity}: #{msg}\n"
end

# Datenbankverbindung konfigurieren
database_config = {
  adapter: 'postgresql',
  host: ENV['DB_HOST'] || ENV['LLQA_POSTGRES_HOST'] || 'localhost',
  port: ENV['DB_PORT'] || 5432,
  database: ENV['DB_NAME'] || ENV['LLQA_POSTGRES_DB'] || 'llqa',
  username: ENV['DB_USER'] || ENV['LLQA_POSTGRES_USERNAME'] || 'postgres',
  password: ENV['DB_PASSWORD'] || ENV['LLQA_POSTGRES_PASSWORD'] || 'admin'
}

logger.info "Verbinde zur Datenbank: #{database_config[:host]}:#{database_config[:port]}/#{database_config[:database]}"

begin
  ActiveRecord::Base.establish_connection(database_config)
  ActiveRecord::Base.connection.execute("SELECT 1")
  logger.info "Datenbankverbindung erfolgreich"
rescue => e
  logger.error "Fehler bei Datenbankverbindung: #{e.message}"
  exit 1
end

# Erweiterte Model-Definitionen
class Check < ActiveRecord::Base
  serialize :checkdata, Hash
  has_many :measurements, dependent: :destroy
  
  def self.with_checkdata
    where.not(checkdata: nil)
         .where("checkdata != '{}'")
         .where("checkdata->'procedures' IS NOT NULL")
  end
  
  def self.problematic
    joins(:measurements)
      .where(measurements: { status: [1, 2, 3, 4, 5] })
      .with_checkdata
      .group('checks.id')
      .having("(
        SELECT COUNT(*)
        FROM jsonb_array_elements(checkdata->'procedures') as proc,
             jsonb_array_elements(proc->'steps') as step,
             jsonb_array_elements(step->'measures') as measure
        WHERE (measure->>'status')::int > 0
      ) = 0")
  end
end

class Measurement < ActiveRecord::Base
  belongs_to :check
  
  # Status-Konstanten für measurements Tabelle
  STATUS_UNPROC = 0
  STATUS_PASSED = 1
  STATUS_FAILED = 2
  STATUS_SKIPPED = 3
  STATUS_INVALID = 4
  STATUS_ERROR = 5
end

# Erweiterte Status-Konstanten (aus constants.rb)
module StatusConstants
  CHK_CDSTATUS_TODO = 0
  CHK_CDSTATUS_PASSED = 10
  CHK_CDSTATUS_WARNINGS = 13
  CHK_CDSTATUS_FAILED = 15
  CHK_CDSTATUS_OMITTED_HARD = 20
  CHK_CDSTATUS_OMITTED_SOFT = 21
  CHK_CDSTATUS_SKIPPED = 30
  
  CHK_CDSTATUS_ALL_FINISHED = [CHK_CDSTATUS_PASSED, CHK_CDSTATUS_WARNINGS, CHK_CDSTATUS_FAILED]
  CHK_CDSTATUS_ALL_FAILURE = [CHK_CDSTATUS_WARNINGS, CHK_CDSTATUS_FAILED]
end

class ImprovedStatisticsDataFixer
  include StatusConstants
  
  def initialize(logger)
    @logger = logger
    @fixed_count = 0
    @error_count = 0
    @skipped_count = 0
    @start_time = Time.now
  end

  def run
    @logger.info "=== LLQA Verbesserte Statistikdaten Reparatur gestartet ==="
    
    begin
      # Schritt 1: Umfassende Diagnose
      diagnose
      
      # Schritt 2: Backup erstellen
      create_backup
      
      # Schritt 3: Reparatur mit Transaktionen
      fix_statistics_data_transactional
      
      # Schritt 4: Verifikation
      verify_fix
      
      # Schritt 5: Performance-Optimierung
      optimize_performance
      
    rescue => e
      @logger.error "Kritischer Fehler: #{e.message}"
      @logger.error e.backtrace.join("\n")
      exit 1
    end
    
    duration = Time.now - @start_time
    @logger.info "=== Reparatur abgeschlossen ==="
    @logger.info "Erfolgreich repariert: #{@fixed_count} Checks"
    @logger.info "Übersprungen: #{@skipped_count} Checks"
    @logger.info "Fehler: #{@error_count} Checks"
    @logger.info "Gesamtdauer: #{duration.round(2)} Sekunden"
  end

  private

  def diagnose
    @logger.info "=== UMFASSENDE DIAGNOSE ==="
    
    total_checks = Check.with_checkdata.count
    @logger.info "Checks mit checkdata: #{total_checks}"
    
    problematic_checks = Check.problematic.count
    @logger.info "Problematische Checks (haben Measurements aber keine Status): #{problematic_checks}"
    
    if problematic_checks == 0
      @logger.info "Keine problematischen Checks gefunden. Reparatur nicht erforderlich."
      exit 0
    end
    
    # Beispiel einer checkdata-Struktur anzeigen
    sample_check = Check.with_checkdata.first
    if sample_check
      @logger.info "Beispiel checkdata (Check #{sample_check.id}):"
      @logger.info sample_check.checkdata.to_json[0..300] + "..."
    end
    
    # Detaillierte Statistiken
    measurements_stats = Measurement.group(:status).count
    @logger.info "Measurements Status-Verteilung: #{measurements_stats}"
  end

  def create_backup
    @logger.info "=== BACKUP ERSTELLEN ==="
    
    backup_table = "checks_checkdata_backup_improved_#{Time.now.strftime('%Y%m%d_%H%M%S')}"
    
    ActiveRecord::Base.connection.execute(<<~SQL)
      CREATE TABLE #{backup_table} AS 
      SELECT id, checkdata, updated_at 
      FROM checks 
      WHERE checkdata IS NOT NULL AND checkdata != '{}'
    SQL
    
    backup_count = ActiveRecord::Base.connection.execute("SELECT COUNT(*) FROM #{backup_table}").first['count']
    @logger.info "Backup erstellt: #{backup_table} mit #{backup_count} Einträgen"
  end

  def fix_statistics_data_transactional
    @logger.info "=== TRANSAKTIONALE REPARATUR ==="
    
    problematic_checks = Check.problematic.includes(:measurements)
    total_count = problematic_checks.count
    
    @logger.info "Repariere #{total_count} problematische Checks..."
    
    problematic_checks.find_each.with_index do |check, index|
      ActiveRecord::Base.transaction do
        begin
          @logger.debug "Repariere Check #{check.id} (#{index + 1}/#{total_count})"
          
          if fix_single_check(check)
            @fixed_count += 1
          else
            @skipped_count += 1
          end
          
          # Fortschrittsanzeige
          if (index + 1) % 10 == 0
            @logger.info "Fortschritt: #{index + 1}/#{total_count} (#{((index + 1).to_f / total_count * 100).round(1)}%)"
          end
          
        rescue => e
          @logger.error "Fehler bei Check #{check.id}: #{e.message}"
          @error_count += 1
          raise ActiveRecord::Rollback
        end
      end
    end
  end

  def fix_single_check(check)
    checkdata = check.checkdata.deep_dup
    procedures = checkdata['procedures'] || []
    
    return false if procedures.empty?
    
    changed = false
    
    procedures.each do |procedure|
      steps = procedure['steps'] || []
      
      steps.each do |step|
        measures = step['measures'] || []
        
        # Measures reparieren
        measures.each do |measure|
          old_status = measure['status']
          new_status = calculate_measure_status_improved(check.id, measure['id'])
          
          if old_status != new_status
            measure['status'] = new_status
            changed = true
          end
        end
        
        # Step Status berechnen
        old_step_status = step['status']
        new_step_status = calculate_step_status_improved(measures)
        
        if old_step_status != new_step_status
          step['status'] = new_step_status
          changed = true
        end
      end
      
      # Procedure Status berechnen
      old_proc_status = procedure['status']
      new_proc_status = calculate_procedure_status_improved(steps)
      
      if old_proc_status != new_proc_status
        procedure['status'] = new_proc_status
        changed = true
      end
    end
    
    if changed
      check.update!(checkdata: checkdata)
      return true
    end
    
    false
  end

  def calculate_measure_status_improved(check_id, measure_id)
    return CHK_CDSTATUS_TODO unless measure_id
    
    measurement = Measurement.where(check_id: check_id, measure_id: measure_id)
                            .order(updated_at: :desc, id: :desc)
                            .first
    
    return CHK_CDSTATUS_TODO unless measurement
    
    case measurement.status
    when Measurement::STATUS_PASSED
      CHK_CDSTATUS_PASSED
    when Measurement::STATUS_FAILED
      CHK_CDSTATUS_FAILED
    when Measurement::STATUS_SKIPPED
      CHK_CDSTATUS_SKIPPED
    when Measurement::STATUS_INVALID, Measurement::STATUS_ERROR
      CHK_CDSTATUS_FAILED  # Invalid/Error als Failed behandeln
    else
      CHK_CDSTATUS_TODO
    end
  end

  def calculate_step_status_improved(measures)
    return CHK_CDSTATUS_TODO if measures.empty?
    
    statuses = measures.map { |m| m['status'] || CHK_CDSTATUS_TODO }
    finished_statuses = statuses.select { |s| CHK_CDSTATUS_ALL_FINISHED.include?(s) }
    
    return CHK_CDSTATUS_TODO if finished_statuses.empty?
    
    # Alle Measures abgeschlossen?
    if finished_statuses.length == measures.length
      failed_count = statuses.count(CHK_CDSTATUS_FAILED)
      warning_count = statuses.count(CHK_CDSTATUS_WARNINGS)
      
      return CHK_CDSTATUS_FAILED if failed_count > 0
      return CHK_CDSTATUS_WARNINGS if warning_count > 0
      return CHK_CDSTATUS_PASSED
    end
    
    # Teilweise abgeschlossen - als PASSED markieren
    CHK_CDSTATUS_PASSED
  end

  def calculate_procedure_status_improved(steps)
    return CHK_CDSTATUS_TODO if steps.empty?
    
    statuses = steps.map { |s| s['status'] || CHK_CDSTATUS_TODO }
    finished_statuses = statuses.select { |s| CHK_CDSTATUS_ALL_FINISHED.include?(s) }
    
    return CHK_CDSTATUS_TODO if finished_statuses.empty?
    
    # Alle Steps abgeschlossen?
    if finished_statuses.length == steps.length
      failed_count = statuses.count(CHK_CDSTATUS_FAILED)
      warning_count = statuses.count(CHK_CDSTATUS_WARNINGS)
      
      return CHK_CDSTATUS_FAILED if failed_count > 0
      return CHK_CDSTATUS_WARNINGS if warning_count > 0
      return CHK_CDSTATUS_PASSED
    end
    
    # Teilweise abgeschlossen - als PASSED markieren
    CHK_CDSTATUS_PASSED
  end

  def verify_fix
    @logger.info "=== UMFASSENDE VERIFIKATION ==="
    
    total_checks = Check.with_checkdata.count
    remaining_problematic = Check.problematic.count
    
    @logger.info "Checks mit checkdata nach Reparatur: #{total_checks}"
    @logger.info "Verbleibende problematische Checks: #{remaining_problematic}"
    
    success_rate = total_checks > 0 ? ((total_checks - remaining_problematic).to_f / total_checks * 100).round(2) : 0
    @logger.info "Erfolgsrate: #{success_rate}%"
    
    if remaining_problematic > 0
      @logger.warn "#{remaining_problematic} Checks konnten nicht repariert werden"
    else
      @logger.info "Alle problematischen Checks erfolgreich repariert!"
    end
  end

  def optimize_performance
    @logger.info "=== PERFORMANCE-OPTIMIERUNG ==="
    
    begin
      # Index für checkdata erstellen (falls nicht vorhanden)
      ActiveRecord::Base.connection.execute(<<~SQL)
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checks_checkdata_gin_improved 
        ON checks USING gin (checkdata)
      SQL
      
      # Index für measurements optimieren
      ActiveRecord::Base.connection.execute(<<~SQL)
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_measurements_check_measure_status 
        ON measurements (check_id, measure_id, status, updated_at)
      SQL
      
      # Statistiken aktualisieren
      ActiveRecord::Base.connection.execute("ANALYZE checks")
      ActiveRecord::Base.connection.execute("ANALYZE measurements")
      
      @logger.info "Performance-Optimierungen abgeschlossen"
    rescue => e
      @logger.warn "Performance-Optimierung fehlgeschlagen: #{e.message}"
    end
  end
end

# Hauptprogramm
if __FILE__ == $0
  fixer = ImprovedStatisticsDataFixer.new(logger)
  fixer.run
end
