#!/usr/bin/env ruby
# LLQA v3.0 Statistikdaten Reparatur-Skript (Ruby Version)
# Behebt das Problem fehlender Statistikdaten nach der Migration
#
# Problem: Die checkdata-Struktur in der checks Tabelle enthält keine gültigen Status-Werte
# für procedures, steps und measures, was zu leeren Statistiken im Frontend führt.
#
# Autor: <PERSON>
# Datum: 2025-08-13
# Ticket: LLQA-2
#
# Verwendung:
#   cd /path/to/llqa2-monolith/server
#   ruby ../utils/fix_statistics_data.rb

require 'bundler/setup'
require 'active_record'
require 'json'
require 'logger'

# Logging konfigurieren
logger = Logger.new(STDOUT)
logger.level = Logger::INFO

# Datenbankverbindung konfigurieren
# Diese Werte müssen an die produktive Umgebung angepasst werden
database_config = {
  adapter: 'postgresql',
  host: ENV['DB_HOST'] || 'localhost',
  port: ENV['DB_PORT'] || 5432,
  database: ENV['DB_NAME'] || 'llqa',
  username: ENV['DB_USER'] || 'postgres',
  password: ENV['DB_PASSWORD'] || 'admin'
}

logger.info "Verbinde zur Datenbank: #{database_config[:host]}:#{database_config[:port]}/#{database_config[:database]}"

begin
  ActiveRecord::Base.establish_connection(database_config)
  ActiveRecord::Base.connection.execute("SELECT 1")
  logger.info "Datenbankverbindung erfolgreich"
rescue => e
  logger.error "Fehler bei Datenbankverbindung: #{e.message}"
  exit 1
end

# Check Model definieren
class Check < ActiveRecord::Base
  serialize :checkdata, Hash
end

# Measurement Model definieren
class Measurement < ActiveRecord::Base
  belongs_to :check
end

# Status-Konstanten (aus constants.rb)
CHK_CDSTATUS_TODO = 0
CHK_CDSTATUS_PASSED = 10
CHK_CDSTATUS_WARNINGS = 13
CHK_CDSTATUS_FAILED = 15
CHK_CDSTATUS_OMITTED_HARD = 20
CHK_CDSTATUS_OMITTED_SOFT = 21
CHK_CDSTATUS_SKIPPED = 30

class StatisticsDataFixer
  def initialize(logger)
    @logger = logger
    @fixed_count = 0
    @error_count = 0
  end

  def run
    @logger.info "=== LLQA Statistikdaten Reparatur gestartet ==="
    
    # Schritt 1: Diagnose
    diagnose
    
    # Schritt 2: Reparatur
    fix_statistics_data
    
    # Schritt 3: Verifikation
    verify_fix
    
    @logger.info "=== Reparatur abgeschlossen ==="
    @logger.info "Erfolgreich repariert: #{@fixed_count} Checks"
    @logger.info "Fehler: #{@error_count} Checks"
  end

  private

  def diagnose
    @logger.info "=== DIAGNOSE ==="
    
    total_checks = Check.where.not(checkdata: nil).count
    @logger.info "Checks mit checkdata: #{total_checks}"
    
    checks_with_procedures = Check.where("checkdata->'procedures' IS NOT NULL").count
    @logger.info "Checks mit procedures: #{checks_with_procedures}"
    
    # Beispiel einer checkdata-Struktur anzeigen
    sample_check = Check.where.not(checkdata: nil).first
    if sample_check
      @logger.info "Beispiel checkdata (Check #{sample_check.id}):"
      @logger.info sample_check.checkdata.to_json[0..200] + "..."
    end
    
    # Status-Verteilung prüfen
    checks_with_status = 0
    Check.where("checkdata->'procedures' IS NOT NULL").find_each do |check|
      procedures = check.checkdata['procedures'] || []
      procedures.each do |proc|
        if proc['status'] && proc['status'] > 0
          checks_with_status += 1
          break
        end
      end
    end
    
    @logger.info "Checks mit gültigen Status-Werten: #{checks_with_status}/#{checks_with_procedures}"
  end

  def fix_statistics_data
    @logger.info "=== REPARATUR ==="
    
    Check.where("checkdata->'procedures' IS NOT NULL").find_each do |check|
      begin
        @logger.debug "Repariere Check #{check.id}"
        
        checkdata = check.checkdata.deep_dup
        procedures = checkdata['procedures'] || []
        
        procedures.each do |procedure|
          steps = procedure['steps'] || []
          
          # Steps reparieren
          steps.each do |step|
            measures = step['measures'] || []
            
            # Measures reparieren
            measures.each do |measure|
              measure['status'] = calculate_measure_status(check.id, measure['id'])
            end
            
            # Step Status basierend auf Measures berechnen
            step['status'] = calculate_step_status(measures)
          end
          
          # Procedure Status basierend auf Steps berechnen
          procedure['status'] = calculate_procedure_status(steps)
        end
        
        # Aktualisierte checkdata speichern
        check.update!(checkdata: checkdata)
        @fixed_count += 1
        
      rescue => e
        @logger.error "Fehler bei Check #{check.id}: #{e.message}"
        @error_count += 1
      end
    end
  end

  def calculate_measure_status(check_id, measure_id)
    return CHK_CDSTATUS_TODO unless measure_id
    
    measurement = Measurement.where(check_id: check_id, measure_id: measure_id)
                            .order(updated_at: :desc)
                            .first
    
    return CHK_CDSTATUS_TODO unless measurement
    
    case measurement.status
    when 1
      CHK_CDSTATUS_PASSED
    when 2
      CHK_CDSTATUS_FAILED
    when 3
      CHK_CDSTATUS_SKIPPED
    else
      CHK_CDSTATUS_TODO
    end
  end

  def calculate_step_status(measures)
    return CHK_CDSTATUS_TODO if measures.empty?
    
    statuses = measures.map { |m| m['status'] || CHK_CDSTATUS_TODO }
    finished_count = statuses.count { |s| [CHK_CDSTATUS_PASSED, CHK_CDSTATUS_WARNINGS, CHK_CDSTATUS_FAILED].include?(s) }
    
    return CHK_CDSTATUS_TODO if finished_count == 0
    
    # Alle Measures abgeschlossen
    if finished_count == measures.length
      failed_count = statuses.count(CHK_CDSTATUS_FAILED)
      warning_count = statuses.count(CHK_CDSTATUS_WARNINGS)
      
      return CHK_CDSTATUS_FAILED if failed_count > 0
      return CHK_CDSTATUS_WARNINGS if warning_count > 0
      return CHK_CDSTATUS_PASSED
    end
    
    # Teilweise abgeschlossen
    CHK_CDSTATUS_PASSED
  end

  def calculate_procedure_status(steps)
    return CHK_CDSTATUS_TODO if steps.empty?
    
    statuses = steps.map { |s| s['status'] || CHK_CDSTATUS_TODO }
    finished_count = statuses.count { |s| [CHK_CDSTATUS_PASSED, CHK_CDSTATUS_WARNINGS, CHK_CDSTATUS_FAILED].include?(s) }
    
    return CHK_CDSTATUS_TODO if finished_count == 0
    
    # Alle Steps abgeschlossen
    if finished_count == steps.length
      failed_count = statuses.count(CHK_CDSTATUS_FAILED)
      warning_count = statuses.count(CHK_CDSTATUS_WARNINGS)
      
      return CHK_CDSTATUS_FAILED if failed_count > 0
      return CHK_CDSTATUS_WARNINGS if warning_count > 0
      return CHK_CDSTATUS_PASSED
    end
    
    # Teilweise abgeschlossen
    CHK_CDSTATUS_PASSED
  end

  def verify_fix
    @logger.info "=== VERIFIKATION ==="
    
    total_checks = Check.where("checkdata->'procedures' IS NOT NULL").count
    checks_with_status = 0
    total_procedures = 0
    procedures_with_status = 0
    
    Check.where("checkdata->'procedures' IS NOT NULL").find_each do |check|
      procedures = check.checkdata['procedures'] || []
      total_procedures += procedures.length
      
      has_status = false
      procedures.each do |proc|
        if proc['status'] && proc['status'] > 0
          procedures_with_status += 1
          has_status = true
        end
      end
      
      checks_with_status += 1 if has_status
    end
    
    @logger.info "Checks mit Status nach Reparatur: #{checks_with_status}/#{total_checks}"
    @logger.info "Procedures mit Status: #{procedures_with_status}/#{total_procedures}"
    
    percentage = total_procedures > 0 ? (procedures_with_status.to_f / total_procedures * 100).round(2) : 0
    @logger.info "Erfolgsrate: #{percentage}%"
  end
end

# Hauptprogramm
if __FILE__ == $0
  fixer = StatisticsDataFixer.new(logger)
  fixer.run
end
