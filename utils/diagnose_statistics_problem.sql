-- LLQA v3.0 Statistikdaten Problem-Diagnose
-- Analysiert die aktuelle Situation der fehlenden Statistikdaten
-- 
-- Autor: Augment Agent
-- Datum: 2025-08-13
-- Ticket: LLQA-2

-- =====================================================
-- SCHRITT 1: GRUNDLEGENDE DATENBANK-ANALYSE
-- =====================================================

-- Überblick über die checks Tabelle
SELECT 
    'Checks Tabelle Überblick' as info,
    COUNT(*) as total_checks,
    COUNT(CASE WHEN checkdata IS NOT NULL AND checkdata != '{}' THEN 1 END) as checks_with_checkdata,
    COUNT(CASE WHEN checkdata->'procedures' IS NOT NULL THEN 1 END) as checks_with_procedures
FROM checks;

-- Beispiel einer checkdata-Struktur anzeigen
SELECT 
    'Beispiel checkdata-Struktur' as info,
    id as check_id,
    LEFT(checkdata::text, 300) as checkdata_preview
FROM checks 
WHERE checkdata IS NOT NULL 
  AND checkdata != '{}' 
  AND checkdata->'procedures' IS NOT NULL
LIMIT 3;

-- =====================================================
-- SCHRITT 2: STATUS-ANALYSE IN CHECKDATA
-- =====================================================

-- Detaillierte Status-Analyse der procedures
WITH procedure_status_analysis AS (
    SELECT 
        c.id as check_id,
        jsonb_array_length(c.checkdata->'procedures') as procedure_count,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc
            WHERE (proc->>'status')::int = 0
        ) as procedures_status_todo,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc
            WHERE (proc->>'status')::int > 0
        ) as procedures_status_nonzero,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc
            WHERE proc->>'status' IS NULL
        ) as procedures_status_null
    FROM checks c
    WHERE c.checkdata IS NOT NULL 
      AND c.checkdata != '{}' 
      AND c.checkdata->'procedures' IS NOT NULL
)
SELECT 
    'Procedure Status-Verteilung' as info,
    COUNT(*) as total_checks_analyzed,
    SUM(procedure_count) as total_procedures,
    SUM(procedures_status_todo) as procedures_with_status_0,
    SUM(procedures_status_nonzero) as procedures_with_status_gt_0,
    SUM(procedures_status_null) as procedures_with_status_null,
    ROUND(
        CASE 
            WHEN SUM(procedure_count) > 0 
            THEN (SUM(procedures_status_nonzero)::decimal / SUM(procedure_count)) * 100 
            ELSE 0 
        END, 2
    ) as percentage_procedures_with_valid_status
FROM procedure_status_analysis;

-- =====================================================
-- SCHRITT 3: MEASUREMENTS TABELLE ANALYSE
-- =====================================================

-- Überblick über measurements Tabelle
SELECT 
    'Measurements Tabelle Überblick' as info,
    COUNT(*) as total_measurements,
    COUNT(CASE WHEN status = 0 THEN 1 END) as status_unproc,
    COUNT(CASE WHEN status = 1 THEN 1 END) as status_passed,
    COUNT(CASE WHEN status = 2 THEN 1 END) as status_failed,
    COUNT(CASE WHEN status = 3 THEN 1 END) as status_skipped,
    COUNT(CASE WHEN status > 3 THEN 1 END) as status_other
FROM measurements;

-- Verknüpfung zwischen measurements und checkdata analysieren
WITH measurement_checkdata_link AS (
    SELECT 
        c.id as check_id,
        m.id as measurement_id,
        m.measure_id,
        m.status as measurement_status,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
            WHERE (measure->>'id')::bigint = m.measure_id
        ) as found_in_checkdata
    FROM checks c
    INNER JOIN measurements m ON c.id = m.check_id
    WHERE c.checkdata IS NOT NULL 
      AND c.checkdata != '{}' 
      AND c.checkdata->'procedures' IS NOT NULL
    LIMIT 100
)
SELECT 
    'Measurement-Checkdata Verknüpfung' as info,
    COUNT(*) as analyzed_measurements,
    COUNT(CASE WHEN found_in_checkdata > 0 THEN 1 END) as measurements_found_in_checkdata,
    COUNT(CASE WHEN found_in_checkdata = 0 THEN 1 END) as measurements_not_found_in_checkdata,
    ROUND(
        CASE 
            WHEN COUNT(*) > 0 
            THEN (COUNT(CASE WHEN found_in_checkdata > 0 THEN 1 END)::decimal / COUNT(*)) * 100 
            ELSE 0 
        END, 2
    ) as percentage_linked
FROM measurement_checkdata_link;

-- =====================================================
-- SCHRITT 4: DETAILLIERTE CHECKDATA-STRUKTUR ANALYSE
-- =====================================================

-- Beispiel einer vollständigen checkdata-Struktur mit Status-Werten
SELECT 
    'Detaillierte checkdata-Struktur Beispiel' as info,
    c.id as check_id,
    jsonb_pretty(c.checkdata->'procedures'->0) as first_procedure_detailed
FROM checks c
WHERE c.checkdata IS NOT NULL 
  AND c.checkdata != '{}' 
  AND c.checkdata->'procedures' IS NOT NULL
  AND jsonb_array_length(c.checkdata->'procedures') > 0
LIMIT 1;

-- Status-Werte in measures analysieren
WITH measure_status_analysis AS (
    SELECT 
        c.id as check_id,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
        ) as total_measures,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
            WHERE (measure->>'status')::int = 0
        ) as measures_status_0,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
            WHERE (measure->>'status')::int > 0
        ) as measures_status_gt_0,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
                 jsonb_array_elements(proc->'steps') as step,
                 jsonb_array_elements(step->'measures') as measure
            WHERE measure->>'status' IS NULL
        ) as measures_status_null
    FROM checks c
    WHERE c.checkdata IS NOT NULL 
      AND c.checkdata != '{}' 
      AND c.checkdata->'procedures' IS NOT NULL
)
SELECT 
    'Measure Status-Verteilung in checkdata' as info,
    COUNT(*) as checks_analyzed,
    SUM(total_measures) as total_measures,
    SUM(measures_status_0) as measures_with_status_0,
    SUM(measures_status_gt_0) as measures_with_status_gt_0,
    SUM(measures_status_null) as measures_with_status_null,
    ROUND(
        CASE 
            WHEN SUM(total_measures) > 0 
            THEN (SUM(measures_status_gt_0)::decimal / SUM(total_measures)) * 100 
            ELSE 0 
        END, 2
    ) as percentage_measures_with_valid_status
FROM measure_status_analysis;

-- =====================================================
-- SCHRITT 5: PROBLEM-IDENTIFIKATION
-- =====================================================

-- Checks identifizieren, die Measurements haben aber keine Status-Werte in checkdata
SELECT 
    'Problematische Checks' as info,
    c.id as check_id,
    c.created_at,
    c.updated_at,
    COUNT(m.id) as measurement_count,
    (
        SELECT COUNT(*)
        FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
             jsonb_array_elements(proc->'steps') as step,
             jsonb_array_elements(step->'measures') as measure
        WHERE (measure->>'status')::int > 0
    ) as measures_with_valid_status_in_checkdata
FROM checks c
LEFT JOIN measurements m ON c.id = m.check_id
WHERE c.checkdata IS NOT NULL 
  AND c.checkdata != '{}' 
  AND c.checkdata->'procedures' IS NOT NULL
GROUP BY c.id, c.created_at, c.updated_at, c.checkdata
HAVING COUNT(m.id) > 0 
   AND (
       SELECT COUNT(*)
       FROM jsonb_array_elements(c.checkdata->'procedures') as proc,
            jsonb_array_elements(proc->'steps') as step,
            jsonb_array_elements(step->'measures') as measure
       WHERE (measure->>'status')::int > 0
   ) = 0
ORDER BY c.id
LIMIT 10;

-- =====================================================
-- DIAGNOSE ABGESCHLOSSEN
-- =====================================================

SELECT 'Diagnose abgeschlossen!' as status,
       NOW() as timestamp;
