DASHBOARD.TYPEF.TITLE,Procedures and models with pending changes,更改处于待定状态的程序和型号,,
DASHBOARD.TYPEF.LINEPROC,Procedure <b>{{ code }}</b>: {{ title.en }},程序 <b>{{ code }}</b>: {{ title.en }},,
FRAME.DASHBOARDS,Dashboards,控制面板,,
FRAME.MANAGE.NOTICES,Problem reports,问题报告,,
FRAME.GSEARCH,Search,搜索,,
DASHBOARD.TOOLTIP.PREVDB,Previous Dashboard (Pressing the shift key while clicking will swap the current with the previous dashboard),前一个控制面板（点击并按shift键可实现当前和前一个控制面板的切换）,,
DASHBOARD.TOOLTIP.NEXTDB,Next Dashboard (Pressing the shift key while clicking will swap the current with the next dashboard),下一个控制面板（点击并按shift键可实现当前和下一个控制面板的切换）,,
DASHBOARD.TOOLTIP.DELDB,Remove Dashboard,移除控制面板,,
DASHBOARD.TOOLTIP.EDITDB,Edit title of Dashboard,编辑控制面板名称,,
DASHBOARD.TOOLTIP.ADDDB,New Dashboard,新控制面板,,
DASHBOARD.ADDDB.TITLE,Add new dashboard,新增控制面板,,
DASHBOARD.ADDDB.TEXT,Please enter a title for the new dashboard. The title must be at least 3 characters long.,请为新控制面板输入一个名称。名称长度不少于3个字符。,,
DASHBOARD.EDITDBNAME.TITLE,Edit Dashboard name,编辑控制面板名称,,
DASHBOARD.EDITDBNAME.TEXT,Please enter a new title for the dashboard. The title must be at least 3 characters long.,请为新控制面板输入一个名称。名称长度不少于3个字符。,,
DASHBOARD.EDITBLKNAME.TITLE,Edit Dashboard block name,编辑控制面板块名,,
DASHBOARD.EDITBLKNAME.TEXT,Please enter a new title for the block. The title must be at least 3 characters long.,请为块输入一个新名称。名称长度不少于3个字符。,,
DASHBOARD.DELETEDB.TITLE,Are you sure?,确定？,,
DASHBOARD.DELETEDB.TEXT,Really delete this dashboard?,确定删除控制面板？,,
DASHBOARD.FILTERS.TITLE,Edit block filters,编辑块波滤器,,
DASHBOARD.FILTERS.TEXT,Please select an action:,请选择一个动作,,
DASHBOARD.FILTERS.ACTENABLE,Enable & Edit,启用&编辑,,
DASHBOARD.FILTERS.ACTEDIT,Edit,编辑,,
DASHBOARD.FILTERS.ACTDISABLE,Disable,禁用,,
DASHBOARD.FILTERS.TYPES.MODELSEL.NAME,model selection filter,型号选择过滤器,,
DASHBOARD.FILTERS.TYPES.MODELSEL.TEXT,Please select the models you want to use in this block,请选择要在该块中使用的型号,,
DASHBOARD.TYPE.UCAM.TITLE,Unfinished checks assigned to me,未完成的验盘分配给我,,
DASHBOARD.TYPE.UCAM.LINET,Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},,
DASHBOARD.TYPE.UCAM.LINEB,"Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>","型号<b>{{ model.code }}: {{ model.title.en }}</b>,安排于 <b>{{ scheduled }}</b>,在<b>{{ dueby }}</b>之前",,
DASHBOARD.TYPE.UCAM.LINEBNS,Model <b>{{ model.code }}: {{ model.title.en }}</b>,型号<b>{{ model.code }}: {{ model.title.en }}</b>,,
DASHBOARD.TYPE.UCMP.TITLE,Unfinished checks that may be processed by me,未完成的验盘可能将由我处理,,
DASHBOARD.TYPE.UCMP.LINET,Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},,
DASHBOARD.TYPE.UCMP.LINEB,"Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>","型号 <b>{{ model.code }}: {{ model.title.en }}</b>,安排于<b>{{ scheduled }}</b>, 在 <b>{{ dueby }}</b>之前",,
DASHBOARD.TYPE.UCMP.LINEBNS,Model <b>{{ model.code }}: {{ model.title.en }}</b>,型号<b>{{ model.code }}: {{ model.title.en }}</b>,,
DASHBOARD.TYPE.FCFR.TITLE,Finished checks for review,已完成的验盘供检查,,
DASHBOARD.TYPE.FCFR.LINET,Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},单元<b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},,
DASHBOARD.TYPE.FCFR.LINEB,"Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>","型号 <b>{{ model.code }}: {{ model.title.en }}</b>, 安排于 <b>{{ scheduled }}</b>,在<b>{{ dueby }}</b>之前",,
DASHBOARD.TYPE.FCFR.LINEBNS,Model <b>{{ model.code }}: {{ model.title.en }}</b>,型号<b>{{ model.code }}: {{ model.title.en }}</b>,,
DASHBOARD.TYPE.UWAC.TITLE,Units without any checks,未经任何验盘的单元,,
DASHBOARD.TYPE.UWAC.LINET,Unit <b>{{ code }}</b> ({{ customer }}),单元<b>{{ code }}</b> ({{ customer }}),,
DASHBOARD.TYPE.UWAC.LINEB,Model <b>{{ model.code }}: {{ model.title.en }}</b>,型号 <b>{{ model.code }}: {{ model.title.en }}</b>,,
DASHBOARD.TYPE.CPCH.TITLE,Currently processed checks,正在执行的验盘,,
DASHBOARD.TYPE.CPCH.LINET,Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }},,
DASHBOARD.TYPE.CPCH.LINEB,"Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>","型号<b>{{ model.code }}: {{ model.title.en }}</b>,安排于 <b>{{ scheduled }}</b>, 在 <b>{{ dueby }}</b>之前",,
DASHBOARD.TYPE.CPCH.LINEBNS,Model <b>{{ model.code }}: {{ model.title.en }}</b>,型号<b>{{ model.code }}: {{ model.title.en }}</b>,,
DASHBOARD.TYPE.PMPC.TITLE,Procedures and models with pending changes,更改处于待定状态的程序和型号,,
DASHBOARD.TYPE.PMPC.LINEPROC,Procedure <b>{{ code }}</b>: {{ title.en }},程序<b>{{ code }}</b>: {{ title.en }},,
DASHBOARD.TYPE.PMPC.LINEMOD,Model <b>{{ code }}</b>: {{ title.en }},型号<b>{{ code }}</b>: {{ title.en }},,
CHECK.ALERT.SCHED.ASSPREALLOCFREE,"Predefined assignment, free assignment for remaining parts",预先定义的任务，剩余部分的自由任务,,
CHECK.ALERT.SCHED.ASSPREALLOCDETAILED,"Predefined assignment, detailed assignment for remaining parts",预先定义的任务，剩余部分的详细任务,,
CHECK.ALERT.SCHEDTT.ASSPREALLOCFREE,"After assigning predefined parts, the remaining parts of the check are available to all groups or users",指定预先定义的部分后，验盘上的剩余部分可供所有用户或组使用,,
CHECK.ALERT.SCHEDTT.ASSPREALLOCDETAILED,"After assigning predefined parts, the remaining parts of the check are not assigned and must be assigned manually",指定预先定义的部分后，验盘上的剩余部分不可指派，必须人工指派,,
CHECK.MAKEPDF.OPTRULE,Measure success rules,路径成功标准,,
CHECK.MAKEPDF.TEXT2,Please select the report type:,请选择报告类型,,
CHECK.MAKEPDF.CUSTOMER.TEXT,Customer report,客户报告,,
CHECK.MAKEPDF.CUSTOMER.TTIP,Measures marked as 'intern' will be omitted; an alternative result string is shown in case of warnings.,标记为“实习”的路径将会忽略掉；在警告情形下，会由一个替代结果字符串表示,,
CHECK.MAKEPDF.INTERNAL.TEXT,Internal report,内部报告,,
CHECK.MAKEPDF.INTERNAL.TTIP,All measures are included.,包括所有路径,,
MEDIAMGR.TOOLTIP.DOWNLOAD,Download original file,下载原始文件,,
MODEL.VIEW.TOOLTIP.PREALLOC0,Predefine assignments (currently no definitions),预先定义任务（目前无定义）,,
MODEL.VIEW.TOOLTIP.PREALLOC1,Predefine assignments (currently all check types defined),预先定义任务（目前所有验盘类型确定）,,
MODEL.VIEW.TOOLTIP.PREALLOC2,Predefine assignments (currently some check types defined),预先定义任务（目前部分验盘类型确定）,,
MODEL.VIEW.TOOLTIP.PREALLOC3,Predefine assignments (defective definitions found),预先定义任务（发现有缺陷的定义）,,
MODEL.VIEW.PREALLOC.TITLE,Predefine assignments,预先定义任务,,
MODEL.VIEW.PREALLOC.TEXT1,Please select the check types for which the predefined assignments are valid:,请选择有效的预先定义任务的验盘类型,,
MODEL.VIEW.PREALLOC.TEXT2,Please select the group and/or user to be assigned to the procedure. An existing definition may be removed with 'remove assignment' (in group or user selection).,请选择分配给该程序的组或用户。“移除任务”可移除一个现存的定义（在组或用户选择上）,,
MODEL.VIEW.PREALLOC.ANY,Any user or group,任何用户或组,,
MODEL.VIEW.PREALLOC.GROUP,Group:,组,,
PROCEDURE.VIEW.TOOLTIP.ENFORCETOP,The first entry may not be workflow locked,首个进入点可能不是锁定的工作流,,
PROCEDURE.VIEW.TOOLTIP.ENFORCE0,No workflow lock,无锁定工作流,,
PROCEDURE.VIEW.TOOLTIP.ENFORCE1,Lock: May only be processed when the last entry has been completed,锁定：最后一个进入点完成后方可执行,,
PROCEDURE.VIEW.TOOLTIP.ENFORCE2,Full lock: May only be processed when all prior entries have been completed,全锁：所有先前的进入点完成后方可执行,,
MEASURE.EDIT.INTERNAL,Internal,内部的,,
MEASURE.TYPES.TIMERSTOPQ,Checked timer (stop time),检查后的计时器（终止时间）,,
MEASURE.TYPES.TIMERSTOPC,Check uncorrected time measurement,检查未纠正的时间测定,,
TOOL.COMMENTUNIT.TITLE,Set comment,可做注释,,
TOOL.COMMENTUNIT.TEXT,"Create, edit or delete the comment for this unit:",创建，编辑或删除该单元的注释,,
TOOL.VIEW.TOOLTIP.SETCOMMENT,comment on this unit,该单元的注释,,
TOOL.VIEW.TOOLTIP.REPORT,Generate tool unit usage report,生成工具单元使用报告,,
TOOL.REPORT.TITLE,Generate usage report,生成使用报告,,
TOOL.REPORT.DATE1,"You can select a date from which all uses of this tool unit will be included in the report. If you do not wish to select a date (include all uses from beginning), then click 'Save' without choosing a date.",你可以在选择一个在报告里所有工具单元都包括的日期。如果你不愿意选择一个日期（包括从一开始的所有使用），那就在不选择日期的情况下点击“保存”。,,
TOOL.REPORT.DATE2,"You can select a date until which all uses of this tool unit will be included in the report. If you do not wish to select a date (include all uses until now), then click 'Save' without choosing a date.",你可以在选择一个在报告里所有工具单元都包括的日期。如果你不愿意选择一个日期（包括到目前为止的所有使用），那就在不选择日期的情况下点击“保存”。,,
TOOL.REPORT.SORT.TEXT,Please choose:,请选择：,,
TOOL.REPORT.SORT.MODEL,"Sort by model, unit, check",按照型号，单元，验盘分类,,
TOOL.REPORT.SORT.TIME,Sort by timestamp,按照时间标记分类,,
UNIT.VIEW.CHECK.WARN,Check passed with warnings!,验盘通过，带警告语！,,
UNIT.VIEW.CHECK.WARNCLOSED,"Closed, check passed with warnings on",封闭的，验盘带警告语通过,,
USERS.DELASSIGN,Remove assignment,移除任务,,
USERS.TOOLTIP.ADDGROUP,Add one or more groups to this user,添加一个或多个组到这个用户,,
USERS.TOOLTIP.ADDUSER,Add one or more users to this group,添加一个或多个用户到该组,,
USERS.TOOLTIP.ADDFIRSTGROUP,Add one or more groups to this user,添加一个或多个组到这个用户,,
USERS.TOOLTIP.ADDFIRSTUSER,Add one or more users to this group,添加一个或多个用户到该组,,
USERS.TOOLTIP.REMOVEGROUP,Remove this group from the user,从该用户中移除该组,,
USERS.TOOLTIP.REMOVEUSER,Remove this user from the group,从该组移除该用户,,
USERS.PRIV.FORGROUP,Group wide privileges,组广泛权限,,
USERS.RANK,Level,等级,,
USERS.USERGROUPS,Affiliated groups,附属组,,
USERS.NOGROUPS,User is not affiliated to any group,用户不从属于任何组,,
USERS.GROUPUSERS,Members,成员,,
USERS.NOUSERS,No users are affiliated to this group,该组没有任何附属用户,,
USERS.ADDUSER,Add user,添加用户,,
USERS.ADDGROUP,Add group,添加组,,
USERMGR.ACTION.ADDGRPTOUSER.TITLE,Add groups,添加多个组,,
USERMGR.ACTION.ADDGRPTOUSER.TEXT,Please select the groups to be added to the user:,请选择要添加到该用户的所有组,,
USERMGR.ACTION.ADDUSERTOGRP.TITLE,Add users,增加用户,,
USERMGR.ACTION.ADDUSERTOGRP.TEXT,Please select the users to be added to the group:,请选择要添加到该组的所有用户,,
WFLOW.INTRO.STATUS.WARN,PASSED W/RES,输出对象通过,,
WFLOW.INTRO.STATUS.WARNC,PASSWD W/RES & CLOSED,输出对象通过&关闭,,
WFLOW.STEP.STATUS.WARNNF,(PASSED W/RES),（输出对象通过）,,
WFLOW.STEP.INPLOCKED,Locked,封闭,,
WFLOW.STEP.VIEW.INFO,Description,描述,,
WFLOW.STEP.VIEW.IMAGES,Images ({{ numi }}),图片 ({{ numi }}),,
WFLOW.STEP.VIEW.DOCS,Documents ({{ numd }}),文件 ({{ numd }}),,
WFLOW.INPUT.STATUS.WARN,FAIL,失败,,
WFLOW.INPUT.TEXTTIMER.START,Please enter the start time,请输入开始时间,,
WFLOW.INPUT.TEXTTIMER.STOP,Please enter the stop time,请输入停止时间,,
WFLOW.INPUT.TEXTTIMER.STOPLT,Please enter the stop time; a resulting time less than {{ time }} min is expected,请输入停止时间；实际停止时间可能少于 {{ time }} 分钟,,
WFLOW.INPUT.TEXTTIMER.STOPGT,Please enter the stop time; a resulting time greater than {{ time }} min is expected,请输入停止时间；实际停止时间可能多于 {{ time }} 分钟,,
WFLOW.INPUT.TEXTTIMER.REDUCE,reduce by,减少,,
WFLOW.INPUT.TEXTTIMER.MIN,min,分钟,,
WFLOW.INPUT.NOINPUT,The result of this measurement is calculated from other measurements; no input necessary.,该路径结果由其他路径手段推算；无必要输入,,
SERVER.ERROR.ACCDENIED.TYPE,Access denied or insufficient,拒绝访问或不足,,
SERVER.ERROR.ACCDENIED.TEXT,Your access level or privileges are not sufficient for this action,你的访问级别或权限不足以实施该动作,,
ERROR.CLIENT.ADTTL,Access denied...,拒绝访问,,
ERROR.CLIENT.MSG.ACCDENY,Access was denied because of missing rights or privileges. Please log in again and continue.,由于权限丢失，访问被拒绝。请重新登入并继续。,,
UI.MEDITOR.MAXSEL,You max select {{ num }} entry/ies,你可以选择{{ num }} 进入点,,
UI.MEDITOR.NOMORESEL,The maximum number of selected entries has been reached.,所选择的进入点已达到限制,,
DASHBLOCK.TOOLTIP.SETFILTER,Edit filter settings,编辑波滤器设置,,
DASHBLOCK.TOOLTIP.EDITTITLE,Edit title of block,编辑块的名称,,
PRV.GLSRCH.TTL,Access to global search,访问全局搜索,,
PRV.GLSRCH.DSC,May use global search function,可使用全局搜索功能,,
PRV.MNGNTC.TTL,View and process problem reports,查看并操作问题报告,,
PRV.MNGNTC.DSC,"May view, manage and process problem reports on the report page",可查看，管理，操作报考页面上的问题报告,,
PDF.STATUS.WARNINT,FAILED <sup>*)</sup>,失败<sup>*)</sup>,,
PDF.STATUS.WARNEXT,PASSED <sup>*)</sup>,通过<sup>*)</sup>,,
PDF.HINT.CHECKWARN.INTERNAL,Some measurements have failed but do not or only slightly affect the proper functionality of the machine.,一些路径失败，但是对机器的正常功能不产生影响,,
PDF.HINT.CHECKWARN.CUSTOMER,A measurement that does not affect the proper functionality of the machine is slightly outside the defined tolerance values.,不影响机器正常功能的路径在所定义公差的范围之外,,
PDF.MINPUT.TIMERA,Time measurement (Start),时间测定（开始）,,
PDF.MINPUT.TIMERS,Time measurement (Stop) for statistical reasons,时间测定（停止）由于统计原因,,
PDF.MINPUT.TIMERQ.T1,Time measurement (Stop); time span must be less than %d minutes,时间测定（停止）；时间长度必须少于%d分钟,,
PDF.MINPUT.TIMERQ.T5,Time measurement (Stop); time span must be more than %d minutes,时间测定（停止）；时间长度必须多于%d分钟,,
PDF.MINPUT.TIMERC.T1,Post check time measurement; time span must be less than %d minutes,暂停时间测定检查；时间长度必须少于%d分钟,,
PDF.MINPUT.TIMERC.T5,Post check time measurement; time span must be less than %d minutes,暂停时间测定检查；时间长度必须少于%d分钟,,
PDF.COMMENT.TOOLTIP,Generate comment report,生成注释报告,,
PDF.COMMENT.ELEMENT.TOOLTYPE,Tool type %s,工具类型%s,,
PDF.TUREPORT.HEADER,Usage report toolunit,用户报告工具单元,,
PDF.TUREPORT.FOOTER,"Tool type <b>%s</b>, unit <b>%s</b>","工具类型<b>%s</b>, 单元 <b>%s</b>",,
PDF.TUREPORT.TITLE.MODEL,"<b>Model %s</b>, Unit %s","<b>型号 %s</b>, 单元 %s",,
PDF.TUREPORT.TITLE.TIME,%s.<b>%s</b>,%s.<b>%s</b>,,
PDF.TUREPORT.CONTINUED,<i>(cont.)</i>,<i>(cont.)</i>,,
PDF.TUREPORT.ENTRY.MODEL,"Procedure <b>%s</b>, Step <b>%s</b>, Measure <b>%s</b>","程序 <b>%s</b>, 步骤<b>%s</b>, 测定 <b>%s</b>",,
PDF.TUREPORT.ENTRY.TIMET,"Model <b>%s</b>, Unit <b>%s</b>","型号 <b>%s</b>, 单元<b>%s</b>",,
PDF.TUREPORT.ENTRY.TIMEB,"<i>Procedure <b>%s</b>, Step <b>%s</b>, Measure <b>%s</b></i>","<i>程序 <b>%s</b>, 步骤 <b>%s</b>, 测定 <b>%s</b></i>",,
CHANGELOG.TYPE.PREALLOCATION,Predefined assignments modified,预先定义的任务已修改,,
GSEARCH.WIZARD.TITLE,Global Search,全局搜索,,
GSEARCH.WIZARD.TEXT1,"Please specify the search term. The term must be at least three characters long, an asterisk ('*') is a wildcard for a maximum of 10 characters. A pipe symbol ('|') at the beginning or the end specifies, that the term must be at the beginning resp. the end of the text. Multiple terms can be specified by concatenating them with plus signs ('+'); all these terms must be found in the text. Case is ignored unless a term is prepended by an exclamation mark ('!').",请定义搜索名称。该名称长度不少于3个字符，星号('*')是最大10个字符的通配符。在开头或结果的类似管道的符号 ('|') 意味着该名称必须在全文的开头或结尾。多个名称中间可用加号('+')连接；所有名称必须在全文中有出现。事例将会被忽略掉除非名称前面加感叹号 ('!').,,
GSEARCH.WIZARD.TEXT2,Please select the fields to be processed during the search.,搜索执行过程中请选择范围,,
GSEARCH.WIZARD.TEXT3,Please select the languages to be processed (in multilangual fields).,请选择要执行的语言（在多个范围里）,,
GSEARCH.WIZARD.TEXT4,Please select the search mode on versionized objects:,请版本化的对象上选择搜索型号,,
GSEARCH.SCOPE.ALLALL.TEXT,All objects (full search),所有对象（全面搜索）,,
GSEARCH.SCOPE.ALLALL.TTIP,"All versions (including non finalized) are searched, all matching objects shown.",所有版本（包括未最终确定的）搜索完毕，已显示所有符合条件的对象,,
GSEARCH.SCOPE.ALLRECENT.TEXT,"Full search, most recent found object only",全面搜索，只查找最近的对象,,
GSEARCH.SCOPE.ALLRECENT.TTIP,"All versions (including non finalized) are searched, but only the most recent object is shown.",所有版本（包括未最终确定的）搜索完毕，但是只显示最近对象,,
GSEARCH.SCOPE.FINALL.TEXT,Finalized objects,选定对象,,
GSEARCH.SCOPE.FINALL.TTIP,"All finalized versions are searched, all matching objects shown.",所有选定的版本搜索完毕，已显示所有符合条件的对象,,
GSEARCH.SCOPE.FINRECENT.TEXT,"Finalized objects, most recent found object only",对象选定完毕，只显示最近的对象,,
GSEARCH.SCOPE.FINRECENT.TTIP,"All finalized versions are searched, but only the most recent object is shown.",所有选定的版本搜索完毕，但是只显示最近的对象,,
GSEARCH.SCOPE.LATESTV.TEXT,Latest version,最新版本,,
GSEARCH.SCOPE.LATESTV.TTIP,Only the latest version (finalized) is searched.,只搜索最新的选定的对象,,
GSEARCH.SCOPE.EDITV.TEXT,Edit version only,只编辑版本,,
GSEARCH.SCOPE.EDITV.TTIP,"Only the most recent, non finalized versions are searched.",只搜索最新对象和未选定的版本,,
GSEARCH.TYPE.CHECK.SELECT,Checks,验盘,,
GSEARCH.TYPE.CHECK.DISPLAY,"<u>Check</u> <b>{{ object.id }}</b> (<i>{{ object.checktype.title.en }}</i>) for unit <b>{{ object.unit.code}}</b>, model <b>{{ object.unit.model.code }}</b>","<u>Check</u> <b>{{ object.id }}</b> (<i>{{ object.checktype.title.en }}</i>) 针对单元 <b>{{ object.unit.code}}</b>, 型号<b>{{ object.unit.model.code }}</b>",,
GSEARCH.TYPE.MEASUREMENT.SELECT,Measurements,路径,,
GSEARCH.TYPE.MEASUREMENT.DISPLAY,"<u>Measurement</u> in check <b>{{ object.check.id }}</b> (unit {{ object.check.unit.code }}, model {{ object.check.unit.model.code }}) for measure <b>{{ object.measure.code }}</b> in step <b>{{ object.measure.step.code }}</b>, procedure <b>{{ object.measure.step.procedure.code }}</b>","<u>路径</u> in check <b>{{ object.check.id }}</b> (unit {{ object.check.unit.code }}, model {{ object.check.unit.model.code }}) 针对路径 <b>{{ object.measure.code }}</b> in step <b>{{ object.measure.step.code }}</b>,程序<b>{{ object.measure.step.procedure.code }}</b>",,
GSEARCH.TYPE.CHECKTYPE.SELECT,Checktypes,验盘类型,,
GSEARCH.TYPE.CHECKTYPE.DISPLAY,<u>Checktype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>),<u>验盘类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>),,
GSEARCH.TYPE.DEVICETYPE.SELECT,Devicetypes,装置类型,,
GSEARCH.TYPE.DEVICETYPE.DISPLAY,<u>Devicetype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>),<u>装置类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>),,
GSEARCH.TYPE.MODEL.SELECT,Models,型号,,
GSEARCH.TYPE.MODEL.DISPLAY,"<u>Model</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), version {{ object.version || '(edit)' }}","<u>型号</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), 版本 {{ object.version || '(edit)' }}",,
GSEARCH.TYPE.UNIT.SELECT,Units,单元,,
GSEARCH.TYPE.UNIT.DISPLAY,<u>Unit</u> <b>{{ object.code }}</b> in model <b>{{ object.model.code }}</b>,<u>单元</u> <b>{{ object.code }}</b> 在型号el <b>{{ object.model.code }}</b>,,
GSEARCH.TYPE.MEASURE.SELECT,Measures,路径,,
GSEARCH.TYPE.MEASURE.DISPLAY,"<u>Measure</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) in step <b>{{ object.step.code }}</b> / procedure <b>{{ object.step.procedure.code }}</b>, version {{ object.procedure.version || '(edit)' }}","<u>路径</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) in step <b>{{ object.step.code }}</b> / 程序 <b>{{ object.step.procedure.code }}</b>, 版本 {{ object.procedure.version || '(edit)' }}",,
GSEARCH.TYPE.STEP.SELECT,Steps,步骤,,
GSEARCH.TYPE.STEP.DISPLAY,"<u>Step</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) in procedure <b>{{ object.procedure.code }}</b>, v {{ object.procedure.version || '(edit)' }}","<u>步骤</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) 在程序 <b>{{ object.procedure.code }}</b>, v {{ object.procedure.version || '(edit)' }}",,
GSEARCH.TYPE.PROCEDURE.SELECT,Procedures,程序,,
GSEARCH.TYPE.PROCEDURE.DISPLAY,"<u>Procedure</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), version {{ object.version || '(edit)' }}","<u>程序</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), 版本 {{ object.version || '(edit)' }}",,
GSEARCH.TYPE.TOOLTYPE.SELECT,Tooltypes,工具类型,,
GSEARCH.TYPE.TOOLTYPE.DISPLAY,<u>Tooltype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>),<u>工具类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>),,
GSEARCH.TYPE.TOOLUNIT.SELECT,Toolunits,工具单元,,
GSEARCH.TYPE.TOOLUNIT.DISPLAY,<u>Toolunit</u> <b>{{ object.code }}</b> in tooltype <b>{{ object.tooltype.code }}</b>,<u>工具单元</u> <b>{{ object.code }}</b> 在工具类型 <b>{{ object.tooltype.code }}</b>,,
GSEARCH.TYPE.USER.SELECT,Users,用户,,
GSEARCH.TYPE.USER.DISPLAY,<u>User</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>),<u>用户</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>),,
GSEARCH.TYPE.NOTICE.SELECT,Problem report,问题报告,,
GSEARCH.TYPE.NOTICE.DISPLAY,<u>Problem report</u> <b>{{ object.id }}</b>,<u>问题报告</u> <b>{{ object.id }}</b>,,
GSEARCH.FIELD.COMMENT,Comment,注释,,
GSEARCH.FIELD.CODE,Code,编码,,
GSEARCH.FIELD.TITLE,Title,名称,,
GSEARCH.FIELD.DESCRIPTION,Description/Hints,描述/线索,,
GSEARCH.FIELD.CUSTOMER,Customer,客户,,
GSEARCH.FIELD.USERNAME,Username,用户名,,
GSEARCH.FIELD.REALNAME,Realname,真实姓名,,
GSEARCH.FIELD.TEXT,Description,描述,,
GSEARCH.BUTTONS.NEWSEARCH,New Search,新搜索,,
GSEARCH.BUTTONS.CLOSE,Close,关闭,,
GSEARCH.RESULT.TITLE,Search results,搜索结果,,
GSEARCH.RESULT.TEXT,"The last search resulted in the following matches, sorted by date of change:",以下为符合条件的最新搜索结果，根据修改时间分类,,
GSEARCH.RESULT.TOOMUCH,(Note: Only the first 50 matches are displayed),（备注：只显示前50个符合条件的对象）,,
GSEARCH.RESULT.NOMATCH,There are no matches,没有符合条件的对象,,
NOTICES.TOOLTIP.SHOW,Show,显示,,
NOTICES.TOOLTIP.SHOWALL,Show all,显示所有,,
NOTICES.TOOLTIP.HIDE,Hide,隐藏,,
NOTICES.TOOLTIP.FILTER,Filter this column,过滤该栏,,
NOTICES.TOOLTIP.NOFILTER,Disable filter,禁用过滤,,
NOTICES.EXPORT.TTL,Export problem reports,发送问题报告,,
NOTICES.EXPORT.TYPETEXT,Please select the file type for export:,请选择发送报告的文件类型,,
NOTICES.EXPORT.FILTERTEXT,Please select the scope:,请选择范围：,,
NOTICES.EXPORT.CSV,Comma Separated Values (CSV),逗号分隔值,,
NOTICES.EXPORT.JSON,JavaScript Output Notation (JSON),基于JavaScript语言的轻量级的数据交换格式,,
NOTICES.EXPORT.XML,Extensible Markup Language (XML), 可扩展标示语言,,
NOTICES.EXPORT.UNARCHIVED,All non archived reports,所有未存档的报告,,
NOTICES.EXPORT.ALL,All (including archived) reports,所有报告（包括存档报告）,,
NOTICES.EXPORT.FILTERED,All shown (filtered) reports,所有显示的（波滤过的）报告,,
NOTICES.FILTEREDIT.TITLE,Please define the filter,请定义波滤器,,
NOTICES.FILTEREDIT.ID,"Please select the range of IDs to be shown. You can use '<from>-<to>', '-<to>' or '<from>-'. Example: '10-20' (all IDs from 10 to 20); '-50' (all IDs up to 50).","请选择所要显示的ID范围. 你可以使用 '<from>-<to>', '-<to>' or '<from>-'. 例如: '10-20' (all IDs from 10 to 20); '-50' (ID数最多为51个).",,
NOTICES.FILTEREDIT.CATEGORY,Please select the categories to be shown:,请选择要显示的类别,,
NOTICES.FILTEREDIT.PATH,Please enter a search term; this will be matched against the displayed path text:,请输入一个搜索名称；该名称将和展示的路径文字相符,,
NOTICES.FILTEREDIT.TEXT,Please enter a search term; this will be matched against the description:,请输入一个搜索名称；该名称将和描述相一致,,
NOTICES.FILTEREDIT.ARTICLE,"Please enter a search term; this will be matched against the article number (not: article description). If you want to match against the article description, prefix the term with a colon ':'.",请输入一个搜索名称；该名称将和文件编号相符（非：文件描述）。如果想和文件描述相符，请在名称前面加上冒号':',,
NOTICES.FILTEREDIT.TIMELOSS,Please select the time loss values to be shown:,请选择要显示的时间损失值,,
NOTICES.FILTEREDIT.STATUS,Please select the statu types values to be shown:,请选择要显示的状态类型值,,
NOTICES.TITLE.PROBLREP,Problem report,问题报告,,
NOTICES.TITLE.DESC,Description,描述,,
NOTICES.TITLE.PROPOSALS,Proposals:,建议：,,
NOTICES.TITLE.CAT,Category,类别,,
NOTICES.TITLE.TIMELOSS,Time loss (ca.),时间消耗,,
NOTICES.TITLE.ARTNO,Article no.,文件编号,,
NOTICES.TITLE.ARTDESC,Article desc.,文件描述,,
NOTICES.TITLE.PROBLREPNO,Problem report no.,问题报告编号,,
NOTICES.TEXT.DESC,Please enter a short and meaningful description. You can choose from a small selection of proposals below the text box or enter your own text.,请输入一个简短有效的描述。可从以下文字框中的几项建议选择或者自己输入文本。,,
NOTICES.TEXT.CHOOSECAT,"Please select one of the given categories matching the problem report best. If you have lost time due to the problem, please select the best matching value in the time loss select box.",请选择一个最符合问题报告的给定的类别。如果你由于该问题耗费时间，可在时间损耗文字框中选择最符选项。,,
NOTICES.TEXT.ARTICLE,If the problem report is related to a certain article you can enter this article by number and description here:,如果该问题报告和某个文件相关，你可在此处输入文件编号和文件描述。,,
NOTICES.TEXT.STTCHANGE,Please enter a short comment regarding the status change:,关于状态更改请输入一个简短注释,,
NOTICES.BUTTON.USE,Use,使用,,
NOTICES.BUTTON.CANCEL,Cancel,取消,,
NOTICES.BUTTON.SEND,Send,发送,,
NOTICES.BUTTON.CATEGORIES,Edit categories,编辑类别,,
NOTICES.BUTTON.TEMPLATES,Edit text proposals,编辑文本建议,,
NOTICES.BUTTON.EXPORT,Export,发送,,
NOTICES.BUTTON.STT_12,Process report,处理报告,,
NOTICES.BUTTON.STT_21,Cancel processing,取消处理,,
NOTICES.BUTTON.STT_25,Finish processing,完成处理,,
NOTICES.BUTTON.STT_52,Restart processing,重新处理,,
NOTICES.BUTTON.STT_59,Archive,存档,,
NOTICES.BUTTON.CLOSE,Close,关闭,,
NOTICES.VIEW.LOCATION,Path,路径,,
NOTICES.VIEW.CATEGORY,Category,类别,,
NOTICES.VIEW.ARTICLE,Article,文件,,
NOTICES.VIEW.TIMELOSS,Time loss,时间损耗,,
NOTICES.VIEW.NOTEXT,N/A,无效,,
NOTICES.VIEW.DESC,Description,描述,,
NOTICES.VIEW.ID,No.,编号,,
NOTICES.VIEW.PATH,Path,路径,,
NOTICES.VIEW.TEXT,Description,描述,,
NOTICES.VIEW.STATUS,Status,状态,,
NOTICES.TIMELOSS.15,approx. 15 minutes,大概15分钟,,
NOTICES.TIMELOSS.30,approx. 30 minutes,大概30分钟,,
NOTICES.TIMELOSS.60,approx. 1 hour,大概1个小时,,
NOTICES.TIMELOSS.90,approx. 1_ hour,大概1个多小时,,
NOTICES.TIMELOSS.120,approx. 2 hours,大概2个小时,,
NOTICES.TIMELOSS.180,approx. 3 hours,大概3个小时,,
NOTICES.TIMELOSS.240,more than 4 hours,超过4个小时,,
NOTICES.ALERT.CATMISS.TITLE,Missing category,类别不存在,,
NOTICES.ALERT.CATMISS.TEXT,You have to select on of the given categories!,你必须在给定类别里选择,,
NOTICES.ALERT.DESCMISS.TITLE,Description missing,描述不存在,,
NOTICES.ALERT.DESCMISS.TEXT,The description is missing or too short!,描述不存在或太短！,,
NOTICES.ALERT.THANKS.TITLE,Thank you!,谢谢！,,
NOTICES.ALERT.THANKS.TEXT,The problem report was sent and will be processed shortly!,问题报告将会尽快发送并处理！,,
NOTICES.ALERT.CONFDEL.TITLE,Really delete?,确定删除？,,
NOTICES.ALERT.CONFDEL.TEXT1,"Really delete this entry? The change can not be undone, earlier use of this entry may be modified or corrupted.",确定删除该进入点？该动作不可撤销。该进入点的先前使用可能被修改或崩溃。,,
NOTICES.ALERT.CONFDEL.TEXT2,"Really delete this entry? The entry will be removed from this list and may not be used in the future. Earlier uses of this entry remain valid, though.",确定删除该进入点？该进入点将从列表移除，或将不能再被使用。该进入点的先前使用依然有效。,,
NOTICES.MODAL.EDITTEXT.TITLE,New entry,新进入点,,
NOTICES.MODAL.EDITTEXT.TEXT,Please edit the text for this entry:,请编辑新进入点的文本,,
NOTICES.MODAL.NEWTEXT.TITLE,Edit entry,编辑进入点,,
NOTICES.MODAL.NEWTEXT.TEXT,Please enter the text for the new entry (at least 3 characters):,请为新进入点输入文本（至少3个字符）,,
NOTICES.MODAL.SNIPPETCAT.TITLE,Problem report categories,问题报告类别,,
NOTICES.MODAL.SNIPPETCAT.SNIPPET,Category,类别,,
NOTICES.MODAL.SNIPPETDESC.TITLE,Text proposals for problem reports,问题报告的文本建议,,
NOTICES.MODAL.SNIPPETDESC.SNIPPET,Text proposal,文本建议,,
NOTICES.STATUS.OPEN,<b>submitted</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }},<b>已提交</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }},,
NOTICES.STATUS.PROCESSED,<b>processed</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }},<b>已执行</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }},,
NOTICES.STATUS.CLOSED,<b>closed</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }},<b>已关闭</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }},,
NOTICES.STATUS.ARCHIVED,<b>archived</b> on {{ time }},<b>已存档</b> on {{ time }},,
NOTICES.STATUS.1,Submitted,提交,,
NOTICES.STATUS.2,Processed,执行,,
NOTICES.STATUS.5,Closed,关闭,,
NOTICES.PATHTYPE.CHECKSTEP,Check step,检查步骤,,
NOTICES.PATHTYPE.CHECKGENERAL,Check (general),一般检查,,
NOTICES.SEGMENT.MODEL,Model <b>{{ code }}</b>,型号<b>{{ code }}</b>,,
NOTICES.SEGMENT.UNIT,Unit <b>{{ code }}</b>,单元 <b>{{ code }}</b>,,
NOTICES.SEGMENT.CHECK,Check <b>{{ id }}</b>,检查<b>{{ id }}</b>,,
NOTICES.SEGMENT.PROCEDURE,Procedure <b>{{ code }}</b>,程序 <b>{{ code }}</b>: {{ title.en }},,
NOTICES.SEGMENT.STEP,Step <b>{{ code }}</b>,步骤 <b>{{ code }}</b>,,
NOTICES.CORRECT.TITLE,Correction,更正,,
NOTICES.CORRECT.DESCRIPTION,Please edit the description:,请编辑描述,,
NOTICES.CORRECT.TIMELOSS,Please select a new value for time loss:,请给时间损耗选择一个时间,,
NOTICES.CORRECT.CATEGORY,Please choose a new category:,请选择一个新类别,,
NOTICES.CORRECT.ARTICLE1,Step 1/2: Please edit the article number:,步骤 1/2：请编辑文件编号：,,
NOTICES.CORRECT.ARTICLE2,Step 2/2: Please edit the article description:,步骤 2/2：请编辑文件描述,,
SNIPPET.TITLE.ADD,add,添加,,
SNIPPET.TOOLTIP.LOCK,"Closes the entry. Earlier uses of this entry are still valid, however the entry may not be chosen any longer.",关闭进入点。该进入点的前期使用保持有效，但是该进入点可能无法再被选择,,
SNIPPET.TOOLTIP.UNLOCK,Reopens the entry; it may be chosen again.,重新打开进入点，该进入点可能又被关闭,,
SNIPPET.TOOLTIP.DELETE,Deletes the entry after confirmation for good.,确定后删除进入点,,
SNIPPET.TOOLTIP.EDIT,Edit the text of the entry.,编辑进入点的文本。,,
SNIPPET.TOOLTIP.REORD,The entry may be moved to another position per drag & drop,该进入点可能在每次拖放的过程中移动到其他位置。,,
SNIPPET.BUTTON.CLOSE,Close,关闭,,