FRAME.TITLE	LeanLogic QA	LeanLogic QA	LeanLogic QA
FRAME.DASHBOARD	Dashboard	仪表板	Dashboard
FRAME.DASHBOARDS	Dashboards	控制面板	Dashboards
FRAME.MANAGE.TITLE	Management	管理人员	Management
FRAME.MANAGE.MODEL	Models	型号	Modelle
FRAME.MANAGE.UNIT	Units	单位	Einheiten
FRAME.MANAGE.PROCEDURE	Procedures	流程	Prozeduren
FRAME.MANAGE.TOOL	Tools	工具	Werkzeuge
FRAME.MANAGE.DEVICETYPE	Device types	设备类型	Gerätetypen
FRAME.MANAGE.CHECKTYPE	Check types	检查类型	Prüfungsarten
FRAME.MANAGE.USERS	Users	多用户	Benutzer
FRAME.MANAGE.CHANGEPW	Change Password	更改密码	Passwort ändern
FRAME.MANAGE.NOTICES	Problem reports	问题报告	Fehlerberichte
FRAME.LOGOUT	Logout	注销	Logout
FRAME.EDITDTYPE.TITLE	@:FRAME.MANAGE.DEVICETYPE	@:FRAME.MANAGE.DEVICETYPE	@:FRAME.MANAGE.DEVICETYPE
FRAME.EDITDTYPE.TEXT	Add, edit and disable device types:	增加, 编辑和禁用设备类型	Gerätetypen hinzufügen, editieren oder deaktivieren:
FRAME.EDITCTYPE.TITLE	@:FRAME.MANAGE.CHECKTYPE	@:FRAME.MANAGE.CHECKTYPE	@:FRAME.MANAGE.CHECKTYPE
FRAME.EDITCTYPE.TEXT	Add, edit and disable check types:	增加, 编辑和禁用检查类型	Prüfungsarten hinzufügen, editieren oder ausschalten:
FRAME.CHPWD.TITLE	@:FRAME.MANAGE.CHANGEPW	@:FRAME.MANAGE.CHANGEPW	@:FRAME.MANAGE.CHANGEPW
FRAME.CHPWD.PW1	Enter your new password:	输入你的新密码	Bitte das neue Passwort eingeben:
FRAME.CHPWD.PW2	Repeat password:	重复密码	Bitte zur Überprüfung wiederholen:
FRAME.CHPWD.NOMATCH	Passwords do not match - operation cancelled!	密码不符-操作取消	Passwörter stimmen nicht überein; Vorgang wird abgebrochen!
FRAME.CHPWD.TOOSHORT	Password too short - must be at least 3 characters long!	密码太短-至少需3个字符长	Das Passwort ist zu kurz; drei Zeichen sind Minimum!
FRAME.CHPWD.OK	Password changed.	密码已修改	Passwort wurde geändert.
FRAME.GSEARCH	Search	搜索	Suche
DASHBOARD.TOOLTIP.PREVDB	Previous Dashboard (Pressing the shift key while clicking will swap the current with the previous dashboard)	前一个控制面板（点击并按shift键可实现当前和前一个控制面板的切换）	Vorheriges Dashboard (mit gehaltener Shift-taste wird das aktuelle Dashboard mit dem vorherigen in der Reihenfolge getauscht)
DASHBOARD.TOOLTIP.NEXTDB	Next Dashboard (Pressing the shift key while clicking will swap the current with the next dashboard)	下一个控制面板（点击并按shift键可实现当前和下一个控制面板的切换）	Nächstes Dashboard (mit gehaltener Shift-taste wird das aktuelle Dashboard mit dem nächsten in der Reihenfolge getauscht)
DASHBOARD.TOOLTIP.DELDB	Remove Dashboard	移除控制面板	Dashboard entfernen
DASHBOARD.TOOLTIP.EDITDB	Edit title of Dashboard	编辑控制面板名称	Dashboardtitel ändern
DASHBOARD.TOOLTIP.ADDDB	New Dashboard	新控制面板	Neues Dashboard
DASHBOARD.NEWBLOCK.TITLE	New Dash	新破折号	Neuer Block
DASHBOARD.NEWBLOCK.TEXT	Select the content of the new Dash below:	选择新破折号下面的内容	Den Inhalt des Blocks festlegen:
DASHBOARD.WELCOME	Welcome, {{ name }}!	欢迎, {{ name }}!	Willkommen, {{ name }}!
DASHBOARD.VERSION	Version {{ version }}	版本 {{ version }}	Version {{ version }}
DASHBOARD.ADDDASH.BUTTON	Add Dash	增加破折号	Neuer Block
DASHBOARD.ADDDB.TITLE	Add new dashboard	新增控制面板	Neues Dashboard hinzufügen
DASHBOARD.ADDDB.TEXT	Please enter a title for the new dashboard. The title must be at least 3 characters long.	请为新控制面板输入一个名称。名称长度不少于3个字符。	Bitte geben Sie einen Titel für das neue Dashboard ein. Der Titel muss mindestens 3 Zeichen lang sein.
DASHBOARD.EDITDBNAME.TITLE	Edit Dashboard name	编辑控制面板名称	Dashboardtitel ändern
DASHBOARD.EDITDBNAME.TEXT	Please enter a new title for the dashboard. The title must be at least 3 characters long.	请为新控制面板输入一个名称。名称长度不少于3个字符。	Bitte geben Sie einen neuen Titel für das Dashboard ein. Der Titel muss mindestens 3 Zeichen lang sein.
DASHBOARD.EDITBLKNAME.TITLE	Edit Dashboard block name	编辑控制面板块名	Blocktitel ändern
DASHBOARD.EDITBLKNAME.TEXT	Please enter a new title for the block. The title must be at least 3 characters long.	请为块输入一个新名称。名称长度不少于3个字符。	Bitte geben Sie einen neuen Titel für den Block ein. Der Titel muss mindestens 3 Zeichen lang sein.
DASHBOARD.DELETEDB.TITLE	Are you sure?	确定？	Sind Sie sicher?
DASHBOARD.DELETEDB.TEXT	Really delete this dashboard?	确定删除控制面板？	Wirklich dieses Dashboard entfernen?
DASHBOARD.FILTERS.TITLE	Edit block filters	编辑块波滤器	Blockfilter editieren
DASHBOARD.FILTERS.TEXT	Please select an action:	请选择一个动作	Bitte wählen Sie eine Aktion aus:
DASHBOARD.FILTERS.ACTENABLE	Enable & Edit	启用&编辑	Verwenden:
DASHBOARD.FILTERS.ACTEDIT	Edit	编辑	Editieren:
DASHBOARD.FILTERS.ACTDISABLE	Disable	禁用	Ausschalten:
DASHBOARD.FILTERS.TYPES.MODELSEL.NAME	model selection filter	型号选择过滤器	Modellauswahlfilter
DASHBOARD.FILTERS.TYPES.MODELSEL.TEXT	Please select the models you want to use in this block	请选择要在该块中使用的型号	Bitte wählen Sie die Modelle aus, die in diesem Block betrachtet werden:
DASHBOARD.TYPE.UCAM.TITLE	Unfinished checks assigned to me	未完成的验盘分配给我	Mir zugeteilte, nicht beendete Prüfungen
DASHBOARD.TYPE.UCAM.LINET	Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}
DASHBOARD.TYPE.UCAM.LINEB	Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>	 scheduled on <b>{{ scheduled }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>
DASHBOARD.TYPE.UCAM.LINEBNS	Model <b>{{ model.code }}: {{ model.title.en }}</b>	型号<b>{{ model.code }}: {{ model.title.en }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>
DASHBOARD.TYPE.UCMP.TITLE	Unfinished checks that may be processed by me	未完成的验盘可能将由我处理	Unbeendete Prüfungen, die von mir bearbeitet werden können
DASHBOARD.TYPE.UCMP.LINET	Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}
DASHBOARD.TYPE.UCMP.LINEB	Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>	 scheduled on <b>{{ scheduled }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>
DASHBOARD.TYPE.UCMP.LINEBNS	Model <b>{{ model.code }}: {{ model.title.en }}</b>	型号<b>{{ model.code }}: {{ model.title.en }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>
DASHBOARD.TYPE.FCFR.TITLE	Finished checks for review	已完成的验盘供检查	Abgeschlossene Prüfungen zur Einsicht
DASHBOARD.TYPE.FCFR.LINET	Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	单元<b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}
DASHBOARD.TYPE.FCFR.LINEB	Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>	 scheduled on <b>{{ scheduled }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>
DASHBOARD.TYPE.FCFR.LINEBNS	Model <b>{{ model.code }}: {{ model.title.en }}</b>	型号<b>{{ model.code }}: {{ model.title.en }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>
DASHBOARD.TYPE.UWAC.TITLE	Units without any checks	未经任何验盘的单元	Einheiten ohne Prüfungen
DASHBOARD.TYPE.UWAC.LINET	Unit <b>{{ code }}</b> ({{ customer }})	单元<b>{{ code }}</b> ({{ customer }})	Einheit <b>{{ code }}</b> ({{ customer }})
DASHBOARD.TYPE.UWAC.LINEB	Model <b>{{ model.code }}: {{ model.title.en }}</b>	型号 <b>{{ model.code }}: {{ model.title.en }}</b>	Modell <b>{{ model.code }}: {{ model.title.en }}</b>
DASHBOARD.TYPE.CPCH.TITLE	Currently processed checks	正在执行的验盘	Prüfungen zur Zeit in Bearbeitung
DASHBOARD.TYPE.CPCH.LINET	Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}	Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}
DASHBOARD.TYPE.CPCH.LINEB	Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>	 scheduled on <b>{{ scheduled }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>
DASHBOARD.TYPE.CPCH.LINEBNS	Model <b>{{ model.code }}: {{ model.title.en }}</b>	型号<b>{{ model.code }}: {{ model.title.en }}</b>	Modell <b>{{ model.code }}: {{ model.title.de }}</b>
DASHBOARD.TYPE.PMPC.TITLE	Procedures and models with pending changes	更改处于待定状态的程序和型号	Prozeduren und Modelle mit Änderungen
DASHBOARD.TYPE.PMPC.LINEPROC	Procedure <b>{{ code }}</b>: {{ title.en }}	程序<b>{{ code }}</b>: {{ title.en }}	Prozedur <b>{{ code }}</b>: {{ title.de }}
DASHBOARD.TYPE.PMPC.LINEMOD	Model <b>{{ code }}</b>: {{ title.en }}	型号<b>{{ code }}</b>: {{ title.en }}	Modell <b>{{ code }}</b>: {{ title.de }}
CHECK.INPUT.TITLE.EDIT	Processing {{mname_en}}	处理中 {{mname_en}}	Bearbeite {{mname_de}}
CHECK.OVERVIEW.ACTION.SCHEDULE.TITLE	Schedule check	进度检查	Prüfung terminieren
CHECK.OVERVIEW.ACTION.SCHEDULE.TEXTSCHED	Select the schedule date (earliest start time)	选择计划日期 (最早开始时间)	Wählen Sie den frühesten Startzeitpunkt
CHECK.OVERVIEW.ACTION.SCHEDULE.TEXTDUE	Select the due date (latest finishing time)	选择到期日(最迟完成时间)	Wählen Sie, bis wann die Prüfung abgeschlossen sein muss
CHECK.OVERVIEW.ACTION.SCHEDULE.TEXTASSIGN	Select the assignment mode	选择作业模式	Wählen Sie den Modus der Zuweisung
CHECK.OVERVIEW.ACTION.SCHEDULE.TEXTASSIGNTO	Select the group or user to assign this check to	选择群组或用户分配检查	Die Prüfung folgender Gruppe oder Benutzer zuweisen
CHECK.OVERVIEW.ACTION.SCHEDULE.TEXTADDASSIGN	Select the group or user to add as assignment target	选择群组或用户添加任务目标	Eine Gruppe oder einen Benutzer als Zuweisungsziel auswählen
CHECK.OVERVIEW.ACTION.CHANGEASS.TITLE	Change assignment	更改任务	Zuweisung ändern
CHECK.OVERVIEW.ACTION.CHANGEASS.TEXT	Select the new assignment for this block:	为此区块选择新任务:	Wählen Sie bitte die neue Zuweisung für diesen Block:
CHECK.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
CHECK.ALERT.DELETEASS.TITLE	Do you really want to delete assignee {{ name }}?	你真的要删除代理人 {{ name }}?	Wirklich das Zuweisungsziel {{ name }} löschen?
CHECK.ALERT.DELETEASS.TEXT	All assignments to this assignee will be reset.	代理人将所有任务重置	Alle Zuweisungen an dieses Ziel werden zurückgesetzt.
CHECK.ALERT.DELETE.TITLE	@:UNIT.ALERT.DELETECHECK.TITLE	@:UNIT.ALERT.DELETECHECK.TITLE	@:UNIT.ALERT.DELETECHECK.TITLE
CHECK.ALERT.DELETE.TEXT	@:UNIT.ALERT.DELETECHECK.TEXT	@:UNIT.ALERT.DELETECHECK.TEXT	@:UNIT.ALERT.DELETECHECK.TEXT
CHECK.ALERT.SCHED.ASSFREE	Free Assignment	自由任务	Freie Zuweisung
CHECK.ALERT.SCHED.ASSFULL	Preassign full check	预先完整检查	Ganze Prüfung vorab zuweisen
CHECK.ALERT.SCHED.ASSDETAILED	Assign detailed	预先指定细节	Detaillierte Zuweisung
CHECK.ALERT.SCHED.ASSPREALLOCFREE	Predefined assignment, free assignment for remaining parts	 free assignment for remaining parts"	Vorabzuweisung, sonst freie Zuweisung
CHECK.ALERT.SCHED.ASSPREALLOCDETAILED	Predefined assignment, detailed assignment for remaining parts	 detailed assignment for remaining parts"	Vorabzuweisung, sonst detaillierte Zuweisung
CHECK.ALERT.SCHEDTT.ASSFREE	Check is free to be registered by any user	任何用户均可自由注册检查	Prüfung ist für alle Gruppen oder Benutzer verfügbar
CHECK.ALERT.SCHEDTT.ASSFULL	The whole check is assigned to a single user or group	整个检查被分配给单个用户或组	Prüfung wird vollständig einer Gruppe / einem Benutzer zugewiesen
CHECK.ALERT.SCHEDTT.ASSDETAILED	Individual areas may be assigned to a user or group	各个区域可以被分配给一个用户或组	Einzelne Teile können verantwortlichen Benutzern / Gruppen zugewiesen werden
CHECK.ALERT.SCHEDTT.ASSPREALLOCFREE	After assigning predefined parts, the remaining parts of the check are available to all groups or users	 the remaining parts of the check are available to all groups or users"	Nach der Vorabzuweisung sind die restlichen Teile der Prüfung für alle Gruppen oder Benutzer verfügbar
CHECK.ALERT.SCHEDTT.ASSPREALLOCDETAILED	After assigning predefined parts, the remaining parts of the check are not assigned and must be assigned manually	 the remaining parts of the check are not assigned and must be assigned manually"	Nach der Vorabzuweisung sind die restlichen Teile der Prüfung nicht zugewiesen und müssen manuell bearbeitet werden
CHECK.ALERT.REASSIGN.TITLE	Do you really want to stop the process and reassign the workflow?	你真的要停止工序及重新分配工作流程?	Wirklich den Prozess unterbrechen und die Prüfungsabschnitte neu zuweisen?
CHECK.ALERT.REASSIGN.TEXT	The assigned users will be unable to work on this check while being stopped!	指定用户将无法检查这次工作在一会儿后停止	Die momentan zugewiesenen Benutzer können an der Prüfung nicht weiter arbeiten, während diese gestoppt ist!
CHECK.ALERT.REGISTER.TITLE	Register	注册	Registrieren
CHECK.ALERT.REGISTER.TEXT	Select the assignment you want to register to:	选择你要注册的任务:	Bitte die Zuweisung auswählen, für die Sie sich registrieren möchten:
CHECK.ALERT.UNREGISTER.TITLE	Unregister	注销注册	Abmelden
CHECK.ALERT.UNREGISTER.TEXT	Select the assignment you want to unregister from:	选择你要注销注册的任务:	Bitte die Zuweisung auswählen, von denen Sie sich abmelden möchten:
CHECK.ALERT.STEPINPROC	{{ nums }} step(s)/{{ nump }} procedure(s)	{{ nums }} 步骤(s)/{{ nump }} 流程(s)	{{ nums }} Schritt(e)/{{ nump }} Prozedur(en)
CHECK.ALERT.REOPEN.TITLE	Reopen	重新开始	Wiedereröffnen
CHECK.ALERT.REOPEN.TEXT	Select the assignments you want to uncommit while reopening:	选择你要未被授权的任务一会儿重新开放:	Bitte die Zuweisungen auswählen, die nach der Wiedereröffnung erneut geprüft werden sollen:
CHECK.ALERT.COPY.TITLE	Create copy	创建副本	Kopie erstellen
CHECK.ALERT.COPY.TEXT	Please select the procedures which will be copied to the new check including all measurements:	请选择程序，这些程序将被复制到包括所有测量的新的检查:	Bitte die Prozeduren auswählen, deren Messergebnisse in die Kopie übernommen werden sollen:
CHECK.ALERT.COPY.NEWVERS.TITLE	Update?	更新?	Aktualisieren?
CHECK.ALERT.COPY.NEWVERS.TEXT	There are newer versions of this check and you can optionally update the copy. Please note, however, that only procedures are copied that have not changed between the versions (regardless of the procedures you selected in the previous dialog) and that the copy may behave incorrectly in case of modified workflow rules.	有此检查的新版本，您可以选择是否更新副本。请注意，只有在不同版本中未做更改的程序会被复制（不论您在之前对话中选择什么样的程序）在工作程序规则更改的情况下，复制可能出错.	Es sind neuere Versionen dieser Prüfung vorhanden; Sie können optional die Kopie aktualisieren. Beachten Sie dabei bitte, dass nur Prozeduren kopiert werden, die sich nicht geändert haben (ungeachtet der Auswahl im vorherigen Dialog) und bei zwischenzeitlichen Änderungen in den Ablaufsteuerungen Teile der Prüfungskopie unkorrektes Verhalten aufweisen können.
CHECK.ALERT.COPY.NEWVERS.NOCHANGE	Keep current version	保留现有版本	Aktuelle Version beibehalten
CHECK.ALERT.SELPROC.TITLE	Procedure selection	程序选择	Prozedurauswahl
CHECK.ALERT.SELPROC.TEXTSING	Please select the procedure from this block for which you want to register:	请选择您要注册区块的一个程序:	Bitte die Prozedur aus dem Block auswählen, für die Sie sich registrieren möchten:
CHECK.ALERT.SELPROC.TEXTMULT	Please select the procedures from this block for which you want to register:	请选择您要注册区块的多个程序:	Bitte die Prozeduren aus dem Block auswählen, für die Sie sich registrieren möchten:
CHECK.ALERT.REGMODE.TITLE	Registration mode	注册方式	Registrierungsmodus
CHECK.ALERT.REGMODE.TEXT	Please select the mode of self registration:	请选择自注册的方式:	Bitte die Art der Selbstregistrierung auswählen:
CHECK.ALERT.REGMODE.COMPLETE	Only complete block	只有完整区块	Registrierung nur komplett
CHECK.ALERT.REGMODE.MAYPART	Partial registration possible	可部分注册	Teilweise Registrierung möglich
CHECK.ALERT.REGMODE.MUSTPART	Registration for single procedures only	仅限单一程序注册	Registrierung nur für einzelne Prozeduren
CHECK.ALERT.REGMODETT.COMPLETE	Self registration is only possible for the complete block	自注册只能在完整区块进行	Registrierung ist nur für den kompletten Block möglich
CHECK.ALERT.REGMODETT.MAYPART	When self registering, the procedures of a block may be selected individually (if there are multiple procedures in the block)	自注册时，区块程序可以被单独选择（如果区块存在多重程序的话）	Bei der Registrierung können Prozeduren eines Blockes individuell ausgewählt werden (sofern mehrere Prozeduren in einem Block vorhanden sind)
CHECK.ALERT.REGMODETT.MUSTPART	When self registering, only a single procedure may be selected and registeres at a time (if there are multiple procedures in the block)	自注册时，一次只有一个单一程序被选择和注册（如果区块存在多重程序的话）	Bei der Registrierung wird je nur eine einzelne, auszuwählende Prozedur zugewiesen (sofern mehrere Prozeduren in einem Block vorhanden sind)
CHECK.ALERT.TAKEOVER.INFO	Take over!	兼并!	Übernahme!
CHECK.ALERT.TAKEOVER.CONFIRM	Do you really want to take over this assignment from the previous owner?	您真的要从之前的主人处兼并此任务吗？	Wollen Sie wirklich diesen Block vom früheren Bearbeiter übernehmen?
CHECK.MODIFY.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE
CHECK.MODIFY.COMMENT.TEXT	Add, edit or delete comments for this check	增加, 编辑或删除该检查的评论	Kommentare für diese Prüfung hinzufügen, löschen oder editieren
CHECK.MAKEPDF.TTL	Create report PDF	创建PDF(便携文档格式)报告	Prüfungsbericht als PDF generieren
CHECK.MAKEPDF.TEXT	Select all optional elements you want to include in the PDF:	选择所有可选的元素要包含在PDF(便携文档格式):	Bitte alle optionalen Elemente auswählen, die in dem PDF erscheinen sollen:
CHECK.MAKEPDF.OPTV	Procedure versions	流程版本	Prozedurversionen
CHECK.MAKEPDF.OPTADM	Assignees and other user information	代理人和其他用户信息	Zuweisungen und andere benutzerspezifische Informationen
CHECK.MAKEPDF.OPTCOMM	Comments on measurements	评论测量	Kommentare zu Messungen
CHECK.MAKEPDF.OPTRAW	Raw values for matrix calculations	矩阵计算的原始值	Rohwerte der Matrixkalkulationen
CHECK.MAKEPDF.OPTTOOL	Tools used for measurements	用于测量工具	Bei Messungen verwendete Werkzeuge
CHECK.MAKEPDF.OPTRULE	Measure success rules	路径成功标准	Messungsregeln
CHECK.MAKEPDF.TEXT2	Please select the report type:	请选择报告类型	Bitte den Typ des Reports auswählen:
CHECK.MAKEPDF.CUSTOMER.TEXT	Customer report	客户报告	Kundenreport
CHECK.MAKEPDF.CUSTOMER.TTIP	Measures marked as 'intern' will be omitted; an alternative result string is shown in case of warnings.	标记为“实习”的路径将会忽略掉；在警告情形下，会由一个替代结果字符串表示	Als 'intern' markierte Messungen auslassen; alternativer Ergebnistext.
CHECK.MAKEPDF.INTERNAL.TEXT	Internal report	内部报告	Interner Report
CHECK.MAKEPDF.INTERNAL.TTIP	All measures are included.	包括所有路径	Alle Messungen einfügen.
CHECK.MAKEFILE.TTL	Export report data	输出报告数据	Reportdaten exportieren
CHECK.MAKEFILE.TEXT	Select the type of output for this report:	选择输出的类型在这份报告:	Bitte den Typ der Exportdaten für diesen Report festlegen:
CHECK.MAKEFILE.CSV	Comma Separated Values (CSV)	逗号分隔值	Comma Separated Values (CSV)
CHECK.MAKEFILE.JSON	JavaScript Output Notation (JSON)	Java描述语言输出符号	JavaScript Output Notation (JSON)
CHECK.MAKEFILE.XML	Extensible Markup Language (XML)	可扩展的标记语言	Extensible Markup Language (XML)
CHECK.SELTOOL.TITLE	Select tool unit	选择工具单位	Werkzeug auswählen
CHECK.SELTOOL.MESSAGE	Select the tool you are using for this step	选择您使用此步骤的工具	Bitte das Werkzeug auswählen, das für diesen Schritt verwendet wird
MEASUREMENT.INPUT.COMMENT.TITLE	Edit comment	编辑意见	Kommentar editieren
MEASUREMENT.INPUT.COMMENT.TEXT	Add, edit or delete comments for this unit	增加, 编辑或删除该单位意见	Kommentare für diese Einheit hinzufügen, löschen oder editieren
MSRSTAT.SELMSR.TTL	@:MSRSTAT.SELM.TTL	@:MSRSTAT.SELM.TTL	@:MSRSTAT.SELM.TTL
MSRSTAT.SELMSR.TEXT	Select the measures you want to include in the report:	选择你要包含在报告的测量:	Bitte wählen Sie die Messungen aus, die im Bericht erscheinen sollen:
MSRSTAT.SELMSR.RUTEXT	Enter the id-String you've copied from a previous report (last page of the PDF):	输入你先前报告中已复制的ID字符串(PDF(便携文档格式)的最后一页):	Bitte geben Sie den id-String ein, den Sie von einem früheren Bericht kopiert haben (letzte Seite des PDF):
MSRSTAT.SELCHK.TTL	@:MSRSTAT.SELC.TTL	@:MSRSTAT.SELC.TTL	@:MSRSTAT.SELC.TTL
MSRSTAT.SELCHK.S1TEXT	Select the model of the unit you want to add checks from:	选择您要添加检查的单位型号:	Bitte wählen Sie das Modell der Einheit, von der Sie Prüfungen hinzufügen möchten:
MSRSTAT.SELCHK.S2TEXT	Enter one or more short strings matching a part of the code or the customer name of the unit you want to add checks from:	输入一个或多个短字符串的一部分代码或你想要添加的检查单位客户名称匹配:	Bitte geben Sie eine kurze Zeichenfolge ein, die auf den Code oder den Kundennamen der gewünschten Einheit passt:
MSRSTAT.SELCHK.S3TEXT	Select one or more check below:	选择以下一个或多个检查:	Bitte wählen Sie ein oder mehrere Prüfungen:
MSRSTAT.SELCHK.ERROR.TTL	Check selection problem...	检查选择问题…	Auswahlproblem...
MSRSTAT.SELCHK.ERROR.NORES	No checks found that use this procedure (in any version) or where the unit matches the given search strings.	没有检查发现,使用此程序(在任何版本)或所在单位匹配给定的搜索字符串	Keine Prüfungen wurden gefunden, die diese Prozedur verwenden oder bei denen die Einheit auf die gegebenen Suchbegriffe passt.
MSRSTAT.SELCHK.ERROR.TOOMANY	Too many units found for the given search strings. Only the first 25 have been tested for valid checks. Please refine the search if the check you wanted to add is not listed.	太多的单位发现于给定的搜索字符串. 只有前25个已经过有效的检查测试. 请修改搜索,如果你想添加的检查是不上市.	Zu viele Einheiten wurden für den gegebenen Suchbegriff gefunden. Nur die ersten 25 wurden verarbeitet und getestet. Bitte die Suche verfeinern, wenn die gesuchte Prüfung nicht in der Liste vorhanden ist.
MSRSTAT.SELCHK.ERROR.LIMIT	You've reached the limit of 30 checks per report. Please remove checks from the list before adding new ones.	你已经达到每30次检查报告的限制. 请先从列表中移除检查在添加新的检查	Sie haben das Limit von 30 Prüfungen pro Report erreicht. Bitte löschen Sie erst Prüfungen aus der Liste, bevor Sie neue hinzufügen.
MSRSTAT.SELCHK.RUTEXT	@:MSRSTAT.SELMSR.RUTEXT	@:MSRSTAT.SELMSR.RUTEXT	@:MSRSTAT.SELMSR.RUTEXT
MSRSTAT.SELCHK.SUCCESS.TTL	Export finished	输出成品	Export beendet
MSRSTAT.SELCHK.SUCCESS.TEXT	The data is being downloaded. Click 'Yes' to close this wizard, or 'No' if you wish to continue using this report generator. To reuse the measure or check selection later, you can copy the following id-strings now:	数据已经下载中. 单击"是"以关闭此向导, 或"否"如果你想继续使用这个报告生成器. 重新测量或稍候尝试选项,你现在可以复制下面的ID字符串:	Die Daten werden heruntergeladen. Klicken Sie 'Ja' um diesen Dialog zu schliessen oder 'Nein' um fortzufahren. Um die Messungs- oder Prüfungsauswahl später wieder zu verwenden, können Sie folgende id-Strings kopieren:
MSRSTAT.SELCHK.SUCCESS.MSTR	Measures: "{{ cstr }}"	测量: {{cstr}}""	Messunten: '{{ cstr }}'
MSRSTAT.SELCHK.SUCCESS.CSTR	Checks: "{{ cstr }}"	检查: {{cstr}}""	Prüfungen: '{{ cstr }}'
MSRSTAT.TOOLTIP.REMCHK	Remove check	移除检查	Prüfung entfernen
MSRSTAT.TITLE	Statistical Report Generator	统计报表生成器	Statistischer Report Generator
MSRSTAT.SUBT	Procedure {{ pcode }}	流程 {{ pcode }}	Prozedur {{ pcode }}
MSRSTAT.SELM.TTL	Select measures	选择测量	Messungen auswählen
MSRSTAT.SELM.TXT	Select the measures which will be included in the report. There is no limit of measures you can choose, each measure will generate one page. You can either reuse a previously copied selection or use the Select button to open the selection wizard.	选择此次测量将包含在报告中. 你可以选择没有限制的测量,每一次测量将生成一页. 你可以重新使用以前复制的选择或使用""选择""按钮打开选择向导.	Bitte wählen Sie die Messungen, die im Report verwendet werden sollen. Es gibt keine Höchstanzahl, jede Messung erzeugt eine neue Seite.
MSRSTAT.SELM.STATUS	{{ msel }} measure(s) selected	{{ msel }} 测量(s) 选定	{{ msel }} Messung(en) ausgewählt
MSRSTAT.SELC.TTL	Select checks	选择检查	Prüfungen auswählen
MSRSTAT.SELC.TXT	Select the checks which will be included in the report. The number of checks is limited to 30. You can use the Add button to open the check finder wizard, delete entries from the list with the red trashcan button or reuse a previously copied selection.	选择此次检查将包含在报告中. 检查的数量限制为30. 您可以使用""添加""按钮打开检查查询向导,从列表中删除条目与红色垃圾桶按钮或重新使用先前复制的选择.	Bitte wählen Sie die Prüfungen, die im Report verwendet werden sollen. Die maximale Anzahl an Prüfungen beträgt 30.
MSRSTAT.SELC.STATUS	{{ csel }} check(s) selected	{{ csel }} 检查 (s) 选定	{{ csel }} Prüfung(en) ausgewählt
MSRSTAT.CLIST	Check <b>{{ id }}</b> <span style='font-size:70%'>for Model <b>{{ mcode }}</b>, Unit <b>{{ ucode }}</b>; Checktype <b>{{ ctcode }}</b></span>	检查 <b>{{ id }}</b> <span style='font-size:70%'>型号 <b>{{ mcode }}</b>, 单元 <b>{{ ucode }}</b>; 检查类型 <b>{{ ctcode }}</b></span>	Prüfung <b>{{ id }}</b> <span style='font-size:70%'>für Modell <b>{{ mcode }}</b>, Einheit <b>{{ ucode }}</b>; Prüfungsart <b>{{ ctcode }}</b></span>
MSRSTAT.BUTTON.SEL	@:UI.BUTTONS.MEDITOR.SELECT	@:UI.BUTTONS.MEDITOR.SELECT	@:UI.BUTTONS.MEDITOR.SELECT
MSRSTAT.BUTTON.REUSE	Reuse	重新使用	Wiederverwenden
MSRSTAT.BUTTON.ADD	@:TLISTEDIT.BUTTON.ADD	@:TLISTEDIT.BUTTON.ADD	@:TLISTEDIT.BUTTON.ADD
MSRSTAT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
MSRSTAT.BUTTON.GENERATE	Generate Report	生成报告	Report generieren
MSRSTAT.BUTTON.EXPORT	Export Data	输出数据	Daten exportieren
MEDIAMGR.IMAGE.ALERT.CONFHEADER	Are you sure?	你确定吗?	Sind Sie sicher?
MEDIAMGR.IMAGE.ALERT.DELETE	Do you really want to delete image {{fname}} from this set?	你真的想从从这设置删除图像{{fname}}?	Möchten Sie wirklich das Bild {{fname}} aus dieser Zusammenstellung löschen?
MEDIAMGR.IMAGE.EDIT.CAPTION.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE
MEDIAMGR.IMAGE.EDIT.CAPTION.TEXT	Add, edit or delete a caption for this image	增加, 编辑或删除这图像的标题	Bildtitel ergänzen, ändern oder löschen
MEDIAMGR.DOC.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
MEDIAMGR.DOC.ALERT.DELETE	Do you really want to delete document {{fname}} from this set?	你真的想从从这设置删除文档 {{fname}}?	Möchten Sie wirklich das Dockument {{fname}} aus dieser Zusammenstellung löschen?
MEDIAMGR.DOC.EDIT.CAPTION.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE
MEDIAMGR.DOC.EDIT.CAPTION.TEXT	Add, edit or delete a caption for this document	增加, 编辑或删除这文件的标题	Dokumenttitel ergänzen, ändern oder löschen
MEDIAMGR.UPLOAD.FILTER.TITLE	Upload impossible	无法上传	Hochladen fehlgeschlagen
MEDIAMGR.UPLOAD.FILTER.MESSAGE	The upload of file {{ filename }} could not be started:	上传的档案 {{ filename }} 无法启动:	Das Hochladen der Datei {{ filename }} konnte nicht gestartet werden:
MEDIAMGR.UPLOAD.FILTER.UNSUPPORTED	The file type ({{ type }}) is unknown or unsupported by the media manager.	此类型档案 ({{ type }}) 为未知或不被支持此媒体管理	Der Dateityp ({{ type }}) ist unbekannt oder wird vom Mediamanager nicht unterstützt.
MEDIAMGR.UPLOAD.FILTER.TOOBIG	The file is too big; for this file type, the maximum size is set to {{ max }} MB, the file's size is {{ has }} MB.	此档案太大;此档案类型,最大的尺寸设置为 {{ max }} MB,此档案的尺寸为 {{ has }}MB.	Die Datei ist zu gross; für diesen Dateityp ist die maximale Grösse auf {{ max }} MB festgelegt, die Datei ist {{ has }} MB gross.
MEDIAMGR.UPLOAD.HINT	Drop media files in this pane for upload	终止媒体档案上传此窗格	Mediendateien in diesem Feld ablegen um sie auf den Server hochzuladen
MEDIAMGR.UPLOAD.RESTR	You may upload images (png, jpeg, tiff), text documents (pdf) and videos (mp4 only).	你可以上传图片 (png, jpeg, tiff),文字文档(pdf) 和影像数据 (mp4 only)	Sie können hier Bilddateien (png, jpeg, tiff), Textdokumente (pdf) und Videos (nur mp4) hochladen.
MEDIAMGR.UPLOAD.STATUS.READY	Ready to be uploaded	准备上传	Bereit zum Hochladen
MEDIAMGR.UPLOAD.STATUS.WAITING	Waiting...	等待…	Bitte warten...
MEDIAMGR.UPLOAD.STATUS.UPLOADING	Uploading: {{ prog }}%	上传: {{ prog }}%	Lade Datei hoch: {{ prog }}%
MEDIAMGR.UPLOAD.STATUS.CANCELLED	File upload cancelled	档案上传已取消	Hochladen abgebrochen
MEDIAMGR.UPLOAD.STATUS.FINISHED	@:WFLOW.INTRO.DATE.FIN	@:WFLOW.INTRO.DATE.FIN	Beendet
MEDIAMGR.UPLOAD.STATUS.FAILED	Upload failed	上传失败	Hochladen fehlgeschlagen
MEDIAMGR.UPLOAD.STATUS.ERROR	File upload error	档案上传错误	Serverfehler
MEDIAMGR.UPLOAD.STARTALL	Start all uploads	开始所有上传	Alle Dateien hochladen
MEDIAMGR.UPLOAD.DELALL	Remove all finished uploads	移除所有完成上传	Alle fertigen Einträge entfernen
MEDIAMGR.TOOLTIP.IMGSIZE	Show image size	显示图像的大小	Bilddateigrösse anzeigen
MEDIAMGR.TOOLTIP.IMGMETA	Show image metadata	显示图像的元数据	Bildmetadaten anzeigen
MEDIAMGR.TOOLTIP.EDITCAP	Set or edit caption	设定或编辑标题	Bildunterschrift hinzufügen oder ändern
MEDIAMGR.TOOLTIP.REMIMG	Remove image	移除图像	Bild entfernen
MEDIAMGR.TOOLTIP.VIEWDOC	View Document	查看文档	Dokument ansehen
MEDIAMGR.TOOLTIP.DOCSIZE	Show document size	显示文档大小	Dateigrösse anzeigen
MEDIAMGR.TOOLTIP.DOCMETA	Show document metadata	显示文档的元数据	Dokumentmetadaten anzeigen
MEDIAMGR.TOOLTIP.REMDOC	Remove document	移除文档	Dokument entfernen
MEDIAMGR.TOOLTIP.UPLOAD	Start upload	开始上传	Upload starten
MEDIAMGR.TOOLTIP.REMUPL	Remove upload from list	从列表中移除上传	Upload von der Liste entfernen
MEDIAMGR.TOOLTIP.CNCLUPL	Cancel upload	取消上传	Upload abbrechen
MEDIAMGR.TOOLTIP.DOWNLOAD	Download original file	下载原始文件	Datei im Original herunterladen
MEDIAMGR.PDFPAGES	{{ nump }} page(s)	{{ nump }} 页(s)	{{ nump }} Seiten
MEDIAMGR.VIDINFO	@:WFLOW.INTRO.VIDINFO	@:WFLOW.INTRO.VIDINFO	@:WFLOW.INTRO.VIDINFO
MEDIAMGR.TITLE	Media Manager	媒体管理	Media Manager
MEDIAMGR.TAB.IMAGES	Images	图像	Bilder
MEDIAMGR.TAB.VIDEOS	Videos	影像数据	Videos
MEDIAMGR.TAB.DOCS	@:WFLOW.STEP.DOCS	@:WFLOW.STEP.DOCS	@:WFLOW.STEP.DOCS
MEDIAMGR.TAB.UPLOAD	Upload	上传	Hochladen
MODEL.EDITOR.TITLE.EDIT	Editing {{modelname_en}}	编辑 {{modelname_en}}	Editiere {{modelname_de}}
MODEL.EDITOR.TITLE.NEW	Editing new model	编辑新的流程	Editiere neues Modell
MODEL.ALERT.EDITVERSION	Do you really want to edit a version of model {{code}}?	你真的想要编辑此版本的型号 {{code}}?	Möchten Sie wirklich eine finalisierte Version des Modells {{code}} bearbeiten?
MODEL.ALERT.EDITVERSIONDETAIL	You are about to edit a finalized version of this model. If this version is used in any workflow, checks already planned or finished will be altered.	你即将编辑此型号最终版本. 如果此版本被用在任何工作流程,检查已计划或完成将会改变.	Dies ist eine finalisierte Version dieses Modells. Wird diese Version in Arbeitsabläufen eingesetzt, werden bereits eingerichtete oder bearbeitete Prüfungen verändert und u.U. dadurch invalid.
MODEL.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
MODEL.ALERT.DELETE	Do you really want to delete model {{code}}?	你真的想要删除型号 {{code}}?	Möchten Sie wirklich Modell {{code}} löschen?
MODEL.ALERT.DELETEVERSION	You are about to remove a finalized version of this model. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.	你即将移除此型号最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏"	Dies ist eine finalisierte Version dieses Modells. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.
MODEL.ALERT.FINALIZE	Do you really want to finalize model {{code}}?	你真的想要完成型号 {{code}}?	Möchten Sie wirklich die aktuelle Version des Modells {{code}} finalisieren?
MODEL.ALERT.FINALIZEDETAIL	The current data will be finalized into a new version. This version will be published and may then be used for new units and checks.	当前的数据将最终形成一个新版本. 这个版本将被发表,随后可将其用于新的单位和检查.	Die aktuellen Daten werden finalisiert und eine neue Version zum Editieren bereitgestellt. Die finalisierte Version kann dann für neue Einheiten und Prüfungen verwendet werden.
MODEL.ALERT.RESET.TITLE	Do you really want to reset all changes?	您真的要重置所有更改吗？	Wirklich alle Änderungen zurücksetzen?
MODEL.ALERT.RESET.TEXT	All changes made to this object since the last finalization will be erased permanently.	至上次审定时对此对象所做的所有更改都将被永久删除.	Alle Änderungen, die an diesem Objekt seit der letzten Finalisierung vorgenommen wurden, werden unwiderruflich gelöscht.
MODEL.SWITCHV.TITLE	Select version	选择版本	Version auswählen
MODEL.SWITCHV.MESSAGE	Select the version to switch to from the following list:	从下面的列表中选择要切换到的版本:	Bitte die gewünschte Version aus der folgenden Liste auswählen:
MODEL.VIEW.ADDPROC.TITLE	Add Procedures	增加流程	Prozeduren hinzufügen
MODEL.VIEW.ADDPROC.TEXT	Select all procedures to add, then click the 'Add'-Button	选择要增加的所有流程,然后单击“增加”按钮	Bitte alle gewünschten Prozeduren auswählen, dann mit dem 'Hinzufügen'-Button bestätigen
MODEL.VIEW.ADDCTYPE.TITLE	Add Checktypes	增加检查类型	Prüfungsarten hinzufügen
MODEL.VIEW.ADDCTYPE.TEXT	Select all checktypes to add, then click the 'Add'-Button	选择要增加的所有检查类型,然后单击“增加”按钮	Bitte alle gewünschten Prüfungsarten auswählen, dann mit dem 'Hinzufügen'-Button bestätigen
MODEL.VIEW.TOOLTIP.ACTREORD	Activate reordering buttons	激活重新排序按钮	Icons zum Umsortieren anzeigen
MODEL.VIEW.TOOLTIP.ADDPROC	Add active procedure	增加有用的流程	Aktive Prozedur hinzufügen
MODEL.VIEW.TOOLTIP.UPDPROC	Update procedure version	更新流程版本	Prozedurversion aktualisieren
MODEL.VIEW.TOOLTIP.REORDER	Drag&Drop to reorder	拖放重新排序	Zum Umsortieren halten und verschieben
MODEL.VIEW.TOOLTIP.GOTOPROC	Goto procedure	转到流程	Zur Prozedur gehen
MODEL.VIEW.TOOLTIP.DELPROC	Remove procedure	移除流程	Prozedur entfernen
MODEL.VIEW.TOOLTIP.ADDCTYPE	Add active check types	增加有用的检查类型	Aktive Prüfungsart hinzufügen
MODEL.VIEW.TOOLTIP.DELCTYPE	Remove check type	移除检查类型	Prüfungsart entfernen
MODEL.VIEW.TOOLTIP.SWITCHVER	Switch version	切换版本	Version wechseln
MODEL.VIEW.TOOLTIP.TESTVER	Test version	测试版本	Version testen
MODEL.VIEW.TOOLTIP.CHANGELOG	View change log	查看更改日志	Änderungslog einsehen
MODEL.VIEW.TOOLTIP.RESET	Reset all changes to the last finalized version	重置所有更改至上次审定版本	Alle Änderungen zur letzten finalisierten Version zurücksetzen
MODEL.VIEW.TOOLTIP.PREALLOC0	Predefine assignments (currently no definitions)	预先定义任务（目前无定义）	Vorabzuweisungen definieren (zurzeit keine vorhanden)
MODEL.VIEW.TOOLTIP.PREALLOC1	Predefine assignments (currently all check types defined)	预先定义任务（目前所有验盘类型确定）	Vorabzuweisungen definieren (zurzeit für alle Prüfungsarten definiert)
MODEL.VIEW.TOOLTIP.PREALLOC2	Predefine assignments (currently some check types defined)	预先定义任务（目前部分验盘类型确定）	Vorabzuweisungen definieren (zurzeit für einige Prüfungsarten definiert)
MODEL.VIEW.TOOLTIP.PREALLOC3	Predefine assignments (defective definitions found)	预先定义任务（发现有缺陷的定义）	Vorabzuweisungen definieren (zurzeit fehlerhafte Definitionen vorhanden)
MODEL.VIEW.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
MODEL.VIEW.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
MODEL.VIEW.DTYPE	@:WFLOWEDIT.OPT1.DT	@:WFLOWEDIT.OPT1.DT	@:WFLOWEDIT.OPT1.DT
MODEL.VIEW.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
MODEL.VIEW.MEDIA	Media	媒体	Medien
MODEL.VIEW.IMREG	{{ imgnum }} Image(s) registered	{{ imgnum }} 图像(s) 已注册的	{{ imgnum }} Bilder registriert
MODEL.VIEW.DOCREG	{{ docnum }} Document(s) registered	{{ docnum }}文档(s) 已注册的	{{ docnum }} Dokumente registriert
MODEL.VIEW.MEDMGR	@:MEDIAMGR.TITLE	@:MEDIAMGR.TITLE	@:MEDIAMGR.TITLE
MODEL.VIEW.BUTTON.EXPORT	Export	输出	Export
MODEL.VIEW.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE
MODEL.VIEW.BUTTON.FIN	Finalize	最终的	Finalisieren
MODEL.VIEW.BUTTON.EDIT	Edit	编辑	Editieren
MODEL.VIEW.BUTTON.UNIT	Goto units	转到单位	Zu den Einheiten
MODEL.VIEW.PROC.STEPNUM	Steps: {{ stepnum }}	步骤: {{ stepnum }}	Schritte: {{ stepnum }}
MODEL.VIEW.PROC.DISCONTINUED	Discontinued!	停止！	Stillgelegt!
MODEL.VIEW.PROC.OLDVERS	Using version {{ pvers }}	使用版本 {{ pvers }}	Verwendet Version {{ pvers }}
MODEL.VIEW.ACTPROC	Active procedures	有用的流程	Aktive Prozeduren
MODEL.VIEW.ACTCTYPE	Active checktypes	有用的检查类型	Aktive Prüfungsarten
MODEL.VIEW.VERSIONTAG	Version:	版本:	Version:
MODEL.VIEW.UNITINFO.TTL	@:FRAME.MANAGE.UNIT	@:FRAME.MANAGE.UNIT	@:FRAME.MANAGE.UNIT
MODEL.VIEW.UNITINFO.TXT	<b>{{ ucnt }} unit(s) registered:</b><br>{{ ocnt }} open, {{ ccnt }} closed, {{ dcnt }} discarded.	<b>{{ ucnt }}单位(s) 已注册的:</b><br>{{ ocnt }} 开启, {{ ccnt }} 关闭, {{ dcnt }} 丢弃.	<b>{{ ucnt }} Einheit(en) registriert:</b><br>{{ ocnt }} offen, {{ ccnt }} geschlossen, {{ dcnt }} verworfen.
MODEL.VIEW.PREALLOC.TITLE	Predefine assignments	预先定义任务	Vorabzuweisungen definieren
MODEL.VIEW.PREALLOC.TEXT1	Please select the check types for which the predefined assignments are valid:	请选择有效的预先定义任务的验盘类型	Bitte wählen Sie die Prüfungsarten aus, für welche die Vorabzuweisung gelten soll:
MODEL.VIEW.PREALLOC.TEXT2	Please select the group and/or user to be assigned to the procedure. An existing definition may be removed with 'remove assignment' (in group or user selection).	请选择分配给该程序的组或用户。“移除任务”可移除一个现存的定义（在组或用户选择上）	Bitte wählen Sie Gruppe und/oder Benutzer aus, der vorab der Prozedur zugewiesen werden soll. Eine vorhandene Zuweisung kann auch mit 'Zuweisung aufheben' (in der Gruppen- oder Benutzerauswahl) entfernt werden.
MODEL.VIEW.PREALLOC.ANY	Any user or group	任何用户或组	Jeder Benutzer/Gruppe
MODEL.VIEW.PREALLOC.GROUP	Group:	组	Gruppe:
MODEL.EDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
MODEL.EDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
MODEL.EDIT.DTYPE	@:WFLOWEDIT.OPT1.DT	@:WFLOWEDIT.OPT1.DT	@:WFLOWEDIT.OPT1.DT
MODEL.EDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
MODEL.EDIT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
MODEL.EDIT.BUTTON.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE
MODEL.LIST.TOOLTIP.CLTF	Clear text filter	清除文字过滤	Textfilter leeren
MODEL.LIST.TOOLTIP.SHOWDEL	Show or hide deleted models	显示或隐藏删除的型号	Gelöschte Modelle zeigen oder verbergen
MODEL.LIST.TOOLTIP.NEWMODEL	New model	新型号	Neues Modell
MODEL.LIST.TOOLTIP.IMPORTMODEL	Import model	输入型号	Modell importieren
MODEL.LIST.TOOLTIP.GOTOUNITS	Go to units	切换到单位	Zu den Einheiten des Modells gehen
MODEL.LIST.TOPROW	{{ dtypt }} ({{ dtypc }}), {{ unitnum }} unit(s)	{{ dtypt }} ({{ dtypc }}), {{ unitnum }} 单位(s)	{{ dtypt }} ({{ dtypc }}), {{ unitnum }} Einheit(en)
MODEL.MODELS	@:FRAME.MANAGE.MODEL	@:FRAME.MANAGE.MODEL	@:FRAME.MANAGE.MODEL
MODEL.TESTCHECK.TITLE	Test model	测试型号	Modell testen
MODEL.TESTCHECK.TEXT	Please select the check type with which you want to test this version of the model:	请选择您想要用于测试此版本型号的检查类型:	Bitte wählen Sie die Prüfungsart, mit der Sie diese Version des Modells testen wollen:
STEP.MODEL.MEDIAMANAGER	Model {{ mcode }}: {{ mtitle }}	型号 {{ mcode }}: {{ mtitle }}	Modell {{ mcode }}: {{ mtitle }}
STEP.EDITOR.TITLE.EDIT	Editing {{stepname_en}}	编辑 {{stepname_en}}	Editiere {{stepname_de}}
STEP.EDITOR.TITLE.NEW	Editing new step	编辑新的步骤	Editiere neuen Schritt
STEP.ALERT.EDITVERSION	Do you really want to edit a version of step {{code}}?	你真的想要编辑此版本的步骤 {{code}}?	Möchten Sie wirklich eine finalisierte Version des Schritts {{code}} bearbeiten?
STEP.ALERT.EDITVERSIONDETAIL	You are about to edit a finalized version of this step. If this version is used in any workflow, checks already planned or finished will be altered.	你即将编辑此步骤的最终版本. 如果此版本被用在任何工作流程,检查已计划或完成将会改变.	Dies ist eine finalisierte Version dieses Schritts. Wird diese Version in Arbeitsabläufen eingesetzt, werden bereits eingerichtete oder bearbeitete Prüfungen verändert und u.U. dadurch invalid.
STEP.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
STEP.ALERT.DELETE	Do you really want to delete step {{scode}} from procedure {{pcode}}?	你真的想要删除步骤 {{scode}} 从流程 {{pcode}}?	Möchten Sie wirklich Schritt {{scode}} von Prozedur {{pcode}} löschen?
STEP.ALERT.DELETECASCADING	All measures ({{mcnt}}) defined for this step will be deleted as well!	所有测量 ({{mcnt}}) 定义此步骤也将被删除!	Alle Messungen ({{mcnt}}), die für diesen Schritt definiert sind, werden dann auch gelöscht!
STEP.ALERT.DELETEVERSION	You are about to remove a finalized version of this step. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.	你即将编辑此步骤的最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏"	Dies ist eine finalisierte Version dieses Schritts. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.
STEP.VIEW.MEDIAMANAGER	Procedure {{ pcode }}: {{ ptitle }}, Step {{ scode }}: {{ stitle }}	流程 {{ pcode }}: {{ ptitle }}, 步骤 {{ scode }}: {{ stitle }}	Prozedur {{ pcode }}: {{ ptitle }}, Schritt {{ scode }}: {{ stitle }}
STEP.VIEW.FLOWEDITOR	Step {{ scode }}: {{ stitle }}	步骤 {{ scode }}: {{ stitle }}	Schritt {{ scode }}: {{ stitle }}
STEP.VIEW.TOOLTIP.ACTREORD	@:MODEL.VIEW.TOOLTIP.ACTREORD	@:MODEL.VIEW.TOOLTIP.ACTREORD	@:MODEL.VIEW.TOOLTIP.ACTREORD
STEP.VIEW.TOOLTIP.UPLMEAS	Upload measures	上传测量	Messungen hochladen
STEP.VIEW.TOOLTIP.CLNMEAS	@:MEASURE.CLONE.TITLE	@:MEASURE.CLONE.TITLE	@:MEASURE.CLONE.TITLE
STEP.VIEW.TOOLTIP.NEWMEAS	Create new measure	创建新测量	Neue Messung erstellen
STEP.VIEW.TOOLTIP.REORDER	@:MODEL.VIEW.TOOLTIP.REORDER	@:MODEL.VIEW.TOOLTIP.REORDER	@:MODEL.VIEW.TOOLTIP.REORDER
STEP.VIEW.TOOLTIP.EDITRULE	Open workflow rule editor	开启工作流程规则编辑器	Editor für Ablaufregeln öffnen
STEP.VIEW.TOOLTIP.EXPMEAS	Export measure	输出测量	Messung exportieren
STEP.VIEW.TOOLTIP.EDITMEAS	Edit measure	编辑测量	Messung editieren
STEP.VIEW.TOOLTIP.TESTMEAS	Test measure	测试测量	Messung testen
STEP.VIEW.TOOLTIP.REMMEAS	Remove measure	移除测量	Messung entfernen
STEP.VIEW.VERSION	@:PROCEDURE.VIEW.VERS	@:PROCEDURE.VIEW.VERS	@:PROCEDURE.VIEW.VERS
STEP.VIEW.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
STEP.VIEW.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
STEP.VIEW.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
STEP.VIEW.MEDIA	@:MODEL.VIEW.MEDIA	@:MODEL.VIEW.MEDIA	@:MODEL.VIEW.MEDIA
STEP.VIEW.IMREG	@:MODEL.VIEW.IMREG	@:MODEL.VIEW.IMREG	@:MODEL.VIEW.IMREG
STEP.VIEW.DOCREG	@:MODEL.VIEW.DOCREG	@:MODEL.VIEW.DOCREG	@:MODEL.VIEW.DOCREG
STEP.VIEW.BUTTON.MEDMGR	@:MEDIAMGR.TITLE	@:MEDIAMGR.TITLE	@:MEDIAMGR.TITLE
STEP.VIEW.BUTTON.REDITOR	@:PROCEDURE.VIEW.REDITOR	@:PROCEDURE.VIEW.REDITOR	@:PROCEDURE.VIEW.REDITOR
STEP.VIEW.BUTTON.EXPORT	@:MODEL.VIEW.BUTTON.EXPORT	@:MODEL.VIEW.BUTTON.EXPORT	@:MODEL.VIEW.BUTTON.EXPORT
STEP.VIEW.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE
STEP.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT
STEP.VIEW.RULES	@:PROCEDURE.VIEW.RULES	@:PROCEDURE.VIEW.RULES	@:PROCEDURE.VIEW.RULES
STEP.VIEW.MEASURES	@:WFLOW.INTRO.MEASURES	@:WFLOW.INTRO.MEASURES	@:WFLOW.INTRO.MEASURES
STEP.CLONE.TITLE	Clone steps	复制步骤	Schritte klonen
STEP.CLONE.PSELTEXT	Select the procedure to clone steps from:	从此流程中选择复制步骤:	Bitte die Prozedur auswählen, von der Sie einzelne Schritte klonen möchten:
STEP.CLONE.TEXT	Select the steps to clone:	从此步骤中选择复制:	Schritte zum Klonen auswählen:
STEP.EDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
STEP.EDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
STEP.EDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
STEP.EDIT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
STEP.EDIT.BUTTON.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE
STEP.MATRIX.TITLE	Setup matrix annotations	设置矩阵的注解	Matrixbeschriftung einrichten
STEP.MATRIX.HINT	Select one of the titles in the matrix above, then edit in the textbox below. The smaller titles directly above the fields are later displayed in the top-left corner of the value field. Also, you can place a short text inside the value fields which will be used as a placeholder in the actual matrix.	小标题字段的正上方为后来显示在左上角的该字段值. 另外,你可以把一个短文字放置在该字段值这将用作为 在实际的矩阵一个预留位置	Einen der Titel in der Matrix auswählen, dann in der Textbox unten bearbeiten. Die kleineren Titel über den Wertfeldern werden später in der oberen linken Ecke der Wertfelder selbst angezeigt. Ebenfalls ist es möglich, einen kurzen Text in ein Wertfeld selbst einzugeben; dieser wird später in der Matrix als Platzhalter angezeigt.
STEP.MATRIX.BUTTON.BACK	Back	返回	Zurück
STEP.LIST.TOOLTIP.GOTOPROC	@:WFLOW.STEP.TOOLTIP.GOTOPROC	@:WFLOW.STEP.TOOLTIP.GOTOPROC	@:WFLOW.STEP.TOOLTIP.GOTOPROC
STEP.LIST.TOOLTIP.UPLOAD	@:PROCEDURE.VIEW.TOOLTIP.UPLSTEP	@:PROCEDURE.VIEW.TOOLTIP.UPLSTEP	@:PROCEDURE.VIEW.TOOLTIP.UPLSTEP
STEP.LIST.TOOLTIP.CLONE	@:STEP.CLONE.TITLE	@:STEP.CLONE.TITLE	@:STEP.CLONE.TITLE
STEP.LIST.TOOLTIP.ADDSTEP	@:PROCEDURE.VIEW.TOOLTIP.NEWSTEP	@:PROCEDURE.VIEW.TOOLTIP.NEWSTEP	@:PROCEDURE.VIEW.TOOLTIP.NEWSTEP
STEP.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF
STEP.LIST.BTMROW	Measures: {{ mcnt }}, Images: {{ icnt }}, Docs: {{ dcnt }}	测量: {{ mcnt }}, 图像: {{ icnt }}, 文档: {{ dcnt }}	Messungen: {{ mcnt }}, Bilder: {{ icnt }}, Dokumente: {{ dcnt }}
STEP.STEP	@:WFLOWEDIT.OPT1.S	@:WFLOWEDIT.OPT1.S	@:WFLOWEDIT.OPT1.S
STEP.STEPS	@:WFLOW.INTRO.STEPS	@:WFLOW.INTRO.STEPS	@:WFLOW.INTRO.STEPS
STEP.FORPROC	of procedure {{ proc }}	程序 {{ proc }}	von Prozedur {{ proc }}
PROCEDURE.EDITOR.TITLE.EDIT	Editing {{procname_en}}	编辑 {{procname_en}}	Editiere {{procname_de}}
PROCEDURE.EDITOR.TITLE.NEW	Editing new procedure	@:MODEL.EDITOR.TITLE.NEW	Editiere neue Prozedur
PROCEDURE.LIST.GOTOPROC.TITLE	Usage view	查看使用情况	Verwendungsansicht
PROCEDURE.LIST.GOTOPROC.TEXT	The following models use the selected procedure. You may choose one to go to the corresponding management page.	以下型号选定使用流程. 你可以去选择一个去转到相应的管理页面.	Die folgenden Modelle verwenden die gewählte Prozedur. Sie können eine auswählen um zur entsprechenden Bearbeitungsseite zu wechseln.
PROCEDURE.LIST.TOOLTIP.IMPORT	Import procedures	输入流程	Prozeduren importieren
PROCEDURE.LIST.TOOLTIP.CLONE	@:PROCEDURE.CLONE.TITLE	@:PROCEDURE.CLONE.TITLE	@:PROCEDURE.CLONE.TITLE
PROCEDURE.LIST.TOOLTIP.ADD	Create new procedure	创建新流程	Neue Prozedur erstellen
PROCEDURE.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF
PROCEDURE.LIST.TOOLTIP.SHOWDEL	Show or hide deleted procedures	显示或隐藏删除的流程	Gelöschte Prozeduren zeigen oder verbergen
PROCEDURE.LIST.TOOLTIP.GOTOMODEL	Show and optionally go to models using this procedure	使用此流程显示和转到可选择地型号	Modelle, die diese Prozedur verwenden anzeigen und optional dorthin gehen
PROCEDURE.LIST.PROCEDURES	@:FRAME.MANAGE.PROCEDURE	@:FRAME.MANAGE.PROCEDURE	@:FRAME.MANAGE.PROCEDURE
PROCEDURE.LIST.BTMROW	Steps: {{ stpcnt }}, Used by: {{ modcnt }} model(s)	步骤: {{ stpcnt }}, 经由 : {{ modcnt }} 型号(s)	Schritte: {{ stpcnt }}, verwendet von {{ modcnt }} Modell(en)
PROCEDURE.LIST.BTMROWUPD	, updatable: {{ updcnt }}	,可更新的: {{ updcnt }}	, alte Vers.: {{ updcnt }}
PROCEDURE.ALERT.EDITVERSION	Do you really want to edit a version of procedure {{code}}?	你真的想要编辑此版本的流程 {{code}}?	Möchten Sie wirklich eine finalisierte Version der Prozedur {{code}} bearbeiten?
PROCEDURE.ALERT.EDITVERSIONDETAIL	You are about to edit a finalized version of this procedure. If this version is used in any workflow, checks already planned or finished will be altered.	你即将编辑此流程的最终版本. 如果此版本被用在任何工作流程,检查已计划或完成将会改变.	Dies ist eine finalisierte Version dieser Prozedur. Wird diese Version in Arbeitsabläufen eingesetzt, werden bereits eingerichtete oder bearbeitete Prüfungen verändert und u.U. dadurch invalid.
PROCEDURE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
PROCEDURE.ALERT.DELETE	Do you really want to delete procedure {{code}}?	你真的想要删除此版本的流程 {{code}}?	Möchten Sie wirklich Prozedur {{code}} löschen?
PROCEDURE.ALERT.DELETECASCADING	All steps ({{scnt}}) and their measures defined for this procedure will be deleted as well!	所有步骤 ({{scnt}}) 和他们为此流程测量定义也将被删除!	Alle Schritte ({{scnt}}) und Messungen, die für diese Prozedur definiert sind, werden dann auch gelöscht!
PROCEDURE.ALERT.DELETEVERSION	You are about to remove a finalized version of this procedure. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.	你即将移除此流程的最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏	Dies ist eine finalisierte Version dieser Prozedur. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.
PROCEDURE.ALERT.FINALIZE.TITLE	Do you really want to finalize procedure {{code}}?	你真的想要完成流程 {{code}}?	Möchten Sie wirklich die aktuelle Version der Prozedur {{code}} finalisieren?
PROCEDURE.ALERT.FINALIZE.TEXT	The current data will be finalized into a new version. This version will be published and may then be used for new units and checks. Be sure to update and finalize all models using this procedure to use this new version.	当前的数据将最终形成一个新版本. 这个版本将被发表,随后可将其用于新的单位和检查. 确保更新并且最终所有型号用此流程来使用这个新的版本.	Die aktuellen Daten werden finalisiert und eine neue Version zum Editieren bereitgestellt. Die finalisierte Version kann dann für neue Einheiten und Prüfungen verwendet werden. Stellen Sie sicher, dass Sie alle Modelle, die diese Prozedur verwenden, aktualisieren und finalisieren um diese Version verwenden zu können.
PROCEDURE.ALERT.FULLUPDATE.TITLE	Do you really want to update pending models?	你真的想更新搁置的型号?	Möchten Sie wirklich alle Modelle, die diese Prozedur verwenden, automatisch aktualisieren?
PROCEDURE.ALERT.FULLUPDATE.TEXT	Models using an older version of this procedure will be updated; this means, that a new version of each model will be created automatically. Other pending changes in the model's editing stage are not finalized.	使用旧版本的这个流程的型号将被更新; 这意味着,每个模型将自动创建为新的版本. 其它搁置变更,在此模型的编辑阶段没有做最后确定.	Modelle, die eine ältere Version dieser Prozedur verwenden, werden aktualisiert; dies bedeutet, dass eine neue Version jedes Modells automatisch erzeugt wird. Allfällige andere Änderungen an den Modellen werden dabei nicht finalisiert.
PROCEDURE.ALERT.RESET.TITLE	Do you really want to reset all changes?	您真的要重置所有更改吗？	Wirklich alle Änderungen zurücksetzen?
PROCEDURE.ALERT.RESET.TEXT	All changes made to this object since the last finalization will be erased permanently.	至上次审定时对此对象所做的所有更改都将被永久删除.	Alle Änderungen, die an diesem Objekt seit der letzten Finalisierung vorgenommen wurden, werden unwiderruflich gelöscht.
PROCEDURE.SWITCHV.TITLE	@:MODEL.SWITCHV.TITLE	@:MODEL.SWITCHV.TITLE	@:MODEL.SWITCHV.TITLE
PROCEDURE.SWITCHV.MESSAGE	@:MODEL.SWITCHV.MESSAGE	@:MODEL.SWITCHV.MESSAGE	@:MODEL.SWITCHV.MESSAGE
PROCEDURE.VIEW.FLOWEDITOR	Procedure {{ pcode }}: {{ ptitle }}	流程 {{ pcode }}: {{ ptitle }}	Prozedur {{ pcode }}: {{ ptitle }}
PROCEDURE.VIEW.TOOLTIP.STAT	Generate statistic report	生成统计数据报告	Statistischen Report generieren
PROCEDURE.VIEW.TOOLTIP.CHVERS	@:MODEL.VIEW.TOOLTIP.SWITCHVER	@:MODEL.VIEW.TOOLTIP.SWITCHVER	@:MODEL.VIEW.TOOLTIP.SWITCHVER
PROCEDURE.VIEW.TOOLTIP.UPDMOD	Update models to latest version	最新版本的更新型号	Modelle auf neueste Version aktualisieren
PROCEDURE.VIEW.TOOLTIP.GOTOSTEP	@:WFLOW.INTRO.TOOLTIP.GOTOSTEP	@:WFLOW.INTRO.TOOLTIP.GOTOSTEP	@:WFLOW.INTRO.TOOLTIP.GOTOSTEP
PROCEDURE.VIEW.TOOLTIP.ACTREORD	@:MODEL.VIEW.TOOLTIP.ACTREORD	@:MODEL.VIEW.TOOLTIP.ACTREORD	@:MODEL.VIEW.TOOLTIP.ACTREORD
PROCEDURE.VIEW.TOOLTIP.UPLSTEP	Upload steps	上传步骤	Schritte hochladen
PROCEDURE.VIEW.TOOLTIP.CLNSTEP	@:STEP.CLONE.TITLE	@:STEP.CLONE.TITLE	@:STEP.CLONE.TITLE
PROCEDURE.VIEW.TOOLTIP.NEWSTEP	Create new step	创建新步骤	Neuen Schritt erstellen
PROCEDURE.VIEW.TOOLTIP.REORD	@:MODEL.VIEW.TOOLTIP.REORDER	@:MODEL.VIEW.TOOLTIP.REORDER	@:MODEL.VIEW.TOOLTIP.REORDER
PROCEDURE.VIEW.TOOLTIP.EXPSTEP	Export step	输出步骤	Schritt exportieren
PROCEDURE.VIEW.TOOLTIP.REMSTEP	Remove step	移除步骤	Schritt entfernen
PROCEDURE.VIEW.TOOLTIP.TESTVER	Test version	测试版本	Version testen
PROCEDURE.VIEW.TOOLTIP.CHANGELOG	View change log	查看更改日志	Änderungslog einsehen
PROCEDURE.VIEW.TOOLTIP.RESET	Reset all changes to the last finalized version	重置所有更改至上次审定版本	Alle Änderungen zur letzten finalisierten Version zurücksetzen
PROCEDURE.VIEW.TOOLTIP.ENFORCETOP	The first entry may not be workflow locked	首个进入点可能不是锁定的工作流	Der erste Eintrag kann keinem Ablaufzwang unterliegen
PROCEDURE.VIEW.TOOLTIP.ENFORCE0	No workflow lock	无锁定工作流	Kein Ablaufzwang
PROCEDURE.VIEW.TOOLTIP.ENFORCE1	Lock: May only be processed when the last entry has been completed	锁定：最后一个进入点完成后方可执行	Kann erst nach dem vorherigen Eintrag bearbeitet werden
PROCEDURE.VIEW.TOOLTIP.ENFORCE2	Full lock: May only be processed when all prior entries have been completed	全锁：所有先前的进入点完成后方可执行	Kann erst bearbeitet werden, wenn alle vorherigen Einträge abgeschlossen sind
PROCEDURE.VIEW.VERS	Version	版本	Version
PROCEDURE.VIEW.UPDATEINFO	There are {{ updcnt }} model(s) using an older version of this procedure.	有 {{ updcnt }} 型号(s) 使用此流程的旧版本.	Es gibt {{ updcnt }} Modelle, die eine ältere Version dieser Prozedur verwenden.
PROCEDURE.VIEW.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
PROCEDURE.VIEW.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
PROCEDURE.VIEW.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
PROCEDURE.VIEW.RULES	Workflow Rules	工作流程规则	Workflowregeln
PROCEDURE.VIEW.REDITOR	Rule Editor	规则编辑器	Regeleditor
PROCEDURE.VIEW.BUTTON.EXPORT	@:MODEL.VIEW.BUTTON.EXPORT	@:MODEL.VIEW.BUTTON.EXPORT	@:MODEL.VIEW.BUTTON.EXPORT
PROCEDURE.VIEW.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE
PROCEDURE.VIEW.BUTTON.FIN	@:MODEL.VIEW.BUTTON.FIN	@:MODEL.VIEW.BUTTON.FIN	@:MODEL.VIEW.BUTTON.FIN
PROCEDURE.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT
PROCEDURE.VIEW.BUTTON.STATISTIC	Statistics	统计数据	Statistik
PROCEDURE.VIEW.STEPS	@:WFLOW.INTRO.STEPS	@:WFLOW.INTRO.STEPS	@:WFLOW.INTRO.STEPS
PROCEDURE.VIEW.MEASURES	Measures: {{ msrcnt }}	测量: {{ msrcnt }}	Messungen: {{ msrcnt }}
PROCEDURE.CLONE.TITLE	Clone procedures	复制流程	Prozeduren klonen
PROCEDURE.CLONE.TEXT	Select one or more procedures to clone:	选择复制一个或多个流程:	Bitte wählen Sie die zu klonenden Prozeduren:
PROCEDURE.EDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
PROCEDURE.EDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
PROCEDURE.EDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
PROCEDURE.EDIT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
PROCEDURE.EDIT.BUTTON.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE
PROCEDURE.TESTCHECK.TITLE	Test procedure	测试程序	Prozedur testen
PROCEDURE.TESTCHECK.TEXT	Please select the check type with which you want to test this version of the procedure:	请选择您想要用于测试此版本程序的检查类型:	Bitte wählen Sie die Prüfungsart, mit der Sie diese Version der Prozedur testen wollen:
MEASURE.EDITOR.TITLE.EDIT	Editing {{mname_en}}	编辑 {{mname_en}}	Editiere {{mname_de}}
MEASURE.EDITOR.TITLE.NEW	Editing new measure	编辑新的测量	Editiere neue Messung
MEASURE.EDITOR.NOTOOL	No tool used	没有使用的工具	Kein Werkzeug benötigt
MEASURE.VIEW.FLOWEDITOR	Measure {{ mcode }}: {{ mtitle }}	测量 {{ mcode }}: {{ mtitle }}	Messung {{ mcode }}: {{ mtitle }}
MEASURE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
MEASURE.ALERT.DELETE	Do you really want to delete measure {{mcode}} from step {{scode}} in procedure {{pcode}}?	你真的想要删除测量 {{mcode}} 来自步骤 {{scode}} 流程中 {{pcode}}?	Möchten Sie wirklich die Messung {{ mcode }} von Schritt {{ scope }} in Prozedur {{ pcode }} löschen?
MEASURE.ALERT.DELETEVERSION	You are about to remove a finalized version of this measure. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.	你即将移除此流程的最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏.	Dies ist eine finalisierte Version dieser Messung. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.
MEASURE.CLONE.TITLE	Clone measures	复制测量	Messungen klonen
MEASURE.CLONE.PSELTEXT	Select the procedure to clone measures from:	从此流程中选择复制测量:	Bitte wählen Sie die Prozedur, von der Sie einzelne Messungen klonen möchten:
MEASURE.CLONE.TEXT	Select the measures to clone:	从此测量中选择复制:	Bitte wählen Sie die zu klonenden Messungen:
MEASURE.EDIT.COMPARATOR	Comparator	比较仪	Vergleichsoperator
MEASURE.EDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
MEASURE.EDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
MEASURE.EDIT.HINTS	Hints	提示	Hinweise
MEASURE.EDIT.TTYPE	Tool type	工具类型	Werkzeugtyp
MEASURE.EDIT.MTYPE	Measure type	测量类型	Messungstyp
MEASURE.EDIT.MANDATORY	Mandatory	强制型	Erforderlich
MEASURE.EDIT.YES	@:WFLOW.INPUT.YES	@:WFLOW.INPUT.YES	@:WFLOW.INPUT.YES
MEASURE.EDIT.NO	@:WFLOW.INPUT.NO	@:WFLOW.INPUT.NO	@:WFLOW.INPUT.NO
MEASURE.EDIT.TARGET	Target Value	目标值	Zielwert
MEASURE.EDIT.UNIT	@:WFLOW.INTRO.UNIT	@:WFLOW.INTRO.UNIT	@:WFLOW.INTRO.UNIT
MEASURE.EDIT.THOLD	@:WFLOW.INPUT.THRESH	@:WFLOW.INPUT.THRESH	@:WFLOW.INPUT.THRESH
MEASURE.EDIT.MIN	Minimum	最小量	Minimum
MEASURE.EDIT.MAX	Maximum	最大量	Maximum
MEASURE.EDIT.MINLEN	Min. Textlength	最小量文字长度	Min. Textlänge
MEASURE.EDIT.REGEXP	Regular Expression	正规表达式	Regulärer Ausdruck
MEASURE.EDIT.EXP	Expected	预期的	Erwartet
MEASURE.EDIT.ANY	Any	任何的	Beide
MEASURE.EDIT.MATRIX	Matrix	矩阵	Matrix
MEASURE.EDIT.BUTTON.SETUP	Setup titles	设置标题	Matrix bearbeiten
MEASURE.EDIT.BUTTON.TEST	Save & Test Measure	存储和测试测量	Speichern & testen
MEASURE.EDIT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
MEASURE.EDIT.BUTTON.CLOSE	Save & Close	存储和关闭	Speichern & schliessen
MEASURE.EDIT.NUMCOL	Number columns	列数	Anzahl Spalten
MEASURE.EDIT.NUMROW	Number rows	行数	Anzahl Reihen
MEASURE.EDIT.FORMULA	Formula	公式	Formel
MEASURE.EDIT.FLOATFRM.TEXT	Numeric format	数字格式	Zahlenformat
MEASURE.EDIT.FLOATFRM.STD	Standard	标准	Standard
MEASURE.EDIT.FLOATFRM.INT	No decimal places	没有小数位	Keine Nachkommastellen
MEASURE.EDIT.FLOATFRM.1DIGIT	1 decimal place	一个小数位	1 Nachkommastelle
MEASURE.EDIT.FLOATFRM.2DIGIT	2 decimal places	两个小数位	2 Nachkommastellen
MEASURE.EDIT.FLOATFRM.3DIGIT	3 decimal places	三个小数位	3 Nachkommastellen
MEASURE.EDIT.FLOATFRM.4DIGIT	4 decimal places	四个小数位	4 Nachkommastellen
MEASURE.EDIT.FLOATFRM.6DIGIT	6 decimal places	六个小数位	6 Nachkommastellen
MEASURE.EDIT.COMPLCODE	Grouping	分组	Gruppierung
MEASURE.EDIT.INTERNAL	Internal	内部的	Intern
MEASURE.TYPES.THRESHOLD	@:WFLOW.INPUT.THRESH	@:WFLOW.INPUT.THRESH	@:WFLOW.INPUT.THRESH
MEASURE.TYPES.ABSOLUTE	Absolute value	绝对值	Absoluter Wert
MEASURE.TYPES.ABSOLUTERNG	Absolute value in range	绝对值范围	Absoluter Wert in einem Bereich
MEASURE.TYPES.TEXT	Random text	随机文字	Beliebiger Text
MEASURE.TYPES.REGEXP	Pattern checked text	选中的文本模式	Geprüfter Text
MEASURE.TYPES.BOOL	Flag (yes/no)	标记(yes/no)	Ja/Nein-Frage
MEASURE.TYPES.RESCHECK	Fixed result check	固定的检查结果	Prüfung eines vorgegebenen Wertes
MEASURE.TYPES.THRESHOLDMATRIX	Threshold, deviation input by matrix calculation	临界值,通过矩阵计算输入误差	Abweichung per Matrixberechnung
MEASURE.TYPES.ABSOLUTEMATRIX	Absolute value, input by matrix calculation	绝对值,通过矩阵计算输入	Absoluter Wert per Matrixberechnung
MEASURE.TYPES.ABSOLUTERNGMATRIX	Absolute value in range, input by matrix calculation	绝对值范围,通过矩阵计算输入	Absoluter Wert in einem Bereich per Matrixberechnung
MEASURE.TYPES.STATISTICAL	Statistical value	统计值	Statistischer Wert
MEASURE.TYPES.STATISTICALMATRIX	Statistical value, input by matrix calculation	统计值, 通过矩阵计算输入	Statistischer Wert per Matrixberechnung
MEASURE.TYPES.TIMERSTART	Timer (start time)	计时 (开始时间)	Zeiterfassung (Startzeit)
MEASURE.TYPES.TIMERSTOP	Timer (stop time)	计时 (停止时间)	Zeiterfassung (Stopzeit)
MEASURE.TYPES.TIMERSTOPQ	Checked timer (stop time)	检查后的计时器（终止时间）	Geprüfte Zeiterfassung (Stopzeit)
MEASURE.TYPES.TIMERSTOPC	Check uncorrected time measurement	检查未纠正的时间测定	Prüfung der unkorrigierten Zeiterfassung
TOOL.EDITOR.TITLE.EDIT	Editing {{ tool }}	编辑 {{ tool }}	Editiere {{ tool }}
TOOL.EDITOR.TITLE.NEW	Editing new tool type	编辑新的工具类型	Editiere neuen Werkzeugtyp
TOOL.NEWUNIT.TITLE	Create new unit	创建新单位	Neue Werkzeugeinheit
TOOL.NEWUNIT.TEXT	Enter the serial number or any other identifying code in the text field below:	在下面的文字段输入序号或任何其他识别码:	Bitte geben Sie die Seriennummer oder einen anderen identifizierenden Wert in das Textfeld ein:
TOOL.EDITUNIT.TITLE	Edit unit	编辑单位	Editiere Werkzeugeinheit
TOOL.EDITUNIT.TEXT	@:TOOL.NEWUNIT.TEXT	@:TOOL.NEWUNIT.TEXT	@:TOOL.NEWUNIT.TEXT
TOOL.COMMENTUNIT.TITLE	Set comment	可做注释	Kommentieren
TOOL.COMMENTUNIT.TEXT	Create, edit or delete the comment for this unit:	 edit or delete the comment for this unit:"	Bitte erstellen, ändern oder löschen Sie hier den Kommentar für diese Einheit:
TOOL.ALERT.DELETEUNIT	Do you really want to delete tool unit {{code}}?	你真的想要删除工具单位 {{code}}?	Möchten Sie wirklich die Werkzeugeinheit {{code}} löschen?
TOOL.ALERT.NODELETEUNIT	You may not delete this tool {{code}}!	您不能删除此工具类型 {{code}}!	Werkzeugeinheit {{code}} kann nicht gelöscht werden!
TOOL.ALERT.DELETEUNITCASCADING	It is already used in one or more measures. Please set it to 'disabled' instead.	它已经应用于一个或多个测量. 请将它设置为'禁用'.	Die Einheit wird bereits in einigen Prüfungen verwendet; bitte setzen Sie diese Einheit auf den Status 'deaktiviert'.
TOOL.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
TOOL.ALERT.DENY	Action not possible...	动作不可能的	Aktion nicht möglich...
TOOL.ALERT.DELETE	Do you really want to delete tooltype {{code}}?	你真的想要删除工具类型 {{code}}?	Möchten Sie wirklich den Werkzeugtyp {{code}} löschen?
TOOL.ALERT.NODELETE	You may not delete this tool type {{code}}!	@:TOOL.ALERT.NODELETEUNIT	Werkzeugtyp {{code}} kann nicht gelöscht werden!
TOOL.ALERT.DELETECASCADING	One or more units are already defined for this type. Please set it to 'disabled' instead.	@:TOOL.ALERT.DELETEUNITCASCADING	Für den Typ sind bereits einige Einheiten definiert; bitte setzen Sie den Typ daher auf den Status 'deaktiviert'.
TOOL.MEDIAMANAGER	Tool type {{ ttcode }}: {{ tttitle }}	工具类型 {{ ttcode }}: {{ tttitle }}	Werkzeugtyp {{ ttcode }}: {{ tttitle }}
TOOL.EDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
TOOL.EDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
TOOL.EDIT.HINTS	@:TLISTEDIT.DESC	@:MEASURE.EDIT.HINTS	@:TLISTEDIT.DESC
TOOL.EDIT.DIS.TITLE	@:TLISTEDIT.DISSET.TITLE	@:TLISTEDIT.DISSET.TITLE	@:TLISTEDIT.DISSET.TITLE
TOOL.EDIT.DIS.TRUE	The tool type is disabled and may not be used any longer.	此工具类型已被删除且不得再使用	Der Werkzeugtyp ist deaktiviert und darf nicht länger verwendet werden.
TOOL.EDIT.DIS.FALSE	The tool type is active.	此工具类型已启动	Der Werkzeugtyp ist aktiv.
TOOL.EDIT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
TOOL.EDIT.BUTTON.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE
TOOL.LIST.TOOLTIP.NEW	Create new tool type	创建新的工具类型	Neuen Werkzeugtyp erstellen
TOOL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF
TOOL.LIST.TOOLTIP.SHOWDEL	Show deactivated tool types	显示停用的工具类型	Deaktivierte Werkzeugtypen zeigen
TOOL.LIST.TTYPES	Tool types	工具类型	Werkzeugtypen
TOOL.LIST.BTMROW	Measures: {{ mcnt }}, Tool units: {{ tcnt }}	测量: {{ mcnt }}, 工具单位: {{ tcnt }}	Messungen: {{ mcnt }}, Werkzeugeinheiten: {{ tcnt }}
TOOL.VIEW.TOOLTIP.NEWTUNIT	Create new tool unit	创建新的工具单位	Neue Werkzeugeinheit erstellen
TOOL.VIEW.TOOLTIP.EDITTUNIT	Edit tool unit	编辑工具单位	Werkzeugeinheit editieren
TOOL.VIEW.TOOLTIP.REENABLE	Reenable tool unit	重新启用工具单位	Werkzeugeinheit wieder aktivieren
TOOL.VIEW.TOOLTIP.DISABLE	Disable tool unit	禁用工具单位	Werkzeugeinheit deaktivieren
TOOL.VIEW.TOOLTIP.REMTUNIT	Remove tool unit	移除工具单位	Werkzeugeinheit entfernen
TOOL.VIEW.TOOLTIP.SHOWDEL	Show deactivated tool units	显示停用的工具单位	Deaktivierte Werkzeugeinheiten zeigen
TOOL.VIEW.TOOLTIP.SETCOMMENT	comment on this unit	该单元的注释	Diese Einheit kommentieren
TOOL.VIEW.TOOLTIP.REPORT	Generate tool unit usage report	生成工具单元使用报告	Werkzeugverwendungsreport generieren
TOOL.VIEW.DISABLED	@:TOOL.EDIT.DIS.TRUE	@:TOOL.EDIT.DIS.TRUE	@:TOOL.EDIT.DIS.TRUE
TOOL.VIEW.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
TOOL.VIEW.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE	@:TLISTEDIT.TITLE
TOOL.VIEW.DESCRIPTION	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
TOOL.VIEW.MEDIA	@:MODEL.VIEW.MEDIA	@:MODEL.VIEW.MEDIA	@:MODEL.VIEW.MEDIA
TOOL.VIEW.IMGREG	{{ imgcnt }} Image(s) registered	{{ imgcnt }}图像(s) 已注册的	{{ imgcnt }} Bilder registriert
TOOL.VIEW.DOCREG	{{ doccnt }} Document(s) registered	{{ doccnt }} 文档(s) 已注册的	{{ doccnt }} Dokumente registriert
TOOL.VIEW.BUTTON.MEDMGR	@:MEDIAMGR.TITLE	@:MEDIAMGR.TITLE	@:MEDIAMGR.TITLE
TOOL.VIEW.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE
TOOL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT
TOOL.VIEW.MEDINFO	Note: only the first image/first document is accessible!	注解: 只有第一个图像/第一个文档是可存取的	Hinweis: Nur jeweils das erste Bild bzw. Dokument ist verfügbar!
TOOL.VIEW.TUNITS	Tool units	工具单位	Werkzeugeinheiten
TOOL.VIEW.TOOLDEL	Disabled	刪除	Deaktiviert
TOOL.VIEW.TOOLUNITCNT	Used in {{ ucnt }} measurements	用于 {{ ucnt }}測量值	Verwendet in {{ ucnt }} Messungen
TOOL.REPORT.TITLE	Generate usage report	生成使用报告	Verwendungsreport generieren
TOOL.REPORT.DATE1	You can select a date from which all uses of this tool unit will be included in the report. If you do not wish to select a date (include all uses from beginning), then click 'Save' without choosing a date.	 then click 'Save' without choosing a date."	Sie können hier das Datum festlegen, ab dem Verwendungen der Werkzeugeinheit in dem Report aufgenommen werden. Wenn Sie kein Datum festlegen möchten (alle Verwendungen seit Anfang), dann betätigen Sie einfach 'Sichern' ohne ein Datum auszuwählen.
TOOL.REPORT.DATE2	You can select a date until which all uses of this tool unit will be included in the report. If you do not wish to select a date (include all uses until now), then click 'Save' without choosing a date.	 then click 'Save' without choosing a date."	Sie können hier das Datum festlegen, bis zu dem Verwendungen der Werkzeugeinheit in dem Report aufgenommen werden. Wenn Sie kein Datum festlegen möchten (alle Verwendungen bis jetzt), dann betätigen Sie einfach 'Sichern' ohne ein Datum auszuwählen.
TOOL.REPORT.SORT.TEXT	Please choose:	请选择：	Bitte wählen Sie aus, nach welchem Kriterium der Report sortiert sein soll:
TOOL.REPORT.SORT.MODEL	Sort by model, unit, check	 unit	Nach Model, Einheit, Prüfung
TOOL.REPORT.SORT.TIME	Sort by timestamp	按照时间标记分类	Nach Zeitstempel
CHECKTYPE.MEDIAMANAGER	Devicetype {{ code }}: {{ title }}	设备类型{{ code }}: {{ title }}	Gerätetype {{ code }}: {{ title }}
DEVICETYPE.MEDIAMANAGER	Checktype {{ code }}: {{ title }}	检查类型 {{ code }}: {{ title }}	Prüfungsart {{ code }}: {{ title }}
UNIT.EDITOR.TITLE.EDIT	Editing {{ unit }}	编辑 {{ unit }}	Editiere {{ unit }}
UNIT.EDITOR.TITLE.NEW	Editing new unit	编辑新单位	Editiere neue Einheit
UNIT.ARCHIVE.TTL	Search in archive	搜索存档	In Archiv suchen
UNIT.ARCHIVE.TXT	Enter one or more short strings matching a part of the code or customer string of the unit(s) you are looking for.	输入一个或多个短字符串的一部分代码或你在寻找客户的单位(s):	Bitte eine oder mehrere Suchbegriffe eingeben, die mit einem Teil des Codes oder des Kundennamens übereinstimmen, nach dem Sie suchen.
UNIT.ARCHIVE.MANYRES.TTL	Too many results	太多的结果	Zu viele Ergebnisse
UNIT.ARCHIVE.MANYRES.TXT	The search returned more than 100 entries, only the first 100 are shown. Please refine your search.	此搜寻返回超过100次项目,只有显示前100. 请修改您的搜索.	Die Suche ergab mehr als 100 Einträge, nur die ersten 100 werden angezeigt. Bitte verfeinern Sie ihre Suche.
UNIT.NEWCHECK.TITLE	Create new check	创建新检查	Neue Prüfung erstellen
UNIT.NEWCHECK.TEXT	Select the check type in the box below:	选择下面的检查键入框中:	Bitte die Prüfungsart wählen:
UNIT.ALERT.DELETECHECK.TITLE	Do you really want to delete this check?	你真的想要删除这次检查?	Möchten Sie wirklich diese Prüfung löschen?
UNIT.ALERT.DELETECHECK.TEXT	All measures and gathered data will be deleted as well!	所有测量和收集的数据也将被删除！	Alle Messungen und weiteren gesammelten Daten gehen hierbei ebenfalls verloren!
UNIT.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER	@:MEDIAMGR.IMAGE.ALERT.CONFHEADER
UNIT.ALERT.DELETE	Do you really want to delete unit {{code}}?	你真的想要删除单位 {{code}}?	Möchten Sie wirklich die Einheit {{code}} löschen?
UNIT.ALERT.DELETECASCADING	All checks ({{ccnt}}) of this unit will be deleted as well! You may want to set the status of this unit to 'discarded' instead.	所有检查 ({{ccnt}}) 这个单位也将被删除！您可能希望将此单元的状态设置为'丢弃'.	Alle Prüfungen dieser Einheit ({{ccnt}}) werden inklusive aller Messergebnisse auch gelöscht!
UNIT.ALERT.STATUSCHG.TTL	Change unit status	更改单位状态	Status der Einheit ändern
UNIT.ALERT.STATUSCHG.TXT	Select the new status of this unit:	选择这个单位的状态:	Bitte wählen Sie den neuen Status dieser Einheit:
UNIT.ALERT.UNARCHIVE.TXT	Really unarchive unit?	真的解档单位?	Wirklich die Einheit aus dem Archiv nehmen?
UNIT.MODIFY.DATE.TITLE	Set new date	设置新日期	Neues Datum setzen
UNIT.MODIFY.DATE.COMMISSIONED	Select the date when this unit has been commissioned	选择已调试的单位日期	Bitte das Datum wählen, an dem diese Einheit in Auftrag gegeben wurde
UNIT.MODIFY.DATE.FINISHED	Select the date when this unit has been finished	选择已完成的单位日期	Bitte das Datum wählen, an dem diese Einheit fertiggestellt wurde
UNIT.MODIFY.DATE.DELIVERED	Select the date when this unit has been delivered to the customer	选择已交付给客户的单位日期	Bitte das Datum wählen, an dem diese Einheit an den Kunden ausgeliefert wurde
UNIT.MODIFY.DATE.APPROVED	Select the date when this unit has been approved by the customer	选择客户已核准的单位日期	Bitte das Datum wählen, an dem diese Einheit vom Kunden abgenommen wurde
UNIT.MODIFY.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE
UNIT.MODIFY.COMMENT.TEXT	@:MEASUREMENT.INPUT.COMMENT.TEXT	@:MEASUREMENT.INPUT.COMMENT.TEXT	@:MEASUREMENT.INPUT.COMMENT.TEXT
UNIT.EDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
UNIT.EDIT.CUST	Customer	客户	Kunde
UNIT.EDIT.MODEL	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD
UNIT.EDIT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
UNIT.EDIT.BUTTON.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE
UNIT.LIST.TOOLTIP.NEW	@:TOOL.NEWUNIT.TITLE	@:TOOL.NEWUNIT.TITLE	Neue Einheit erfassen
UNIT.LIST.TOOLTIP.GOTOMOD	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL
UNIT.LIST.TOOLTIP.ARCHMODE	Enter or leave archive mode	进入或离开存档模式	Archivmodus starten oder verlassen
UNIT.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF
UNIT.LIST.TOOLTIP.LOCKMODE	Show or hide closed units	显示或隐藏关闭的单位	Abgeschlossene Einheiten anzeigen oder verbergen
UNIT.LIST.UNITS	@:FRAME.MANAGE.UNIT	@:FRAME.MANAGE.UNIT	@:FRAME.MANAGE.UNIT
UNIT.FORMODEL	of model {{ mdl }}	型号 {{ mdl }}	des Modells {{ mdl }}
UNIT.INARCHIVE	in archive, matching '{{ pat }}'	存档,匹配 '{{ pat }}'	im Archiv, passend auf '{{ pat }}'
UNIT.VIEW.TOOLTIP.UNITSTAT	Set unit status	设置单位状态	Status der Einheit ändern
UNIT.VIEW.TOOLTIP.UNARCH	Unarchive (reactivate) unit	解档(reactivate)单位	Einheit aus Archiv nehmen (reaktivieren)
UNIT.VIEW.TOOLTIP.GOTOMOD	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL
UNIT.VIEW.TOOLTIP.EDITDATE	Set or change date	设置或更改日期	Datum setzen oder ändern
UNIT.VIEW.TOOLTIP.EDITCOMM	Add or change comment	增加或更改评论	Kommentar hinzufügen oder ändern
UNIT.VIEW.TOOLTIP.ADDCHK	@:UNIT.NEWCHECK.TITLE	@:UNIT.NEWCHECK.TITLE	@:UNIT.NEWCHECK.TITLE
UNIT.VIEW.TOOLTIP.DELCHK	@:MSRSTAT.TOOLTIP.REMCHK	@:MSRSTAT.TOOLTIP.REMCHK	@:MSRSTAT.TOOLTIP.REMCHK
UNIT.VIEW.TOOLTIP.GOTOCHK	Go to check	转到检查	Zur Prüfung gehen
UNIT.VIEW.MODEL	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD
UNIT.VIEW.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE	@:TLISTEDIT.CODE
UNIT.VIEW.CUST	@:UNIT.EDIT.CUST	@:UNIT.EDIT.CUST	@:UNIT.EDIT.CUST
UNIT.VIEW.CHECKS	Checks	检查	Prüfungen
UNIT.VIEW.CHK	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER
UNIT.VIEW.DATES	Dates	日期	Daten
UNIT.VIEW.COMM.ON	Commissioned on	调试于	In Auftrag gegeben am
UNIT.VIEW.COMM.NOT	Not yet commissioned	尚未调试	Noch nicht in Auftrag gegeben
UNIT.VIEW.FIN.ON	Finished on	结束于	Fertiggestellt am
UNIT.VIEW.FIN.NOT	Not yet finished	尚未结束	Noch nicht fertiggestellt
UNIT.VIEW.DEL.ON	Delivered on	交货于	Ausgeliefert am
UNIT.VIEW.DEL.NOT	Not yet delivered	尚未交货	Noch nicht ausgeliefert
UNIT.VIEW.APPR.ON	Approved on	核准于	Abgenommen am
UNIT.VIEW.APPR.NOT	Not yet approved	尚未核准	Noch nicht abgenommen
UNIT.VIEW.COMMENT	Comment	评论	Kommentar
UNIT.VIEW.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE	@:WFLOW.INTRO.BUTTON.DELETE
UNIT.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT	@:MODEL.VIEW.BUTTON.EDIT
UNIT.VIEW.BUTTON.CHSTATUS	Change status	更改状态	Status ändern
UNIT.VIEW.BUTTON.UNARCHIVE	Unarchive	解档	Dearchivieren
UNIT.VIEW.CHECK.MODVERS	using model version {{ mvers }}	使用型号版本	verwendet Modellversion {{ mvers }}
UNIT.VIEW.CHECK.FAIL	check failed!	检查失败!	Prüfung fehlgeschlagen!
UNIT.VIEW.CHECK.WARN	Check passed with warnings!	验盘通过，带警告语！	Prüfung mit Warnungen bestanden!
UNIT.VIEW.CHECK.PASS	check passed!	检查通过!	Prüfung bestanden!
UNIT.VIEW.CHECK.FAILCLOSED	Closed, check failed on	关闭, 检查失败	Geschlossen, Prüfung fehlgeschlagen am
UNIT.VIEW.CHECK.WARNCLOSED	Closed, check passed with warnings on	 check passed with warnings on"	Geschlossen, Prüfung mit Warnungen bestanden am
UNIT.VIEW.CHECK.PASSCLOSED	Closed, check passed on	关闭, 检查通过	Geschlossen, Prüfung bestanden am
UNIT.VIEW.CHECK.PASSCANCEL	Cancelled	取消	Abgebrochen
UNIT.VIEW.SCHED.NOT	@:WFLOW.INTRO.DATE.NOSCHED	@:WFLOW.INTRO.DATE.NOSCHED	@:WFLOW.INTRO.DATE.NOSCHED
UNIT.VIEW.SCHED.ON	Scheduled for	计划于	Vorgesehen für
UNIT.VIEW.DUE.ON	due by	截止日期	abzuschliessen bis
UNIT.VIEW.DUE.NOT	no due date	无截止日期	Kein Enddatum
UNIT.VIEW.START.ON	@:WFLOW.INTRO.DATE.START	@:WFLOW.INTRO.DATE.START	@:WFLOW.INTRO.DATE.START
UNIT.VIEW.STATUS.TTL	@:WFLOW.INPUT.STATUS.TITLE	@:WFLOW.INPUT.STATUS.TITLE	@:WFLOW.INPUT.STATUS.TITLE
UNIT.VIEW.STATUS.OPEN	OPEN	开启	OFFEN
UNIT.VIEW.STATUS.CLOSED	CLOSED	关闭	GESCHLOSSEN
UNIT.VIEW.STATUS.DISCARDED	DISCARDED	丢弃	VERWORFEN
UNIT.VIEW.STATUS.ARCHIVED	ARCHIVED	存档	ARCHIVIERT
UNIT.VIEW.STATUS.CLOSEDARCH	CLOSED (ARCHIVED)	关闭 (ARCHIVED)	GESCHLOSSEN (ARCHIVIERT)
UNIT.VIEW.STATUS.DISCARCH	DISCARDED (ARCHIVED)	丢弃 (ARCHIVED)	VERWORFEN (ARCHIVIERT)
USERS.ANYUSER	Any user	任何用户	Irgendein Benutzer
USERS.ANYGROUP	Any group	任何群组	Irgendeine Gruppe
USERS.DELASSIGN	Remove assignment	移除任务	Zuweisung aufheben
USERS.TOOLTIP.NEWUSER	Create new user	创建新用户	Neuen Benutzer anlegen
USERS.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF	@:MODEL.LIST.TOOLTIP.CLTF
USERS.TOOLTIP.HIDEGRP	Hide groups	隐藏群组, 显示所有用户	Gruppen verbergen
USERS.TOOLTIP.NEWGRP	Create new group	创建新群组	Neue Gruppe anlegen
USERS.TOOLTIP.SHOWGRP	Show groups	显示群组, 通过群组筛选用户	Gruppen anzeigen
USERS.TOOLTIP.DISUSER	Deactivate user	停用用户	Benutzer deaktivieren
USERS.TOOLTIP.ENUSER	Activate user	启动用户	Benutzer aktivieren
USERS.TOOLTIP.EDITUNAM	Edit user name	编辑用户名	Benutername editieren
USERS.TOOLTIP.EDITURN	Edit user's real name	编辑用户的真实姓名	Vollen Namen des Benutzers editieren
USERS.TOOLTIP.EDITUCOMM	Add or edit comments about the user	增加或编辑评论此用户	Kommentare zum Benutzer hinzufügen oder editieren
USERS.TOOLTIP.REMGRP	@:USERMGR.ACTION.DELETEGROUP.TITLE	@:USERMGR.ACTION.DELETEGROUP.TITLE	@:USERMGR.ACTION.DELETEGROUP.TITLE
USERS.TOOLTIP.CHGGRP	Put user into another group	将用户加入另一个群组	Benutzer in andere Gruppe verlegen
USERS.TOOLTIP.EDITGNAME	Edit group name	编辑群组名	Gruppenname editieren
USERS.TOOLTIP.EDITGLVL	Edit group level	编辑群组层级	Gruppenstufe editieren
USERS.TOOLTIP.EDITGDESC	Edit group description	编辑群组描述	Gruppenbeschreibung editieren
USERS.TOOLTIP.ADDGPRV	Add group privileges	增加群组权限	Gruppenprivilegien hinzufügen
USERS.TOOLTIP.REMGPRV	Remove group privilege	移除群组权限	Gruppenprivileg entfernen
USERS.TOOLTIP.ADDUPRV	Add user privileges	增加用户权限	Benutzerprivilegien hinzufügen
USERS.TOOLTIP.REMUPRV	Remove user privilege	移除用户权限	Benutzerprivileg entfernen
USERS.TOOLTIP.SHOWDEL	Show deactivated users	显示停用的用户	Deaktivierte Benutzer anzeigen
USERS.TOOLTIP.ADDGROUP	Add one or more groups to this user	添加一个或多个组到这个用户	Diesem Benutzer eine oder mehrere Gruppen zuordnen
USERS.TOOLTIP.ADDUSER	Add one or more users to this group	添加一个或多个用户到该组	Dieser Gruppe einen oder mehrere Benutzer zuordnen
USERS.TOOLTIP.ADDFIRSTGROUP	Add one or more groups to this user	添加一个或多个组到这个用户	Diesem Benutzer eine oder mehrere Gruppen zuordnen
USERS.TOOLTIP.ADDFIRSTUSER	Add one or more users to this group	添加一个或多个用户到该组	Dieser Gruppe einen oder mehrere Benutzer zuordnen
USERS.TOOLTIP.REMOVEGROUP	Remove this group from the user	从该用户中移除该组	Diese Gruppe vom Benutzer entfernen
USERS.TOOLTIP.REMOVEUSER	Remove this user from the group	从该组移除该用户	Diesen Benutzer aus der Gruppe entfernen
USERS.USERS	@:FRAME.MANAGE.USERS	@:FRAME.MANAGE.USERS	@:FRAME.MANAGE.USERS
USERS.GROUPS	Groups	群组	Gruppen
USERS.USER	@:MEDITOR.USER	@:MEDITOR.USER	@:MEDITOR.USER
USERS.USERNAME	@:LOGIN.USERNAME	@:LOGIN.USERNAME	@:LOGIN.USERNAME
USERS.PASSWORD.TITLE	@:LOGIN.PASSWORD	@:LOGIN.PASSWORD	@:LOGIN.PASSWORD
USERS.PASSWORD.SET	Password set	密码设置	Passwort gesetzt
USERS.PASSWORD.NOTSET	Password not set	密码未设置	Passwort nicht gesetzt
USERS.BUTTON.CHPASSW	@:FRAME.MANAGE.CHANGEPW	@:FRAME.MANAGE.CHANGEPW	@:FRAME.MANAGE.CHANGEPW
USERS.REALNAME	Realname	真实名	Echter Name
USERS.COMMENT	@:UNIT.VIEW.COMMENT	@:UNIT.VIEW.COMMENT	@:UNIT.VIEW.COMMENT
USERS.GROUP	@:MEDITOR.GROUP	@:MEDITOR.GROUP	@:MEDITOR.GROUP
USERS.GNAME	Group name	群组名	Gruppenname
USERS.LEVEL	Level	层级	Level
USERS.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC	@:TLISTEDIT.DESC
USERS.PRIVILEGES	Privileges	权限	Privilegien
USERS.PRIV.BYGROUP	Granted by groups	授予群组	erhalten durch die Gruppen
USERS.PRIV.BYUSER	Granted individually	授予个别地	individuell erhalten
USERS.PRIV.FORGROUP	Group wide privileges	组广泛权限	Gruppenweite Privilegien
USERS.RANK	Level	等级	Level
USERS.USERGROUPS	Affiliated groups	附属组	Gruppenzugehörigkeit
USERS.NOGROUPS	User is not affiliated to any group	用户不从属于任何组	Benutzer gehört keiner Gruppe an
USERS.GROUPUSERS	Members	成员	Mitglieder
USERS.NOUSERS	No users are affiliated to this group	该组没有任何附属用户	Keine Benutzer sind dieser Gruppe zugeordnet
USERS.ADDUSER	Add user	添加用户	Benutzer hinzufügen
USERS.ADDGROUP	Add group	添加组	Gruppen zuordnen
USERMGR.ACTION.ADDUSER.TITLE	Add user	增加用户	Benutzer hinzufügen
USERMGR.ACTION.EDITUSER.TITLE	Edit user	编辑用户	Editiere Benutzer
USERMGR.ACTION.USER.USERNAME	Enter a new username	输入一个新的用户名	Bitte neuen Benutzernamen eingeben
USERMGR.ACTION.USER.REALNAME	Enter the user's real name	输入此用户真实名	Bitte den echten Namen des Benutzers eingeben
USERMGR.ACTION.USER.COMMENT	Edit or add a comment for the user	编辑或增加一个评论此用户	Bitte einen Kommentar zu dem Benutzer hinzufügen oder editieren
USERMGR.ACTION.USER.PASSWORD	Set a new password or leave empty to clear password	设置新的密码或腾让出来以清除密码	Bitte neues Passwort für den Benutzer eingeben oder das Feld leer lassen um das Passwort zu löschen
USERMGR.ACTION.USER.GROUP	Select the new group for this user	选择一个新群组给此用户	Bitte die neue Gruppe für den Benutzer wählen
USERMGR.ACTION.ADDGROUP.TITLE	Add group	增加群组	Gruppe hinzufügen
USERMGR.ACTION.EDITGROUP.TITLE	Edit group	编辑群组	Editiere Gruppe
USERMGR.ACTION.GROUP.NAME	Enter a new group name	输入一个新的群组名	Bitte den neuen Gruppennamen eingeben
USERMGR.ACTION.GROUP.LEVEL	Enter a new group level	输入一个新的群组层级	Bitte den neuen Level der Gruppe eingeben (Zahl zwischen 1 und 1000)
USERMGR.ACTION.GROUP.DESCRIPTION	Enter or add a description for this group	输入或增加描述给此群组	Bitte die Beschreibung für diese Gruppe eingeben oder ändern
USERMGR.ACTION.DELETEGROUP.TITLE	Delete group	删除群组	Gruppe löschen
USERMGR.ACTION.DELETEGROUP.TEXT	Do you really want to delete the group? Some users in this group may be not be affiliated to any group afterwards.	选择一个群组作为替代(当前所有用户群组将加入在这个新群组)	Möchten Sie wirklich die Gruppe löschen? Die Benutzer in dieser Gruppe sind danach unter Umständen keiner Gruppe mehr angehörig.
USERMGR.ACTION.ADDGRANT.TITLE	Add privileges	增加权限	Privilegien hinzufügen
USERMGR.ACTION.ADDGRANT.TEXT.GROUP	Select the privileges to add to the group:	选择要添加到该群组此权限:	Bitte wählen Sie die Privilegien, die der Gruppe hinzugefügt werden sollen:
USERMGR.ACTION.ADDGRANT.TEXT.USER	Select the privileges to add to the user:	选择要添加到该用户此权限:	Bitte wählen Sie die Privilegien, die dem Benutzer hinzugefügt werden sollen:
USERMGR.ACTION.ADDGRPTOUSER.TITLE	Add groups	添加多个组	Gruppen hinzufügen
USERMGR.ACTION.ADDGRPTOUSER.TEXT	Please select the groups to be added to the user:	请选择要添加到该用户的所有组	Bitte wählen Sie die Gruppen aus, die Sie dem Benutzer hinzufügen wollen:
USERMGR.ACTION.ADDUSERTOGRP.TITLE	Add users	增加用户	Benutzer hinzufügen
USERMGR.ACTION.ADDUSERTOGRP.TEXT	Please select the users to be added to the group:	请选择要添加到该组的所有用户	Bitte wählen Sie die Benutzer aus, die Sie der Gruppe hinzufügen wollen:
WFLOW.INTRO.STATUS.INIT	INITIALIZED	初始化	INITIALISIERT
WFLOW.INTRO.STATUS.SCHED	SCHEDULED	计划于	EINGERICHTET
WFLOW.INTRO.STATUS.START	STARTED	开始	GESTARTET
WFLOW.INTRO.STATUS.FAIL	FAILED	失败	FEHLGESCHLAGEN
WFLOW.INTRO.STATUS.WARN	PASSED W/RES	输出对象通过	BESTANDEN M.E.
WFLOW.INTRO.STATUS.PASS	PASSED	通过	BESTANDEN
WFLOW.INTRO.STATUS.FAILC	FAILED & CLOSED	失败及关闭	FEHLGESCHLAGEN & GESCHLOSSEN
WFLOW.INTRO.STATUS.WARNC	PASSWD W/RES & CLOSED	输出对象通过&关闭	BESTANDEN M.E. & GESCHLOSSEN
WFLOW.INTRO.STATUS.PASSC	PASSED & CLOSED	通过及关闭	BESTANDEN & GESCHLOSSEN
WFLOW.INTRO.STATUS.CANCEL	CANCELLED	取消	ABGEBROCHEN
WFLOW.INTRO.MEDIAMANAGER	Check	检查	Prüfung
WFLOW.INTRO.TOOLTIP.EXPPDF	Export check as PDF	检查输出为PDF	Prüfung als PDF exportieren
WFLOW.INTRO.TOOLTIP.EXPFILE	Export check data	输出检查数据	Prüfungsdaten exportieren
WFLOW.INTRO.TOOLTIP.GOTOMODEL	Go to model	转到型号	Zum Modell gehen
WFLOW.INTRO.TOOLTIP.GOTOUNIT	Go to unit	转到单位	Zur Einheit gehen
WFLOW.INTRO.TOOLTIP.IMGLEFT	Previous image	上一个图像	Vorhergehendes Bild
WFLOW.INTRO.TOOLTIP.IMGRIGHT	Next image	下一个图像	Nächstes Bild
WFLOW.INTRO.TOOLTIP.VIEWDOC	View document	查看文档	Dokument ansehen
WFLOW.INTRO.TOOLTIP.EDITCOMM	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE	@:MEASUREMENT.INPUT.COMMENT.TITLE
WFLOW.INTRO.TOOLTIP.VIEWIMG	View image	查看图像	Bild ansehen
WFLOW.INTRO.TOOLTIP.REMASS	Remove assignee	移除代理人	Zuweisungsziel entfernen
WFLOW.INTRO.TOOLTIP.ASSIGN	Assign to selected items	指定到选定的项目	Alle ausgewählten Elemente zuweisen
WFLOW.INTRO.TOOLTIP.HIGHLASS	Highlight assigned items	强调分配项目	Zugewiesene Elemente hervorheben
WFLOW.INTRO.TOOLTIP.ADDASS	Add assignee	增加代理人	Zuweisungsziel hinzufügen
WFLOW.INTRO.TOOLTIP.SELASS	Select for assignment	分配任务	Für die Zuweisung auswählen
WFLOW.INTRO.TOOLTIP.GOTOSTEP	Go to step	转到步骤	Zum Schritt gehen
WFLOW.INTRO.TOOLTIP.CHANGEASSIGN	Change assignment	更改任务	Zuweisung ändern
WFLOW.INTRO.MEDIAMGR	Add, delete or add images and documents	增加, 删除或增加图像和文文件	Bilder und Dokumente hinzufügen, löschen oder ändern
WFLOW.INTRO.TOOEARLY	The check is scheduled in the future; it may not be processed now!	在未来的检查计划; 它现在可能无法处理	Die Prüfung ist für einen zukünftigen Zeitpunkt vorgesehen, sie kann noch nicht bearbeitet werden.
WFLOW.INTRO.TOOLATE	The check is due in the past; hurry up!	在过去此检查已到期;赶快!	Der Abschluss der Prüfung ist überfällig, bitte beeilen!
WFLOW.INTRO.MODEL	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD
WFLOW.INTRO.UNIT	Unit	单位	Einheit
WFLOW.INTRO.PDFINFO	{{ pnum }} page(s)	{{ pnum }} 页面 (s)	{{ pnum }} Seiten
WFLOW.INTRO.VIDINFO	{{ width }} x {{ height }}, {{ fps }} fps, runtime: {{ rtime }} sec	{{ width }} x {{ height }}, {{ fps }}英尺/秒, 运行时间 : {{ rtime }} 秒	{{ width }} x {{ height }}, {{ fps }} bps, Laufzeit: {{ rtime }} sek
WFLOW.INTRO.IMGINFO	{{ width }} x {{ height }}	{{ 宽度}} x {{ 高度 }}	{{ width }} x {{ height }}
WFLOW.INTRO.DATE.SCHED	Scheduled	计划于	Vorgesehen für
WFLOW.INTRO.DATE.DUE	Due by	交付	Fertigzustellen bis
WFLOW.INTRO.DATE.NOSCHED	Not scheduled yet	计划尚未排定	Starttermin nicht geplant
WFLOW.INTRO.DATE.NODUE	No due date set yet	没有截止日期尚未确定	Kein Fertigstellungstermin geplant
WFLOW.INTRO.DATE.START	Started on	开始	Gestartet am
WFLOW.INTRO.DATE.FIN	Finished	已完成	Beendet am
WFLOW.INTRO.DATE.NOSTART	Not started yet	还未开始	Noch nicht gestartet
WFLOW.INTRO.DATE.NOFIN	Not finished yet	还没完工	Noch nicht beendet
WFLOW.INTRO.CHECK	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER
WFLOW.INTRO.NOCOMMENT	Check not commented yet; Click here to add a comment	检查尚未评论;单击此处增加评论	Prüfung ist noch nicht kommentiert
WFLOW.INTRO.STEPINPROC	step(s) in	插入	Schritt(e) in
WFLOW.INTRO.PROCS	procedure(s)	流程(s)	Prozedur(en)
WFLOW.INTRO.NOASS	No assignments yet	尚未分配	Noch keine Zuweisungen
WFLOW.INTRO.ADDASS	Add Assignment	增加分配	Zuweisung hinzufügen
WFLOW.INTRO.PROCSUNASS	procedure(s) unassigned	流程(s) 未分配	Prozedur(en) nicht zugewiesen
WFLOW.INTRO.PROCEDURES	@:FRAME.MANAGE.PROCEDURE	@:FRAME.MANAGE.PROCEDURE	@:FRAME.MANAGE.PROCEDURE
WFLOW.INTRO.STEPS	Steps	步骤	Schritte
WFLOW.INTRO.MEASURES	Measures	测量	Messungen
WFLOW.INTRO.STAT.TOTAL	Total	Total⌥	Total
WFLOW.INTRO.STAT.PASSED	Passed	已通过的	Bestanden
WFLOW.INTRO.STAT.UNFIN	In Progress	未完成的	In Bearbeitung
WFLOW.INTRO.STAT.FAILED	Not passed	已失败的	Nicht bestanden
WFLOW.INTRO.STAT.SKIPPED	Optional	已跳过的	Optional
WFLOW.INTRO.BUTTON.DELETE	Delete	删除	Löschen
WFLOW.INTRO.BUTTON.SCHEDULE	Schedule	计划	Einrichten
WFLOW.INTRO.BUTTON.RESCHEDULE	Reschedule	重新计划	Umplanen
WFLOW.INTRO.BUTTON.START	Start	开始	Starten
WFLOW.INTRO.BUTTON.REASS	Stop check	停止及再分配	Prüfung stoppen / Neu zuweisen
WFLOW.INTRO.BUTTON.UNREG	@:CHECK.ALERT.UNREGISTER.TITLE	@:CHECK.ALERT.UNREGISTER.TITLE	@:CHECK.ALERT.UNREGISTER.TITLE
WFLOW.INTRO.BUTTON.REG	@:CHECK.ALERT.REGISTER.TITLE	@:CHECK.ALERT.REGISTER.TITLE	@:CHECK.ALERT.REGISTER.TITLE
WFLOW.INTRO.BUTTON.CONTINUE	Continue	继续	Fortsetzen
WFLOW.INTRO.BUTTON.COMMIT	Commit	提交	Bestätigen
WFLOW.INTRO.BUTTON.CLOSE	@:WFLOW.INPUT.BUTTON.CLOSE	@:WFLOW.INPUT.BUTTON.CLOSE	@:WFLOW.INPUT.BUTTON.CLOSE
WFLOW.INTRO.BUTTON.REOPEN	@:CHECK.ALERT.REOPEN.TITLE	@:CHECK.ALERT.REOPEN.TITLE	@:CHECK.ALERT.REOPEN.TITLE
WFLOW.INTRO.BUTTON.CANCEL	Cancel check	取消检查	Prüfung abbrechen
WFLOW.INTRO.BUTTON.COPY	Create Copy	创建副本	Kopie erstellen
WFLOW.INTRO.FILTER.BUTTONS.COLLAPSE	Collapse:	崩溃:	Ausklappen:
WFLOW.INTRO.FILTER.BUTTONS.PROC	Proc.	程序.	Proz.
WFLOW.INTRO.FILTER.BUTTONS.STEP	Steps	步骤	Schr.
WFLOW.INTRO.FILTER.BUTTONS.MEAS	Meas.	测量.	Mess.
WFLOW.INTRO.FILTER.BUTTONS.FILTER	Filter	筛选	Filter
WFLOW.INTRO.FILTER.BUTTONS.FILTERPLUS	Filter+	筛选+	Filter+
WFLOW.INTRO.FILTER.TOOLTIP.VIEWFILT	Show or hide filter bar (hiding the filter bar deactivates filters)	显示或隐藏筛选栏（隐藏筛选栏停用的筛选）	Filterleiste ein- oder ausklappen (Einklappen deaktiviert Filter)
WFLOW.INTRO.FILTER.TOOLTIP.PROC	Collapse all, show procedures only	关闭所有，仅显示程序	Alles einklappen und nur Prozeduren zeigen
WFLOW.INTRO.FILTER.TOOLTIP.STEP	Expand steps, collapse measures (default)	展开步骤，关闭测量（默认）	Schritte ausklappen, Messungen einklappen
WFLOW.INTRO.FILTER.TOOLTIP.MEAS	Show all, everything is expanded	显示所有，展开所有	Alles anzeigen, Schritte und Messungen ausklappen
WFLOW.INTRO.FILTER.TOOLTIP.FILTER	Show everything marked via filters; collapse anything else	显示所有通过筛选标记的项，关闭其他	Alles anzeigen was per Filter markiert ist, alles andere einklappen
WFLOW.INTRO.FILTER.TOOLTIP.FILTERPLUS	Expand all elements marked via filter: collapse anything else	展开所有通过筛选标记的元素，关闭其他	Per Filter markierte Elemente ausklappen, alles andere einklappen
WFLOW.INTRO.FILTER.TOOLTIP.RESET	Reset all filters	重置所有筛选	Alle Filter zurücksetzen
WFLOW.INTRO.FILTER.TOOLTIP.FILTCODE	Apply text filter on codes	应用文本筛选代码	Textfilter auf Codes anwenden
WFLOW.INTRO.FILTER.TOOLTIP.FILTTEXT	Apply text filter on element titles	应用文本筛选元素标题	Textfilter auf Titel anwenden
WFLOW.INTRO.FILTER.TOOLTIP.FILTPROC	Apply text filter on procedures	应用文本筛选程序	Textfilter auf Prozeduren anwenden
WFLOW.INTRO.FILTER.TOOLTIP.FILTSTEP	Apply text filter on steps	应用文本筛选步骤	Textfilter auf Schritte anwenden
WFLOW.INTRO.FILTER.TOOLTIP.FILTMEAS	Apply text filter on measures	应用文本筛选测量	Textfilter auf Messungen anwenden
WFLOW.INTRO.FILTER.TOOLTIP.FILTUSER	Mark all/only elements assigned to the current user	标记所有/仅分配给当前用户的元素	Nur Elemente markieren, die dem aktuellen Benutzer zugewiesen sind
WFLOW.INTRO.FILTER.TOOLTIP.FILTUNFIN	Mark all/only unfinished elements	标记所有/仅未完成的元素	Nur Elemente markieren, die noch nicht vollständig bearbeitet sind
WFLOW.INTRO.FILTER.TOOLTIP.FILTFAIL	Mark all/only failed elements	标记所有/仅失败的元素	Nur Elemente markieren, die fehlgeschlagen sind
WFLOW.INTRO.FILTER.TOOLTIP.FILTSUCC	Mark all/only suceeded elements	标记所有/仅成功的元素	Nur Elemente markieren, die vollständig bearbeitet und fehlerfrei sind
WFLOW.INTRO.VERSION	Check is based on model version	检查基于型号版本	Prüfung basiert auf Modellversion
WFLOW.INTRO.UNFINVERSION	Check is based on the currently edited, not yet finalized model version	检查基于当前编辑尚未审定的型号版本	Prüfung basiert auf der aktuell editierten, noch nicht finalisierten Modellversion
WFLOW.INTRO.TESTRUN.PROCEDURE	Test run of a check for procedure:	为程序测试运行检查:	Testlauf einer Prüfung für Prozedur:
WFLOW.INTRO.TESTRUN.MODEL	Test run of a check for model:	为型号测试运行检查:	Testlauf einer Prüfung für Modell:
WFLOW.STEP.STATUS.TODO	@:WFLOW.INTRO.STATUS.INIT	@:WFLOW.INTRO.STATUS.INIT	@:WFLOW.INTRO.STATUS.INIT
WFLOW.STEP.STATUS.PASS	@:WFLOW.INTRO.STATUS.PASS	@:WFLOW.INTRO.STATUS.PASS	@:WFLOW.INTRO.STATUS.PASS
WFLOW.STEP.STATUS.WARN	@:WFLOW.INTRO.STATUS.WARN	NOT_FOUND!,@:WFLOW.INTRO.STATUS.WARN
WFLOW.STEP.STATUS.FAIL	@:WFLOW.INTRO.STATUS.FAIL	@:WFLOW.INTRO.STATUS.FAIL	@:WFLOW.INTRO.STATUS.FAIL
WFLOW.STEP.STATUS.OMIT	OMITTED	忽略的	AUSGELASSEN
WFLOW.STEP.STATUS.SKIP	SKIPPED	已跳过的	ÜBERSPRUNGEN
WFLOW.STEP.STATUS.PASSNF	(PASSED)	通过	(BESTANDEN)
WFLOW.STEP.STATUS.FAILNF	(FAILED)	失败	(FEHLGESCHLAGEN)
WFLOW.STEP.STATUS.WARNNF	(PASSED W/RES)	（输出对象通过）	(BESTANDEN M.E.)
WFLOW.STEP.TOOLTIP.GOTOMODEL	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL	@:WFLOW.INTRO.TOOLTIP.GOTOMODEL
WFLOW.STEP.TOOLTIP.GOTOUNIT	@:WFLOW.INTRO.TOOLTIP.GOTOUNIT	@:WFLOW.INTRO.TOOLTIP.GOTOUNIT	@:WFLOW.INTRO.TOOLTIP.GOTOUNIT
WFLOW.STEP.TOOLTIP.GOTOPROC	Go to procedure	转到流程	Zur Prozedur gehen
WFLOW.STEP.TOOLTIP.VIEWIMG	@:WFLOW.INTRO.TOOLTIP.VIEWIMG	@:WFLOW.INTRO.TOOLTIP.VIEWIMG	@:WFLOW.INTRO.TOOLTIP.VIEWIMG
WFLOW.STEP.TOOLTIP.VIEWDOC	@:WFLOW.INTRO.TOOLTIP.VIEWDOC	@:WFLOW.INTRO.TOOLTIP.VIEWDOC	@:WFLOW.INTRO.TOOLTIP.VIEWDOC
WFLOW.STEP.COMMITTER	Step committed by:	步骤通过提交:	Schritt bestätigt von:
WFLOW.STEP.NOCOMMIT	Not committed yet	尚未通过提交	Noch nicht bestätigt
WFLOW.STEP.MEASURER	Measurement {{ code }} by:	测量{{ code }}通过:	Messung {{ code }} von:
WFLOW.STEP.NOMEASURE	Not done yet	还没有完成	Noch nicht bearbeitet
WFLOW.STEP.PDFINFO	@:WFLOW.INTRO.PDFINFO	@:WFLOW.INTRO.PDFINFO	@:WFLOW.INTRO.PDFINFO
WFLOW.STEP.VIDINFO	{{ width }} x {{ height }}, {{ fps }} fps, {{ rtime }} sec	{{ width }} x {{ height }}, {{ fps }} 英尺/秒, {{ rtime }}秒	{{ width }} x {{ height }}, {{ fps }} bps, {{ rtime }} sek
WFLOW.STEP.MODEL	Model	给型号	Modell
WFLOW.STEP.UNIT	@:WFLOW.INTRO.UNIT	@:WFLOW.INTRO.UNIT	@:WFLOW.INTRO.UNIT
WFLOW.STEP.PROCEDURE	@:WFLOWEDIT.OPT1.P	@:WFLOWEDIT.OPT1.P	@:WFLOWEDIT.OPT1.P
WFLOW.STEP.BUTTON.BACK	Back to Overview	返回概况	Zurück zur Übersicht
WFLOW.STEP.BUTTON.CONTINUE	@:WFLOW.INTRO.BUTTON.CONTINUE	@:WFLOW.INTRO.BUTTON.CONTINUE	@:WFLOW.INTRO.BUTTON.CONTINUE
WFLOW.STEP.BUTTON.FIN	Finish!	完成!	Beenden!
WFLOW.STEP.BUTTON.REWIND	Last step	上一步	Zum vorherigen Schritt
WFLOW.STEP.BUTTON.PROCALL	Process all	处理全部	Alle bearbeiten
WFLOW.STEP.MEASURES	@:WFLOW.INTRO.MEASURES	@:WFLOW.INTRO.MEASURES	@:WFLOW.INTRO.MEASURES
WFLOW.STEP.MEASUREINP	Enter result...	输入结果…	Ergebnis eintragen...
WFLOW.STEP.INPLOCKED	Locked	封闭	Gesperrt
WFLOW.STEP.TOOLS	@:FRAME.MANAGE.TOOL	@:FRAME.MANAGE.TOOL	@:FRAME.MANAGE.TOOL
WFLOW.STEP.TOOLCHOOSE	Choose tool...	选择工具…	Werkzeug wählen...
WFLOW.STEP.DOCS	Documents	文檔	Dokumente
WFLOW.STEP.VIEW.INFO	Description	描述	Beschreibung
WFLOW.STEP.VIEW.IMAGES	Images ({{ numi }})	图片 ({{ numi }})	Bilder ({{ numi }})
WFLOW.STEP.VIEW.DOCS	Documents ({{ numd }})	文件 ({{ numd }})	Dokumente ({{ numd }})
WFLOW.INPUT.VALUECMP1	Value must be less than {{ val }}	值必须小于{{ val }}	Wert muss kleiner sein als {{ val }}
WFLOW.INPUT.VALUECMP2	Value must be less than or equal to {{ val }}	值必须小于或等于{{ val }}	Wert muss kleiner oder gleich sein als {{ val }}
WFLOW.INPUT.VALUECMP3	Value must be equal to {{ val }}	值必须等于{{ val }}	Wert muss gleich sein wie {{ val }}
WFLOW.INPUT.VALUECMP4	Value must be greater than or equal to {{ val }}	值必须大于或等于{{ val }}	Wert muss grösser oder gleich sein als {{ val }}
WFLOW.INPUT.VALUECMP5	Value must be greater than {{ val }}	值必须大于{{ val }}	Wert muss grösser sein als {{ val }}
WFLOW.INPUT.CHECKCMP1	Check if your measured value is less than {{ val }}, then click 'Yes' or 'No' accordingly	检查如果你的测量值为小于 {{ val }}, 然后于是单击'是'或'不是'	Bitte prüfen Sie, ob Ihr gemessener Wert kleiner ist als {{ val }}, dann wählen Sie entsprechend 'Ja' oder 'Nein'
WFLOW.INPUT.CHECKCMP2	Check if your measured value is less than or equal to {{ val }}, then click 'Yes' or 'No' accordingly	检查如果你的测量值为小于或等于 {{ val }}, 然后于是单击'是'或'不是'	Bitte prüfen Sie, ob Ihr gemessener Wert kleiner oder gleich ist als {{ val }}, dann wählen Sie entsprechend 'Ja' oder 'Nein'
WFLOW.INPUT.CHECKCMP3	Check if your measured value is equal to  {{ val }}, then click 'Yes' or 'No' accordingly	检查如果你的测量值为等于 {{ val }}, 然后于是单击'是'或'不是'	Bitte prüfen Sie, ob Ihr gemessener Wert gleich ist wie {{ val }}, dann wählen Sie entsprechend 'Ja' oder 'Nein'
WFLOW.INPUT.CHECKCMP4	Check if your measured value is greater than or equal to {{ val }}, then click 'Yes' or 'No' accordingly	检查如果你的测量值为大于或等于 {{ val }}, 然后于是单击'是'或'不是'	Bitte prüfen Sie, ob Ihr gemessener Wert grösser oder gleich ist als {{ val }}, dann wählen Sie entsprechend 'Ja' oder 'Nein'
WFLOW.INPUT.CHECKCMP5	Check if your measured value is greater than {{ val }}, then click 'Yes' or 'No' accordingly	检查如果你的测量值为大于{{ val }}, 然后于是单击'是'或'不是'	Bitte prüfen Sie, ob Ihr gemessener Wert grösser ist als {{ val }}, dann wählen Sie entsprechend 'Ja' oder 'Nein'
WFLOW.INPUT.TARGET	Target value	目标值	Zielwert
WFLOW.INPUT.STATISTIC	Please enter the measured value; the value will not be checked	请输入一个值用于统计目的	Bitte den gemessenen Wert eingeben; es findet keine Prüfung des Wertes statt.
WFLOW.INPUT.THRESH	Threshold	临界值	Abweichung
WFLOW.INPUT.VALUERNG1	Value must be between	值必须介于	Wert liegt zwischen
WFLOW.INPUT.VALUERNG2	and	和	und
WFLOW.INPUT.TEXTLEN1	Text must be at least	至少文字必须	Text ist mindestens
WFLOW.INPUT.TEXTLEN2	characters long	字符长	Zeichen lang
WFLOW.INPUT.TEXTPAT	Text must match a specific pattern	文字必须与特定模式匹配	Text passt in ein festgelegtes Muster
WFLOW.INPUT.EXPNO	'No' is expected	No' 预计	'Nein' wird erwartet
WFLOW.INPUT.EXPYES	'Yes' is expected	Yes' 预计	'Ja' wird erwartet
WFLOW.INPUT.EXPBOTH	'Yes' or 'No' will both be accepted	Yes' 或 'No' 都将被接受	'Ja' oder 'Nein' wird erwartet
WFLOW.INPUT.OPTSKIP	This measurement is optional. Skip?	此测量是可选的. 跳过?	Diese Messung ist optional. Überspringen?
WFLOW.INPUT.YES	Yes	是	Ja
WFLOW.INPUT.NO	No	否	Nein
WFLOW.INPUT.INPUT.VALUE	Enter the measured value in the textbox below:	输入测量值在以下文字框中:	Bitte den gemessenen Wert unten eintragen:
WFLOW.INPUT.INPUT.TEXT	Enter the required text in the textbox below:	输入请求在以下文字框中:	Bitte den einzugebenden Text unten eintragen:
WFLOW.INPUT.INPUT.BOOL	Check either 'Yes' or 'No':	检查'是'或'不是'	Bitte entweder 'Ja' oder 'Nein' wählen:
WFLOW.INPUT.INPUT.MATRIX	Fill out the matrix below, then hit 'Check & Save' to calculate the result:	填写以下的矩阵, 然后点击'检查及保存'来计算结果:	Bitte die Matrix unten ausfüllen, dann 'Prüfen' anklicken um das Ergebnis zu berechnen:
WFLOW.INPUT.RESULT	Result	结果	Ergebnis
WFLOW.INPUT.STATUS.TITLE	Status	状态	Status
WFLOW.INPUT.STATUS.TODO	TODO	待办事项	LEER
WFLOW.INPUT.STATUS.PASS	PASS	通过	OK
WFLOW.INPUT.STATUS.WARN	FAIL	失败	NICHT OK
WFLOW.INPUT.STATUS.FAIL	FAIL	失败	NICHT OK
WFLOW.INPUT.STATUS.SKIP	SKIP	跳过	ÜBERSPRUNGEN
WFLOW.INPUT.STATUS.INV	INVALID	无效的	UNGÜLTIG
WFLOW.INPUT.STATUS.ERROR	ERROR	错误	FEHLER
WFLOW.INPUT.BUTTON.CLOSE	Close	关闭	Schliessen
WFLOW.INPUT.BUTTON.CLEAR	Clear	清除	Löschen
WFLOW.INPUT.BUTTON.CANCEL	Cancel	取消	Abbrechen
WFLOW.INPUT.BUTTON.ADDCOMM	Add Comment	增加评论	Kommentar
WFLOW.INPUT.BUTTON.EDITCOMM	Edit Comment	编辑评论	Kommentar
WFLOW.INPUT.BUTTON.CHECK	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER
WFLOW.INPUT.BUTTON.CONTINUE	Check & Continue	检查及继续	Prüfen & weiter
WFLOW.INPUT.TEXTTIMER.START	Please enter the start time	请输入开始时间	Bitte die Startzeit eingeben
WFLOW.INPUT.TEXTTIMER.STOP	Please enter the stop time	请输入停止时间	Bitte die Stopzeit eingeben
WFLOW.INPUT.TEXTTIMER.STOPLT	Please enter the stop time; a resulting time less than {{ time }} min is expected	请输入停止时间；实际停止时间可能少于 {{ time }} 分钟	Bitte die Stopzeit eingeben; es wird eine Zeit bis {{ time }} min erwartet
WFLOW.INPUT.TEXTTIMER.STOPGT	Please enter the stop time; a resulting time greater than {{ time }} min is expected	请输入停止时间；实际停止时间可能多于 {{ time }} 分钟	Bitte die Stopzeit eingeben; es wird eine Zeit grösser als {{ time }} min erwartet
WFLOW.INPUT.TEXTTIMER.REDUCE	reduce by	减少	abzüglich
WFLOW.INPUT.TEXTTIMER.MIN	min	分钟	min
WFLOW.INPUT.NOINPUT	The result of this measurement is calculated from other measurements; no input necessary.	该路径结果由其他路径手段推算；无必要输入	Das Ergebnis dieser Messung berechnet sich aus anderen Messungen.
WFLOWEDIT.OPT1.CT	Checktype	检查类型	Prüfungsart
WFLOWEDIT.OPT1.MD	Model	型号	Modell
WFLOWEDIT.OPT1.DT	Devicetype	设备类型	Gerätetyp
WFLOWEDIT.OPT1.P	Procedure	流程	Prozedur
WFLOWEDIT.OPT1.S	Step	步骤	Schritt
WFLOWEDIT.OPT1.M	Measure	测量	Messung
WFLOWEDIT.OPT2.SEL	selected	已选择的	gewählt
WFLOWEDIT.OPT2.NSEL	not selected	未选择的	nicht gewählt
WFLOWEDIT.OPT2.QUEUED	queued	已队列的	vorgesehen
WFLOWEDIT.OPT2.NQUEUED	not queued	未队列的	nicht vorgesehen
WFLOWEDIT.OPT2.FINPASS	finished & passed	完成的及通过的	beendet & bestanden
WFLOWEDIT.OPT2.FINFAIL	finished & failed	完成的及失败的	beendet & fehlg.
WFLOWEDIT.OPT2.SKIP	skipped	跳过的	übersprungen
WFLOWEDIT.OPT2.NSKIP	not skipped	未跳过的	nicht übersprungen
WFLOWEDIT.OPT2.OMIT	omitted	忽略的	ausgelassen
WFLOWEDIT.OPT2.NOMIT	not omitted	未忽略的	nicht ausgelassen
WFLOWEDIT.OPT2.YES	set to 'YES'	设置为'YES'	auf 'Ja' gesetzt
WFLOWEDIT.OPT2.NO	set to 'NO'	设置为'NO'	auf 'Nein' gesetzt
WFLOWEDIT.OPT3.SKIP	skip	跳过	überspringen
WFLOWEDIT.OPT3.OMIT	omit	忽略	auslassen
WFLOWEDIT.TOOLTIP.REMMOD	Remove modifier	移除修改者	Regel entfernen
WFLOWEDIT.TOOLTIP.ADDMOD	Add new modifier	增加新修改者	Regel hinzufügen
WFLOWEDIT.LOADING	Loading Editor...	加载编辑器…	Lade Editor...
WFLOWEDIT.TITLE	Workflow Rules Editor	工作流程规则编辑器	Editor für Ablaufregeln
WFLOWEDIT.ACTIVER	Active Rules	主动规则	Aktive Regeln
WFLOWEDIT.ADDRULE	Add new Rule	增加新规则	Neue Regel hinzufügen
WFLOWEDIT.RULE1	If the	如果这个	Wenn
WFLOWEDIT.RULE2	with code	使用代码	mit Code
WFLOWEDIT.RULE3	is	是	ist
WFLOWEDIT.RULE4	, then	,然后	, dann
SERVER.ERROR.TITLE	Error!	错误!	Fehler!
SERVER.ERROR.VALIDATION.USERGROUP.NAME.NOTNULL	A group name must be provided	群组名称必须提供	Ein Gruppenname muss angegeben werden
SERVER.ERROR.VALIDATION.USERGROUP.NAME.UNIQUE	The group name must be unique; no other group may have the same name	群组名称必须是唯一的; 没有其他群组可以有相同的名称	Der Gruppenname muss eindeutig sein; keine andere Gruppe darf denselben Namen haben
SERVER.ERROR.VALIDATION.USERGROUP.NAME.MATCH	The group name may only contain characters, numbers and the underscore; It must be at least 3 characters wide	组名只能包含字符, 数字和下划线; 它必须至少3个字符宽	Der Gruppenname darf nur Buchstaben, Nummern und den Unterstrich beinhalten; er muss mindestend drei Zeichen lang sein
SERVER.ERROR.VALIDATION.USERGROUP.LEVEL.NOTNULL	A level must be specified	此级别必须指定	Ein Level muss angegeben werden
SERVER.ERROR.VALIDATION.USERGROUP.LEVEL.NUMBER	The group level must be a positive integer number between 1 and 1000	群组级别必须是正整数1和1000之间	Der Gruppenlevel muss eine positive Ganzzahl zwischen 1 und 1000 sein
SERVER.ERROR.VALIDATION.USER.USERNAME.NOTNULL	A username must be provided	用户名必须提供	Ein Benutzername muss angegeben werden
SERVER.ERROR.VALIDATION.USER.USERNAME.UNIQUE	The username must be unique; no other user may have the same username	用户名称必须是唯一的; 没有其他用户可以有相同的用户名称	Der Benutzername muss eindeutig sein; kein anderer Benutzer darf denselben Namen haben
SERVER.ERROR.VALIDATION.USER.USERNAME.MATCH	The username may only contain characters, numbers and the underscore; It must be at least 3 characters wide	用户名称只能包含字符, 数字和下划线; 它必须至少3个字符宽	Der Benutzername darf nur Buchstaben, Nummern und den Unterstrich beinhalten; er muss mindestend drei Zeichen lang sein
SERVER.ERROR.VALIDATION.USER.PASSHASH.NOTNULL	A passhash must be provided	验证登陆必须提供	Ein Passhash muss angegeben werden
SERVER.ERROR.VALIDATION.USER.PASSHASH.MATCH	The passhash is not supported by the current password mechanism	验证登陆当前不支持密码机制	Der Passhash wird vom aktuellen Passwortmechanismus nicht unterstützt
SERVER.ERROR.VALIDATION.USER.REALNAME.NOTNULL	The user's real name must be provided	用户真实名称必须提供	Der echte Namen des Benutzers muss angegeben werden
SERVER.ERROR.VALIDATION.USER.REALNAME.MINLEN	The real name must be at least 3 characters long	用户真实名称必须至少3个字符长	Der echte Name des Benutzers muss mindestens drei Zeichen lang sein
SERVER.ERROR.VALIDATION.USER.USERGROUP_ID.NOTNULL	The user's group must be set	用户的群组必须设置	Die Benutzergruppe muss gesetzt sein
SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.UNIQUE	The code value must be unique; no other tool type may have the same code	代码值必须是唯一的; 没有其他工具类型可以有相同的代码	Der Code muss eindeutig sein; kein anderer Werkzeugtyp darf denselben Code haben
SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	The code value must be at least 2 characters long	代码值必须至少3个字符长	Der Code muss mindestens zwei Zeichen lang sein
SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	The code value may only contain characters, numbers, dots, commas, underscores and hyphens	代码值只能包含字符, 数字,点,逗号, 下划线和连字符	Der Code darf nur Buchstaben, Nummern, Punkte, Kommata, Unterstriche und Bindestriche beinhalten
SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	The language block for the title is invalid	标题语言块是无效的	Der Sprachenblock für den Titel ist ungültig
SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	The language block for the title misses the language entry for '{{ lcode }}'	标题语言块遗漏了语言输入 '{{ lcode }}'	Im Sprachenblock für den Titel fehlt der Eintrag für '{{ lcode }}'
SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	The language entry '{{ lcode }}' for the title is too short (< {{ minlength }})	语言输入 '{{ lcode }}' 对于此标题太短 (< {{ minlength }})	Der Spracheintrag '{{ lcode }}' für den Titel ist zu kurz (< {{ minlength }})
SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	The language block for the decription is invalid	标题语言块的描述是无效的	Der Sprachenblock für die Beschreibung ist ungültig
SERVER.ERROR.VALIDATION.TOOLUNIT.CODE.UNIQUE	The code value must be unique within the tool type; no other unit may have the same code	代码值在此工具类型必须是唯一的; 没有其他单位可以有相同的代码	Der Code muss eindeutig sein; keine andere Werkzeugeinheit darf innerhalb des Werkzeugtyps denselben Code haben
SERVER.ERROR.VALIDATION.TOOLUNIT.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.TOOLUNIT.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.PROCEDURE.CODE.UNIQUE	The code value must be unique; no other procedure may have the same code	代码值必须是唯一的; 没有其他流程可以有相同的代码	Der Code muss eindeutig sein; keine andere Prozedur darf denselben Code haben
SERVER.ERROR.VALIDATION.PROCEDURE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.PROCEDURE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.PROCEDURE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID
SERVER.ERROR.VALIDATION.PROCEDURE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE
SERVER.ERROR.VALIDATION.PROCEDURE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT
SERVER.ERROR.VALIDATION.PROCEDURE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID
SERVER.ERROR.VALIDATION.STEP.CODE.UNIQUE	The code value must be unique; no other step for this procedure may have the same code	代码值必须是唯一的; 没有其他此流程步骤可以有相同的代码	Der Code muss eindeutig sein; kein anderer Schritt darf innerhalb der Prozedur denselben Code haben
SERVER.ERROR.VALIDATION.STEP.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.STEP.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.STEP.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID
SERVER.ERROR.VALIDATION.STEP.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE
SERVER.ERROR.VALIDATION.STEP.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT
SERVER.ERROR.VALIDATION.STEP.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID
SERVER.ERROR.VALIDATION.MEASURE.CODE.UNIQUE	The code value must be unique; no other measure for this step may have the same code	代码值必须是唯一的; 没有其他此测量步骤可以有相同的代码	Der Code muss eindeutig sein; keine andere Messung darf innerhalb des Schritts denselben Code haben
SERVER.ERROR.VALIDATION.MEASURE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.MEASURE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.MEASURE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID
SERVER.ERROR.VALIDATION.MEASURE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE
SERVER.ERROR.VALIDATION.MEASURE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT
SERVER.ERROR.VALIDATION.MEASURE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.OPTMISS	The optional field must be set either to Yes or No	可选字段必须设置为 是(Yes)或 否(No)	Das 'Erforderlich'-Feld muss entweder auf 'Ja' oder 'Nein' gesetzt sein
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.INVMTYPE	The measuretype is invalid	测量类型是无效的	Der Messungstyp ist ungültig
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.AINVVAL	The target value and the threshold must have a valid number specified; the threshold must be positive.	目标值和临界值必须指定一个有效的数字; 临界值必须是正数	Für den Zielwert und die Abweichung müssen gültige Nummern spezifiziert sein; die Abweichung muss positiv sein
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.BINVVAL	A valid number must be specified for the minimum and maximum value	必须指定一个有效数字的最小值和最大值	Gültige Zahlen für den Minimal- und Maximalwert müssen angegeben sein
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.CINVVAL	The minimum text length must be a positive integer value	最小量文字长度必须是正整数值	Die minimal Textlänge muss eine positive Ganzzahl oder 0 sein
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.DMISSING	A regular expression must be specified	正规表达式必须是指定的	Ein regulärer Ausdruck als Muster muss gegeben sein
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.DINVREGEXP	The given regular expression is not valid	给予的正则表达式是无效的	Der reguläre Ausdruck ist nicht gültig
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.EINVVAL	The expected value is missing - it must be set to either Yes or No	缺少预期值-它必须设置为Yes或No	Der erwartete Wert ist nicht gültig; es muss entweder 'Ja', 'Nein' oder 'Beides' gewählt sein
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL	A valid number must be specified for the reference value	必须指定一个有效的数字参考值	Eine gültige Zahl muss als Referenzzahl angegeben werden
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP	The comparator must be specified properly	必须指定比较器	Ein gültiger Vergleichsoperator muss angegeben werden
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.HINVVAL	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.HINVCMP	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.OINVVAL	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL	NOT_FOUND!,@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.OINVCMP	@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP	NOT_FOUND!,@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.XINVMTRX	Invalid matrix definition; sizes are not given or are invalid	无效的矩阵定义; 尺寸不给予或无效	Ungültige Matrixdefinition; die Grösse ist nicht korrekt angegeben oder ungültig
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.XINVFORM	No matrix formula specified	没有指定矩阵公式	Keine Matrixformel spezifiziert
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.XVARIOUS	Unexpected error while verifiying matrix	未预期的错误,同时验证矩阵	Unbekannter Fehler beim Prüfen der Matrixformel
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.XSYNTAX	Syntax error while verifying matrix formula; please check for typing errors	语法错误,同时验证矩阵公式; 检查是否输入错误	Syntaxfehler beim Prüfen der Matrixformel; bitte auf Tippfehler achten
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.XINVVAR	Invalid variable name detected while verifying matrix formula	检测到无效的变量名称,同时验证矩阵公式	Ungültiger Variablenname beim Prüfen der Matrixformel gefunden
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.XINVFUN	Invalid method name detected while verifying matrix formula	检测到无效的方法名称,同时验证矩阵公式	Unbekannte Methode beim Prüfen der Matrixformel gefunden
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.XINVUSE	One ore more methods are used improperly (e.g. one instead of two arguments)	一个或多个方法使用不当 (列如: 一个代替两个参数)	Eine oder mehr Methoden werden ungültig verwendet (z.B. einer statt zwei Argumente)
SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.MCODEMISSING	The grouping code must be set for this measure type and exceed 3 characters	必须设置此测量类型的分组代码，必须超过3个字符	Der Gruppierungscode muss für diesen Messtyp gesetzt und mindestens drei Zeichen lang sein
SERVER.ERROR.VALIDATION.CHECKTYPE.CODE.UNIQUE	The code value must be unique; no other checktype may have the same code	代码值必须是唯一的; 没有其他测试类型可以有相同的代码	Der Code muss eindeutig sein; keine andere Prüfungsart darf denselben Code haben
SERVER.ERROR.VALIDATION.CHECKTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.CHECKTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.CHECKTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID
SERVER.ERROR.VALIDATION.CHECKTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE
SERVER.ERROR.VALIDATION.CHECKTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT
SERVER.ERROR.VALIDATION.CHECKTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID
SERVER.ERROR.VALIDATION.DEVICETYPE.CODE.UNIQUE	The code value must be unique; no other devicetype may have the same code	代码值必须是唯一的; 没有其他设备类型可以有相同的代码	Der Code muss eindeutig sein; kein anderer Gerätetyp darf denselben Code haben
SERVER.ERROR.VALIDATION.DEVICETYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.DEVICETYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.DEVICETYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID
SERVER.ERROR.VALIDATION.DEVICETYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE
SERVER.ERROR.VALIDATION.DEVICETYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT
SERVER.ERROR.VALIDATION.DEVICETYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID
SERVER.ERROR.VALIDATION.MODEL.CODE.UNIQUE	The code value must be unique; no other model may have the same code	代码值必须是唯一的; 没有其他型号可以有相同的代码	Der Code muss eindeutig sein; kein anderes Modell darf denselben Code haben
SERVER.ERROR.VALIDATION.MODEL.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.MODEL.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.MODEL.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID
SERVER.ERROR.VALIDATION.MODEL.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE
SERVER.ERROR.VALIDATION.MODEL.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT
SERVER.ERROR.VALIDATION.MODEL.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID
SERVER.ERROR.VALIDATION.MODEL.DEVICETYPE_ID.NULL	The devicetype must be set	设备类型必须设置	Der Gerätetyp muss gesetzt sein
SERVER.ERROR.VALIDATION.MODEL.DEVICETYPE_ID.NOREF	The devicetype {{ refid}} is not valid or does not exist	设备类型 {{ refid}} 为无效或不存在	Der Gerätetyp {{ refid}} ist nicht gültig oder existiert nicht
SERVER.ERROR.VALIDATION.UNIT.CODE.UNIQUE	The code value must be unique; no other unit may have the same code	代码值必须是唯一的; 没有其他单位可以有相同的代码	Der Code muss eindeutig sein; keine andere Einheit darf denselben Code haben
SERVER.ERROR.VALIDATION.UNIT.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT
SERVER.ERROR.VALIDATION.UNIT.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID
SERVER.ERROR.VALIDATION.UNIT.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID	@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID
SERVER.ERROR.VALIDATION.UNIT.COMMENT.INVALID	The language block for the customer is invalid	标题语言块给客户是无效的	Der Sprachblock für den Kunden ist ungültig
SERVER.ERROR.VALIDATION.UNIT.MODEL_ID.NULL	The model must be set	型号必须设置	Das Modell muss gesetzt sein
SERVER.ERROR.VALIDATION.UNIT.MODEL_ID.NOREF	The model {{ refid }} is not valid or does not exist	型號 {{ refid }}为无效或不存在	Das Modell {{ refid }} ist nicht gültig oder existiert nicht
SERVER.ERROR.VALIDATION.TYPE	The server reported one or more data validation errors:	服务器报告一个或多个数据验证错误:	Der Server meldet ein oder mehrere Datenvalidierungsfehler:
SERVER.ERROR.INVMOVIE.TYPE	Uploaded video is not valid	上传视频為无效的	Das hochgeladene Video ist ungültig
SERVER.ERROR.INVMOVIE.TEXT	The uploaded video file was checked and found invalid. It can not be used in this application.	上传的视频文件，经检查发现无效. 它不能用于此应用.	Das hochgeladene Video wurde geprüft und für ungültig bewertet. Es kann in dieser Applikation nicht verwendet werden.
SERVER.ERROR.INVPDF.TYPE	Uploaded PDF document is not valid	上传的PDF文档无效	Das hochgeladene PDF Dokument ist ungültig
SERVER.ERROR.INVPDF.TEXT	The uploaded PDF document was checked and found invalid. It can not be used in this application.	上传的PDF文档，经检查发现无效. 它不能用于此应用.	Das hochgeladene PDF Dokument wurde geprüft und für ungültig bewertet. Es kann in dieser Applikation nicht verwendet werden.
SERVER.ERROR.CHECKACTION.TYPE	An error occurred during this action	此操作期间出现错误	Ein Fehler ist während dieser Aktion aufgetreten
SERVER.ERROR.CHECKACTION.INVALIDPHASE	This action may not be performed in this phase	在这个阶段可能无法执行此操作	Diese Aktion darf in dieser Phase nicht ausgeführt werden
SERVER.ERROR.CHECKACTION.REQFAILED	Some requirements are not met for this action	此操作不符合某些要求	Einige Bedingungen für diese Aktion sind nicht erfüllt
SERVER.ERROR.CHECKACTION.INVCOMMAND	Invalid command	无效命令	Ungültiges Kommando
SERVER.ERROR.STEPACTION.TYPE	@:SERVER.ERROR.CHECKACTION.TYPE	@:SERVER.ERROR.CHECKACTION.TYPE	@:SERVER.ERROR.CHECKACTION.TYPE
SERVER.ERROR.STEPACTION.INVALIDPHASE	@:SERVER.ERROR.CHECKACTION.INVALIDPHASE	@:SERVER.ERROR.CHECKACTION.INVALIDPHASE	@:SERVER.ERROR.CHECKACTION.INVALIDPHASE
SERVER.ERROR.STEPACTION.REQFAILED	@:SERVER.ERROR.CHECKACTION.REQFAILED	@:SERVER.ERROR.CHECKACTION.REQFAILED	@:SERVER.ERROR.CHECKACTION.REQFAILED
SERVER.ERROR.STEPACTION.INVCOMMAND	@:SERVER.ERROR.CHECKACTION.INVCOMMAND	@:SERVER.ERROR.CHECKACTION.INVCOMMAND	@:SERVER.ERROR.CHECKACTION.INVCOMMAND
SERVER.ERROR.LOGIN.TYPE	Login error	登入错误	Login Fehler
SERVER.ERROR.LOGIN.USERUNKNOWN	The username does not exist in the database	用户名不存在数据库中	Der Benutzername existiert nicht in der Datenbank
SERVER.ERROR.LOGIN.NOTINITIALIZED	The user has not yet been initialized	用户尚未初始化	Der Benutzer ist gesperrt oder nicht initialisiert
SERVER.ERROR.LOGIN.WRONGPASSWORD	The password does not match	密码不匹配	Das Passwort stimmt nicht
SERVER.ERROR.LOGIN.NOTLOGGEDIN	The user is not logged in	用户没有登录	Der Benuter ist nicht eingeloggt
SERVER.ERROR.NOTFOUND.TYPE	An object is missing	缺少一个对象	Ein Objekt wurde nicht gefunden
SERVER.ERROR.NOTFOUND.TEXT	The {{class}}-object with ID {{id}} could not be found in the database	此 {{class}}- 有ID的对象 {{id}} 在数据库中找不到	Das {{class}}-Object mit ID {{id}} konnte in der Datenbank nicht gefunden werden
SERVER.ERROR.NOTFOUND.CHECKSTEXT	The step #{{sid}} in check (ID: {{cid}}) could not be found in the database	此步骤 #{{sid}} 在确认 (ID: {{cid}}) 在数据库中找不到	Der Schritt #{{sid}} in der Prüfung (ID: {{cid}}) konnte in der Datenbank nicht gefunden werden
SERVER.ERROR.NOTFOUND.CHECKMTEXT	The measure #{{mid}} in step #{{sid}} in check (ID: {{cid}}) could not be found in the database	此测量 #{{mid}} 在步骤 #{{sid}} 在确认 (ID: {{cid}}) 在数据库中找不到	Die Messung #{{mid}} in Schritt #{{sid}} in Prüfung (ID: {{cid}}) konnte in der Datenbank nicht gefunden werden
SERVER.ERROR.ACCDENIED.TYPE	Access denied or insufficient	拒绝访问或不足	Zugriff abgelehnt
SERVER.ERROR.ACCDENIED.TEXT	Your access level or privileges are not sufficient for this action	你的访问级别或权限不足以实施该动作	Ihre Zugriffsrechte sind für diese Aktion nicht ausreichend.
SERVER.ERROR.INVMEDIA.TYPE	Uploaded media type invalid	上传的媒体类型无效	Hochgeladener Medientyp ungültig oder unbekannt
SERVER.ERROR.INVMEDIA.TEXT	The uploaded media has an invalid type; the server was unable to store the data.	上传的媒体具有无效的类型; 服务器无法存储数据	Die hochgeladene Mediendatei hat einen ungültigen Typ; der Server kann die Daten nicht abspeichern
SERVER.ERROR.IMPORT.TYPE	Import error	输入错误	Importfehler
SERVER.ERROR.IMPORT.WRONGTYPE	The import file ({{ fname }}) has the wrong type. Requested was an import of type {{ req }}, the file contains type {{ is }}.	输入文件 ({{ fname }}) 的类型错误. 要求输入的类型 {{ req }}, 此文件包含类型{{ is }}.	Die Importdatei ({{ fname }}) hat den falschen Typ. Es wurde ein Import vom Typ {{ req }} gefordert, die Datei beinhaltet den Typ {{ is }}.
SERVER.ERROR.IMPORT.BADCRC	The import file ({{ fname }}) has been tampered with. Only original, unmodified files downloaded with the export function may be imported.	输入文件 ({{ fname }}) 已被篡改. 只有原始的,未经修改的文件下载输出功能可能被输入."	Die Importdatei ({{ fname }}) wurde modifiziert. Nur originale, unmodifizierte Dateien dürfen importiert werden.
SERVER.ERROR.IMPORT.INVALIDFILE	The given file ({{ fname }}) is invalid; either it has been tampered with or you have selected a wrong file.	给定的文件 ({{ fname }}) 是无效的; 它要么已被篡改或您选择了一个错误的文件	Die Datei ({{ fname }}) ist invalid; sie wurde entweder verändert oder Sie haben die falsche Datei ausgesucht.
EDITOR.LOADING	Editor loading...	编辑器加载…	Editor lädt...
EDITOR.CODECHNG.TITLE	Code changed!	代码更改!	Code geändert!
EDITOR.CODECHNG.TEXT	Changing the code value of any object should be avoided. It may alter existing workflows rulesets, rendering them useless or invalid. Are you sure you want to save?	应避免更改任何对象的代码值。它可能会改变现有的工作流规则设置，使它们失效或无效。你确定要储存吗?	Den Code eines Objektes zu verändern sollte vermieden werden, da hierdurch Ablaufregeln verändert und ungültig werden können. Bitte fahren Sie nur fort, wenn das Objekt sicher noch nicht in Arbeitsabläufen verwendet wird.
VERSIONING.EDIT.VERSIONED	Edit (V. {{ vers }})	编辑 (V. {{ vers }})	Edit (V. {{ vers }})
VERSIONING.EDIT.NEW	Edit (new)	编辑 (new)	Edit (neu)
VERSIONING.VERSION	Version {{ vers }}	版本 {{ vers }}	Version {{ vers }}
VERSIONING.LASTCHG	last change by {{ realname }} ({{ username }}) on {{ date }}	上次更改由 {{ realname }} ({{ username }}) 在 {{ date }}	letzte Änderung von {{ realname }} ({{ username }}) am {{ date }}
WFMODIFIER.NOMODIFIERS	No workflow modifiers set	没有工作流程修改设定	Keine Ablaufanweisungen festgelegt
WFMODIFIER.HASMODIFIERS	{{ num }} workflow modifiers set:	{{ num }} 工作流程修改设定:	{{ num }} Ablaufanweisungen festgelegt:
WFMODIFIER.CT.SELECT.STD	Omit if used in a check with checktype {{ code }}	省略,如果用于检查和检查类型{{ code }}	Auslassen wenn in einer Prüfung mit Prüfungsart {{ code }} verwendet
WFMODIFIER.CT.SELECT.INV	Omit if not used in a check with checktype {{ code }}	省略,如果不是用于检查和检查类型 {{ code }}	Auslassen wenn nicht in einer Prüfung mit Prüfungsart {{ code }} verwendet
WFMODIFIER.MD.SELECT.STD	Omit if used for model {{ code }}	省略,如果用于型号 {{ code }}	Auslassen wenn für Modell {{ code }} eingesetzt
WFMODIFIER.MD.SELECT.INV	Omit if not used for model {{ code }}	省略,如果不是用于型号 {{ code }}	Auslassen wenn nicht für Modell {{ code }} eingesetzt
WFMODIFIER.DT.SELECT.STD	Omit if used for a model with devicetype {{ code }}	省略,如果用于设备类型的型号{{ code }}	Auslassen wenn für ein Modell mit Gerätetyp {{ code }} verwendet
WFMODIFIER.DT.SELECT.INV	Omit if not used for a model with devicetype {{ code }}	省略,如果不是用于设备类型的型号{{ code }}	Auslassen wenn nicht für ein Modell mit Gerätetyp {{ code }} verwendet
WFMODIFIER.P.INQUEUE.STD	Omit if procedure {{ code }} is also queued in the check	省略,如果流程 {{ code }} 也在排队检查	Auslassen wenn die Prozedur {{ code }} auch in der Prüfung verwendet wird
WFMODIFIER.P.INQUEUE.INV	Omit if procedure {{ code }} is not queued in the check	省略,如果流程 {{ code }} 不排队检查	Auslassen wenn die Prozedur {{ code }} nicht in der Prüfung verwendet wird
WFMODIFIER.P.PASS.OMIT	Omit if procedure {{ code }} is finished and has passed	省略,如果流程 {{ code }} 完成并通过了	Auslassen wenn die Prozedur {{ code }} beendet und bestanden wurde
WFMODIFIER.P.PASS.SKIP	Skip if procedure {{ code }} is finished and has passed	跳过,如果流程 {{ code }} 完成并通过了	Überspringen wenn ie Prozedur {{ code }} beendet und bestanden wurde
WFMODIFIER.P.FAIL.OMIT	Omit if procedure {{ code }} is finished and has failed	省略,如果流程 {{ code }} 完成并失败了	Auslassen wenn die Prozedur {{ code }} beendet und wurde und fehlgeschlagen ist
WFMODIFIER.P.FAIL.SKIP	Skip if procedure {{ code }} is finished and has failed	跳过,如果流程 {{ code }} 完成并失败了	Überspringen wenn die Prozedur {{ code }} beendet und wurde und fehlgeschlagen ist
WFMODIFIER.P.SKIP.OMIT.STD	Omit if procedure {{ code }} is skipped	省略,如果流程 {{ code }} 被跳过	Auslassen wenn die Prozedur {{ code }} übersprungen wurde
WFMODIFIER.P.SKIP.OMIT.INV	Omit if procedure {{ code }} is not skipped	省略,如果流程 {{ code }} 未跳过	Auslassen wenn die Prozedur {{ code }} nicht übersprungen wurde
WFMODIFIER.P.SKIP.SKIP.STD	Skip if procedure {{ code }} is skipped	跳过,如果流程 {{ code }} 被跳过	Überspringen wenn die Prozedur {{ code }} übersprungen wurde
WFMODIFIER.P.SKIP.SKIP.INV	Skip if procedure {{ code }} is not skipped	跳过,如果流程 {{ code }} 未跳过	Überspringen wenn die Prozedur {{ code }} nicht übersprungen wurde
WFMODIFIER.P.OMIT.OMIT.STD	Omit if procedure {{ code }} is omitted	省略,如果流程 {{ code }} 被省略	Auslassen wenn die Prozedur {{ code }} ausgelassen wurde
WFMODIFIER.P.OMIT.OMIT.INV	Omit if procedure {{ code }} is not omitted	省略,如果流程 {{ code }} 未省略	Auslassen wenn die Prozedur {{ code }} nicht ausgelassen wurde
WFMODIFIER.P.OMIT.SKIP.STD	Skip if procedure {{ code }} is omitted	跳过,如果流程 {{ code }} 被省略	Überspringen wenn die Prozedur {{ code }} ausgelassen wurde
WFMODIFIER.P.OMIT.SKIP.INV	Skip if procedure {{ code }} is not omitted	跳过,如果流程 {{ code }} 未省略	Überspringen wenn die Prozedur {{ code }} nicht ausgelassen wurde
WFMODIFIER.S.PASS.OMIT	Omit if step {{ code }} is finished and has passed	省略,如果步骤 {{ code }} 完成并通过了	Auslassen wenn der Schritt {{ code }} beendet und bestanden wurde
WFMODIFIER.S.PASS.SKIP	Skip if step {{ code }} is finished and has passed	跳过,如果步骤 {{ code }} 完成并通过了	Überspringen wenn der Schritt {{ code }} beendet und bestanden wurde
WFMODIFIER.S.FAIL.OMIT	Omit if step {{ code }} is finished and has failed	省略,如果步骤 {{ code }} 完成并失败了	Auslassen wenn der Schritt {{ code }} beendet wurde und fehlgeschlagen ist
WFMODIFIER.S.FAIL.SKIP	Skip if step {{ code }} is finished and has failed	跳过,如果步骤 {{ code }} 完成并失败了	Überspringen wenn der Schritt {{ code }} beendet wurde und fehlgeschlagen ist
WFMODIFIER.S.SKIP.OMIT.STD	Omit if step {{ code }} is skipped	省略,如果步骤 {{ code }} 被跳过	Auslassen wenn der Schritt {{ code }} übersprungen wurde
WFMODIFIER.S.SKIP.OMIT.INV	Omit if step {{ code }} is not skipped	省略,如果步骤 {{ code }} 未跳过	Auslassen wenn der Schritt {{ code }} nicht übersprungen wurde
WFMODIFIER.S.SKIP.SKIP.STD	Skip if step {{ code }} is skipped	跳过,如果步骤 {{ code }} 被跳过	Überspringen wenn der Schritt {{ code }} übersprungen wurde
WFMODIFIER.S.SKIP.SKIP.INV	Skip if step {{ code }} is not skipped	跳过,如果步骤 {{ code }} 未跳过	Überspringen wenn der Schritt {{ code }} nicht übersprungen wurde
WFMODIFIER.S.OMIT.OMIT.STD	Omit if step {{ code }} is omitted	省略,如果步骤 {{ code }} 被省略	Auslassen wenn der Schritt {{ code }} ausgelassen wurde
WFMODIFIER.S.OMIT.OMIT.INV	Omit if step {{ code }} is not omitted	省略,如果步骤 {{ code }} 未省略	Auslassen wenn der Schritt {{ code }} nicht ausgelassen wurde
WFMODIFIER.S.OMIT.SKIP.STD	Skip if step {{ code }} is omitted	跳过,如果步骤 {{ code }} 被省略	Überspringen wenn der Schritt {{ code }} ausgelassen wurde
WFMODIFIER.S.OMIT.SKIP.INV	Skip if step {{ code }} is not omitted	跳过,如果步骤 {{ code }} 未省略	Überspringen wenn der Schritt {{ code }} nicht ausgelassen wurde
WFMODIFIER.M.PASS.OMIT	Omit if step/measure {{ code }} is finished and has passed	省略,如果步骤/测量 {{ code }} 完成并通过了	Auslassen wenn der Schritt/Messung {{ code }} beendet und bestanden wurde
WFMODIFIER.M.PASS.SKIP	Skip if step/measure {{ code }} is finished and has passed	跳过,如果步骤/测量 {{ code }} 完成并通过了	Überspringen wenn der Schritt/Messung {{ code }} beendet und bestanden wurde
WFMODIFIER.M.FAIL.OMIT	Omit if step/measure {{ code }} is finished and has failed	省略,如果步骤/测量 {{ code }} 完成并失败了	Auslassen wenn der Schritt/Messung {{ code }} beendet wurde und fehlgeschlagen ist
WFMODIFIER.M.FAIL.SKIP	Skip if step/measure {{ code }} is finished and has failed	跳过,如果步骤/测量 {{ code }} 完成并失败了	Überspringen wenn der Schritt/Messung {{ code }} beendet wurde und fehlgeschlagen ist
WFMODIFIER.M.YES.OMIT	Omit if step/measure {{ code }} has been set to 'Yes'	省略,如果步骤/测量 {{ code }} 已设置为“是”	Auslassen wenn der Schritt/Messung {{ code }} mit 'Ja' beantwortet wurde
WFMODIFIER.M.YES.SKIP	Skip if step/measure {{ code }} has been set to 'Yes'	跳过,如果步骤/测量 {{ code }} 已设置为“是”	Überspringen wenn der Schritt/Messung {{ code }} mit 'Ja' beantwortet wurde
WFMODIFIER.M.NO.OMIT	Omit if step/measure {{ code }} has been set to 'No'	省略,如果步骤/测量 {{ code }} 已设置为“否”	Auslassen wenn der Schritt/Messung {{ code }} mit 'Nein' beantwortet wurde
WFMODIFIER.M.NO.SKIP	Skip if step/measure {{ code }} has been set to 'No'	跳过,如果步骤/测量 {{ code }} 已设置为“否”	Überspringen wenn der Schritt/Messung {{ code }} mit 'Nein' beantwortet wurde
WFMODIFIER.M.SKIP.OMIT.STD	Omit if step/measure {{ code }} is skipped	省略,如果步骤/测量 {{ code }} 被跳过	Auslassen wenn der Schritt/Messung {{ code }} übersprungen wurde
WFMODIFIER.M.SKIP.OMIT.INV	Omit if step/measure {{ code }} is not skipped	省略,如果步骤/测量 {{ code }} 未跳过	Auslassen wenn der Schritt/Messung {{ code }} nicht übersprungen wurde
WFMODIFIER.M.SKIP.SKIP.STD	Skip if step/measure {{ code }} is skipped	跳过,如果步骤/测量 {{ code }} 被跳过	Überspringen wenn der Schritt/Messung {{ code }} übersprungen wurde
WFMODIFIER.M.SKIP.SKIP.INV	Skip if step/measure {{ code }} is not skipped	跳过,如果步骤/测量 {{ code }} 未跳过	Überspringen wenn der Schritt/Messung {{ code }} nicht übersprungen wurde
WFMODIFIER.M.OMIT.OMIT.STD	Omit if step/measure {{ code }} is omitted	省略,如果步骤/测量 {{ code }} 被省略	Auslassen wenn der Schritt/Messung {{ code }} ausgelassen wurde
WFMODIFIER.M.OMIT.OMIT.INV	Omit if step/measure {{ code }} is not omitted	省略,如果步骤/测量 {{ code }} 未省略	Auslassen wenn der Schritt/Messung {{ code }} nicht ausgelassen wurde
WFMODIFIER.M.OMIT.SKIP.STD	Skip if step/measure {{ code }} is omitted	跳过,如果步骤/测量 {{ code }} 被省略	Überspringen wenn der Schritt/Messung {{ code }} ausgelassen wurde
WFMODIFIER.M.OMIT.SKIP.INV	Skip if step/measure {{ code }} is not omitted	跳过,如果步骤/测量 {{ code }} 未省略	Überspringen wenn der Schritt/Messung {{ code }} nicht ausgelassen wurde
ERROR.CLIENT.TITLE	A severe error has occurred...	发生了一个严重错误…	Ein schwerer Fehler trat auf...
ERROR.CLIENT.TOTTL	Timeout...	逾时…	Zeitüberschreitung...
ERROR.CLIENT.ADTTL	Access denied...	拒绝访问	Zugriff verweigert...
ERROR.CLIENT.DETAILS	Details following:	细节如下:	Details:
ERROR.CLIENT.BACK	Back to login	回到登录	Zurück zum Login
ERROR.CLIENT.MSG.TIMEOUT	The current session has been cancelled due to an timeout. Please log in again and continue.	由于逾时当前工作段已被取消.请重新登录并继续.	Die aktuelle Sitzung wurde wegen eines Timeout beendet. Bitte erneut einloggen und fortfahren.
ERROR.CLIENT.MSG.ACCDENY	Access was denied because of missing rights or privileges. Please log in again and continue.	由于权限丢失，访问被拒绝。请重新登入并继续。	Der Zugriff wurde auf Grund fehlender Rechte oder Privilegien nicht erlaubt. Bitte erneut einloggen und fortfahren.
ERROR.CLIENT.MSG.SEVREST	A severe server error has occurred; the process can not be continued in this stage. Please return to the login or use the title bar to select the next destination.	发生了严重的服务器错误;在这一阶段的过程无法继续. 请返回到登录或使用标题栏选择下一个目的地.	Ein schwerer Serverfehler trat auf; der Prozess kann nicht fortgeführt werden. Bitte zurück zum Login gehen oder in der Titelzeile zu einer anderen Seite wechseln.
ERROR.CLIENT.MSG.HTTP404	@:ERROR.CLIENT.MSG.SEVREST	@:ERROR.CLIENT.MSG.SEVREST	@:ERROR.CLIENT.MSG.SEVREST
ERROR.CLIENT.MSG.HTTP500	@:ERROR.CLIENT.MSG.SEVREST	@:ERROR.CLIENT.MSG.SEVREST	@:ERROR.CLIENT.MSG.SEVREST
LOGIN.TITLE	Login	登录	Login
LOGIN.USERNAME	Username	用户名	Benutzername
LOGIN.PASSWORD	Password	密码	Passwort
LOGIN.BUTTON	@:LOGIN.TITLE	@:LOGIN.TITLE	@:LOGIN.TITLE
UI.BUTTONS.MEDITOR.RESET	@:WFLOW.INPUT.BUTTON.CLEAR	@:WFLOW.INPUT.BUTTON.CLEAR	@:WFLOW.INPUT.BUTTON.CLEAR
UI.BUTTONS.MEDITOR.ADDMSEL	Add {{ num }} item(s)	增加 {{ num }} 项目(s)	{{ num }} Eintrag/äge hinzufügen
UI.BUTTONS.MEDITOR.ADDMSELTEST	Test {{ num }} item(s)	测试 {{ num }} 项目(s)	Test {{ num }} item(s)
UI.BUTTONS.MEDITOR.SAVE	Save	储存	Sichern
UI.BUTTONS.MEDITOR.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
UI.BUTTONS.MEDITOR.SELECT	Select	选择	Auswählen
UI.BUTTONS.MEDITOR.SELALL	Select all	选择全部	Alles auswählen
UI.BUTTONS.MEDITOR.DESELALL	Clear all	清除全部	Zurücksetzen
UI.BUTTONS.ALERT.OK	OK	好	OK
UI.BUTTONS.ALERT.YES	@:WFLOW.INPUT.YES	@:WFLOW.INPUT.YES	@:WFLOW.INPUT.YES
UI.BUTTONS.ALERT.NO	@:WFLOW.INPUT.NO	@:WFLOW.INPUT.NO	@:WFLOW.INPUT.NO
UI.MEDITOR.MAXSEL	You max select {{ num }} entry/ies	你可以选择{{ num }} 进入点	Es dürfen noch {{ num }} Eintrag/äge ausgewählt werden
UI.MEDITOR.NOMORESEL	The maximum number of selected entries has been reached.	所选择的进入点已达到限制	Die maximale Anzahl ausgewählter Einträge ist erreicht
VIEWER.IMAGE.TITLE	Image viewer	图像浏览	Bildbetrachter
VIEWER.IMAGE.ZOOM.FULL	Full view	NOT_FOUND!,Vollansicht
VIEWER.IMAGE.ZOOM.FIT	Fit into window	NOT_FOUND!,Eingepasst
VIEWER.IMAGE.ZOOM.STEP	Zoom {{ factor }}x	NOT_FOUND!,Zoom {{ factor }}x
VIEWER.IMAGE.ZOOM.MAX	Original size	NOT_FOUND!,Originale Grösse
VIEWER.PDF.TITLE	PDF viewer	PDF浏览	PDF Anzeige
VIEWER.VIDEO.TITLE	Video player	影像拨放器	Video player
TLISTEDIT.TOOLTIP.DEL	Delete entry	删除条目	Eintrag entfernen
TLISTEDIT.TOOLTIP.MEDMGR	Start media manage for this item	启动媒体管理这个项目	Mediamanager für diesen Eintrag starten
TLISTEDIT.TOOLTIP.EDIT	Edit item	编辑项目	Eintrag editieren
TLISTEDIT.TOOLTIP.NEW	Create new item	创建新项目	Neuen Eintrag erstellen
TLISTEDIT.LOADING	Loading editor...	加载编辑器…	Lade Editor...
TLISTEDIT.DISABLED	Disabled!	禁用!	Deaktiviert!
TLISTEDIT.CNT.CHECK	{{ cnt }} check(s)	{{ cnt }} 检查(s)	{{ cnt }} Prüfung(en)
TLISTEDIT.CNT.MODEL	{{ cnt }} model(s)	{{ cnt }} 型号(s)	{{ cnt }} Modell(e)
TLISTEDIT.CNT.IMAGES	{{ cnt }} image(s)	{{ cnt }} 图像(s)	{{ cnt }} Bild(er)
TLISTEDIT.CNT.DOCS	{{ cnt }} document(s)	{{ cnt }} 文檔(s)	{{ cnt }} Dokument(e)
TLISTEDIT.NEW	New entry	新条目	Neuer Eintrag
TLISTEDIT.CODE	Code	代码	Code
TLISTEDIT.TITLE	Title	标题	Titel
TLISTEDIT.DESC	Description	描述	Beschreibung
TLISTEDIT.DISSET.TITLE	Disabled	禁用	Deaktiviert
TLISTEDIT.DISSET.TRUE	This entry is deleted and may not be used any longer.	这个条目被删除,不得再使用.	Dieser Eintrag ist deaktiviert und kann nicht mehr eingesetzt werden.
TLISTEDIT.DISSET.FALSE	This entry is active.	这个条目是启动.	Dieser Eintrag ist aktiv.
TLISTEDIT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL	@:WFLOW.INPUT.BUTTON.CANCEL
TLISTEDIT.BUTTON.ADD	Add	增加	Hinzufügen
TLISTEDIT.BUTTON.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE	@:UI.BUTTONS.MEDITOR.SAVE
MEDITOR.GROUP	Group	群组	Gruppe
MEDITOR.USER	User	@:FRAME.MANAGE.USER	Benutzer
DASHBLOCK.TOOLTIP.CLOSE	Remove block	移除区块	Block entfernen
DASHBLOCK.TOOLTIP.GOTO	Go to item	转到项目	Zum Eintrag gehen
DASHBLOCK.TOOLTIP.SETFILTER	Edit filter settings	编辑波滤器设置	Filtereinstellungen editieren
DASHBLOCK.TOOLTIP.EDITTITLE	Edit title of block	编辑块的名称	Blocktitel ändern
DROPBOX.INTRO	Drag procedure export files into this box for import!	拖拽流程输出文件到此框输入!	Für den Import eine Exportdatei in dieses Feld ziehen!
DROPBOX.UPLOADING	Uploading {{ num }} file(s)	上传 {{ num }} 文件(s)	Lade {{ num }} Datei(en) hoch
DROPBOX.REMAINING	{{ num }} file(s) remaining	{{ num }} 文件(s) 还需处理的	{{ num }} Datei(en) in der Warteschlange
DROPBOX.SUCCESS	{{ num }} file(s) uploaded successfully	{{ num }} 文件(s) 上传成功	{{ num }} Datei(en) erfolgreich hochgeladen
DROPBOX.ERRORS	{{ num }} file(s) uploaded with errors	{{ num }} 文件(s) 上传错误	{{ num }} Datei(en) mit Fehlern
PRV.MNGMUC.TTL	Manage models/unit/checks	管理 型号/单位/检查	Modelle/Einheiten/Checks bearbeiten
PRV.MNGMUC.DSC	May use management views for models, units and checks	型号可以使用管理查看,单位和检查	Darf die Bearbeitungsansicht für Modelle, Einheiten und Prüfungen aufrufen
PRV.MNGPSM.TTL	Manage procedures	管理流程	Prozeduren bearbeiten
PRV.MNGPSM.DSC	May use management views for procedures, as well as steps and measures	流程可以使用管理查看,以及步骤和测量	Darf die Bearbeitungsansicht für Prozeduren, sowie Schritte und Messungen aufrufen
PRV.MNGTOL.TTL	Manage tooltypes	管理工具类型	Werkzeugtypen bearbeiten
PRV.MNGTOL.DSC	May use management views for tool types	工具类型可以使用管理查看	Darf die Bearbeitungsansicht für Werkzeugtypen aufrufen
PRV.MNGPAR.TTL	Manage parameters	管理参数	Parameter bearbeiten
PRV.MNGPAR.DSC	May use management views for parameters, devicetypes and checktypes	参数可以使用管理查看,设备类型和检查类型	Darf die Bearbeitungsansicht für Parameter, Gerätetypen und Prüfungsarten aufrufen
PRV.MNGUSR.TTL	Manage users	管理用户	Benutzer bearbeiten
PRV.MNGUSR.DSC	May use the user management console	用户可以使用管理控制台	Darf die Benutzermanagementansicht aufrufen
PRV.MNGALL.TTL	Manage all	管理全部	Alles bearbeiten
PRV.MNGALL.DSC	May use all management views (privilege collection)	可以使用所有管理查看(权限采集)	Darf alle Bearbeitungsansichten aufrufen (Privilegiensammlung)
PRV.EDTMOD.TTL	Edit models	编辑型号	Modelle editieren
PRV.EDTMOD.DSC	May modify existing models	可以修改现有型号	Darf existierende Modelle editieren
PRV.EDTUNT.TTL	Edit units	编辑单元	Einheiten editieren
PRV.EDTUNT.DSC	May modify existing units	可以修改现有单元	Darf existierende Einheiten editieren
PRV.EDTPSM.TTL	Edit procedures	编辑流程	Prozeduren editieren
PRV.EDTPSM.DSC	May modify existing procedures, including steps and measures (which also may be deleted)	可以修改现有流程,包括步骤和测量(这也可以被删除)	Darf existierende Prozeduren, einschliesslich deren Schritte und Messungen editieren (Schritte und Messungen dürfen auch gelöscht werden)
PRV.EDTTTY.TTL	Edit tooltypes	编辑工具类型	Werkzeugtypen editieren
PRV.EDTTTY.DSC	May modify existing tooltypes	可以修改现有工具类型	Darf existierende Werkzeugtypen editieren
PRV.EDTALL.TTL	Edit all	编辑全部	Alles editieren
PRV.EDTALL.DSC	May modify all existing objects (privilege collection)	可以修改现有的所有对象(权限采集)	Darf alle existierenden Objekte editieren (Privilegiensammlung)
PRV.CRTMOD.TTL	Create models	创建型号	Modelle erstellen
PRV.CRTMOD.DSC	May add new models	可以添加新型号	Darf neue Modelle erstellen
PRV.CRTUNT.TTL	Create units	创建单元	Einheiten erstellen
PRV.CRTUNT.DSC	May add new units	可以添加新单元	Darf neue Einheiten erstellen
PRV.CRTPSM.TTL	Create procedures	创建流程	Prozeduren erstellen
PRV.CRTPSM.DSC	May add new procedures	可以添加新流程	Darf neue Prozeduren erstellen
PRV.CRTTOL.TTL	Create tools	创建工具	Werkzeuge erstellen
PRV.CRTTOL.DSC	May add new tool units	可以添加新工具单元	Darf neue Werkzeuge unter existierenden Werkzeugtypen eintragen
PRV.CRTTTY.TTL	Create tool types	创建工具类型	Werkzeugtypen erstellen
PRV.CRTTTY.DSC	May add new tool types	可以添加新工具类型	Darf neue Werkzeugtypen erstellen
PRV.CRTCHK.TTL	Create checks	创建检查	Prüfungen erstellen
PRV.CRTCHK.DSC	May add new checks or delete checks not yet started	可以新增新的检查或删除尚未开始的检查	Darf neue Prüfungen einrichten oder Prüfungen löschen, die noch nicht gestartet sind
PRV.CRTALL.TTL	Create all	创建全部	Alles erstellen
PRV.CRTALL.DSC	May add new object of any kind (privilege collection)	可以新增任何一种新的对象(权限采集)	Darf neue Objekte jeden Typs erstellen (Privilegiensammlung)
PRV.DELMOD.TTL	Delete models	删除型号	Modelle löschen
PRV.DELMOD.DSC	May delete or disable models	可以删除或禁用型号	Darf Modelle löschen oder deaktivieren
PRV.DELUNT.TTL	Delete units	删除单元	Einheiten löschen
PRV.DELUNT.DSC	May delete units	可以删除单元	Darf Einheiten löschen oder deaktivieren
PRV.DELPRC.TTL	Delete procedures	删除流程	Prozeduren löschen
PRV.DELPRC.DSC	May delete or disable procedures	可以删除或禁用流程	Dark Prozeduren löschen oder deaktivieren
PRV.DELTTY.TTL	Delete tooltypes	删除工具类型	Werkzeugtypen löschen
PRV.DELTTY.DSC	May delete or disable tool types	可以删除或禁用工具类型	Darf Werkzeugtypen löschen oder deaktivieren
PRV.DELTOL.TTL	Delete tools	删除工具类型	Werkzeuge löschen
PRV.DELTOL.DSC	May delete or disable tools	可以删除或禁用工具类型	Darf Werkzeuge löschen oder deaktivieren
PRV.DELCHK.TTL	Delete checks	删除检查	Prüfungen löschen
PRV.DELCHK.DSC	May delete or disable checks	可以删除或禁用检查	Darf Prüfungen löschen
PRV.DELALL.TTL	Delete all	删除全部	Alles löschen
PRV.DELALL.DSC	May delete or disable all objects (privilege collection)	可以删除或禁用所有对象(权限采集)	Darf Objekte aller Typen löschen oder deaktivieren (Privilegiensammlung)
PRV.USRMGO.TTL	@:PRV.MNGUSR.TTL	@:PRV.MNGUSR.TTL	@:PRV.MNGUSR.TTL
PRV.USRMGO.DSC	May manage (add, change, disable) users of lower level	可以管理 (新增,更改,禁用) 较低级别的用户	Darf Benutzer niedrigerer Stufen bearbeiten (hinzufügen, ändern, deaktivieren)
PRV.USRMGA.TTL	Manage all users	管理全部用户	Alle Benutzer bearbeiten
PRV.USRMGA.DSC	May manage (add, change, disable) users of all levels	可以管理 (新增,更改,禁用) 所有级别的用户	Darf alle Benutzer bearbeiten (hinzufügen, ändern, deaktivieren)
PRV.GRTPRO.TTL	Grant own privileges	授予自己的权限	Eigene Privilegien vergeben
PRV.GRTPRO.DSC	May grant privileges owned to individuals	可以授予个人拥有的权限	Darf selbst besessene Privilegien an andere Benutzer vergeben
PRV.GRTPRA.TTL	Grant all privileges	授予所有权限	Alle Privilegien vergeben
PRV.GRTPRA.DSC	May grant all privileges to individuals	可以个人授予所有权限	Darf alle verfügbaren Privilegien an andere Benutzer vergeben
PRV.GRTTOG.TTL	Grant group privileges	授予群组权限	Gruppenprivilegien vergeben
PRV.GRTTOG.DSC	May grant privileges also to groups (extending privilege)	也可以授予权限给群组(延伸权限)	Darf Privilegien auch an Gruppen vergeben (erweiterndes Privileg)
PRV.GRPCRT.TTL	Create group	创建群组	Gruppen erstellen
PRV.GRPCRT.DSC	May create new groups	可以创建新群组	Darf Gruppen erstellen und editieren
PRV.WFLMNG.TTL	Manage workflow	管理工作流程	Arbeitsablauf verwalten
PRV.WFLMNG.DSC	May manage check and check workflows, comment on checks, view supervisor information	可以管理检查和检查工作流程,评论检查, 查看主管信息	Darf Prüfungen und Prüfungsabläufe verwalten, Prüfungen kommentieren und erweiterte Informationen einsehen
PRV.WFLREG.TTL	Register to workflow	注册工作流程	An Arbeitsablauf anmelden
PRV.WFLREG.DSC	May register to workflows (if applicable) or unregister from workflows (if self-registered)	可以注册工作流程 (如果适用)或从工作流程注销 (如果自助注册)	Darf sich an verfügbare und passende Arbeitsabläufe anmelden, bzw. von selbst-registrierten Arbeitsabläufen abmelden
PRV.CHGCOD.TTL	Change code	更改代码	Codewert ändern
PRV.CHGCOD.DSC	May change the object code when editing (extending privilege)	当编辑时,可以更改对象的代码(延伸权限)	Darf den Codewert beim Ändern verschiedener Objekte editieren (erweiterndes Privileg)
PRV.FINALZ.TTL	Finalize version	最终版本	Version finalisieren
PRV.FINALZ.DSC	May finalize current version of object (only available with privilege to edit object; extending privilege)	可以确定对象的当前版本(唯一可用的权限去编辑对象;延伸权限)	Darf die aktuellen Versionen der Objekte finalisieren (nur verfügbar, wenn auch das Editierrecht für das Objekt besessen ist; erweiterndes Privileg)
PRV.MODVRS.TTL	Modify version	修改版本	Version veränden
PRV.MODVRS.DSC	May edit or delete a finalized version of an object	可以编辑或删除一个对象的最终版本	Darf eine finalisierte Version eines Objektes verändern oder löschen
PRV.TRUSER.TTL	Experienced user	有经验用户	Erfahrener Benutzer
PRV.TRUSER.DSC	The user is experienced and may use several higher level functions	用户经验丰富，可能使用几种高级功能	Der Benutzer ist in dem System erfahren und darf zusätzlich einige beschleunigende Funktionen verwenden
PRV.TKOVER.TTL	Take over assigned tasks	合并分配的任务	Zugewiesene Aufgaben übernehmen
PRV.TKOVER.DSC	The user may take over tasks self assigned to other users if applicable	如适用，用户可以合并自分配给其他用户的任务	Der Benutzer kann bei Eignung Aufgaben übernehmen, für die sich andere Benutzer bereits registriert haben.
PRV.GLSRCH.TTL	Access to global search	访问全局搜索	Zugriff auf global Suche
PRV.GLSRCH.DSC	May use global search function	可使用全局搜索功能	Der Benutzer darf die globale Suche verwenden.
PRV.MNGNTC.TTL	View and process problem reports	查看并操作问题报告	Fehlerberichte anzeigen und bearbeiten
PRV.MNGNTC.DSC	May view, manage and process problem reports on the report page	 manage and process problem reports on the report page"	Der Benutzer darf Fehlerberichte einsehen und bearbeiten.
PDF.BOOL.YES	YES	是	JA
PDF.BOOL.NO	NO	否	NEIN
PDF.MAINTTL	Check Report	检查报告	Prüfungsbericht
PDF.STATUS.OPEN	@:UNIT.VIEW.STATUS.OPEN	@:UNIT.VIEW.STATUS.OPEN	OFFEN
PDF.STATUS.FAILED	@:WFLOW.INTRO.STATUS.FAIL	@:WFLOW.INTRO.STATUS.FAIL	FEHLG.
PDF.STATUS.WARNINT	FAILED <sup>*)</sup>	失败<sup>*)</sup>	FEHLG. <sup>*)</sup>
PDF.STATUS.WARNEXT	PASSED <sup>*)</sup>	通过<sup>*)</sup>	OK <sup>*)</sup>
PDF.STATUS.PASSED	@:WFLOW.INTRO.STATUS.PASS	@:WFLOW.INTRO.STATUS.PASS	OK
PDF.STATUS.UNFIN	@:WFLOW.INPUT.STATUS.TODO	@:WFLOW.INPUT.STATUS.TODO	ZU ERL.
PDF.STATUS.PASS	@:WFLOW.INPUT.STATUS.PASS	@:WFLOW.INPUT.STATUS.PASS	OK
PDF.STATUS.FAIL	@:WFLOW.INPUT.STATUS.FAIL	@:WFLOW.INPUT.STATUS.FAIL	FEHLG.
PDF.STATUS.SKIP	@:WFLOW.STEP.STATUS.SKIP	@:WFLOW.STEP.STATUS.SKIP	AUSGEL.
PDF.STATUS.CANCELLED	CANCELLED	取消	ABGEBR.
PDF.HINT.CHECKWARN.INTERNAL	Some measurements have failed but do not or only slightly affect the proper functionality of the machine.	一些路径失败，但是对机器的正常功能不产生影响	Die fehlgeschlagenen Messungsresultate sind für die Funktion der Maschine nicht oder nur geringfügig von Bedeutung.
PDF.HINT.CHECKWARN.CUSTOMER	A measurement that does not affect the proper functionality of the machine is slightly outside the defined tolerance values.	不影响机器正常功能的路径在所定义公差的范围之外	Ein Messungsresultat, das die Funktion der Maschine nicht beeinflusst, liegt geringfügig ausserhalb der festgelegten Toleranzen.
PDF.USTATUS.OPEN	Open	开启	Offen
PDF.USTATUS.CLOSED	Closed	关闭	Geschlossen
PDF.USTATUS.DISCARDED	Discarded	丢弃	Verworfen
PDF.USTATUS.ACLOSED	Closed & archived	关闭及存档	Geschlossen & archiviert
PDF.USTATUS.ADISCARDED	Discarded & archived	丢弃及存档	Verworfen & archiviert
PDF.MODEL	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD	@:WFLOWEDIT.OPT1.MD
PDF.UNIT	@:WFLOW.INTRO.UNIT	@:WFLOW.INTRO.UNIT	@:WFLOW.INTRO.UNIT
PDF.CHECK	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER	@:WFLOW.INTRO.MEDIAMANAGER
PDF.DATE.SCHED	@:WFLOW.INTRO.DATE.SCHED	@:WFLOW.INTRO.DATE.SCHED	@:WFLOW.INTRO.DATE.SCHED
PDF.DATE.DUE	@:WFLOW.INTRO.DATE.DUE	@:WFLOW.INTRO.DATE.DUE	@:WFLOW.INTRO.DATE.DUE
PDF.DATE.NOSCHED	@:WFLOW.INTRO.DATE.NOSCHED	@:WFLOW.INTRO.DATE.NOSCHED	@:WFLOW.INTRO.DATE.NOSCHED
PDF.DATE.NODUE	@:WFLOW.INTRO.DATE.NODUE	@:WFLOW.INTRO.DATE.NODUE	@:WFLOW.INTRO.DATE.NODUE
PDF.DATE.START	@:WFLOW.INTRO.DATE.START	@:WFLOW.INTRO.DATE.START	@:WFLOW.INTRO.DATE.START
PDF.DATE.FIN	@:WFLOW.INTRO.DATE.FIN	@:WFLOW.INTRO.DATE.FIN	@:WFLOW.INTRO.DATE.FIN
PDF.DATE.NOSTART	@:WFLOW.INTRO.DATE.NOSTART	@:WFLOW.INTRO.DATE.NOSTART	@:WFLOW.INTRO.DATE.NOSTART
PDF.DATE.NOFIN	@:WFLOW.INTRO.DATE.NOFIN	@:WFLOW.INTRO.DATE.NOFIN	@:WFLOW.INTRO.DATE.NOFIN
PDF.USER.NOONE	No one yet	还没有人	Noch niemand
PDF.USER.ANY	Anyone	任何	Nicht festgelegt
PDF.USER.GRP	User of group %s	群组的用户 %s	Benutzer der Gruppe %s
PDF.VERSION	@:PROCEDURE.VIEW.VERS	@:PROCEDURE.VIEW.VERS	@:PROCEDURE.VIEW.VERS
PDF.STEP.ASSNC	<i>Assignee:</i> %s, <b>not committed yet</b>	<i>代理人:</i> %s, <b>尚未提交</b>	<i>Zugewiesen an:</i> %s, <b>noch nicht bestätigt</b>
PDF.STEP.ASSC	<i>Assignee:</i> %s, <i>committed by:</i> %s	<i>代理人:</i> %s, <i>提交由:</i> %s	<i>Zugewiesen an:</i> %s, <i>bestätigt von:</i> %s
PDF.MEASURE.TOOL	<i>Tool (type </i><b>%s</b>: %s) used:</i> <b>%s</b>	<i>工具 (type </i><b>%s</b>: %s)使用:</i> <b>%s</b>	<i>Werkzeug (typ </i><b>%s</b>: %s) verwendet:</i> <b>%s</b>
PDF.MEASURE.TOOLNOSEL	<i>Used tool (type </i><b>%s</b>: %s<i>) <b>not selected yet</b></i>	<i>使用工具 (type </i><b>%s</b>: %s<i>) <b>尚未选择</b></i>	<i>Verwendetes Werkzeug (typ </i><b>%s</b>: %s<i>) <b>noch nicht eingetragen</b></i>
PDF.MEASURE.REP	<i>Measured by </i><b>%s</b> <i>at</i> <b>%s</b>	<i>测量由 </i><b>%s</b> <i>at</i> <b>%s</b>	<i>Gemessen von </i><b>%s</b> <i>am</i> <b>%s</b>
PDF.MEASURE.COMM	<i>Comment:</i> %s	<i>评论:</i> %s	<i>Kommentar:</i> %s
PDF.MEASURE.RAW	<i>Matrix values:</i> %s	<i>矩阵值:</i> %s	<i>Matrixwerte:</i> %s
PDF.MINPUT.VALUECMP	Value must be %s %s	值必须是 %s %s	Der Wert muss %s %s sein
PDF.MINPUT.CHECKCMP	'Yes' if the measured value is %s %s	 '是' 如果测量值是 %s %s	'Ja' wenn der gemessene Wert %s %s ist
PDF.MINPUT.COMP.T1	less than	少于	kleiner als
PDF.MINPUT.COMP.T2	less than or equal to	少于或等于	kleiner als oder gleich
PDF.MINPUT.COMP.T3	equal to	等于	gleich
PDF.MINPUT.COMP.T4	greater than or equal to	大于或等于	grösser als oder gleich
PDF.MINPUT.COMP.T5	greater than	大于	grösser als
PDF.MINPUT.THRESH	Target value: %s, Threshold %s	目标值: %s, 临界值 %s	Zielwert: %s, Abweichung %s
PDF.MINPUT.VALUERNG	Value must be between %s and %s	值必须介于 %s 及 %s	Wert muss zwischen %s und %s sein
PDF.MINPUT.TEXTLEN	Text must be at least %d characters long	文字必须至少 %d 字符长	Der Text muss mindestens %d Zeichen lang sein
PDF.MINPUT.TEXTPAT	Text must match a specific pattern: %s	文字必须与特定模式匹配: %s	Der Text muss auf ein bestimmtes Muster passen: %s
PDF.MINPUT.EXP.NO	@:WFLOW.INPUT.EXPNO	@:WFLOW.INPUT.EXPNO	@:WFLOW.INPUT.EXPNO
PDF.MINPUT.EXP.YES	@:WFLOW.INPUT.EXPYES	@:WFLOW.INPUT.EXPYES	@:WFLOW.INPUT.EXPYES
PDF.MINPUT.EXP.BOTH	@:WFLOW.INPUT.EXPBOTH	@:WFLOW.INPUT.EXPBOTH	@:WFLOW.INPUT.EXPBOTH
PDF.MINPUT.STATISTICAL	For statistical analysis; any value is accepted	用于统计分析;接受任何值	Für statistische Auswertungen; jeder Wert wird akzeptiert
PDF.MINPUT.TIMERA	Time measurement (Start)	时间测定（开始）	Zeitmessung (Start)
PDF.MINPUT.TIMERS	Time measurement (Stop) for statistical reasons	时间测定（停止）由于统计原因	Zeitmessung (Ende) für statistische Auswertungen
PDF.MINPUT.TIMERQ.T1	Time measurement (Stop); time span must be less than %d minutes	时间测定（停止）；时间长度必须少于%d分钟	Zeitmessung (Ende); gemessene Zeit muss unter %d Minuten liegen
PDF.MINPUT.TIMERQ.T5	Time measurement (Stop); time span must be more than %d minutes	时间测定（停止）；时间长度必须多于%d分钟	Zeitmessung (Ende); gemessene Zeit muss über %d Minuten liegen
PDF.MINPUT.TIMERC.T1	Post check time measurement; time span must be less than %d minutes	暂停时间测定检查；时间长度必须少于%d分钟	Prüfung Zeitmessung; gemessene Zeit muss unter %d Minuten liegen
PDF.MINPUT.TIMERC.T5	Post check time measurement; time span must be less than %d minutes	暂停时间测定检查；时间长度必须少于%d分钟	Prüfung Zeitmessung; gemessene Zeit muss über %d Minuten liegen
PDF.STATREPORT	Measurement Statistics	计量统计法	Messungsstatistik
PDF.PAGENO	Page %d of %d	页 %d 的 %d	Seite %d von %d
PDF.STAT.CHECKINTRO	For the statistical overview the following checks are used:	使用下列检查此统计概况:	Für die Statistik werden folgende Prüfungen verwendet:
PDF.STAT.CHECK.LINE1	Check <b>#%s</b> (%s) for unit <b>%s</b> (%s), model <b>%s: %s	检查 <b>#%s</b> (%s) 单元 <b>%s</b> (%s), 型号 <b>%s: %s	Prüfung <b>#%s</b> (%s) für Einheit <b>%s</b> (%s), Modell <b>%s: %s
PDF.STAT.CHECK.LINE2	Check Status: <b>%s</b>, unit status: <b>%s</b>	检查状态: <b>%s</b>, 单元状态: <b>%s</b>	Prüfungsstatus: <b>%s</b>, Status der Einheit: <b>%s</b>
PDF.STAT.MSR1	Measure <b>%s: %s</b>	测量 <b>%s: %s</b>	Messung <b>%s: %s</b>
PDF.STAT.MSR2	Step <b>%s</b>, Procedure <b>%s</b>	步骤 <b>%s</b>, 流程 <b>%s</b>	Schritt <b>%s</b>, Prozedur <b>%s</b>
PDF.STAT.MSR3S	Rule: <b>%s</b>	规则: <b>%s</b>	Regel: <b>%s</b>
PDF.STAT.MSR3O	Rule: <b>%s</b> (optional, may be skipped)	规则: <b>%s</b> (任选的, 可以跳过)	Regel: <b>%s</b> (optional, kann übersprungen werden)
PDF.STAT.MSR4T	Tooltype: <b>%s: %s</b>	工具类型: <b>%s: %s</b>	Werkzeugtyp: <b>%s: %s</b>
PDF.STAT.MSR4N	Tooltype: <i>no tool used</i>	工具类型: <i>没有工具使用</i>	Werkzeugtyp: <i>Kein Werkzeug verwendet</i>
PDF.STAT.OLDV	<sup>*)</sup> An older version of the measure was used during the check; the results may not be compatible with the current version	<sup>*)</sup> 在检查期间是使用此测量的旧版本; 结果可能与当前版本不兼容	<sup>*)</sup> Eine ältere Version der Messung wurde während der Prüfung verwendet; die Ergebnisse können u.U. nicht kompatibel sein.
PDF.STAT.NODATA	No data available	无可用数据	Keine Daten verfügbar
PDF.STAT.HDR.STATUS	@:WFLOW.INPUT.STATUS.TITLE	@:WFLOW.INPUT.STATUS.TITLE	@:WFLOW.INPUT.STATUS.TITLE
PDF.STAT.HDR.USER	@:MEDITOR.USER	@:MEDITOR.USER	@:MEDITOR.USER
PDF.STAT.HDR.TOOL	Tool used	工具使用	Werkzeug
PDF.STAT.HDR.VALUE	Value	值	Wert
PDF.STAT.FINAL.INTRO	The following id-strings were used creating this report. You may use them to recreate this report or parts from it:	下面的ID自符串被用来创建这个报告. 你可以使用它们来创建这个报告或使用部分:	Die folgenden id-Strings wurden bei der Generierung des Reports verwendet. Sie können sie verwenden, um den Report noch einmal ganz oder modifiziert zu generieren:
PDF.STAT.FINAL.CHECKS	Checks:	检查:	Prüfungen:
PDF.STAT.FINAL.MEASURES	Measures:	测量:	Messungen:
PDF.STAT.TITLE.FRONT	Overview	概况	Übersicht
PDF.STAT.TITLE.STATS	Measure %s	测量 %s	Messung %s
PDF.COMMENT.TOOLTIP	Generate comment report	生成注释报告	Kommentarreport anzeigen
PDF.COMMENT.TTL	Comment report	评论报告	Kommentarreport
PDF.COMMENT.FOOTER.USERS	All users	全部用户	Alle Benutzer
PDF.COMMENT.FOOTER.TOOLTYPE	Tooltype '%s' (%s)	工具类型 '%s' (%s)	Werkzeugtyp '%s' (%s)
PDF.COMMENT.FOOTER.MODEL	Model '%s' (%s)	型号 '%s' (%s)	Modell '%s' (%s)
PDF.COMMENT.FOOTER.UNIT	Unit %s	单位 %s	Einheit %s
PDF.COMMENT.FOOTER.CHECK	Check #%d (unit %s, model %s)	检查 #%d (unit %s, model %s)	Prüfung #%d (Einheit %s, Modell %s)
PDF.COMMENT.FOOTER.STEP	Step '%s' V.%d (%s.%s)	步骤 '%s' V.%d (%s.%s)	Schritt '%s' V.%d (%s.%s)
PDF.COMMENT.FOOTER.MEASURE	Measure '%s' V.%d (%s.%s.%s)	测量 '%s' V.%d (%s.%s.%s)	Messung '%s' V.%d (%s.%s.%s)
PDF.COMMENT.ELEMENT.USER	User %s (%s)	用户 %s (%s)	Benutzer %s (%s)
PDF.COMMENT.ELEMENT.TOOL	Tool unit %s	工具单位 %s	Werkzeugeinheit %s
PDF.COMMENT.ELEMENT.TOOLTYPE	Tool type %s	工具类型%s	Werkzeugtyp %s
PDF.COMMENT.ELEMENT.UNIT	Unit %s (%s)	单位 %s (%s)	Einheit %s (%s)
PDF.COMMENT.ELEMENT.CHECK	Check #%d (unit %s, model %s)	检查 #%d (unit %s, model %s)	Prüfung #%d (Einheit %s, Modell %s)
PDF.COMMENT.ELEMENT.MSMNT	Measure %s in step %s of procedure %s	测量 %s 在步骤 %s 程序 %s	Messung %s in Schritt %s, Prozedur %s
PDF.COMMENT.ELEMENT.STEP	Step %s	步骤 %s	Schritt %s
PDF.COMMENT.ELEMENT.PROC	Procedure %s	程序 %s	Prozedur %s
PDF.COMMENT.ELEMENT.MSMNTS	Measure %s	测量 %s	Messung %s
PDF.COMMENT.ELEMENT.MEAS	Measure %s	测量 %s	Messung %s
PDF.TUREPORT.HEADER	Usage report toolunit	用户报告工具单元	Verwendungsreport Werkzeug
PDF.TUREPORT.FOOTER	Tool type <b>%s</b>, unit <b>%s</b>	 unit <b>%s</b>"	Werkzeugtyp <b>%s</b>, Einheit <b>%s</b>
PDF.TUREPORT.TITLE.MODEL	<b>Model %s</b>, Unit %s	 Unit %s"	<b>Modell %s</b>, Einheit %s
PDF.TUREPORT.TITLE.TIME	%s.<b>%s</b>	%s.<b>%s</b>	%s.<b>%s</b>
PDF.TUREPORT.CONTINUED	<i>(cont.)</i>	<i>(cont.)</i>	<i>(fortg.)</i>
PDF.TUREPORT.ENTRY.MODEL	Procedure <b>%s</b>, Step <b>%s</b>, Measure <b>%s</b>	 Step <b>%s</b>	Prozedur <b>%s</b>, Schritt <b>%s</b>, Messung <b>%s</b>
PDF.TUREPORT.ENTRY.TIMET	Model <b>%s</b>, Unit <b>%s</b>	 Unit <b>%s</b>"	Modell <b>%s</b>, Einheit <b>%s</b>
PDF.TUREPORT.ENTRY.TIMEB	<i>Procedure <b>%s</b>, Step <b>%s</b>, Measure <b>%s</b></i>	 Step <b>%s</b>	<i>Prozedur <b>%s</b>, Schritt <b>%s</b>, Messung <b>%s</b></i>
CHANGELOG.ITEM.PROCEDURE	Procedure	程序 %s	Prozedur
CHANGELOG.ITEM.STEP	Step	步骤	Schritt
CHANGELOG.ITEM.MEASURE	Measure	测量	Messung
CHANGELOG.ITEM.IMAGE	Image	图像	Bild
CHANGELOG.ITEM.DOCUMENT	Document	文档	Dokument
CHANGELOG.ITEM.MODEL	Model	型号 	Modell
CHANGELOG.ITEM.CHECKTYPE	Check type	检查类型	Prüfungsart
CHANGELOG.TYPE.CREATE	Created	已创建	Neu hinzugefügt
CHANGELOG.TYPE.CHANGECODE	Code changed from {{ oldcode }}	代码更改由 {{ oldcode}}	Code geändert (ursprünglich: {{ oldcode }})
CHANGELOG.TYPE.CHANGEFIELD	Field {{ field }} changed	字段 {{ field }} 更改	Feld '{{ field }}' geändert
CHANGELOG.TYPE.CHANGESEQ	Order changed (multiple items affected)	顺序更改 (影响到多个项目)	Reihenfolge geändert (mehrere Objekte betroffen)
CHANGELOG.TYPE.CHANGEPID	Procedure updated to version {{ version }}	程序更新到版本 {{ version }}	Prozedur auf Version {{ version }} aktualisiert
CHANGELOG.TYPE.FINALIZE	Finalized	审定	Finalisiert
CHANGELOG.TYPE.DELETE	Deleted	已删除	Gelöscht
CHANGELOG.TYPE.CREATEATT	Created and added to {{ tgt }}	已创建并添加到 {{ tgt }}	Neu zu {{ tgt }} hinzugefügt
CHANGELOG.TYPE.DELETEATT	Deleted from {{ tgt }}	已从 {{ tgt }}删除	Von {{ tgt }} entfernt
CHANGELOG.TYPE.CREATEACTP	Added procedure to model	已添加到型号的程序	Prozedur zu Modell hinzugefügt
CHANGELOG.TYPE.DELETEACTP	Removed procedure from model	已从型号删除的程序	Prozedur von Modell entfernt
CHANGELOG.TYPE.CREATEACTCT	Added check type to model	已添加到型号的检查类型	Prüfungsart zu Modell hinzugefügt
CHANGELOG.TYPE.DELETEACTCT	Removed check type from model	已从型号删除的检查类型	Prüfungsart von Modell entfernt
CHANGELOG.TYPE.PREALLOCATION	Predefined assignments modified	预先定义的任务已修改	Vorabzuweisungen modifiziert
CHANGELOG.VIEWER.TITLE	Change log	更改日志	Änderungsliste
CHANGELOG.VIEWER.EXTRODIRTY	Entries marked blue are direct user changes and require a finalization to become active.	蓝色标记条目为用户直接更改，需要审定才能激活.	Blau markierte Einträge sind direkte Benutzeränderungen und benötigen eine Finalisierung um aktiv genutzt zu werden.
CHANGELOG.VIEWER.EXTROPOSTFIN	Entries marked red are changes to the item that has been made after finalization.	红色标记条目为审定后项目的更改.	Rot markierte Einträge sind Änderungen, die nach der Finalisierung am Objekt vorgenommen wurden.
CHANGELOG.VIEWER.NOENTRIES	The change log is currently empty.	更改日志目前是空的	Die Änderungsliste ist momentan leer.
CHANGELOG.BUTTON.OPENDIALOG	Change log	更改日志	Änderungsliste
CHANGELOG.BUTTON.CLOSE	Close	关闭	Schliessen
GSEARCH.WIZARD.TITLE	Global Search	全局搜索	Globale Suche
GSEARCH.WIZARD.TEXT1	Please specify the search term. The term must be at least three characters long, an asterisk ('*') is a wildcard for a maximum of 10 characters. A pipe symbol ('|') at the beginning or the end specifies, that the term must be at the beginning resp. the end of the text. Multiple terms can be specified by concatenating them with plus signs ('+'); all these terms must be found in the text. Case is ignored unless a term is prepended by an exclamation mark ('!').	 an asterisk ('*') is a wildcard for a maximum of 10 characters. A pipe symbol ('|') at the beginning or the end specifies	Bitte geben Sie den Suchbegriff ein. Der Begriff muss mindestens drei Zeichen lang sein, ein Asterisk ('*') gilt als Platzhalter, ein Pipesymbol ('|') am Anfang oder Ende des Begriffs spezifizieren, dass der Text mit dem gesuchten Begriff anfangen bzw. enden muss. Mehrere Suchbegriffe können per Plus-Zeichen ('+') zusammengefügt werden, diese müssen alle im Text vorkommen. Gross-/Kleinschreibung wird nur beachtet, wenn ein Begriff mit einem Ausrufezeichen ('!') vorangestellt wird.
GSEARCH.WIZARD.TEXT2	Please select the fields to be processed during the search.	搜索执行过程中请选择范围	Bitte wählen Sie die Felder aus, über die nach dem Begriff gesucht werden soll.
GSEARCH.WIZARD.TEXT3	Please select the languages to be processed (in multilangual fields).	请选择要执行的语言（在多个范围里）	Bitte wählen Sie die Sprachen aus, über die der Begriff gesucht werden soll (in mehrsprachigen Feldern).
GSEARCH.WIZARD.TEXT4	Please select the search mode on versionized objects:	请版本化的对象上选择搜索型号	Bitte wählen Sie, welche Einschränkung bei versionierten Objekten gelten soll:
GSEARCH.SCOPE.ALLALL.TEXT	All objects (full search)	所有对象（全面搜索）	Keine Einschränkung (Vollsuche)
GSEARCH.SCOPE.ALLALL.TTIP	All versions (including non finalized) are searched, all matching objects shown.	 all matching objects shown."	Alle Versionen (inkl. nicht finalisierter) werden durchsucht, alle gefundenen Ergebnisse angezeigt.
GSEARCH.SCOPE.ALLRECENT.TEXT	Full search, most recent found object only	 most recent found object only"	Vollsuche, nur aktuellstes Element
GSEARCH.SCOPE.ALLRECENT.TTIP	All versions (including non finalized) are searched, but only the most recent object is shown.	 but only the most recent object is shown."	Alle Versionen (inkl. nicht finalisierter) werden durchsucht, jedoch nur das aktuellste (zuletzt geänderte) Objekt angezeigt.
GSEARCH.SCOPE.FINALL.TEXT	Finalized objects	选定对象	Finalisierte Objekte
GSEARCH.SCOPE.FINALL.TTIP	All finalized versions are searched, all matching objects shown.	 all matching objects shown."	Alle finalisierten Versionen werden durchsucht, alle gefundenen Ergebnisse angezeigt.
GSEARCH.SCOPE.FINRECENT.TEXT	Finalized objects, most recent found object only	 most recent found object only"	Finalisierte Objekte, nur aktuellstes Element
GSEARCH.SCOPE.FINRECENT.TTIP	All finalized versions are searched, but only the most recent object is shown.	 but only the most recent object is shown."	Alle finalisierten Versionen werden durchsucht, jedoch nur das aktuellste (zuletzt geänderte) Objekt angezeigt.
GSEARCH.SCOPE.LATESTV.TEXT	Latest version	最新版本	Letzte Version
GSEARCH.SCOPE.LATESTV.TTIP	Only the latest version (finalized) is searched.	只搜索最新的选定的对象	Nur die jeweils letzte finalisierte Version wird durchsucht.
GSEARCH.SCOPE.EDITV.TEXT	Edit version only	只编辑版本	Editierversion
GSEARCH.SCOPE.EDITV.TTIP	Only the most recent, non finalized versions are searched.	 non finalized versions are searched."	Nur die aktuellsten, noch nicht finalisierten Versionen werden durchsucht.
GSEARCH.TYPE.CHECK.SELECT	Checks	验盘	Prüfungen
GSEARCH.TYPE.CHECK.DISPLAY	<u>Check</u> <b>{{ object.id }}</b> (<i>{{ object.checktype.title.en }}</i>) for unit <b>{{ object.unit.code}}</b>, model <b>{{ object.unit.model.code }}</b>	 model <b>{{ object.unit.model.code }}</b>"	<u>Prüfung</u> <b>{{ object.id }}</b> (<i>{{ object.checktype.title.de }}</i>) für Einheit <b>{{ object.unit.code}}</b>, Modell <b>{{ object.unit.model.code }}</b>
GSEARCH.TYPE.MEASUREMENT.SELECT	Measurements	路径	Messeintragungen
GSEARCH.TYPE.MEASUREMENT.DISPLAY	<u>Measurement</u> in check <b>{{ object.check.id }}</b> (unit {{ object.check.unit.code }}, model {{ object.check.unit.model.code }}) for measure <b>{{ object.measure.code }}</b> in step <b>{{ object.measure.step.code }}</b>, procedure <b>{{ object.measure.step.procedure.code }}</b>	 model {{ object.check.unit.model.code }}) for measure <b>{{ object.measure.code }}</b> in step <b>{{ object.measure.step.code }}</b>	<u>Messeintragung</u> in Prüfung <b>{{ object.check.id }}</b> (Einheit {{ object.check.unit.code }}, Modell {{ object.check.unit.model.code }}) für Messung <b>{{ object.measure.code }}</b> in Schritt <b>{{ object.measure.step.code }}</b>, Prozedur <b>{{ object.measure.step.procedure.code }}</b>
GSEARCH.TYPE.CHECKTYPE.SELECT	Checktypes	验盘类型	Prüfungstypen
GSEARCH.TYPE.CHECKTYPE.DISPLAY	<u>Checktype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)	<u>验盘类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)	<u>Prüfungstyp</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)
GSEARCH.TYPE.DEVICETYPE.SELECT	Devicetypes	装置类型	Gerätetypen
GSEARCH.TYPE.DEVICETYPE.DISPLAY	<u>Devicetype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)	<u>装置类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)	<u>Gerätetyp</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)
GSEARCH.TYPE.MODEL.SELECT	Models	型号	Modelle
GSEARCH.TYPE.MODEL.DISPLAY	<u>Model</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), version {{ object.version || '(edit)' }}	 version {{ object.version || '(edit)' }}"	<u>Modell</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>), Version {{ object.version || '(Edit)' }}
GSEARCH.TYPE.UNIT.SELECT	Units	单元	Einheiten
GSEARCH.TYPE.UNIT.DISPLAY	<u>Unit</u> <b>{{ object.code }}</b> in model <b>{{ object.model.code }}</b>	<u>单元</u> <b>{{ object.code }}</b> 在型号el <b>{{ object.model.code }}</b>	<u>Einheit</u> <b>{{ object.code }}</b> in Modell <b>{{ object.model.code }}</b>
GSEARCH.TYPE.MEASURE.SELECT	Measures	路径	Messungen
GSEARCH.TYPE.MEASURE.DISPLAY	<u>Measure</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) in step <b>{{ object.step.code }}</b> / procedure <b>{{ object.step.procedure.code }}</b>, version {{ object.procedure.version || '(edit)' }}	 version {{ object.procedure.version || '(edit)' }}"	<u>Messung</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>) in Schritt <b>{{ object.step.code }}</b> / Prozedur <b>{{ object.step.procedure.code }}</b>, Version {{ object.procedure.version || '(Edit)' }}
GSEARCH.TYPE.STEP.SELECT	Steps	步骤	Schritte
GSEARCH.TYPE.STEP.DISPLAY	<u>Step</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) in procedure <b>{{ object.procedure.code }}</b>, v {{ object.procedure.version || '(edit)' }}	 v {{ object.procedure.version || '(edit)' }}"	<u>Schritt</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>) in Prozedur <b>{{ object.procedure.code }}</b>, Version {{ object.procedure.version || '(Edit)' }}
GSEARCH.TYPE.PROCEDURE.SELECT	Procedures	程序	Prozeduren
GSEARCH.TYPE.PROCEDURE.DISPLAY	<u>Procedure</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), version {{ object.version || '(edit)' }}	 version {{ object.version || '(edit)' }}"	<u>Prozedur</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>), Version {{ object.version || '(Edit)' }}
GSEARCH.TYPE.TOOLTYPE.SELECT	Tooltypes	工具类型	Werkzeugtypen
GSEARCH.TYPE.TOOLTYPE.DISPLAY	<u>Tooltype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)	<u>工具类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)	<u>Werkzeugtyp</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)
GSEARCH.TYPE.TOOLUNIT.SELECT	Toolunits	工具单元	Werkzeugeinheiten
GSEARCH.TYPE.TOOLUNIT.DISPLAY	<u>Toolunit</u> <b>{{ object.code }}</b> in tooltype <b>{{ object.tooltype.code }}</b>	<u>工具单元</u> <b>{{ object.code }}</b> 在工具类型 <b>{{ object.tooltype.code }}</b>	<u>Werkzeugeinheit</u> <b>{{ object.code }}</b> in Werkzeugtyp <b>{{ object.tooltype.code }}</b>
GSEARCH.TYPE.USER.SELECT	Users	用户	Benutzer
GSEARCH.TYPE.USER.DISPLAY	<u>User</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)	<u>用户</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)	<u>Benutzer</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)
GSEARCH.TYPE.NOTICE.SELECT	Problem report	问题报告	Fehlerbericht
GSEARCH.TYPE.NOTICE.DISPLAY	<u>Problem report</u> <b>{{ object.id }}</b>	<u>问题报告</u> <b>{{ object.id }}</b>	<u>Fehlerbericht</u> <b>{{ object.id }}</b>
GSEARCH.FIELD.COMMENT	Comment	注释	Kommentar
GSEARCH.FIELD.CODE	Code	编码	Code
GSEARCH.FIELD.TITLE	Title	名称	Titel
GSEARCH.FIELD.DESCRIPTION	Description/Hints	描述/线索	Beschreibung/Hinweis
GSEARCH.FIELD.CUSTOMER	Customer	客户	Kunde
GSEARCH.FIELD.USERNAME	Username	用户名	Benutzername
GSEARCH.FIELD.REALNAME	Realname	真实姓名	Echter Name
GSEARCH.FIELD.TEXT	Description	描述	Beschreibung
GSEARCH.BUTTONS.NEWSEARCH	New Search	新搜索	Neue Suche
GSEARCH.BUTTONS.CLOSE	Close	关闭	Schliessen
GSEARCH.RESULT.TITLE	Search results	搜索结果	Suchergebnis
GSEARCH.RESULT.TEXT	The last search resulted in the following matches, sorted by date of change:	 sorted by date of change:"	Die letzte Suche ergab folgende Ergebnisse, nach Änderungsdatum sortiert:
GSEARCH.RESULT.TOOMUCH	(Note: Only the first 50 matches are displayed)	（备注：只显示前50个符合条件的对象）	(Hinweis: Es werden nur die ersten 50 Treffer angezeigt)
GSEARCH.RESULT.NOMATCH	There are no matches	没有符合条件的对象	Es gab keine Treffer
NOTICES.TOOLTIP.SHOW	Show	显示	Anzeigen
NOTICES.TOOLTIP.SHOWALL	Show all	显示所有	Alle anzeigen
NOTICES.TOOLTIP.HIDE	Hide	隐藏	Ausblenden
NOTICES.TOOLTIP.FILTER	Filter this column	过滤该栏	Nach diesem Wert filtern
NOTICES.TOOLTIP.NOFILTER	Disable filter	禁用过滤	Filter ausschalten
NOTICES.EXPORT.TTL	Export problem reports	发送问题报告	Fehlerberichte exportieren
NOTICES.EXPORT.TYPETEXT	Please select the file type for export:	请选择发送报告的文件类型	Bitte wählen Sie den Dateityp, in dem die Liste exportiert werden soll:
NOTICES.EXPORT.FILTERTEXT	Please select the scope:	请选择范围：	Bitte geben Sie den Umfang der Liste an:
NOTICES.EXPORT.CSV	Comma Separated Values (CSV)	逗号分隔值	Comma Separated Values (CSV)
NOTICES.EXPORT.JSON	JavaScript Output Notation (JSON)	基于JavaScript语言的轻量级的数据交换格式	JavaScript Output Notation (JSON)
NOTICES.EXPORT.XML	Extensible Markup Language (XML)	 可扩展标示语言	Extensible Markup Language (XML)
NOTICES.EXPORT.UNARCHIVED	All non archived reports	所有未存档的报告	Alle nicht archivierten Berichte
NOTICES.EXPORT.ALL	All (including archived) reports	所有报告（包括存档报告）	Alle (auch archivierte) Berichte
NOTICES.EXPORT.FILTERED	All shown (filtered) reports	所有显示的（波滤过的）报告	Alle in der gefilterten Liste enthaltenen Berichte
NOTICES.FILTEREDIT.TITLE	Please define the filter	请定义波滤器	Bitte den Filter definieren
NOTICES.FILTEREDIT.ID	Please select the range of IDs to be shown. You can use '<from>-<to>', '-<to>' or '<from>-'. Example: '10-20' (all IDs from 10 to 20); '-50' (all IDs up to 50).	 '-<to>' or '<from>-'. Example: '10-20' (all IDs from 10 to 20); '-50' (all IDs up to 50)."	Bitte geben Sie die Reichweite der IDs an, die angezeigt werden sollen. Sie können dabei '<von>-<bis>', '-<bis>' oder '<von>-' angeben. Beispiel: '10-20' (alle IDs von 10 bis 20); '-50' (alle IDs bis 50).
NOTICES.FILTEREDIT.CATEGORY	Please select the categories to be shown:	请选择要显示的类别	Bitte wählen Sie die Kategorien aus, die angezeigt werden sollen:
NOTICES.FILTEREDIT.PATH	Please enter a search term; this will be matched against the displayed path text:	请输入一个搜索名称；该名称将和展示的路径文字相符	Bitte geben Sie einen Suchbegriff ein; dieser wird gegen den angezeigten Pfadtext geprüft:
NOTICES.FILTEREDIT.TEXT	Please enter a search term; this will be matched against the description:	请输入一个搜索名称；该名称将和描述相一致	Bitte geben Sie einen Suchbegriff ein; dieser wird gegen die Beschreibung geprüft:
NOTICES.FILTEREDIT.ARTICLE	Please enter a search term; this will be matched against the article number (not: article description). If you want to match against the article description, prefix the term with a colon ':'.	 prefix the term with a colon ':'."	Bitte geben Sie einen Suchbegriff ein; dieser wird gegen die Artikelnummer (nicht: Artikelbezeichnung) geprüft. Soll statt dessen gegen die Artikelbezeichnung geprüft werden, leiten Sie bitte den Suchbegriff mit einem Doppelpunkt ':' ein:
NOTICES.FILTEREDIT.TIMELOSS	Please select the time loss values to be shown:	请选择要显示的时间损失值	Bitte wählen Sie die Angaben zum Zeitverlust aus, die in der Liste angezeigt werden sollen:
NOTICES.FILTEREDIT.STATUS	Please select the statu types values to be shown:	请选择要显示的状态类型值	Bitte wählen Sie die Stati aus, die in der Liste angezeigt werden sollen:
NOTICES.TITLE.PROBLREP	Problem report	问题报告	Fehlerbericht
NOTICES.TITLE.DESC	Description	描述	Beschreibung
NOTICES.TITLE.PROPOSALS	Proposals:	建议：	Vorschläge:
NOTICES.TITLE.CAT	Category	类别	Kategorie
NOTICES.TITLE.TIMELOSS	Time loss (ca.)	时间消耗	Zeitverlust (ca.)
NOTICES.TITLE.ARTNO	Article no.	文件编号	Artikelnr.
NOTICES.TITLE.ARTDESC	Article desc.	文件描述	Bezeichnung
NOTICES.TITLE.PROBLREPNO	Problem report no.	问题报告编号	Fehlerbericht nr.
NOTICES.TEXT.DESC	Please enter a short and meaningful description. You can choose from a small selection of proposals below the text box or enter your own text.	请输入一个简短有效的描述。可从以下文字框中的几项建议选择或者自己输入文本。	Bitte geben Sie zu dem Fehlerbericht eine kurze und aussagekräftige Beschreibung; Sie können dazu, falls passend, aus einigen Vorschlägen auswählen oder einen eigenen Text eingeben.
NOTICES.TEXT.CHOOSECAT	Please select one of the given categories matching the problem report best. If you have lost time due to the problem, please select the best matching value in the time loss select box.	 please select the best matching value in the time loss select box."	Bitte wählen Sie aus den gegebenen Kategorien eine aus, die auf den Fehlerbericht passt. Falls Sie durch den Fehler beträchtliche Zeit verloren haben, geben Sie dies durch Auswahl einer passenden Zeit an.
NOTICES.TEXT.ARTICLE	If the problem report is related to a certain article you can enter this article by number and description here:	如果该问题报告和某个文件相关，你可在此处输入文件编号和文件描述。	Falls der Fehler einen bestimmten Artikel betrifft oder damit im Zusammenhang steht, können Sie hier den Artikel mit Nummer und Bezeichnung angeben.
NOTICES.TEXT.STTCHANGE	Please enter a short comment regarding the status change:	关于状态更改请输入一个简短注释	Bitte geben Sie einen kurzen Kommentar zu der Statusänderung ein:
NOTICES.BUTTON.USE	Use	使用	Übernehmen
NOTICES.BUTTON.CANCEL	Cancel	取消	Abbrechen
NOTICES.BUTTON.SEND	Send	发送	Senden
NOTICES.BUTTON.CATEGORIES	Edit categories	编辑类别	Kategorien editieren
NOTICES.BUTTON.TEMPLATES	Edit text proposals	编辑文本建议	Textvorschläge editieren
NOTICES.BUTTON.EXPORT	Export	发送	Liste exportieren
NOTICES.BUTTON.STT_12	Process report	处理报告	Bearbeitung starten
NOTICES.BUTTON.STT_21	Cancel processing	取消处理	Bearbeitung abbrechen
NOTICES.BUTTON.STT_25	Finish processing	完成处理	Bearbeitung beenden
NOTICES.BUTTON.STT_52	Restart processing	重新处理	Bearbeitung wieder aufnehmen
NOTICES.BUTTON.STT_59	Archive	存档	Archivieren
NOTICES.BUTTON.CLOSE	Close	关闭	Schliessen
NOTICES.VIEW.LOCATION	Path	路径	Pfad
NOTICES.VIEW.CATEGORY	Category	类别	Kategorie
NOTICES.VIEW.ARTICLE	Article	文件	Artikel
NOTICES.VIEW.TIMELOSS	Time loss	时间损耗	Zeitverlust
NOTICES.VIEW.NOTEXT	N/A	无效	Keine Angabe
NOTICES.VIEW.DESC	Description	描述	Beschreibung
NOTICES.VIEW.ID	No.	编号	Nummer
NOTICES.VIEW.PATH	Path	路径	Pfad
NOTICES.VIEW.TEXT	Description	描述	Beschreibung
NOTICES.VIEW.STATUS	Status	状态	Status
NOTICES.TIMELOSS.15	approx. 15 minutes	大概15分钟	ca. 15 Minuten
NOTICES.TIMELOSS.30	approx. 30 minutes	大概30分钟	ca. 30 Minuten
NOTICES.TIMELOSS.60	approx. 1 hour	大概1个小时	ca. 1 Stunde
NOTICES.TIMELOSS.90	approx. 1½ hour	大概1个多小时	ca. 1½ Stunden
NOTICES.TIMELOSS.120	approx. 2 hours	大概2个小时	ca. 2 Stunden
NOTICES.TIMELOSS.180	approx. 3 hours	大概3个小时	ca. 3 Stunden
NOTICES.TIMELOSS.240	more than 4 hours	超过4个小时	mehr als 4 Stunden
NOTICES.ALERT.CATMISS.TITLE	Missing category	类别不存在	Kategorie fehlt
NOTICES.ALERT.CATMISS.TEXT	You have to select on of the given categories!	你必须在给定类别里选择	Es muss eine der verfügbaren Kategorien ausgewählt werden!
NOTICES.ALERT.DESCMISS.TITLE	Description missing	描述不存在	Beschreibung fehlt
NOTICES.ALERT.DESCMISS.TEXT	The description is missing or too short!	描述不存在或太短！	Die Beschreibung fehlt oder ist zu kurz!
NOTICES.ALERT.THANKS.TITLE	Thank you!	谢谢！	Vielen Dank!
NOTICES.ALERT.THANKS.TEXT	The problem report was sent and will be processed shortly!	问题报告将会尽快发送并处理！	Der Fehlerbericht wurde übertragen und wird in Kürze bearbeitet!
NOTICES.ALERT.CONFDEL.TITLE	Really delete?	确定删除？	Wirklich löschen?
NOTICES.ALERT.CONFDEL.TEXT1	Really delete this entry? The change can not be undone, earlier use of this entry may be modified or corrupted.	 earlier use of this entry may be modified or corrupted."	Den Eintrag wirklich löschen? Die Änderung ist unwiderruflich, frühere Verwendungen dieses Eintrages werden möglicherweise modifiziert.
NOTICES.ALERT.CONFDEL.TEXT2	Really delete this entry? The entry will be removed from this list and may not be used in the future. Earlier uses of this entry remain valid, though.	 though."	Den Eintrag wirklich löschen? Der Eintrag wird aus der Liste entfernt und steht nicht mehr für zukünftige Nutzung zur Verfügung; frühere Nutzungen bleiben jedoch unverändert.
NOTICES.MODAL.EDITTEXT.TITLE	New entry	新进入点	Neuer Eintrag
NOTICES.MODAL.EDITTEXT.TEXT	Please edit the text for this entry:	请编辑新进入点的文本	Bitte hier den Text für den Eintrag editieren:
NOTICES.MODAL.NEWTEXT.TITLE	Edit entry	编辑进入点	Eintrag editieren
NOTICES.MODAL.NEWTEXT.TEXT	Please enter the text for the new entry (at least 3 characters):	请为新进入点输入文本（至少3个字符）	Bitte hier den Text für den Eintrag eingeben (mindestens drei Zeichen):
NOTICES.MODAL.SNIPPETCAT.TITLE	Problem report categories	问题报告类别	Fehlerberichtkategorien
NOTICES.MODAL.SNIPPETCAT.SNIPPET	Category	类别	Kategorie
NOTICES.MODAL.SNIPPETDESC.TITLE	Text proposals for problem reports	问题报告的文本建议	Textvorschläge für Fehlerbericht
NOTICES.MODAL.SNIPPETDESC.SNIPPET	Text proposal	文本建议	Textvorschlag
NOTICES.STATUS.1	Submitted	提交	Eingereicht
NOTICES.STATUS.2	Processed	执行	Bearbeitet
NOTICES.STATUS.5	Closed	关闭	Geschlossen
NOTICES.STATUS.OPEN	<b>submitted</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}	<b>已提交</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}	<b>eingereicht</b> von <b>{{ user }}</b> ({{ realname }}) am {{ time }}
NOTICES.STATUS.PROCESSED	<b>processed</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}	<b>已执行</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}	<b>bearbeitet</b> durch <b>{{ user }}</b> ({{ realname }}) ab {{ time }}
NOTICES.STATUS.CLOSED	<b>closed</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}	<b>已关闭</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}	<b>geschlossen</b> von <b>{{ user }}</b> ({{ realname }}) am {{ time }}
NOTICES.STATUS.ARCHIVED	<b>archived</b> on {{ time }}	<b>已存档</b> on {{ time }}	<b>archiviert</b> am {{ time }}
NOTICES.PATHTYPE.CHECKSTEP	Check step	检查步骤	Prüfungsschritt
NOTICES.PATHTYPE.CHECKGENERAL	Check (general)	一般检查	Prüfung (allgemein)
NOTICES.SEGMENT.MODEL	Model <b>{{ code }}</b>	型号<b>{{ code }}</b>	Modell <b>{{ code }}</b>
NOTICES.SEGMENT.UNIT	Unit <b>{{ code }}</b>	单元 <b>{{ code }}</b>	Einheit <b>{{ code }}</b>
NOTICES.SEGMENT.CHECK	Check <b>{{ id }}</b>	检查<b>{{ id }}</b>	Prüfung <b>{{ id }}</b>
NOTICES.SEGMENT.PROCEDURE	Procedure <b>{{ code }}</b>	程序 <b>{{ code }}</b>: {{ title.en }}	Prozedur <b>{{ code }}</b>
NOTICES.SEGMENT.STEP	Step <b>{{ code }}</b>	步骤 <b>{{ code }}</b>	Schritt <b>{{ code }}</b>
NOTICES.CORRECT.TITLE	Correction	更正	Korrektur
NOTICES.CORRECT.DESCRIPTION	Please edit the description:	请编辑描述	Bitte editieren Sie die Beschreibung:
NOTICES.CORRECT.TIMELOSS	Please select a new value for time loss:	请给时间损耗选择一个时间	Bitte wählen Sie einen neuen Wert für den Zeitverlust:
NOTICES.CORRECT.CATEGORY	Please choose a new category:	请选择一个新类别	Bitte wählen Sie eine neue Kategorie:
NOTICES.CORRECT.ARTICLE1	Step 1/2: Please edit the article number:	步骤 1/2：请编辑文件编号：	Bitte editieren Sie die Artikelnummer (Artikelbezeichnung folgt im nächsten Schritt):
NOTICES.CORRECT.ARTICLE2	Step 2/2: Please edit the article description:	步骤 2/2：请编辑文件描述	Bitte editieren Sie die Artikelbezeichnung:
SNIPPET.TITLE.ADD	add	添加	hinzufügen
SNIPPET.TOOLTIP.LOCK	Closes the entry. Earlier uses of this entry are still valid, however the entry may not be chosen any longer.	 however the entry may not be chosen any longer."	Schliesst den Eintrag. Frühere Verwendungen bleiben erhalten, es kann jedoch nicht weiter ausgewählt werden.
SNIPPET.TOOLTIP.UNLOCK	Reopens the entry; it may be chosen again.	重新打开进入点，该进入点可能又被关闭	Eröffnet den Eintrag; er kann danach wieder ausgewählt werden.
SNIPPET.TOOLTIP.DELETE	Deletes the entry after confirmation for good.	确定后删除进入点	Löscht den Eintrag nach Rückfrage unwiderruflich.
SNIPPET.TOOLTIP.EDIT	Edit the text of the entry.	编辑进入点的文本。	Der Text des Eintrages kann hiermit geändert werden.
SNIPPET.TOOLTIP.REORD	The entry may be moved to another position per drag & drop	该进入点可能在每次拖放的过程中移动到其他位置。	Den Eintrag kann hiermit per Drag&Drop an anderer Stelle einfügt werden
SNIPPET.BUTTON.CLOSE	Close	关闭	Schliessen
