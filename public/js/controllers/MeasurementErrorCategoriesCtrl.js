cmod.controller('MeasurementErrorCategoriesCtrl', function ($scope, $modalInstance, Restangular, AlertService, ModalEditorService, ListManagementService) {

  var ctrl = this;

  ListManagementService.installReorderableSublistManagement($scope, 'measurementerrorcategories');


  activate();

  function activate() {
    loadData();
  }

  ctrl.addCategory = function() {
    ModalEditorService.showMeasurementErrorCategoriesDetailEditor("MEASUREMENTERRORCATEGORIES.ADD", "", "", []).result.then(function (result) {
      // Calling route MISC_29
      Restangular.all('measurementerrorcategories').post({name: result.value}).then(function () {
        loadData();
      }, AlertService.showRESTError);
    });
  };

  ctrl.close = function() {
    $modalInstance.close();
  };

  ctrl.editCategory = function(category) {
    ModalEditorService.showMeasurementErrorCategoriesDetailEditor("MEASUREMENTERRORCATEGORIES.EDIT", "", category.name, null).result.then(function (result) {
      // Calling route MISC_25
      category.name = result.value;
      Restangular.one('measurementerrorcategories').customPUT(category).then(function () {
        loadData();
      }, AlertService.showRESTError);
    });
  };

  ctrl.removeCategory = function(category) {
    AlertService.createConfirmMessage("SETTINGS.ALERT.CONFDEL.TITLE", "SETTINGS.ALERT.CONFDEL.TEXT", null).show().result.then(function () {
      // Calling route MISC_24
      category.deleted = true;
      Restangular.one('measurementerrorcategories').customPUT(category).then(function () {
        loadData();
      }, AlertService.showRESTError);
    })
  };

  function loadData() {
    // Calling route MISC_28
    Restangular.all('measurementerrorcategories').getList().then(function (data) {
      ctrl.items = data;
      ListManagementService.prepareSublist($scope, data);
    }, AlertService.showSevereRESTError);
  }

});