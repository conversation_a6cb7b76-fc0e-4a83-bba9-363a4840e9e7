cmod.controller('PdfViewerCtrl', function ($modalInstance, $rootScope, $scope, $window, medium) {

  $scope.setupConfig = setupConfig;
  $scope.downloadPdf = downloadPdf;
  $scope.close = close;

  $scope.setupConfig();


  //////////


  function setupConfig() {
    $scope.title = medium.caption;
    if ($scope.title === null || $scope.title === "") {
      $scope.title = medium.binaryfile.original_filename;
    }
    $scope.pdfurl = "/media/" + medium.binaryfile.filename;
  }

  function close() {
    $modalInstance.close();
  }

  function downloadPdf() {
    $window.open("/servlet/mediadownload/doc/" + medium.id);
  }

});