cmod.controller('CheckFilterViewModalCtrl', function ($modal, $modalInstance, $scope, listFilter, checkStatus) {

  var ctrl = this;

  ctrl.checkStatus = checkStatus;
  ctrl.listFilter = angular.copy(listFilter);

  // set booleans for numeric values in order to use them as checkboxes/radio buttons
  ctrl.filterStatus = {
    unfin: ctrl.listFilter.filterStatus === 3,
    success: ctrl.listFilter.filterStatus === 1,
    fail: ctrl.listFilter.filterStatus === 2
  };
  ctrl.focusUser = ctrl.listFilter.focusUser === -1;

  // toggle function for checkbox in order to offer multi selection and simultaneously to make sure
  // at least one checkbox is checked
  ctrl.toggleCheckbox = function (key) {
    ctrl.listFilter[key] = !ctrl.listFilter[key];

    if (key === "useOnCodes" || key === "useOnTitle") {
      if (!ctrl.listFilter.useOnCodes && !ctrl.listFilter.useOnTitle) {
        ctrl.listFilter.useOnCodes = true;
      }
    } else if (key === "applyProcedures" || key === "applySteps" || key === "applyMeasures") {
      if (!ctrl.listFilter.applyProcedures && !ctrl.listFilter.applySteps && !ctrl.listFilter.applyMeasures) {
        ctrl.listFilter.applyProcedures = true;
      }
    } else {
      console.error("No suiting key for checkbox provided!");
    }
  };

  // toggle radio button function to offer single or no selection
  ctrl.toggleRadioBtn = function (type) {
    ctrl.filterStatus[type] = !ctrl.filterStatus[type];
    _.forOwn(ctrl.filterStatus, function (value, key) {
      if (!(type === key)) {
        ctrl.filterStatus[key] = false;
      }
    })
  };

  ctrl.enableFilter = function () {
    ctrl.listFilter.enableFilter = true;
    ctrl.close(false);
  };

  ctrl.disableFilter = function () {
    ctrl.listFilter.enableFilter = false;
    ctrl.close(false);
  };

  $scope.$on("$destroy", function () {
    $modal.closeall();
  });

  ctrl.close = function (cancel) {
    if (cancel) {
      $modalInstance.close(null);
    } else {

      // make sure that numeric values are set back properly
      if (ctrl.filterStatus.unfin) {
        ctrl.listFilter.filterStatus = 3;
      } else if (ctrl.filterStatus.success) {
        ctrl.listFilter.filterStatus = 1;
      } else if (ctrl.filterStatus.fail) {
        ctrl.listFilter.filterStatus = 2;
      } else {
        ctrl.listFilter.filterStatus = null;
      }
      ctrl.listFilter.focusUser = ctrl.focusUser === true ? -1 : null;

      // disable filter if no actual filter value has been selected
      if ((!ctrl.listFilter.filterText || ctrl.listFilter.filterText === '') && !ctrl.listFilter.filterStatus && !ctrl.focusUser && !ctrl.listFilter.hideInstructionSteps && !ctrl.listFilter.hideOmittedOrSkipped) {
        ctrl.listFilter.enableFilter = false;
      }

      if (ctrl.listFilter.filterText) {
        ctrl.listFilter.filterText = ctrl.listFilter.filterText.toLowerCase();
      }
      $modalInstance.close(ctrl.listFilter);
    }
  };
});