cmod.controller('AlertModalCtrl', function ($scope, $modalInstance, type, head, intro, reasons, confirmationInput) {
  $scope.type = type;
  $scope.head = head;
  $scope.reasons = reasons;
  $scope.intro = intro;
  $scope.confirmationInput = confirmationInput;

  $scope.ok = function () {

    // type 5 = confirmation by input
    if ($scope.type === 5) {
      $modalInstance.close($scope.confirmationInput);
    } else {
      $modalInstance.close();
    }
  };

  $scope.cancel = function () {
    $modalInstance.dismiss();
  };

  $scope.gettypestr = function () {
    if ($scope.type === 1 || $scope.type === 5) {
      return "primary";
    }
    else if ($scope.type == 2) {
      return "danger";
    }
    else if ($scope.type == 3) {
      return "warning";
    }
    else if ($scope.type == 4) {
      return "success";
    }
    else {
      return "primary";
    }
  };
});
