fmod.filter('dashboarditemfilter', function ($translate) {
  return function (items, quickfilter) {
    var filtered = [];

    if (items != null) {
      for (var i = 0; i < items.length; i++) {
        var item = items[i];

        if (quickfilter && quickfilter !== "") {
          if (item.unit && item.unit.code && !(_.includes(item.unit.code.toLowerCase(), quickfilter))) {
            continue;
          }
        }
        if (item.code != null) {
          var code = item.code;
          if (!(code.slice(-2) == "()" || code.slice(-4) == "(TR)")) {
            filtered.push(item);
          }
        } else {
          filtered.push(item);
        }
      }
    }
    return filtered;
  };
});
