{"FRAME": {"TITLE": "LeanLogic QA", "DASHBOARD": "Tableau de bord  ", "DASHBOARDS": "Tableaux de bord", "MANAGE": {"TITLE": "Gestion", "MODEL": "<PERSON><PERSON><PERSON><PERSON>", "UNIT": "Unités  ", "PROCEDURE": "Procédures", "TOOL": "Outils  ", "DEVICETYPE": "Types de matériel", "CHECKTYPE": "Types de contrôle", "USERS": "Utilisateurs", "CHANGEPW": "Changer le mot de passe", "NOTICES": "Rapports d'erreur", "CONFIGTABLE": "Tables de configuration", "SETTINGS": "Réglages", "MEASUREMENTERRORCATEGORIES": "Measurement error categories", "MEASUREMENTERRORCATEGORIESREPORT": "Report Measurement errors"}, "LOGOUT": "Logout", "EDITDTYPE": {"TITLE": "@:FRAME.MANAGE.DEVICETYPE", "TEXT": "A<PERSON><PERSON>, modifier ou désactiver les types de matériel:"}, "EDITCTYPE": {"TITLE": "@:FRAME.MANAGE.CHECKTYPE", "TEXT": "<PERSON><PERSON><PERSON>, modifier ou éteindre les types de contrôle:"}, "CHPWD": {"TITLE": "@:FRAME.MANAGE.CHANGEPW", "PW1": "Veuillez rentrer le nouveau mot de passe:", "PW2": "Veuillez répéter pour vérification:", "NOMATCH": "Les mots de passe ne correspondent pas; L'opération est annulée!", "TOOSHORT": "Le mot de passe est trop court; trois caractères au minimum!", "OK": "Le mot de passe a été modifié."}, "GSEARCH": "Recherche"}, "DASHBOARD": {"TOOLTIP": {"PREVDB": "Tableau de bord précédent (en appuyant sur la touche Shift le tableau de bord actuel est échangé avec le précédent dans la séquence)", "NEXTDB": "Tableau de bord suivant (en appuyant sur la touche Shift le tableau de bord actuel est échangé avec le prochain dans la séquence)", "DELDB": "Enlever tableau de bord", "EDITDB": "Modifier titre du tableau de bord", "ADDDB": "Nouveau tableau de bord"}, "NEWBLOCK": {"TITLE": "Nouveau bloc", "TEXT": "Définir le contenu du bloc:"}, "WELCOME": "Bienvenue, {{ name }}!", "VERSION": "Version {{ version }}", "ADDDASH": {"BUTTON": "Nouveau bloc"}, "ADDDB": {"TITLE": "Ajouter nouveau tableau de bord", "TEXT": "Veuillez rentrer un titre pour le nouveau tableau de bord. Le titre doit contenir au moins 3 caractères."}, "EDITDBNAME": {"TITLE": "Modifier titre du tableau de bord", "TEXT": "Veuillez rentrer un titre pour le nouveau tableau de bord. Le titre doit contenir au moins 3 caractères."}, "EDITBLKNAME": {"TITLE": "Modifier titre de bloc", "TEXT": "Veuillez rentrer un nouveau titre pour le bloc.  Le titre doit contenir au moins 3 caractères."}, "DELETEDB": {"TITLE": "E<PERSON>-vous sûr?", "TEXT": "Vraiment enlever ce tableau de bord?"}, "FILTERS": {"TITLE": "Editer filtre de bloc", "TEXT": "Choisissez une action:", "ACTENABLE": "Utiliser:", "ACTEDIT": "Editer:", "ACTDISABLE": "Eteindre:", "TYPES": {"MODELSEL": {"NAME": "Filtre sélection de modèle", "TEXT": "Veuillez sélectionner les modèles qui sont considérés dans ce bloc:"}}}, "TYPE": {"UCAM": {"TITLE": "Contrôles non terminés et alloués à moi", "LINET": "Unité <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>, à vérifier à partir de <b>{{ scheduled }}</b>, compléter jusqu'à <b>{{ dueby }}</b>", "LINEBNS": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>"}, "UCMP": {"TITLE": "Contrôles non terminés que je peux éditer", "LINET": "Unité <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>, à vérifier à partir de <b>{{ scheduled }}</b>, compléter jusqu'à <b>{{ dueby }}</b>", "LINEBNS": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>"}, "FCFR": {"TITLE": "Contrôles terminés pour consultation", "LINET": "Unité <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>, à vérifier à partir de <b>{{ scheduled }}</b>, compléter jusqu'à <b>{{ dueby }}</b>", "LINEBNS": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>"}, "UWAC": {"TITLE": "Units without any set up checks", "LINET": "Unité <b>{{ code }}</b> ({{ customer }})", "LINEB": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.en }}</b>"}, "CPCH": {"TITLE": "Contrôles en cours de traitement", "LINET": "Unité <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>, à vérifier à partir de <b>{{ scheduled }}</b>, compléter jusqu'à <b>{{ dueby }}</b>", "LINEBNS": "<PERSON><PERSON><PERSON><PERSON> <b>{{ model.code }}: {{ model.title.de }}</b>"}, "PMPC": {"TITLE": "Procédures et modèles avec modifications", "LINEPROC": "Procédure <b>{{ code }}</b>: {{ title.de }}", "LINEMOD": "<PERSON><PERSON><PERSON><PERSON> <b>{{ code }}</b>: {{ title.de }}"}}, "TYPEF": {"TITLE": "Procedures and models with pending changes", "LINEPROC": "Procedure <b>{{ code }}</b>: {{ title.en }}"}}, "CHECK": {"INPUT": {"TITLE": {"EDIT": "Modifie  {{mname_de}}"}}, "OVERVIEW": {"ACTION": {"SCHEDULE": {"TITLE": "Planification du contrôle", "TEXTSCHED": "Choisissez l'heure de lancement le plus tôt", "TEXTDUE": "Choisissez jusqu'à ce que le contrôle doit être terminé", "TEXTASSIGN": "Sélectionnez le mode d'attribution", "TEXTASSIGNTO": "Attribuer le contrôle au groupe ou utilisateur suivant", "TEXTADDASSIGN": "Sélectionnez un groupe ou un utilisateur comme cible d'attribution"}, "CHANGEASS": {"TITLE": "Modifier l'attribution", "TEXT": "Veuillez choisir une nouvelle attribution pour ce bloc:"}}}, "ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "CANCEL": "Please confirm by typing in the check number.", "WRONGNUMBER": "Wrong number", "WRONGDELETEINPUT": "Your input does not match the number. The check has not been cancelled.", "DELETEASS": {"TITLE": "R<PERSON><PERSON><PERSON> effacer l'objectif d'attribution {{ name }}?", "TEXT": "Toutes attributions à cette objectif seront réinitialisées"}, "DELETE": {"TITLE": "Please confirm by typing in the check number.", "WRONGDELETEINPUT": "Your input does not match the number. The check has not been deleted.", "TEXT": "@:UNIT.ALERT.DELETECHECK.TEXT"}, "SCHED": {"ASSFREE": "Attribution libre", "ASSFULL": "Tout contrôle à attribuer préalablement", "ASSDETAILED": "Attribution dé<PERSON><PERSON>e", "ASSPREALLOCFREE": "Pré-allocation, sinon attribution libre", "ASSPREALLOCDETAILED": "Pré-allocation, sinon attribution dé<PERSON><PERSON>e"}, "SCHEDTT": {"ASSFREE": "Le contrôle est disponible pour tous les groupes ou utilitsateurs", "ASSFULL": "Le contrôle sera attribué complètement à un groupe ou utilisateur", "ASSDETAILED": "Des parties individuelles peuvent être attribuées aux utilisateurs/groupes responsables", "ASSPREALLOCFREE": "Après la pré-allocation les autres parties du contrôle sont disponible pour tous les groupes ou utilisateurs", "ASSPREALLOCDETAILED": "Après la pré-allocation les autres parties du contrôle ne sont pas attribuées et doivent être modifiées manuellement"}, "REASSIGN": {"TITLE": "Vraiment interrompre le processus et réaffecter les sections de contrôle?", "TEXT": "Les utilisateurs actuellement affectés ne peuvent pas continuer à travailler sur la vérification alors que celle-ci est arrêtée!"}, "REGISTER": {"TITLE": "Enregistrer", "TEXT": "Veuillez sélectionner l'attribution pour laquelle vous aimeriez vous enregistrer:"}, "UNREGISTER": {"TITLE": "Déconnecter", "TEXT": "Veuillez sélectionner les attributions auxquelles vous aimeriez vous déconnecter:"}, "STEPINPROC": "{{ nums }} Étape(s)/{{ nump }} Procédure(s)", "REOPEN": {"TITLE": "Réouverture", "TEXT": "Veuillez sélectionner les attributions qui devront être réexaminées après la réouverture:"}, "COPY": {"TITLE": "Établir copie", "TEXT": "Veuillez sélectionner les procédures dont les résultats de mesure doivent être repris dans la copie:", "NEWVERS": {"TITLE": "Mettre à jour?", "TEXT": "Il existe des versions plus nouvelles de ce contrôle; En option, vous pouvez mettre à jour la copie. V<PERSON><PERSON><PERSON> noter que seules des procédures non changées peuvent être copiées (indépendamment de la sélection dans la boîte de dialogue précédente) et lors des modifications ultérieures dans les commandes séquentielles des parties de la copie de vérfication peuvent avoir un comportement incorrect.", "NOCHANGE": "Conserver la version actuelle"}}, "SELPROC": {"TITLE": "<PERSON><PERSON> de procé<PERSON>", "TEXTSING": "Veuillez sélectionner la procédure du bloc pour laquelle vous aimeriez vous enregistrer:", "TEXTMULT": "Veuillez sélectionner les procédures du bloc pour lesquelles vous aimeriez vous enregistrer:"}, "REGMODE": {"TITLE": "Mode d'enregistrement", "TEXT": "Veuillez sélectionner le type d'auto-enregistrement:", "COMPLETE": "Que procédure d'enregistrement complète", "MAYPART": "Procédure d'enregistrement partielle possible", "MUSTPART": "Procédure d'enregistrement que pour procédures individuelles"}, "REGMODETT": {"COMPLETE": "Procédure d'enregistrement que pour le bloc complet possible", "MAYPART": "Lors de la procédure d'enregistrement des procédures d'un bloc peuvent être choisies individuellement (si plusieurs procédures sont disponibles dans un bloc)", "MUSTPART": "Lors de la procédure d'enregistrement seule une procédure sera séléctionnée et attribuée (si plusieurs procédures sont disponibles dans un bloc)"}, "TAKEOVER": {"INFO": "Reprise", "CONFIRM": "Voulez-vous réellement reprendre ce bloc de l'ancien gestionnaire?"}}, "MODIFY": {"COMMENT": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "<PERSON><PERSON><PERSON>, enlever ou éditer des commentaires pour ce contrôle"}}, "MAKEPDF": {"TTL": "Créer un rapport de contrôle en PDF", "TEXT": "<PERSON><PERSON><PERSON>z sélectionner tous les éléments facultatifs qui doivent apparaître dans le PDF:", "OPTV": "Versions de procédure", "OPTADM": "Attributions et autres informations spécifiques à l'utilisateur", "OPTCOMM": "Commentaires sur les mesures", "OPTRAW": "Valeurs brutes des calculs de matrice", "OPTTOOL": "Outils utilisés pour les mesures", "OPTRULE": "<PERSON><PERSON><PERSON> de mesure", "TEXT2": "Veuillez sélectionner le type de rapport:", "CUSTOMER": {"TEXT": "Rapport de clientèle", "TTIP": "Omettre les mesures marquées 'interne'; résultat de texte alternatif."}, "INTERNAL": {"TEXT": "Rapport interne", "TTIP": "Ins<PERSON><PERSON> toutes les mesures."}}, "MAKEFILE": {"TTL": "Exporter le rapport de données", "TEXT": "Veuillez spécifier le type de données d'exportation pour ce rapport:", "CSV": "Comma Separated Values (CSV)", "JSON": "JavaScript Output Notation (JSON)", "XML": "Extensible Markup Language (XML)"}, "SELTOOL": {"TITLE": "Sélectionnez l'outil", "MESSAGE": "Sélectionnez l'outil qui est utilisé pour cette étape", "NOTOOLAVAILABLE": "There are no tools with active units available!"}}, "MEASUREMENT": {"INPUT": {"COMMENT": {"TITLE": "Éditer commentaire", "TEXT": "<PERSON><PERSON><PERSON>, effacer ou editer commentaire pour cette untié"}}}, "MSRSTAT": {"SELMSR": {"TTL": "@:MSRSTAT.SELM.TTL", "TEXT": "Veuillez sélectionner les mesures qui doivent figurer dans le rapport:", "RUTEXT": "Veuillez entrer la chaîne identifiant que vous avez copié à partir d'un précédent rapport (dernière page du PDF):"}, "SELCHK": {"TTL": "@:MSRSTAT.SELC.TTL", "S1TEXT": "Veuillez sélectionner le modèle de l'unité à partir de laquelle vous souhaitez ajouter des sontrôles:", "S2TEXT": "<PERSON><PERSON><PERSON><PERSON> entrer une courte séquence de caractères qui correspond au code ou au nom du client de l'unité choisie:", "S3TEXT": "Veuillez sélectionner un ou plusieurs contrôles:", "ERROR": {"TTL": "Problème de sélection...", "NORES": "Aucun contrôle n'a été trouvé qui utilise cette procédure ou où l'unité convient aux mots clés donnés.", "LIMIT": "Vous avez atteint la limite de 30 contrôles par rapport. Veuillez supprimer d'abord les contrôles de la liste avant d'en ajouter des nouveaux."}, "RUTEXT": "@:MSRSTAT.SELMSR.RUTEXT", "SUCCESS": {"TTL": "Export terminé", "TEXT": "Les données sont téléchargées. Cliquez sur 'Oui' pour fermer cette boîte de dialogue ou 'Non' pour continuer. Pour réutiliser ultérieurement le choix de mesure ou de contrôle, vous pouvez copier les chaînes d'identité suivantes:", "MSTR": "Mesures: '{{ cstr }}'", "CSTR": "Contrôles: '{{ cstr }}'"}}, "TOOLTIP": {"REMCHK": "Contrôle enlevé"}, "TITLE": "Générateur rapport statistique", "SUBT": "Procédure {{ pcode }}", "SELM": {"TTL": "<PERSON><PERSON> mesures", "TXT": "<PERSON>euillez sélectionner les mesures qui doivent être utilisées dans le rapport. Il n'y a pas de nombre maximum, chaque mesure crée une nouvelle page.", "STATUS": "{{ msel }} Mesure (s) choisie (s)"}, "SELC": {"TTL": "<PERSON><PERSON><PERSON><PERSON><PERSON> contr<PERSON><PERSON>", "TXT": "Veuillez sélectionner les contrôles à utiliser dans le rapport.", "STATUS": "{{ csel }} Contr<PERSON><PERSON> (s) sélectionné (s)"}, "CLIST": "Contrôle <b>{{ id }}</b> <span style='font-size:70%'>pour modèle <b>{{ mcode }}</b>, Unité <b>{{ ucode }}</b>; Type de contrôle <b>{{ ctcode }}</b></span>", "BUTTON": {"SEL": "@:UI.BUTTONS.MEDITOR.SELECT", "REUSE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ADD": "@:TLISTEDIT.BUTTON.ADD", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "GENERATE": "Générer un rapport", "EXPORT": "Exporter les dates"}}, "MEDIAMGR": {"IMAGE": {"ALERT": {"CONFHEADER": "E<PERSON>-vous sûr?", "DELETE": "<PERSON><PERSON><PERSON><PERSON>-vous vraiment éffacer l'image {{fname }} de cette liste?"}, "EDIT": {"CAPTION": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "<PERSON><PERSON><PERSON>, modifier ou éffacer le titre de l'image"}}}, "DOC": {"ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "D<PERSON><PERSON>z-vous vraiment éffacer le document {{fname}} de cette liste?"}, "EDIT": {"CAPTION": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "<PERSON><PERSON><PERSON>, modifier ou éffacer le titre du document"}}}, "UPLOAD": {"FILTER": {"TITLE": "Téléchargement échoué", "MESSAGE": "Le téléchargement du document  {{ filename }} n'a pas pu être commencé:", "UNSUPPORTED": "Le type de fichier ({{type}}) n'est pas connu ou n'est pas soutenu par le Media Manager.", "TOOBIG": "Le fichier est trop grand; pour ce type de fichier la taille maximale est définie à {{ max }} MB, le fichier est {{ has }} MB."}, "HINT": "Enregistrer dans cette case les fichiers multimédias pour les télécharger sur le serveur.", "RESTR": "Vous pouvez télécharger ici des fichiers d'image (png, jpeg, tiff), fichiers de texte (pdf), packaged files (zip) et vidéos (seul mp4).", "STATUS": {"READY": "Prêt pour téléchargement", "WAITING": "Veuillez attendre...", "UPLOADING": "<PERSON><PERSON><PERSON><PERSON>", "CANCELLED": "Téléchargement abandonné", "FINISHED": "<PERSON><PERSON><PERSON><PERSON>", "FAILED": "Téléchargement échoué", "ERROR": "Défaillance de serveur"}, "STARTALL": "Télécharger tous les fichiers", "DELALL": "Enlever toutes les entrées terminées"}, "TOOLTIP": {"IMGSIZE": "Afficher la taille de l'image", "IMGMETA": "Afficher les métadonnées de l'image", "EDITCAP": "Ajouter ou modifier la signature de l'image", "REMIMG": "Enlever image", "VIEWDOC": "Voir le document", "DOCSIZE": "Afficher la taille du document", "DOCMETA": "Afficher les métadonnées du document", "REMDOC": "Enlever document", "UPLOAD": "Commencer téléchargement", "REMUPL": "Enlever téléchargement de la liste", "CNCLUPL": "Annuler téléchargement", "DOWNLOAD": "Télécharger l'original du document"}, "PDFPAGES": "{{ nump }} Pages", "VIDINFO": "@:WFLOW.INTRO.VIDINFO", "TITLE": "Media Manager", "TAB": {"IMAGES": "Images", "VIDEOS": "Vid<PERSON><PERSON>", "DOCS": "@:WFLOW.STEP.DOCS", "UPLOAD": "Télécharger"}}, "MODEL": {"EDITOR": {"TITLE": {"EDIT": "Editer {{modelname_de}}", "NEW": "Editer nouveau modèle"}}, "ALERT": {"EDITVERSION": "Désirez-vous vraiment éditer une version finalisée du modèle {{code}}?", "EDITVERSIONDETAIL": "Ceci est une version finalisée de ce modèle. Si cette version est utilisée dans les déroulements des opérations, les contrôles déjà fournis ou traitées seront modifiés.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Please confirm the deletion of this model by typing in its code.", "WRONGCODE": "Wrong code", "WRONGDELETEINPUT": "Your input does not match the code. The model has not been deleted.", "DELETEVERSION": "Ceci est une version finalisée de ce modèle. Si cette version est utilisée dans les déroulements des opérations, cette opération échouera et peut également provoquer des changements de données indésirables ou la corruption du processus.", "FINALIZE": "Désirez-vous vraiment finaliser la version actuelle du modèle {{code}}?", "FINALIZEDETAIL": "Les fichiers actuelles seront finalisés et une nouvelle version à éditer est fournie. La version finale peut ensuite être utilisée pour de nouvelles unités et des contrôles.", "RESET": {"TITLE": "Vraiment réinitialiser tous les changements?", "TEXT": "Toutes modifications apportées à cet objet depuis la dernière finalisation seront supprimées définitivement."}, "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "SWITCHV": {"TITLE": "Sélectionner version", "MESSAGE": "Veuillez sélectionner la version souhaitée dans la liste suivante:"}, "VIEW": {"ADDPROC": {"TITLE": "A<PERSON>ter procédures", "TEXT": "<PERSON><PERSON><PERSON>z sélectionner toutes les procédures, ensuite valider avec le button 'ajouter'."}, "ADDCTYPE": {"TITLE": "Ajouter types de contrôle", "TEXT": "Veuillez sélectionner tous les types de contrôle souhaités, ensuite valider avec le button 'ajouter'."}, "ADDCONFIGTABLE": {"TITLE": "Attribution de la table de configuration", "TEXT": "<PERSON><PERSON><PERSON>z sélectionner une table de configuration qui devra être liée au modèle actuel."}, "TOOLTIP": {"ACTREORD": "Afficher les icônes à réorganiser", "ADDPROC": "Ajouter procédure active", "UPDPROC": "Actualiser version de procédure", "REORDER": "Pour réorganiser tenir et déplacer", "GOTOPROC": "Aller à la procédure", "DELPROC": "Enlever procédure", "ADDCTYPE": "Ajouter type de contrôle actif", "DELCTYPE": "Enlever type de contrôle", "SWITCHVER": "Changer version", "TESTVER": "Tester version", "CHANGELOG": "Consulter le journal des modifications", "RESET": "Annuler toutes les modifications apportées à la dernière version finalisée", "PREALLOC0": "Définir pré-allocations (actuellement aucune disponible)", "PREALLOC1": "Définir pré-allocations (actuellement définit pour tout type de contrôle)", "PREALLOC2": "Définir pré-allocations (actuellement définit pour quelques types de contrôle)", "PREALLOC3": "Définir pré-allocations (actuellement définitions défectueuses disponibles)", "ADDCONFIGTABLE": "Rattacher la table de configuration", "DELCONFIGTABLE": "Dé<PERSON>cher le rattachement du tableau"}, "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DTYPE": "@:WFLOWEDIT.OPT1.DT", "DESC": "@:TLISTEDIT.DESC", "MEDIA": "Media   ", "IMREG": "{{ imgnum }} Images enregistrées", "DOCREG": "{{ docnum }} Documents enregistrés", "MEDMGR": "@:MEDIAMGR.TITLE", "BUTTON": {"EXPORT": "Exporter", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "FIN": "Finaliser", "EDIT": "Editer", "UNIT": "Aux unités", "CONFIGTABLE": "A la table de configuration"}, "PROC": {"STEPNUM": "Etapes: {{ stepnum }}", "DISCONTINUED": "Désaffecté", "OLDVERS": "Version utilisée {{ pvers }}"}, "ACTPROC": "Procédures actives", "ACTCTYPE": "Types de contrôles actifs", "VERSIONTAG": "Version:", "UNITINFO": {"TTL": "@:FRAME.MANAGE.UNIT", "TXT": "<b>{{ ucnt }} Unité (s) inscrit:</b><br>{{ ocnt }} ouvert, {{ ccnt }} fermé, {{ dcnt }} <PERSON><PERSON><PERSON>."}, "CONFIGTABLE": {"TTL": "Table de configuration", "NORELATION": "Pas de table de configuration attribuée"}, "PREALLOC": {"TITLE": "Définir pré-allocation", "TEXT1": "Veuillez sélectionner les types de contrôle pour lesquels la pré-allocation doit être appliquée:", "TEXT2": "Veuillez sélectionner un groupe et/ou un utilisateur à être pré-affecté à la procédure. Une allocation existante peut être enlevée avec 'retirer attribution' (dans la sélection de groupe ou d'utilisateur).", "ANY": "Chaque utilisateur/groupe", "GROUP": "Groupe:"}}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DTYPE": "@:WFLOWEDIT.OPT1.DT", "DESC": "@:TLISTEDIT.DESC", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"CLTF": "Vider filtre de texte", "SHOWDEL": "Afficher ou masquer les modèles supprimés", "NEWMODEL": "Nouveau modèle", "IMPORTMODEL": "Importer modèle", "GOTOUNITS": "Aller dans les unités du modèle"}, "TOPROW": "{{ dtypt }} ({{ dtypc }}), {{ unitnum }} Unité (s)"}, "MODELS": "@:FRAME.MANAGE.MODEL", "TESTCHECK": {"TITLE": "Tester m<PERSON><PERSON><PERSON>", "TEXT": "Veuillez sélectionner le type de contrôle avec lequel vous souhaitez tester cette version du modèle:"}}, "STEP": {"MODEL": {"MEDIAMANAGER": "Modèle {{ mcode }}: {{ mtitle }}"}, "EDITOR": {"TITLE": {"EDIT": "Edite {{stepname_de}}", "NEW": "Edite nouvelle étape"}}, "ALERT": {"EDITVERSION": "Désirez-vous vraiment modifier une version finalisée de l'étape {{code}}?", "EDITVERSIONDETAIL": "Ceci est une version finalisée de cette étape. Si cette version est utilisée dans les flux de travail, les contrôles déjà fournis ou traités seront modifiés.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Voulez-vous vraiment supprimer l'étape {{scode }} de la procédure {{pcode}} ?", "DELETECASCADING": "Toutes les mesures ({{mcnt}}) qui sont définies pour cette étape sont ensuite également supprimées!", "DELETEVERSION": "Ceci est une version finalisée de cette étape. Si cette version est utilisée dans les flux de travail, cette opération échouera et peut également provoquer des changements indésirables de données ou la corruption du processus.", "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "VIEW": {"MEDIAMANAGER": "Procédure {{ pcode }}: {{ ptitle }}, Etape {{ scode }}: {{ stitle }}", "FLOWEDITOR": "Etape {{ scode }}: {{ stitle }}", "TOOLTIP": {"ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD", "UPLMEAS": "Télécharger mesure", "CLNMEAS": "@:MEASURE.CLONE.TITLE", "NEWMEAS": "Créer nouvelle mesure", "REORDER": "@:MODEL.VIEW.TOOLTIP.REORDER", "EDITRULE": "O<PERSON><PERSON><PERSON>r l'éditeur de règles d'expiration", "EXPMEAS": "Exporter mesure", "EDITMEAS": "Editer mesure", "TESTMEAS": "Tester mesure", "REMMEAS": "Enlever mesure"}, "VERSION": "@:PROCEDURE.VIEW.VERS", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESC": "@:TLISTEDIT.DESC", "MEDIA": "@:MODEL.VIEW.MEDIA", "IMREG": "@:MODEL.VIEW.IMREG", "DOCREG": "@:MODEL.VIEW.DOCREG", "BUTTON": {"MEDMGR": "@:MEDIAMGR.TITLE", "REDITOR": "@:PROCEDURE.VIEW.REDITOR", "EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"}, "RULES": "@:PROCEDURE.VIEW.RULES", "MEASURES": "@:WFLOW.INTRO.MEASURES"}, "CLONE": {"TITLE": "Dupliquer étapes", "PSELTEXT": "Veuillez sélectionner la procédure à partir de laquelle vous voulez dupliquer des différentes étapes:", "TEXT": "Sélectionner des étapes à dupliquer:"}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "INSTRUCTION": "Instructions", "DESC": "@:TLISTEDIT.DESC", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}, "TOOLTIP": {"INSTRUCTION": "Une étape avec instructions à type ne peut pas contenir des mesures"}}, "MATRIX": {"TITLE": "Réglage de l'étiquette de la matrice", "HINT": "Sélectionnez un des titres dans la matrice, puis modifiez-le dans la zone de texte ci-dessous. Les titres plus petits au-dessus des cases de valeur sont eux-mêmes affichés plus tard dans le coin supérieur gauche des cases de valeur. Il est également possible d'entrer soi-même un texte court dans une case de valeur; il sera ensuite affiché dans la matrice comme un espace réservé.", "BUTTON": {"BACK": "Retour"}}, "LIST": {"TOOLTIP": {"GOTOPROC": "@:WFLOW.STEP.TOOLTIP.GOTOPROC", "UPLOAD": "@:PROCEDURE.VIEW.TOOLTIP.UPLSTEP", "CLONE": "@:STEP.CLONE.TITLE", "ADDSTEP": "@:PROCEDURE.VIEW.TOOLTIP.NEWSTEP", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF"}, "BTMROW": "Mesures: {{ mcnt }}, Images: {{ icnt }}, Documents: {{ dcnt }}"}, "STEP": "@:WFLOWEDIT.OPT1.S", "STEPS": "@:WFLOW.INTRO.STEPS", "FORPROC": "de procédure {{proc}}"}, "PROCEDURE": {"EXPORT": {"TITLE": "Export procedure", "MESSAGESYSTEM": "Choose if the procedure should be exported for the same or for another system.", "MESSAGECONTENTS": "Choose which contents should be exported.", "OPTIONS": {"SAMESYSTEM": {"SAMESYSTEM": "For the same system", "OTHERSYSTEM": "For another system", "FILES": "Images and Documents", "TOOLS": "Tools", "RULES": "Workflow rules", "ENFORCEMENTS": "Workflow locks"}}}, "EDITOR": {"TITLE": {"EDIT": "Éditer {{procname_de}}", "NEW": "Éditer nouvelle procédure"}}, "LIST": {"GOTOPROC": {"TITLE": "Vue d'utilisation", "TEXT": "Les modèles suivants utilisent la procédure choisie. Vous pouvez en choisir une pour pouvoir passer à la page d'édition correspondante."}, "TOOLTIP": {"IMPORT": "Importer procédure", "CLONE": "@:PROCEDURE.CLONE.TITLE", "ADD": "Établir nouvelle procédure", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "SHOWDEL": "Afficher ou masquer les procédures supprimées", "GOTOMODEL": "Afficher les modèles qui utilisent cette procédure et qui s'y rendent optionnellement", "PROCFILTER": "Filtrer par attributs personnalisés"}, "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE", "BTMROW": "Étape: {{ stpcnt }}, utilisé de {{ modcnt }} <PERSON><PERSON><PERSON><PERSON> (s)", "BTMROWUPD": ", vieille version: {{ updcnt }}"}, "ALERT": {"EDITVERSION": "Voulez-vous vraiment modifier une version finalisée de la procédure {{code}}?", "EDITVERSIONDETAIL": "Ceci est une version finalisée de cette procédure. Si cette version est utilisée dans les flux de travail, des contrôles déjà installés ou traités seront modifiés.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "D<PERSON><PERSON>z-vous vraiment effacer la procédure  {{code}} ?", "DELETECASCADING": "Toutes les étapes ({{scnt}}) et les mesures qui sont définies pour cette procédure sont alors également supprimées!", "DELETEVERSION": "Ceci est une version finalisée de cette procédure. Si cette version est utilisée dans les flux de travail, cette opération échouera et peut également provoquer des changements indésirables de données ou la corruption du processus.", "FINALIZE": {"TITLE": "Désirez-vous vraiment finaliser la version actuelle de la procédure {{code}}?", "TEXT": "Les données actuelles sont finalisées et une nouvelle version à éditer est prête. La version finale peut ensuite être utilisée pour de nouvelles unités et de tests. Assurez vous que vous mettiez à jour tous les modèles qui utilisent cette procédure, et finalisez-les pour pouvoir utiliser cette version."}, "FULLUPDATE": {"TITLE": "Voulez-vous vraiment mettre à jour automatiquement tous les modèles qui utilisent cette procédure ?", "TEXT": "Les modèles qui utilisent une ancienne version de cette procédure sont mis à jour; Cela signifie qu'une nouvelle version de chaque modèle est générée automatiquement. Des éventuelles autres modifications aux modèles ne sont pas finalisées."}, "RESET": {"TITLE": "Vraiment réinitialiser toutes les modifications?", "TEXT": "Toutes modifications apportées à cet objet depuis la dernière finalisation seront supprimées définitivement."}, "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "SWITCHV": {"TITLE": "@:MODEL.SWITCHV.TITLE", "MESSAGE": "@:MODEL.SWITCHV.MESSAGE"}, "VIEW": {"FLOWEDITOR": "Procédure {{ pcode }}: {{ ptitle }}", "TOOLTIP": {"STAT": "Générer un rapport statistique", "CHVERS": "@:MODEL.VIEW.TOOLTIP.SWITCHVER", "UPDMOD": "Mise à jour des modèles vers la version la plus nouvelle", "GOTOSTEP": "@:WFLOW.INTRO.TOOLTIP.GOTOSTEP", "ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD", "UPLSTEP": "Télécharger étapes", "CLNSTEP": "@:STEP.CLONE.TITLE", "NEWSTEP": "Etablir nouvelle étape", "REORD": "@:MODEL.VIEW.TOOLTIP.REORDER", "EXPSTEP": "Exporter étape", "REMSTEP": "Enlever é<PERSON>pe", "TESTVER": "Tester version", "CHANGELOG": "Voir le journal des modifications", "RESET": "Annuler toutes les modifications apportées à la dernière version finalisée", "ENFORCETOP": "La première entrée ne peut pas être contrainte de débit", "ENFORCEINSTR": "Une directive ne peut pas être contrainte de débit", "ENFORCE0": "Aucune contrainte de débit", "ENFORCE1": "Peut être traité qu'après l'entrée précédente", "ENFORCE2": "Peut être traité que si toutes les entrées précédentes ont été accomplies"}, "VERS": "Version", "UPDATEINFO": "Il y a des {{updcnt}} modèles qui utilisent une ancienne version de cette procédure.", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "PROCTIME": "Processing Time", "HOURS": "hours", "MINUTES": "minutes", "DESC": "@:TLISTEDIT.DESC", "RULES": "Règles de flux de travail", "REDITOR": "<PERSON><PERSON><PERSON> <PERSON>", "BUTTON": {"EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "FIN": "@:MODEL.VIEW.BUTTON.FIN", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT", "STATISTIC": "Statistique  "}, "STEPS": "@:WFLOW.INTRO.STEPS", "MEASURES": "Mesurages: {{ msrcnt }}"}, "CLONE": {"TITLE": "<PERSON>p<PERSON><PERSON> procé<PERSON><PERSON>", "TEXT": "Veuillez sélectionner les procédures à dupliquer:"}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESC": "@:TLISTEDIT.DESC", "PROCTIME": "Processing Time", "HOURS": "hours", "MINUTES": "minutes", "WRONGPROCTIME": "Wrong processing time", "WRONGPROCTIMETXT": "The processing time has been entered incorrectly.", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "TESTCHECK": {"TITLE": "Tester procé<PERSON>re", "TEXT": "Veuillez sélectionner le type de contrôle que vous souhaitez tester avec cette version de la procédure:"}, "FILTER": {"TITLE": "Filtre global (Procédure-Attribute)", "PLEASECHOOSE": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner  ", "CANCEL": "Réinitialiser filter", "APPLY": "Appliquer filtre"}}, "MEASURE": {"EDITOR": {"TITLE": {"EDIT": "Éditer {{mname_de}}", "NEW": "Éditer une nouvelle mesure"}, "NOTOOL": "Pas d'outil nécessaire"}, "VIEW": {"FLOWEDITOR": "Mesure {{ mcode }}: {{ mtitle }}"}, "ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "D<PERSON>irez-vous vraiment éffacer la mesure {{ mcode }} de l'étape {{ scope }} dans la procédure {{ pcode }} ?", "DELETEVERSION": "Ceci est une version finalisée de cette mesure. Si cette version est utilisée dans les déroulemenst des opérations, cette opération échouera et peut également provoquer des changements de données indésirables ou la corruption du processus.", "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "CLONE": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> les mesures", "PSELTEXT": "Veuillez sélectionner le processus duquel vous désirez dupliquer des mesures individuelles.", "TEXT": "Veuillez sélectionner les mesures à dupliquer."}, "EDIT": {"COMPARATOR": "Opérateur de comparaison", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "HINTS": "<PERSON><PERSON><PERSON>", "TTYPE": "Type d'outil", "MTYPE": "Type de mesure", "MANDATORY": "Requis", "YES": "@:WFLOW.INPUT.YES", "NO": "@:WFLOW.INPUT.NO", "TARGET": "Valeur cible", "UNIT": "@:WFLOW.INTRO.UNIT", "THOLD": "@:WFLOW.INPUT.THRESH", "MIN": "Minimum", "MAX": "Maximum", "MINLEN": "Longueur de texte minimum", "REGEXP": "Expression régulière", "EXP": "<PERSON><PERSON><PERSON>", "ANY": "Les deux", "MATRIX": "<PERSON><PERSON>", "BUTTON": {"SETUP": "Modifier la matrice", "TEST": "Enregistrer et tester", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "CLOSE": "Enregistrer et fermer"}, "NUMCOL": "Colonnes", "NUMROW": "<PERSON><PERSON><PERSON>", "FORMULA": "Formule", "FLOATFRM": {"TEXT": "Format numérique", "STD": "Standard", "INT": "pas de décimale", "1DIGIT": "1 décimale", "2DIGIT": "2 décimales", "3DIGIT": "3 décimales", "4DIGIT": "4 décimales", "6DIGIT": "6 décimales"}, "COMPLCODE": "Regroupement", "INTERNAL": "Interne", "INTERNALEXTERNAL": "Internal & External", "CHOICE": "Nombre de valeurs", "CHOICEVAL": "{{ val }}. <PERSON><PERSON>"}, "TYPES": {"THRESHOLD": "@:WFLOW.INPUT.THRESH", "ABSOLUTE": "<PERSON><PERSON> absolue", "ABSOLUTERNG": "Valeur absolue dans un domaine", "TEXT": "Texte quelconque", "REGEXP": "Texte vérifié", "BOOL": "Oui/non – question", "RESCHECK": "Contrôle d'une valeur prédéterminée", "THRESHOLDMATRIX": "Écart par calcul matriciel", "ABSOLUTEMATRIX": "Valeur absolue par calcul matriciel", "ABSOLUTERNGMATRIX": "Valeur absolue dans un domaine par calcul matriciel", "STATISTICAL": "Valeur statistique", "STATISTICALMATRIX": "Valeur statistique par calcul matriciel", "TIMERSTART": "<PERSON><PERSON> de temps (heure de début)", "TIMERSTOP": "<PERSON><PERSON> de temps (heure de fin)", "TIMERSTOPQ": "<PERSON>sie de temps vérifié (heure de fin)", "TIMERSTOPC": "Contrôle non corrigé de la saisie de temps", "CHOICELIST": "Liste de choix"}}, "MEASUREMENTERRORCATEGORYEDITOR": {"TITLE": "Editing of a finished measurement", "TEXT": "This measurement has already been accomplished. Please choose one of the reasons for the editing below to continue."}, "TOOL": {"EDITOR": {"TITLE": {"EDIT": "Editer {{ tool }}", "NEW": "Editer nouveau type d'outil {{ tool }}"}}, "NEWUNIT": {"TITLE": "Nouvelle unité d'outil", "TEXT": "Veuillez entrer dans la zone de texte le numéro de série ou autre valeur identifiante:"}, "EDITUNIT": {"TITLE": "Editer unit<PERSON> d'outil", "TEXT": "@:TOOL.NEWUNIT.TEXT"}, "COMMENTUNIT": {"TITLE": "<PERSON><PERSON><PERSON>", "TEXT": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, modifier ou supprimer le commentaire pour cette unité ici:"}, "ALERT": {"DELETEUNIT": "D<PERSON><PERSON>z-vous vraiment effacer l'unité d'outil {{code}}?", "NODELETEUNIT": "Unité d'outil {{code}} ne peut pas être effacée!", "DELETEUNITCASCADING": "Cette unité est déjà utilisée dans plusieurs contrôles; veuil<PERSON><PERSON> changer le statut de l'unité à «désactivé».", "DELETELASTUNITCASCADING": "The tooltype to this unit is already used in some procedures; at least one unit must be available. Please set it to 'disabled' instead.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DENY": "Action pas possible...", "DELETE": "D<PERSON><PERSON>z-vous vraiment effacer le type d'outil {{code}}?", "NODELETE": "Type d'outil {{code}} ne peut pas être effacé!", "DELETECASCADING": "Certaines unités sont déjà définies pour ce type; veuillez changer le statut du type à «désactivé»."}, "MEDIAMANAGER": "Type d'outil {{ ttcode }}: {{ tttitle }}", "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "HINTS": "@:TLISTEDIT.DESC", "DIS": {"TITLE": "@:TLISTEDIT.DISSET.TITLE", "TRUE": "Le type d'outil est désactivé et ne peut plus être utilisé.", "FALSE": "Le type d'outil est actif."}, "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"NEW": "Etablir nouveau type d'outil", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "SHOWDEL": "Montrer types d'outils désactivés"}, "TTYPES": "Types d'outil", "BTMROW": "Mesurage: {{ mcnt }}, Type d'outil: {{ tcnt }}"}, "VIEW": {"TOOLTIP": {"NEWTUNIT": "Etablir nouvelle unité d'outil", "EDITTUNIT": "Editer unit<PERSON> d'outil", "REENABLE": "Reactiver unité d'outil", "DISABLE": "Désactiver unité d'outil", "REMTUNIT": "Enlever unité d'outil", "SHOWDEL": "Montrer unités d'outils désactivées", "SETCOMMENT": "Commenter cette unité", "REPORT": "Générer un rapport d'utilisation d'outil"}, "DISABLED": "@:TOOL.EDIT.DIS.TRUE", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESCRIPTION": "@:TLISTEDIT.DESC", "MEDIA": "@:MODEL.VIEW.MEDIA", "IMGREG": "{{ imgcnt }} Enregistrer images", "DOCREG": "{{ doccnt }} Enregistrer documents", "BUTTON": {"MEDMGR": "@:MEDIAMGR.TITLE", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"}, "MEDINFO": "Note: Seule la première image ou document est disponible!", "TUNITS": "Unités d'outils", "TOOLDEL": "Désactivé", "TOOLUNITCNT": "Utilisé dans {{ ucnt }} mesures"}, "REPORT": {"TITLE": "Générer un rapport d'utilisation", "DATE1": "Vous pouvez définir ici la date à partir de laquelle les utilisations d'unité d'outil seront incluses dans le rapport. Si vous ne voulez pas fixer de date (toutes les utilisations depuis le début) vous appuyez simplement sur \"Enregistrer\" sans date de sélection.", "DATE2": "Vous pouvez définir ici la date jusqu'à laquelle les utilisations d'unité d'outil seront incluses dans le rapport. Si vous ne voulez pas fixer de date (toutes les utilisations jusqu'à maintenant) vous appuyez simplement sur \"Enregistrer\" sans date de sélection.", "SORT": {"TEXT": "Veuillez choisir suivant quel critère le rapport est trié:", "MODEL": "<PERSON><PERSON> mod<PERSON>, unité, contr<PERSON>le", "TIME": "<PERSON><PERSON> horodateur"}}}, "CHECKTYPE": {"MEDIAMANAGER": "Types de matériel {{ code }}: {{ title }}"}, "DEVICETYPE": {"MEDIAMANAGER": "Type de vérification {{ code }}: {{ title }}"}, "UNIT": {"EDITOR": {"TITLE": {"EDIT": "<PERSON>e {{ unité }}", "NEW": "Edite nouvelle unité"}}, "ARCHIVE": {"TTL": "Chercher dans archive", "TXT": "Veuillez entrer un ou plusieurs mots-clés qui correspondent à une partie du code ou le nom du client que vous recherchez.", "MANYRES": {"TTL": "Trop de résultats", "TXT": "La recherche a trouvé plus de 100 entrées, seule les 100 premières seront affichées. Veuillez affiner votre recherche."}}, "NEWCHECK": {"TITLE": "Etablir nouveau contrôle", "TEXT": "Veuillez sélectionner le type de contrôle:", "CONFIGTABLE": {"NEW": "Reprendre tableau du modèle de présentation", "TITLE": "Sélectionner le modèle de table de configuration", "TEXT": "Veuillez choisir si la table de configuration doit être prise d'après le modèle de présentation ou d'un contrôle précédent:", "CHECK": "Contr<PERSON>le"}}, "ALERT": {"DELETECHECK": {"TITLE": "Voulez-vous vraiment supprimer ce contrôle?", "TEXT": "Toutes les mesures et d'autres données recueillies vont ainsi aussi être perdues!"}, "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Please confirm the deletion of this unit by typing in its code.", "WRONGCODE": "Wrong code", "WRONGDELETEINPUT": "Your input does not match the code. The unit has not been deleted.", "DELETECASCADING": "Tous les contrôles de cette unité ({{ccnt}}) y compris tous les résultats de mesure seront également supprimés!", "STATUSCHG": {"TTL": "Changer le statut de l'unité", "TXT": "Veuillez sélectionner le nouveau statut de cette unité:"}, "UNARCHIVE": {"TXT": "Vraiment prendre l'unité de l'archive?"}}, "MODIFY": {"DATE": {"TITLE": "Mettre nouvelle date", "COMMISSIONED": "Veuillez choisir la date à laquelle cette unité a été commandée", "FINISHED": "Veuillez choisir la date à laquelle cette unité a été achevée", "DELIVERED": "Veuillez choisir la date à laquelle cette unité a été livrée au client", "APPROVED": "Veuillez choisir la date à laquelle cette unité a été acceptée par le client"}, "COMMENT": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "@:MEASUREMENT.INPUT.COMMENT.TEXT"}}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "CUST": "Client", "MODEL": "@:WFLOWEDIT.OPT1.MD", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"NEW": "Créer nouvelle unité", "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "ARCHMODE": "<PERSON><PERSON><PERSON><PERSON> ou quitter le mode archive", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "LOCKMODE": "A<PERSON><PERSON><PERSON> ou masquer les éléments terminés"}, "UNITS": "@:FRAME.MANAGE.UNIT"}, "FORMODEL": "du modèle {{mdl}}", "INARCHIVE": "dans l'archive, correspondant à  '{{ pat }}'", "VIEW": {"TOOLTIP": {"UNITSTAT": "Changer le statut de l'unité", "UNARCH": "Prendre l'unité de l'archive (réactiver)", "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "EDITDATE": "<PERSON>tre ou modifier une date", "EDITCOMM": "Ajouter ou modifier un commentaire", "ADDCHK": "@:UNIT.NEWCHECK.TITLE", "DELCHK": "@:MSRSTAT.TOOLTIP.REMCHK", "GOTOCHK": "Aller au contrôle"}, "MODEL": "@:WFLOWEDIT.OPT1.MD", "CODE": "@:TLISTEDIT.CODE", "CUST": "@:UNIT.EDIT.CUST", "CHECKS": "<PERSON><PERSON><PERSON><PERSON>", "CHK": "@:WFLOW.INTRO.MEDIAMANAGER", "DATES": "<PERSON><PERSON><PERSON>", "COMM": {"ON": "<PERSON><PERSON> le", "NOT": "Pas encore commandé"}, "FIN": {"ON": "<PERSON><PERSON><PERSON><PERSON> le", "NOT": "Pas encore terminé"}, "DEL": {"ON": "<PERSON><PERSON>", "NOT": "Pas encore expédié"}, "APPR": {"ON": "<PERSON><PERSON><PERSON> le", "NOT": "Pas encore accepté"}, "COMMENT": "Commentaire", "BUTTON": {"DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT", "CHSTATUS": "Changer statut", "UNARCHIVE": "Désactiver  "}, "CHECK": {"MODVERS": "Version du modèle utilisé {{mvers}}", "FAIL": "<PERSON><PERSON><PERSON><PERSON>", "PASS": "Contrôle réussi!", "FAILCLOSED": "<PERSON><PERSON><PERSON>, contrôle écho<PERSON> le", "PASSCLOSED": "<PERSON><PERSON><PERSON>, contr<PERSON>le r<PERSON> le", "PASSCANCEL": "<PERSON><PERSON><PERSON>", "WARN": "Contrôle réussi avec avertissements!", "WARNCLOSED": "<PERSON><PERSON><PERSON>, contrôle réussi avec avertissements le"}, "SCHED": {"NOT": "@:WFLOW.INTRO.DATE.NOSCHED", "ON": "Prévu pour"}, "DUE": {"ON": "à terminer jusqu'au", "NOT": "Pas de date de fin"}, "START": {"ON": "@:WFLOW.INTRO.DATE.START"}, "STATUS": {"TTL": "@:WFLOW.INPUT.STATUS.TITLE", "OPEN": "OUVERT", "CLOSED": "FERMÉ", "DISCARDED": "REJETÉ", "ARCHIVED": "ARCHIVÉ", "CLOSEDARCH": "FERMÉ (ARCHIVÉ)", "DISCARCH": "REJETÉ (ARCHIVÉ)"}}}, "USERS": {"ANYUSER": "Quelconque utilisateur", "ANYGROUP": "Quelconque groupe", "DELASSIGN": "Supprimer attribution", "TOOLTIP": {"NEWUSER": "Créer nouvel utilisateur", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "HIDEGRP": "Cacher groupes", "NEWGRP": "Créer nouveau groupe", "SHOWGRP": "Afficher groupes  ", "DISUSER": "Désactiver utilisateur", "ENUSER": "<PERSON>r utilisateur", "EDITUNAM": "Editer nom d'utilisateur", "EDITURN": "Editer nom complet de l'utilisateur", "EDITUCOMM": "A<PERSON>ter ou éditer des commentaires à l'utilisateur", "REMGRP": "@:USERMGR.ACTION.DELETEGROUP.TITLE", "REACTGRP": "Réactiver groupe", "DISABLED": "Désactivé", "SHOWDELGROUPS": "Afficher groupes désactivés", "CHGGRP": "Transferer l'utilisateur dans un autre groupe", "EDITGNAME": "Editer nom de groupe", "EDITGLVL": "Editer niveau de groupe", "EDITGDESC": "Editer description de groupe", "ADDGPRV": "Ajouter des privilèges de groupe", "REMGPRV": "Enlever privilège de groupe", "ADDUPRV": "Ajouter des privilèges d'utilisateur", "REMUPRV": "Enlever le privilège de groupe", "SHOWDEL": "Afficher utilisateurs désactivés", "ADDGROUP": "Attribuer un ou plusieurs groupes à cet utilisateur", "ADDUSER": "Attribuer un ou plusieurs utilisateurs à ce groupe", "ADDFIRSTGROUP": "Attribuer un ou plusieurs groupes à cet utilisateur", "ADDFIRSTUSER": "Attribuer un ou plusieurs utilisateurs à ce groupe", "REMOVEGROUP": "Enlever ce groupe de l'utilisateur", "REMOVEUSER": "Enlever cet utilisateur du groupe"}, "USERS": "@:FRAME.MANAGE.USERS", "GROUPS": "Groupes", "USER": "@:MEDITOR.USER", "USERNAME": "@:LOGIN.USERNAME", "PASSWORD": {"TITLE": "@:LOGIN.PASSWORD", "SET": "Mot de passe défini", "NOTSET": "Mot de passe non défini"}, "BUTTON": {"CHPASSW": "@:FRAME.MANAGE.CHANGEPW"}, "REALNAME": "Vrai nom", "COMMENT": "@:UNIT.VIEW.COMMENT", "GROUP": "@:MEDITOR.GROUP", "GNAME": "Nom de groupe", "LEVEL": "Niveau", "DESC": "@:TLISTEDIT.DESC", "PRIVILEGES": "Privilèges   ", "PRIV": {"BYGROUP": "reçu à travers les groupes", "BYUSER": "reçu individuellement", "FORGROUP": "Privilèges à travers tous les groupes"}, "RANK": "Niveau", "USERGROUPS": "Appartenance à un groupe", "NOGROUPS": "L'utilisateur appartient à aucun groupe", "GROUPUSERS": "Me<PERSON><PERSON>", "NOUSERS": "Aucun utilisateur est affecté à ce groupe", "ADDUSER": "Ajouter des utilisateurs", "ADDGROUP": "Attribuer des groupes"}, "USERMGR": {"ACTION": {"ADDUSER": {"TITLE": "Ajouter utilisateurs"}, "EDITUSER": {"TITLE": "Editer utilisate<PERSON>"}, "USER": {"USERNAME": "Veuillez entrer le nouveau nom d'utilisateur", "REALNAME": "Veuillez entrer le vrai nom de l'utilisateur", "COMMENT": "Veuillez ajouter ou éditer un commentaire au sujet de l'utilisateur", "PASSWORD": "Entrez un nouveau mot de passe pour l'utilisateur ou laisser vide pour supprimer le mot de passe", "GROUP": "Veuillez choisir un nouveau groupe pour l'utilisateur"}, "ADDGROUP": {"TITLE": "Ajouter groupe"}, "EDITGROUP": {"TITLE": "Editer groupe"}, "GROUP": {"NAME": "Veuillez entrer le nouveau nom du groupe", "LEVEL": "Veuillez entrer le nouveau niveau du groupe (nombre compris entre 1 et 1000)", "DESCRIPTION": "Veuillez entrer ou modifier la description pour ce groupe"}, "DELETEGROUP": {"TITLE": "Désactiver groupe", "TEXT": "Voulez-vous vraiment désactiver le groupe? Les utilisateurs de ce groupe ne seront ensuite éventuellement plus appartenants à un groupe."}, "REACTIVATEGROUP": {"TITLE": "Activer groupe", "TEXT": "Ce groupe a été désactivé. Voulez-vous le réactiver?"}, "ADDGRANT": {"TITLE": "Ajouter privilèges", "TEXT": {"GROUP": "Veuillez choisir les privilèges qui doivent être ajoutés au groupe:", "USER": "Veuillez choisir les privilèges qui doivent être ajoutés à l'utilisateur:"}}, "ADDGRPTOUSER": {"TITLE": "Ajouter groupes", "TEXT": "Veuillez choisir les groupes que vous voulez ajouter à l'utilisateur:"}, "ADDUSERTOGRP": {"TITLE": "A<PERSON>ter utilisateur", "TEXT": "Veuillez choisir les utilisateurs que vous voulez ajouter au groupe:"}}}, "WFLOW": {"INTRO": {"STATUS": {"INIT": "INITIALISE", "SCHED": "INSTALLE", "START": "DEBUTE", "FAIL": "ECHOUE", "PASS": "REUSSI", "FAILC": "ECHOUE & FERME", "PASSC": "REUSSI & FERME", "CANCEL": "ANNULE", "WARN": "REUSSI avec Lim.", "WARNC": "REUSSI avec Lim. & FERME"}, "MEDIAMANAGER": "Contr<PERSON>le  ", "FILEINFO": {"TITLE": "Upload information", "UPLOADDATE": "Date:", "UPLOADTIME": "Time:", "UPLOADEDBY": "By:"}, "TOOLTIP": {"EXPPDF": "Exporter le contrôle en PDF", "EXPFILE": "Exporter dates de contrôle", "GOTOMODEL": "<PERSON><PERSON> au modèle", "GOTOUNIT": "Aller à l'unité", "IMGLEFT": "Image précédente", "IMGRIGHT": "Image suivante", "VIEWDOC": "Voir document", "EDITCOMM": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "VIEWIMG": "Voir image", "REMASS": "Enlever cible d'attribution", "ASSIGN": "Attribuer tous les éléments sélectionnés", "HIGHLASS": "Mettre en évidence les éléments affectés", "ADDASS": "Ajouter cible d'attribution", "SELASS": "Choisir pour l'attribution", "GOTOSTEP": "Aller à l'étape", "CHANGEASSIGN": "Modifier attribution", "CONFIGTABLE": "Ouvrir la table de configuration"}, "MEDIAMGR": "<PERSON><PERSON><PERSON>, effacer ou modifier images ou documents", "TOOEARLY": "Le contrôle est prévu pour une date ultérieure, il ne peut pas encore être modifié.", "TOOLATE": "L'achèvement du contrôle est en retard,  veuillez vous dépêcher!", "MODEL": "@:WFLOWEDIT.OPT1.MD", "UNIT": "Unité", "PDFINFO": "{{ pnum }} Pages", "VIDINFO": "{{ width }} x {{ height }}, {{ fps }} bps, Période: {{ rtime }} sek", "IMGINFO": "{{ width }} x {{ height }}", "DATE": {"SCHED": "Prévu pour", "DUE": "<PERSON><PERSON><PERSON> jusqu'à", "NOSCHED": "Aucune date de début prévue", "NODUE": "Aucune date d'achèvement prévue", "START": "<PERSON><PERSON><PERSON><PERSON> le", "FIN": "<PERSON><PERSON><PERSON><PERSON> le", "NOSTART": "Pas encore commencé", "NOFIN": "Pas encore terminé"}, "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "NOCOMMENT": "Le contrôle n'est pas encore commenté", "STEPINPROC": "Étape (s) dans", "PROCS": "Procédure (s)", "NOASS": "Pas encore attribué", "ADDASS": "Ajouter attribution", "PROCSUNASS": "Procédure (s) non attribuée (s)", "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE", "STEPS": "Étapes", "INSTRUCTIONS": "Instructions", "MEASURES": "Mesurages", "STAT": {"TOTAL": "Totale", "PASSED": "<PERSON><PERSON><PERSON><PERSON>", "UNFIN": "En cours", "FAILED": "<PERSON><PERSON><PERSON>", "SKIPPED": "Optionnel"}, "BUTTON": {"DELETE": "<PERSON><PERSON><PERSON><PERSON>", "SCHEDULE": "Installer", "RESCHEDULE": "Réordonnancement", "START": "<PERSON><PERSON><PERSON><PERSON>", "REASS": "Arrêter contrôle / nouvelle attribution", "UNREG": "@:CHECK.ALERT.UNREGISTER.TITLE", "REG": "@:CHECK.ALERT.REGISTER.TITLE", "CONTINUE": "<PERSON><PERSON><PERSON>", "COMMIT": "Confirmer", "CLOSE": "@:WFLOW.INPUT.BUTTON.CLOSE", "REOPEN": "@:CHECK.ALERT.REOPEN.TITLE", "CANCEL": "<PERSON><PERSON><PERSON> contrôle", "COPY": "Etablir copie"}, "FILTER": {"BUTTONS": {"COLLAPSE": "Dépliage", "PROC": "Procédure", "STEP": "Étape", "MEAS": "Mesure", "FILTER": "Filtre", "FILTERPLUS": "Filtre+"}, "TITLE": "Filter", "DISABLE": "Disable filter", "FILTTITLE": "Apply text filter on:", "FILTCODE": "codes", "FILTTEXT": "element titles", "FILTPROC": "procedures", "FILTSTEP": "steps", "FILTMEAS": "measures", "FILTMARKELEMS": "Mark elements:", "FILTUSER": "Mark all/only elements assigned to the current user (will reset filters above):", "FILTUNFIN": "Mark all/only unfinished elements", "FILTFAIL": "Mark all/only failed elements", "FILTSUCC": "Mark all/only suceeded elements", "HIDEINSTR": "Hide instruction steps:", "HIDEOMITTEDORSKIPPED": "Hide omitted or skipped elements:", "TOOLTIP": {"VIEWFILT": "Show filter dialog", "PROC": "Tout plier et seulement montrer les procédures", "STEP": "<PERSON><PERSON><PERSON><PERSON> é<PERSON>pes, plier mesures", "MEAS": "<PERSON><PERSON> affiche<PERSON>, déplier étapes et mesures", "FILTER": "Tout afficher qui est sélectionné par un filtre, plier tout le reste", "FILTERPLUS": "Développer par filtre les éléments marqués, plier tout le reste", "RESET": "Supprimer tous les filtres"}}, "VERSION": "Contrôle se base sur la version de modèle", "UNFINVERSION": "Le contrôle est basé sur la version en cours d'édition et non-finalisée", "TESTRUN": {"PROCEDURE": "Essai d'un contrôle de procédure:", "MODEL": "<PERSON><PERSON>i d'un contrôle de modèle:"}}, "STEP": {"IMAGE": {"NEXT": "Next", "PREVIOUS": "Previous"}, "STATUS": {"TODO": "@:WFLOW.INTRO.STATUS.INIT", "PASS": "@:WFLOW.INTRO.STATUS.PASS", "FAIL": "@:WFLOW.INTRO.STATUS.FAIL", "OMIT": "OMIS", "SKIP": "SAUTE", "PASSNF": "(REUSSI)", "FAILNF": "(ECHOUE)", "WARN": "@:WFLOW.INTRO.STATUS.WARN", "WARNNF": "(REUSSI avec Lim.)"}, "TOOLTIP": {"GOTOMODEL": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "GOTOUNIT": "@:WFLOW.INTRO.TOOLTIP.GOTOUNIT", "GOTOPROC": "Aller vers la procédure", "VIEWIMG": "@:WFLOW.INTRO.TOOLTIP.VIEWIMG", "VIEWDOC": "@:WFLOW.INTRO.TOOLTIP.VIEWDOC"}, "COMMITTER": "Étape confirm<PERSON> de:", "NOCOMMIT": "Pas encore confirmé", "MEASURER": "Mesure {{code}} de:", "NOMEASURE": "Pas encore modifié", "PDFINFO": "@:WFLOW.INTRO.PDFINFO", "VIDINFO": "{{ width }} x {{ height }}, {{ fps }} bps, {{ rtime }} sek", "MODEL": "<PERSON><PERSON><PERSON><PERSON>", "UNIT": "@:WFLOW.INTRO.UNIT", "PROCEDURE": "@:WFLOWEDIT.OPT1.P", "STEP": "Etape", "BUTTON": {"BACK": "Retour au sommaire", "CONTINUE": "@:WFLOW.INTRO.BUTTON.CONTINUE", "FIN": "Terminer!", "REWIND": "Étape   ", "FORWARD": "Étape   ", "PROCALL": "Tout modifier"}, "MEASURES": "@:WFLOW.INTRO.MEASURES", "MEASUREINP": "Résultat entré...", "INPLOCKED": "<PERSON><PERSON><PERSON><PERSON>", "TOOLS": "@:FRAME.MANAGE.TOOL", "TOOLCHOOSE": "Choisir type d'outil...", "DOCS": "Documents  ", "VIEW": {"TIMER": {"TITLE": "Processing time", "FINISHED": "The processing time for this procedure is now over!"}, "INFO": "Description", "IMAGES": "Images ({{ numi }})", "DOCS": "Documents ({{ numd }})"}}, "INPUT": {"VALUECMP1": "Valeur doit être inférieure à {{val}}", "VALUECMP2": "Valeur doit être inférieure ou égale à {{val}}", "VALUECMP3": "Valeur doit être égale à {{val}}", "VALUECMP4": "Valeur doit être supérieure ou égale à {{val}}", "VALUECMP5": "Valeur doit être supérieure à {{val}}", "CHECKCMP1": "Veuillez vérifier que votre valeur mesurée est inférieure à {{val}}, puis sélectionnez en conséquence «Oui» ou «Non»", "CHECKCMP2": "Veuillez vérifier que votre valeur mesurée est inférieure ou égale à {{val}}, puis sélectionnez en conséquence «Oui» ou «Non»", "CHECKCMP3": "Veuillez vérifier que votre valeur mesurée est égale à {{val}}, puis sélectionnez en conséquence «Oui» ou «Non»", "CHECKCMP4": "Veuillez vérifier que votre valeur mesurée est supérieure ou égale à {{val}}, puis sélectionnez en conséquence «Oui» ou «Non»", "CHECKCMP5": "Veuillez vérifier que votre valeur mesurée est supérieure à {{val}}, puis sélectionnez en conséquence «Oui» ou «Non»", "TARGET": "Valeur cible", "STATISTIC": "Veuillez entrer la valeur mesurée; il n'y aura pas d'examen de la valeur.", "THRESH": "<PERSON><PERSON><PERSON>", "VALUERNG1": "La valeur est entre", "VALUERNG2": "et  ", "TEXTLEN1": "Le texte est au minimum", "TEXTLEN2": "caractères long", "TEXTPAT": "Ajustement de texte dans un modèle fixe", "EXPNO": "Non' est attendu", "EXPYES": "<PERSON><PERSON>' est attendu", "EXPBOTH": "O<PERSON>' ou 'Non' est attendu", "OPTSKIP": "Cette mesure est facultative. Sauter?", "YES": "O<PERSON>  ", "NO": "Non", "INPUT": {"VALUE": "Veuillez écrire la valeur mesurée ci-dessous:", "TEXT": "Veuillez écrire le texte à taper ci-dessous:", "LIST": "Veuillez sélectionner la valeur appropriée:", "BOOL": "Veuillez choisir soit 'Oui' ou 'Non':", "MATRIX": "Veuillez remplir le tableau ci-dessous, puis cliquez sur \"Vérifier\" pour calculer le résultat:"}, "RESULT": "Résultat", "STATUS": {"TITLE": "Statut", "TODO": "VIDE", "PASS": "OK", "FAIL": "PAS CORRECTE", "SKIP": "PASSÉ", "INV": "INVALIDE", "ERROR": "ERREUR", "WARN": "PAS CORRECTE"}, "BUTTON": {"CLOSE": "<PERSON><PERSON><PERSON>", "CLEAR": "<PERSON><PERSON><PERSON><PERSON>", "CANCEL": "Annuler", "ADDCOMM": "Commentaire", "EDITCOMM": "Commentaire", "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "CONTINUE": "Contrôler & en avant"}, "TEXTTIMER": {"START": "Entrez le temps de début", "STOP": "Entrez le temps de fin", "STOPLT": "Entrez le temps d'arrêt; il est attendu que le temps sera à {{time}} minimum", "STOPGT": "Entrez le temps d'arrêt; il est attendu que le temps sera supérieur à {{time}} minimum", "REDUCE": "moins", "MIN": "min"}, "NOINPUT": "Le résultat de cette mesure est calculé à partir d'autres mesures."}}, "WFLOWEDIT": {"OPT1": {"CT": "Type de contrôle", "MD": "<PERSON><PERSON><PERSON><PERSON>", "DT": "Type d'appareil", "P": "Procédure", "S": "Etape", "M": "Mesurage", "CO": "Table de configuration"}, "OPT2": {"SEL": "choisi", "NSEL": "pas choisi", "QUEUED": "pré<PERSON>  ", "NQUEUED": "pas prévu", "FINPASS": "termin<PERSON> <PERSON>", "FINFAIL": "terminé & écho<PERSON>", "SKIP": "sauté", "NSKIP": "pas sauté", "OMIT": "omis", "NOMIT": "pas omis", "YES": "mis sur 'Oui'", "NO": "mis sur 'Non'", "ACTIVE": "actif", "NACTIVE": "pas actif"}, "OPT3": {"SKIP": "sauté", "OMIT": "omis"}, "TOOLTIP": {"REMMOD": "<PERSON><PERSON><PERSON> r<PERSON><PERSON>", "ADDMOD": "<PERSON><PERSON><PERSON> règle"}, "LOADING": "Charge éditeur...", "TITLE": "Editeur pour les règles de flux", "ACTIVER": "<PERSON><PERSON><PERSON>s", "ADDRULE": "Ajouter nouvelle règle", "RULE1": "Si", "RULE2": "avec code", "RULE3": "est", "RULE4": ", puis"}, "SERVER": {"ERROR": {"TITLE": "Erreur!", "VALIDATION": {"MEASUREMENTERRORCATEGORY": {"NAME": {"SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"}}, "USERGROUP": {"NAME": {"NOTNULL": "Un nom de groupe doit être indiqué", "UNIQUE": "Le nom du groupe doit être unique; aucun autre groupe peut avoir le même nom", "MATCH": "Le nom du groupe ne peut contenir que des lettres, des chiffres et des caractères de soulignement; il doit contenir au moins trois caractères"}, "LEVEL": {"NOTNULL": "Un niveau doit être spécifié", "NUMBER": "Le niveau du groupe doit être un nombre entier positif entre 1 et 1000"}}, "USER": {"USERNAME": {"NOTNULL": "Un nom d'utilisateur doit être indiqué", "UNIQUE": "Le nom d'utilisateur doit être unique; qu'aucun autre utilisateur ne peut avoir le même nom", "MATCH": "Le nom d'utilisateur ne doit contenir que des lettres, des chiffres et des caractères de soulignement; il doit contenir au moins trois caractères"}, "PASSHASH": {"NOTNULL": "Un passhash doit être indiqué", "MATCH": "Le Passhash n'est pas pris en charge par le mécanisme de mot de passe actuel"}, "REALNAME": {"NOTNULL": "Le vrai nom de l'utilisateur doit être affiché", "MINLEN": "Le vrai nom de l'utilisateur doit contenir au moins trois caractères"}, "USERGROUP_ID": {"NOTNULL": "Le groupe d'utilisateurs doit être déterminé"}}, "TOOLTYPE": {"CODE": {"UNIQUE": "Le code doit être unique; aucun autre type d'outil doit avoir le même code", "SHORT": "Le code doit contenir au moins deux caractères", "INVALID": "Le code ne peut contenir que des lettres, des chiffres, des périodes, des virgules, des caractères de soulignement et des tirets"}, "TITLE": {"INVALID": "Le bloc de langue pour le titre est invalide", "INCOMPLETE": "Dans le bloc de langue pour le titre manque l'entrée pour '{{ lcode}}'", "SHORT": "L'entrée de lanque '{{ lcode }}' pour le titre est trop court (< {{ minlength }})"}, "DESCRIPTION": {"INVALID": "Le bloc de langue pour la description est invalide"}}, "TOOLUNIT": {"CODE": {"UNIQUE": "Le code doit être unique; aucune autre unité d'outil peut avoir le même code à l'intérieur du type d'outil", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}}, "PROCEDURE": {"CODE": {"UNIQUE": "Le code doit être unique; aucune autre procédure peut avoir le même code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "STEP": {"CODE": {"UNIQUE": "Le code doit être unique; aucune autre étape peut avoir le même code dans la procédure", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "MEASURE": {"CODE": {"UNIQUE": "Le code doit être unique; aucune autre mesure ne peut avoir le même code dans l'étape", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "CALCULATION": {"OPTMISS": "La case «Obligatoire» doit être réglé soit sur 'Oui' ou 'Non'", "INVMTYPE": "Le type de mesure est invalide", "AINVVAL": "Pour la valeur cible et la déviation, des numéros valides doivent être précisés; l'écart doit être positif", "BINVVAL": "Les numéros valides pour la valeur minimale et maximale doivent être donnés", "CINVVAL": "La longueur minimale de texte doit être un nombre entier positif ou 0", "EMPTYVAL": "Les valeurs de liste entrées ne doivent pas être vides", "DMISSING": "Une expression régulière comme modèle doit être donné", "DINVREGEXP": "L'expression régulière est invalide", "EINVVAL": "La valeur attendue est invalide; soit «Oui», «Non» ou «Deux» doit être sélectionné", "JINVVAL": "Un numéro valide doit être spécifié comme numéro de référence", "JINVCMP": "Un opérateur de comparaison valide doit être spécifié", "HINVVAL": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL", "HINVCMP": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP", "OINVVAL": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL", "OINVCMP": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP", "XINVMTRX": "Définition de la matrice invalide; la taille n'est pas correctement spécifiée ou est invalide", "XINVFORM": "Aucune formule matricielle spécifiée", "XVARIOUS": "Erreur inconnue lors de la vérification de la formule matricielle", "XSYNTAX": "Erreur de syntaxe trouvé lors de la vérification de la formule matricielle ; veuillez prêter attention aux erreurs de frappe", "XINVVAR": "Trouvé nom de variable invalide lors de la vérification de la formule matricielle", "XINVFUN": "Trouvé méthode inconnue lors de la vérification de la formule matricielle", "XINVUSE": "Une ou plusieurs méthodes qui sont utilisées sont invalides (par exemple, un au lieu de deux arguments)", "MCODEMISSING": "Le code de regroupement doit être défini pour ce type de mesure, et doit contenir au moins trois caractères"}}, "CHECKTYPE": {"CODE": {"UNIQUE": "Le code doit être unique; aucun autre type de contrôle peut avoir le même code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "DEVICETYPE": {"CODE": {"UNIQUE": "Le code doit être unique; aucun autre type d'outil peut avoir le même code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "MODEL": {"CODE": {"UNIQUE": "Le code doit être unique; aucun autre modèle ne peut avoir le même code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "DEVICETYPE_ID": {"NULL": "Le type de matériel doit être réglé", "NOREF": "Le type de matériel {{refid}} est invalide ou n'existe pas"}}, "UNIT": {"CODE": {"UNIQUE": "Le code doit être unique; aucune autre unité peut avoir le même code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "COMMENT": {"INVALID": "Le bloc de langue du client est invalide"}, "MODEL_ID": {"NULL": "Le modèle doit être réglé", "NOREF": "Le modèle {{ refid }} n'est pas valide ou n'existe pas"}}, "SETTINGS": {"VALUE": {"INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"}}, "TYPE": "Le serveur rapporte une ou plusieurs erreurs de validation de données:"}, "INVMOVIE": {"TYPE": "La vidéo téléchargée est invalide", "TEXT": "La vidéo téléchargée a été testée et évaluée étant invalide. EIle ne peut pas être utilisée dans cette application."}, "INVPDF": {"TYPE": "Le document PDF téléchargé est invalide", "TEXT": "Le document PDF téléchargé a été testé et évalué étant invalide. Il ne peut pas être utilisé dans cette application."}, "CHECKACTION": {"TYPE": "Une erreur est survenue lors de cette action", "INVALIDPHASE": "Cette action ne doit pas être effectuée dans cette phase", "REQFAILED": "Certaines conditions de cette action ne sont pas remplies", "INVCOMMAND": "Commande non valide"}, "STEPACTION": {"TYPE": "@:SERVER.ERROR.CHECKACTION.TYPE", "INVALIDPHASE": "@:SERVER.ERROR.CHECKACTION.INVALIDPHASE", "REQFAILED": "@:SERVER.ERROR.CHECKACTION.REQFAILED", "INVCOMMAND": "@:SERVER.ERROR.CHECKACTION.INVCOMMAND"}, "LOGIN": {"TYPE": "<PERSON><PERSON><PERSON> login", "USERUNKNOWN": "Le nom d'utilisateur n'existe pas dans la banque de données", "NOTINITIALIZED": "Utilisateur non initialisé", "WRONGPASSWORD": "Le mot de passe est incorrect", "NOTLOGGEDIN": "L'utilisateur n'est pas connecté", "USERLOCKED": "Utilisateur désactivé"}, "NOTFOUND": {"TYPE": "L'objet n'a pas été trouvé", "TEXT": "L' objet {{class}} avec identité {{id}} n'a pas été trouvé dans la banque de données", "CHECKSTEXT": "L'étape  #{{sid}} dans le contrôle (ID: {{cid}}) n'a pas été trouvé dans la banque de données", "CHECKMTEXT": "Le mesurage #{{mid}} dans étape #{{sid}} dans contrôle (ID: {{cid}}) n'a pas été trouvé dans la banque de données"}, "STEPTYPE": {"TYPE": "Erreur en mettant le type d'étape", "HASMEASURES": "Le type de l'étape {{id}} ne peut pas être réglé sur «Manuel», aussi longtemps que les mesures sont enregistrées sur l'étape."}, "ACCDENIED": {"TYPE": "<PERSON><PERSON>ès refusé", "TEXT": "Vos droits d'accès ne sont pas suffisants pour cette action."}, "INVMEDIA": {"TYPE": "Type de support télécharg<PERSON> invalide ou inconnu", "TEXT": "Le fichier multimédia téléchargé a un type invalide; le serveur ne peut pas enregistrer les données"}, "IMPORT": {"TYPE": "Erreur d'importation", "WRONGTYPE": "Le fichier d'importation ({{fname}}) est le mauvais type. Une importation du type {{req}} a été demandé, le fichier contient le type {{est}}.", "BADCRC": "Le fichier d'importation ({{fname}}) a été modifié.  Que des fichiers originaux, non modifiés peuvent être importés.", "INVALIDFILE": "Le fichier ({{fname}}) est invalide; soit il a été changé, soit vous avez choisi le mauvais fichier."}}}, "EDITOR": {"LOADING": "Editeur charge....", "CODECHNG": {"TITLE": "Code changé!", "TEXT": "La modification du code d'un objet doit être évitée, puisque les règles de flux peuvent être altérées ou rendues invalides. Veuillez ne procéder si l'objet est certainement pas utilisé dans les flux de travail."}}, "VERSIONING": {"EDIT": {"VERSIONED": "<PERSON><PERSON> (V. {{ vers }})", "NEW": "Edite (nouveau)"}, "VERSION": "Version {{ vers }}", "LASTCHG": "dernier changement du {{ realname }} ({{ username }}) le {{ date }}"}, "WFMODIFIER": {"NOMODIFIERS": "Pas d'instructions de flux défini", "HASMODIFIERS": "Défini {{ num }} instructions de flux:", "CT": {"SELECT": {"STD": "Omettre si utilisé dans un contrôle avec type de contrôle {{code}}", "INV": "Omettre si n'est pas utilisé dans un contrôle avec type de contrôle {{code}}"}}, "MD": {"SELECT": {"STD": "Omettre si utilisé pour modèle {{code}}", "INV": "O<PERSON>re si n'est pas utilisé pour modèle {{code}}"}}, "DT": {"SELECT": {"STD": "Omettre si utilisé pour un modèle avec type d'appareil {{code}}", "INV": "Omettre si n'est pas utilisé pour un modèle avec type d'appareil  {{code}}"}}, "CO": {"SELECT": {"STD": "Sauter si l'entrée de table de configuration {{code}} est active", "INV": "Sauter si l'entrée de table de configuration {{code}} est inactive"}}, "P": {"INQUEUE": {"STD": "Omettre si la procédure {{ code }} est également utilisée dans le contrôle", "INV": "Omettre si la procédure {{ code }} n'est pas utilisée dans le contrôle"}, "PASS": {"OMIT": "Omettre si la procédure {{ code }} a été terminée et réussie", "SKIP": "Sauter si la procédure {{ code }} a été terminée et réussie"}, "FAIL": {"OMIT": "Omettre si la procédure {{ code }} a été terminée et a échoué", "SKIP": "Sauter si la procédure {{ code }} a été terminée et a échoué"}, "SKIP": {"OMIT": {"STD": "Omettre si la procéure {{ code }} a été sautée", "INV": "Omettre si la procéure {{ code }} n'a pas été sautée"}, "SKIP": {"STD": "Sauter si la procéure {{ code }} a été sautée", "INV": "Sauter si la procéure {{ code }} n'a pas été sautée"}}, "OMIT": {"OMIT": {"STD": "Omettre si la procédure {{ code }} a été omise", "INV": "Omettre si la procédure {{ code }} n'a pas été omise"}, "SKIP": {"STD": "Sauter si la procédure {{ code }} a été omise", "INV": "Sauter si la procédure {{ code }} n'a pas été omise"}}}, "S": {"PASS": {"OMIT": "Omettre si l'étape {{ code }} a été terminée et a réussie", "SKIP": "Sauter si l'étape {{ code }} a été terminée et a réussie"}, "FAIL": {"OMIT": "Omettre si l'étape {{ code }} a été terminée et a échoué", "SKIP": "Sauter si l'étape {{ code }} a été terminée et a échoué"}, "SKIP": {"OMIT": {"STD": "Omettre si l'étape {{ code }} a été sautée", "INV": "Omettre si l'étape {{ code }} n'a pas été sautée"}, "SKIP": {"STD": "Sauter si l'étape {{ code }} a été sautée", "INV": "Sauter si l'étape {{ code }} n'a pas été sautée"}}, "OMIT": {"OMIT": {"STD": "Omettre si l'étape {{ code }} a été omise", "INV": "Omettre si l'étape {{ code }} n'a pas été omise"}, "SKIP": {"STD": "Sauter si l'étape {{ code }} a été omise", "INV": "Sauter si l'étape {{ code }} n'a pas été omise"}}}, "M": {"PASS": {"OMIT": "Omettre si l'étape/la mesure {{ code }} a été terminée et réussie", "SKIP": "Sauter si l'étape/la mesure {{ code }} a été terminée et réussie"}, "FAIL": {"OMIT": "Omettre si l'étape/la mesure {{ code }} a éte terminée et a échoué", "SKIP": "Sauter si l'étape/la mesure {{ code }} a éte terminée et a échoué"}, "YES": {"OMIT": "O<PERSON>re si l'étape/la mesure {{ code }} a répondu 'Oui'", "SKIP": "Sauter si l'étape/la mesure {{ code }} a répondu 'Oui'"}, "NO": {"OMIT": "Omettre si l'étape/la mesure {{ code }} a répondu 'Non'", "SKIP": "Sauter si l'étape/la mesure {{ code }} a répondu 'Non'"}, "SKIP": {"OMIT": {"STD": "Omettre si l'étape/la mesure {{ code }} a été sautée", "INV": "Omettre si l'étape/la mesure {{ code }} n'a pas été sautée"}, "SKIP": {"STD": "Sauter si l'étape/la mesure {{ code }} a été sautée", "INV": "Sauter si l'étape/la mesure {{ code }} n'a pas été sautée"}}, "OMIT": {"OMIT": {"STD": "Omettre si l'étape/la mesure {{ code }} a été omise", "INV": "Omettre si l'étape/la mesure {{ code }} n'a pas été omise"}, "SKIP": {"STD": "Sauter si l'étape/la mesure {{ code }} a été omise", "INV": "Sauter si l'étape/la mesure {{ code }} n'a pas été omise"}}}}, "ERROR": {"CLIENT": {"TITLE": "Une erreur grave a eu lieu....", "TOTTL": "Timeout...", "ADTTL": "Accès refusé...", "DETAILS": "Détails:", "BACK": "Retour à l'identification", "MSG": {"TIMEOUT": "La session en cours a été interrompue en raison d'un délai d'attente. Veuillez vous connecter à nouveau et continuer.", "ACCDENY": "L'accès est interdit en raison de l'absence de droits ou privilèges. Veuillez vous connecter à nouveau et continuer.", "SEVREST": "Une erreur de serveur grave a eu lieu; le processus ne peut pas être poursuivi. Veuillez vous reconnecter ou changer à travers de la barre de titre à une autre page.", "HTTP404": "@:ERROR.CLIENT.MSG.SEVREST", "HTTP500": "@:ERROR.CLIENT.MSG.SEVREST"}}}, "LOGIN": {"TITLE": "<PERSON><PERSON>", "USERNAME": "Nom d'utilisateur", "PASSWORD": "Mot de passe", "BUTTON": "@:LOGIN.TITLE"}, "UI": {"BUTTONS": {"MEDITOR": {"RESET": "@:WFLOW.INPUT.BUTTON.CLEAR", "ADDMSEL": "{{ num }} Ajouter entrée(s)", "ADDMSELTEST": "Test {{ num }} item(s)", "SAVE": "Enregistrer", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SELECT": "<PERSON><PERSON><PERSON><PERSON><PERSON>  ", "SELALL": "<PERSON><PERSON>", "DESELALL": "Retour"}, "ALERT": {"OK": "OK", "YES": "@:WFLOW.INPUT.YES", "NO": "@:WFLOW.INPUT.NO"}}, "MEDITOR": {"MAXSEL": "Il est possible de sélectionner encore {{ num }} entrée(s)", "NOMORESEL": "Le nombre maximum d'entrées sélectionnés est atteint"}}, "VIEWER": {"IMAGE": {"TITLE": "Visionneur", "ZOOM": {"FULL": "Vue plein écran", "FIT": "Encadré", "STEP": "Zoom {{ factor }}x", "MAX": "Taille originale"}}, "PDF": {"TITLE": "Affichage PDF"}, "VIDEO": {"TITLE": "Le<PERSON>eur vid<PERSON><PERSON>"}}, "TLISTEDIT": {"TOOLTIP": {"DEL": "Supprimer entrée", "MEDMGR": "<PERSON><PERSON><PERSON><PERSON> le Media Manager pour cette entrée", "EDIT": "Editer entr<PERSON>", "NEW": "Etablir nouvelle entrée"}, "LOADING": "Charger éditeur...", "DISABLED": "Désactivé!", "CNT": {"CHECK": "{{ cnt }} Contr<PERSON>le (s)", "MODEL": "{{ cnt }} <PERSON><PERSON><PERSON><PERSON> (s)", "IMAGES": "{{ cnt }} Image (s)", "DOCS": "{{ cnt }} Document (s)"}, "NEW": "Nouvelle entrée", "CODE": "Code", "TITLE": "Titre", "DESC": "Description", "DISSET": {"TITLE": "Désactivé", "TRUE": "Cette entrée est désactivée et ne peut plus être utilisée.", "FALSE": "Cette entrée est active."}, "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "ADD": "Ajouter  ", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "MEDITOR": {"GROUP": "Groupe", "USER": "Utilisa<PERSON>ur"}, "DASHBLOCK": {"TOOLTIP": {"CLOSE": "Enlever le bloc", "GOTO": "Aller à l'entrée", "SETFILTER": "Editer les réglages de filtre", "EDITTITLE": "Changer le titre du bloc"}}, "DROPBOX": {"INTRO": "Pour l'importation faire glisser un fichier d'export dans cette case!", "UPLOADING": "Télécharger {{ num }} fichier (s)", "REMAINING": "{{ num }} <PERSON><PERSON><PERSON> (s) dans la file d'attente", "SUCCESS": "{{ num }} <PERSON><PERSON><PERSON> (s) téléchargé (s) avec succès", "ERRORS": "{{ num }} <PERSON><PERSON><PERSON> (s) avec erreurs"}, "PRV": {"MNGMUC": {"TTL": "Modifier modèles/unités/cheques", "DSC": "Peut accéder aux fenêtres de modification des modèles, unités et contrôles"}, "MNGPSM": {"TTL": "Modifier procédures", "DSC": "Peut accéder aux fenêtres de modification des procédures, étapes et mesures"}, "MNGTOL": {"TTL": "Modifier types d'outil", "DSC": "Peut accéder aux fenêtres de modification des types d'outil"}, "MNGPAR": {"TTL": "Modifier paramètres", "DSC": "Peut accéder aux fenêtres de modification des paramètres, types d'appareils et types de contrôle et modifier les paramètres facultatifs"}, "MNGUSR": {"TTL": "Modifier utilisateurs", "DSC": "Peut accéder à la fenêtre de gestion des utilisateurs"}, "MNGALL": {"TTL": "Tout modifier", "DSC": "Peut accéder à toutes les fenêtres de modification (collection de privilèges)"}, "EDTMOD": {"TTL": "<PERSON><PERSON><PERSON> mod<PERSON>", "DSC": "Peut éditer des modèles existants"}, "EDTUNT": {"TTL": "Éditer unités", "DSC": "Peut éditer des unités existantes"}, "EDTPSM": {"TTL": "É<PERSON>er procé<PERSON>", "DSC": "Peut éditer des procédures existantes, y compris modifier leurs étapes et mesures (les étapes et les mesures peuvent également être supprimées)"}, "EDTTTY": {"TTL": "Éditer types d'outil", "DSC": "Peut éditer des types d'outil existants"}, "EDTALL": {"TTL": "<PERSON><PERSON>", "DSC": "Peut éditer tous les objets existants (collection de privilèges)"}, "CRTMOD": {"TTL": "<PERSON><PERSON><PERSON> m<PERSON>", "DSC": "Peut créer de nouveaux modèles"}, "CRTUNT": {"TTL": "Créer unités", "DSC": "Peut créer de nouvelles unités"}, "CRTPSM": {"TTL": "<PERSON><PERSON><PERSON> pro<PERSON>", "DSC": "Peut créer de nouvelles procédures"}, "CRTTOL": {"TTL": "<PERSON><PERSON><PERSON> out<PERSON>", "DSC": "Peut entrer de nouveaux outils sous types d'outil existants"}, "CRTTTY": {"TTL": "<PERSON><PERSON><PERSON> types d'outil", "DSC": "Peut créer de nouveaux types d'outil"}, "CRTCHK": {"TTL": "<PERSON><PERSON><PERSON> con<PERSON>", "DSC": "Peut établir de nouveaux tests ou supprimer des contrôles qui n'ont pas encore débuté"}, "CRTALL": {"TTL": "<PERSON><PERSON> c<PERSON>", "DSC": "Peut créer de nouveaux objets de tout type (collection de privilèges)"}, "DELMOD": {"TTL": "<PERSON><PERSON><PERSON><PERSON> mod<PERSON>", "DSC": "<PERSON>eut supprimer ou désactiver des modèles"}, "DELUNT": {"TTL": "Supprimer unités", "DSC": "Peut supprimer ou désactiver des unités"}, "DELPRC": {"TTL": "Supprimer procédures", "DSC": "Peut supprimer ou désactiver des procédures"}, "DELTTY": {"TTL": "Supprimer types d'outil", "DSC": "Peut supprimer ou désactiver des types d'outil"}, "DELTOL": {"TTL": "Supprimer outils", "DSC": "Peut supprimer ou désactiver des outils"}, "DELCHK": {"TTL": "<PERSON><PERSON><PERSON><PERSON> contr<PERSON><PERSON>", "DSC": "Peut supprimer des contrôles"}, "DELALL": {"TTL": "<PERSON>ut supprimer", "DSC": "<PERSON><PERSON><PERSON> supprimer ou désactiver les objets de tous les types (collection de privilèges)"}, "USRMGO": {"TTL": "@:PRV.MNGUSR.TTL", "DSC": "Peut modifier les utilisateurs des niveaux inférieurs (ajouter, modifier, désactiver)"}, "USRMGA": {"TTL": "Modifier tous les utilitsateurs", "DSC": "Peut modifier tous les utilisateurs (ajouter, modifier, désactiver)"}, "GRTPRO": {"TTL": "Donner ses propres privilèges", "DSC": "Peut donner ses propres privilèges à d'autres utilisateurs"}, "GRTPRA": {"TTL": "Donner tous les privilèges", "DSC": "Peut donner tous les privilèges disponibles à d'autres utilisateurs"}, "GRTTOG": {"TTL": "Donner des privilèges de groupe", "DSC": "Peut également donner des privilèges à des groupes (expansion du privilège)"}, "GRPCRT": {"TTL": "Créer groupes", "DSC": "Peut créer et éditer des groupes"}, "WFLMNG": {"TTL": "Gérer flux de travail", "DSC": "Peut gérer des tests et des processus de tests, commenter des tests et consulter des informations élargies"}, "WFLREG": {"TTL": "Se connecter aux flux de travail", "DSC": "Peut se connecter à des flux de travail disponibles et appropriés, ou se déconnecter des flux de travail auto-enregistrés"}, "CHGCOD": {"TTL": "Changer la valeur de code", "DSC": "Peut editer la valeur de code lors du changement de divers objets (privilège en expansion)"}, "FINALZ": {"TTL": "Finaliser version", "DSC": "Peut finaliser les versions actuelles des objets (disponible uniquement si les droits d'édition appartenant à l'objet sont possédés; expansion du privilège)"}, "MODVRS": {"TTL": "Modifier version", "DSC": "Peut modifier une version finalisée d'un objet (spelling mistakes only)"}, "TRUSER": {"TTL": "Utilisateur expérimenté", "DSC": "L'utilisateur est expérimenté dans le système et peut également utiliser certaines fonctions d'accélération"}, "TKOVER": {"TTL": "Reprendre les tâches assignées", "DSC": "Selon aptitude l'utilisateur peut reprendre les tâches pour lesquelles d'autres utilisateurs se sont déjà enregistrées."}, "GLSRCH": {"TTL": "Accès à la recherche globale", "DSC": "L'utilisateur peut utiliser la recherche globale."}, "MNGNTC": {"TTL": "Afficher et modifier des rapports d'erreur", "DSC": "L'utilisateur est autorisé à accéder et modifier des rapports d'erreur."}, "MNGCFG": {"TTL": "Afficher et modifier des tables de configuration", "DSC": "L'utilisateur est autorisé à visualiser et modifier des tables de configuration"}, "MNGCFE": {"TTL": "Modifier des configurations pour les tests", "DSC": "L'utilisateur est autorisé à effectuer des configurations sur les tests."}}, "PDF": {"MEASUREMENTERRORREPORT": {"TITLE": "Measurement error report", "PROCEDURE": "Procedure", "STEP": "Step", "MEASURE": "Measure", "MODEL": "Model", "UNIT": "Unit", "VALUE": "Value", "SAVEDBY": "Saved by", "ERRORCATEGORY": "Error category", "STATUS": {"TITLE": "State", "UNPROC": "Left out", "PASSED": "Passed", "FAILED": "Failed", "INVALID": "Invalid", "ERROR": "Error"}, "EXPINPUT": {"TARGET": "@:wflow_input_target", "THRESH": "@:WFLOW.INPUT.THRESH", "VALUERNG1": "@:WFLOW.INPUT.VALUERNG1", "VALUERNG2": "@:WFLOW.INPUT.VALUERNG2", "VALUECMP1": "@:WFLOW.INPUT.VALUECMP1", "VALUECMP2": "@:WFLOW.INPUT.VALUECMP2", "VALUECMP3": "@:WFLOW.INPUT.VALUECMP3", "VALUECMP4": "@:WFLOW.INPUT.VALUECMP4", "VALUECMP5": "@:WFLOW.INPUT.VALUECMP5", "STATISTIC": "Measured value, no check took place", "CHECKCMP1": "Check if measured value is less than {{ val }}", "CHECKCMP2": "Check if measured value ist less or equal to  {{ val }}", "CHECKCMP3": "Check if measured value ist equal to {{ val }}", "CHECKCMP4": "Check if measured value is greater or equal to  {{ val }}", "CHECKCMP5": "Check if measured value is greater than {{ val }}", "TEXTLEN1": "@:WFLOW.INPUT.TEXTLEN1", "TEXTLEN2": "@:WFLOW.INPUT.TEXTLEN2", "REGEXP": "Checked text:", "EXPNO": "@:WFLOW.INPUT.EXPNO", "EXPYES": "@:WFLOW.INPUT.EXPYES", "EXPBOTH": "@:WFLOW.INPUT.EXPBOTH", "TEXTTIMERSTART": "Entered start time", "TEXTTIMERSTOP": "Entered stop time", "TEXTTIMERSTOPLT": "Entered start time: exptected time to {{ val }} mins", "TEXTTIMERSTOPGT": "Entered start time: expected time greater than {{ val }} mins", "CHOICELIST": "Choice list"}}, "BOOL": {"YES": "OUI", "NO": "NON"}, "MAINTTL": "Rapport de contrôle", "STATUS": {"OPEN": "OUVERT", "FAILED": "ÉCHOUÉ", "PASSED": "OK", "UNFIN": "A FAIRE", "PASS": "OK", "FAIL": "ÉCHOUÉ", "SKIP": "<PERSON>lar<PERSON>", "CANCELLED": "AVORTÉ", "WARNINT": "ÉCHOUÉ.<sup>*)</sup>", "WARNEXT": "OK <sup>*)</sup>"}, "USTATUS": {"OPEN": "OUVERT", "CLOSED": "<PERSON><PERSON><PERSON>  ", "DISCARDED": "<PERSON><PERSON><PERSON>  ", "ACLOSED": "Fermé & archivé", "ADISCARDED": "Rejeté & archivé"}, "MODEL": "@:WFLOWEDIT.OPT1.MD", "UNIT": "@:WFLOW.INTRO.UNIT", "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "DATE": {"SCHED": "@:WFLOW.INTRO.DATE.SCHED", "DUE": "@:WFLOW.INTRO.DATE.DUE", "NOSCHED": "@:WFLOW.INTRO.DATE.NOSCHED", "NODUE": "@:WFLOW.INTRO.DATE.NODUE", "START": "@:WFLOW.INTRO.DATE.START", "FIN": "@:WFLOW.INTRO.DATE.FIN", "NOSTART": "@:WFLOW.INTRO.DATE.NOSTART", "NOFIN": "@:WFLOW.INTRO.DATE.NOFIN"}, "USER": {"NOONE": "<PERSON><PERSON>", "ANY": "Non spécifié", "GRP": "Utilisateur du groupe %s"}, "VERSION": "@:PROCEDURE.VIEW.VERS", "STEP": {"ASSNC": "<i>Attribué à:</i> %s, <b>pas encore confirmé</b>", "ASSC": "<i>Attribué à:</i> %s, <i>confirmé de:</i> %s"}, "MEASURE": {"TOOL": "<i>Outil (type </i><b>%s</b>: %s) utilisé:</i> <b>%s</b>", "TOOLNOSEL": "<i>Outil utilisé (type </i><b>%s</b>: %s<i>) <b>pas encore entré</b></i>", "REP": "<i><PERSON><PERSON><PERSON> par </i><b>%s</b> <i>le</i> <b>%s</b>", "COMM": "<i>Commentaire:</i> %s", "RAW": "<i><PERSON>ur de matrice:</i> %s"}, "MINPUT": {"VALUECMP": "La valeur doit être %s %s", "CHECKCMP": "Oui' si la valeur mesurée est %s %s", "COMP": {"T1": "inférieur", "T2": "Inférieur ou égal", "T3": "<PERSON><PERSON>", "T4": "supérie<PERSON> ou <PERSON><PERSON>", "T5": "supérieur  "}, "THRESH": "Valeur cible: % s, écart % s", "VALUERNG": "La valeur doit être comprise entre% s et % s", "TEXTLEN": "Le texte doit contenir au moins% d caractères", "TEXTPAT": "Le texte doit correspondre à un modèle donné: % s", "CHOICE": "Une valeur doit être sélectionnée dans la liste", "EXP": {"NO": "@:WFLOW.INPUT.EXPNO", "YES": "@:WFLOW.INPUT.EXPYES", "BOTH": "@:WFLOW.INPUT.EXPBOTH"}, "STATISTICAL": "Pour les évaluations statistiques; toute valeur est acceptée", "TIMERA": "Chronométrage (Démarrer)", "TIMERS": "Chronométrage (fin) pour évaluations statistiques", "TIMERQ": {"T1": "Chronométrage (fin); le temps mesuré doit être inférieur à %d minutes", "T5": "Chronométrage (fin); le temps mesuré doit être supérieur à %d minutes"}, "TIMERC": {"T1": "Contrôle chronométrage; le temps mesuré doit être inférieur à %d minutes", "T5": "Contrôle chronométrage; le temps mesuré doit être supérieur à %d minutes"}}, "STATREPORT": "Statistique de mesurage", "PAGENO": "Page %d de %d", "STAT": {"CHECKINTRO": "Pour les statistiques, les contrôles suivants sont utilisés:", "CHECK": {"LINE1": "Contrôle <b>#%s</b> (%s) pour unité <b>%s</b> (%s), Mod<PERSON><PERSON> <b>%s: %s", "LINE2": "Statut de contrôle: <b>%s</b>, Statut d'unité: <b>%s</b>"}, "MSR1": "Mesurage <b>%s: %s</b>", "MSR2": "Étape <b>%s: %s</b>, Procédure <b>%s: %s</b>", "MSR2A": "Étape <b>%s: %s</b>", "MSR2B": "Procédure <b>%s: %s</b>", "MSR3S": "Règle: <b>%s</b>", "MSR3O": "Règle: <b>%s</b> (facultative, peut être ignorée)", "MSR4T": "Type d'outil: <b>%s: %s</b>", "MSR4N": "Type d'outil: <i>Pas d'outil utilisé</i>", "OLDV": "<Sup> *) </ sup> Une version plus ancienne de la mesure a été utilisée pendant le contrôle; les résultats ne peuvent éventuellement pas être compatibles.", "NODATA": "<PERSON><PERSON><PERSON> donnée disponible", "HDR": {"STATUS": "@:WFLOW.INPUT.STATUS.TITLE", "UNIT": "Unit", "USER": "@:MEDITOR.USER", "TOOL": "Outil", "VALUE": "<PERSON><PERSON>"}, "FINAL": {"INTRO": "Les chaînes d'identification suivantes ont été utilisées dans la génération de rapport. Vous pouvez les utiliser à nouveau pour générer le même rapport complet ou modifié:", "CHECKS": "<PERSON><PERSON><PERSON><PERSON>", "MEASURES": "Mesurages:"}, "TITLE": {"FRONT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATS": "Mesurage %s"}}, "COMMENT": {"TOOLTIP": "Afficher le rapport de commentaire", "TTL": "Rapport de commentaire", "FOOTER": {"USERS": "Tous les utilisateurs", "TOOLTYPE": "Type d'outil '%s' (%s)", "MODEL": "Modèle '%s' (%s)", "UNIT": "Unité %s", "CHECK": "Contrôle #%d (Unité %s, Modèle %s)", "STEP": "Etape '%s' V.%d (%s.%s)", "MEASURE": "Mesurage '%s' V.%d (%s.%s.%s)"}, "ELEMENT": {"USER": "Utilisateur %s (%s)", "TOOLTYPE": "Type d'outil %s", "TOOL": "Editer catégories d'outil", "UNIT": "Unité %s (%s)", "CHECK": "Contrôle #%d (Unité %s, Modèle %s)", "MSMNT": "Mesurage %s en étape %s, Procédure %s", "STEP": "Etape %s", "PROC": "Procédure %s", "MSMNTS": "Mesurage %s", "MEAS": "Mesurage %s"}}, "TUREPORT": {"HEADER": "Rapport d'utilisation d'outil", "FOOTER": "Type d'outil <b>%s</b>, Unité <b>%s</b>", "TITLE": {"MODEL": "<b>Modèle %s</b>, Unité %s", "TIME": "%s.<b>%s</b>"}, "CONTINUED": "<i>(continue)</i>", "ENTRY": {"MODEL": "Procédure <b>%s</b>, Étape <b>%s</b>, Mesurage <b>%s</b>", "TIMET": "<PERSON><PERSON><PERSON><PERSON> <b>%s</b>, <PERSON><PERSON> <b>%s</b>", "TIMEB": "<i>Procédure <b>%s</b>, Étape <b>%s</b>, Mesurage <b>%s</b></i>"}}, "HINT": {"CHECKWARN": {"INTERNAL": "Les résultats de mesure défaillants n'ont pas ou peu d'importance pour le fonctionnement de la machine.", "CUSTOMER": "Un résultat de la mesure qui n'a pas d'incidence sur le fonctionnement de la machine se trouve un peu en dehors des tolérances spécifiées."}}}, "CHANGELOG": {"ITEM": {"PROCEDURE": "Procédure", "STEP": "Étape", "MEASURE": "Mesure", "IMAGE": "Image", "DOCUMENT": "Document", "MODEL": "<PERSON><PERSON><PERSON><PERSON>", "CHECKTYPE": "Type de vérification"}, "TYPE": {"CREATE": "Nouvellement ajouté", "CHANGECODE": "Code modifié (initialement: {{ oldcode }})", "CHANGEFIELD": "Champ '{{ field }}' modifié", "CHANGESEQ": "Modification de l'ordre (plusieurs objets concernés)", "CHANGEPID": "Procé<PERSON><PERSON> de mise à niveau de la version {{ version }}", "FINALIZE": "Finalisé", "DELETE": "Supprimé", "CREATEATT": "Nouvellement ajouté à {{ tgt }}", "DELETEATT": "Supprimé de {{ tgt }}", "CREATEACTP": "<PERSON>cé<PERSON><PERSON> a<PERSON> au modèle", "DELETEACTP": "Procédure supprimée du modèle", "CREATEACTCT": "Type de vérification ajouté au modèle", "DELETEACTCT": "Type de contrôle supprimé du modèle", "PREALLOCATION": "Pré-allocations modifiées"}, "VIEWER": {"TITLE": "Liste des modifications", "EXTRODIRTY": "Les entrées marquées en bleu sont des modifications d'utilisateur directes qui nécessitent une finalisation pour être utilisées activement.", "EXTROPOSTFIN": "Les entrées marquées en rouge sont des modifications qui ont été faites après la finalisation de l'objet.", "NOENTRIES": "La liste des modifications est momentanément vide."}, "BUTTON": {"OPENDIALOG": "Liste des modifications", "CLOSE": "<PERSON><PERSON><PERSON>"}}, "GSEARCH": {"WIZARD": {"TITLE": "Recherche globale", "TEXT1": "Veuillez entrer votre terme de recherche. Le terme doit contenir au moins trois caractères, un astérisque ('*') est considéré comme un espace réservé, un symbole pipe (\"|\") au début ou à la fin du terme, précise que le texte doit commencer ou terminer avec le terme. Plusieurs termes de recherche peuvent être combinés en ajoutant le signe plus ('+') mais tous les termes doivent être dans le texte. Des majuscules / minuscules seront seulement considérées si un terme est précédé par un point d'exclamation (!).", "TEXT2": "Veuillez sélectionner les cases à travers lesquelles le terme sera recherché.", "TEXT3": "<PERSON><PERSON>illez sélectionner les langues à travers lesquelles le terme sera recherché (dans les cases multilingues).", "TEXT4": "Veuillez sélectionner quel restriction devrait être appliquée aux objets versionnés."}, "TYPE": {"CHECK": {"SELECT": "<PERSON><PERSON><PERSON><PERSON>  ", "DISPLAY": "<u><PERSON><PERSON><PERSON><PERSON> </u> <b>{{ object.id }}</b> (<i>{{ object.checktype.title.de }}</i>) pour unité <b>{{ object.unit.code}}</b>, <PERSON><PERSON><PERSON><PERSON> <b>{{ object.unit.model.code }}</b>"}, "MEASUREMENT": {"SELECT": "Enregistrements de mesure", "DISPLAY": "<u>Enregistrement de mesure</u> en contrôle <b>{{ object.check.id }}</b> (Unité {{ object.check.unit.code }}, <PERSON><PERSON><PERSON><PERSON> {{ object.check.unit.model.code }}) pour mesurer <b>{{ object.measure.code }}</b> en étape <b>{{ object.measure.step.code }}</b>, Procédure <b>{{ object.measure.step.procedure.code }}</b>"}, "CHECKTYPE": {"SELECT": "Types de contrôle", "DISPLAY": "<u>Type de contrôle</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"}, "DEVICETYPE": {"SELECT": "Types de matériel", "DISPLAY": "<u>Type de matériel</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"}, "MODEL": {"SELECT": "<PERSON><PERSON><PERSON><PERSON>", "DISPLAY": "<u><PERSON><PERSON><PERSON><PERSON> </u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>), Version {{ object.version || '(Edit)' }}"}, "UNIT": {"SELECT": "Unités  ", "DISPLAY": "<u>Unité</u> <b>{{ object.code }}</b> en modèle <b>{{ object.model.code }}</b>"}, "MEASURE": {"SELECT": "Mesures", "DISPLAY": "<u>Mesure </u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>) en étape <b>{{ object.step.code }}</b> / Procédure <b>{{ object.step.procedure.code }}</b>, Version {{ object.procedure.version || '(Edit)' }}"}, "STEP": {"SELECT": "Étapes", "DISPLAY": "<u>Étape</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>) en procédure <b>{{ object.procedure.code }}</b>, Version {{ object.procedure.version || '(Edit)' }}"}, "PROCEDURE": {"SELECT": "Procédures   ", "DISPLAY": "<u>Procédure </u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>), Version {{ object.version || '(Edit)' }}"}, "TOOLTYPE": {"SELECT": "Types d'outils", "DISPLAY": "<u>Type d'outil</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"}, "TOOLUNIT": {"SELECT": "Untiés d'outils", "DISPLAY": "<u>Unité d'outil</u> <b>{{ object.code }}</b> en type d'outil <b>{{ object.tooltype.code }}</b>"}, "USER": {"SELECT": "Utilisateurs", "DISPLAY": "<u>Utilisateur</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)"}, "NOTICE": {"SELECT": "Rapport d'erreur", "DISPLAY": "<u>Rapport d'erreur </u> <b>{{ object.id }}</b>"}, "CONFIGTABLE": {"SELECT": "Tables de configuration", "DISPLAY": "<u>Table de configuration</u> <b>{{ object.id }}</b>"}, "CONFIGENTRY": {"SELECT": "Tables de configuration – Entrées", "DISPLAY": "<u>Entrée </u> <b>{{ object.code_id }}</b> dans tableau de configuration <b>{{ object.configtable_id }}</b>"}}, "SCOPE": {"ALLALL": {"TEXT": "Aucune restriction (pleine recherche)", "TTIP": "Toutes les versions (incluant les non finalisées) seront recherchées et tous les résultats trouvés affichés."}, "ALLRECENT": {"TEXT": "Recherche complète, seul l'élément le plus récent", "TTIP": "Toutes versions (incluant celles non finalisées) seront recherchées, mais seul l'objet le plus récent (modifié en dernier lier) sera affiché."}, "FINALL": {"TEXT": "Objets finalisés", "TTIP": "Toutes les versions finalisées seront recherchées, tous les résultats trouvés seront affichés."}, "FINRECENT": {"TEXT": "Objets finalisés, seul élément le plus récent", "TTIP": "Toutes les versions finalisées seront recherchées, par contre seul l'objet le plus récent (modifié en dernier lieu) sera affiché."}, "LATESTV": {"TEXT": "Dernière version", "TTIP": "Seule la version finalisée la plus récente sera recherchée."}, "EDITV": {"TEXT": "Version éditée", "TTIP": "Seules les versions les plus actuelles et non finalisées seront recherchées."}}, "FIELD": {"ID": "Number", "COMMENT": "Commentaire", "CODE": "Code", "TITLE": "Titre", "DESCRIPTION": "Description / Remarque", "CUSTOMER": "Client", "USERNAME": "Nom d'utilisateur", "REALNAME": "Nom réel", "TEXT": "Description  ", "COL1": "Première colonne", "COL2": "Deuxième colonne", "COL3": "Troisième colonne", "COL4": "Quatrième colonne", "COL5": "Cinquième colonne", "COL6": "Sixième colonne", "DELETED": "Informations méta", "CALCULATION": "Liste de choix"}, "BUTTONS": {"NEWSEARCH": "Nouvelle recherche", "CLOSE": "<PERSON><PERSON><PERSON>"}, "RESULT": {"TITLE": "Résultats de recherche", "TEXT": "La dernière recherche a donné les résultats suivants, triés par date de modification:", "TOOMUCH": "(Note: Seuls les 50 premiers résultats sont affichés)", "NOMATCH": "Il n'y avait aucun résultat"}}, "NOTICES": {"TOOLTIP": {"SHOW": "<PERSON><PERSON><PERSON><PERSON>", "SHOWALL": "<PERSON><PERSON> afficher", "HIDE": "<PERSON>sp<PERSON><PERSON><PERSON>", "FILTER": "Filtrer par cette valeur", "NOFILTER": "<PERSON><PERSON><PERSON> le filtre"}, "EXPORT": {"TTL": "Exporter les rapports d'erreur", "TYPETEXT": "Veuillez sélectionner le type de fichier dans la liste à exporter:", "FILTERTEXT": "Veuillez indiquer la taille de la liste:", "CSV": "Comma Separated Values (CSV)", "JSON": "JavaScript Output Notation (JSON)", "XML": "Extensible Markup Language (XML)", "UNARCHIVED": "Tous les rapports non archivés", "ALL": "Tous les rapports (également les archivés)", "FILTERED": "Tous les rapports de la liste filtrée"}, "FILTEREDIT": {"TITLE": "Veuillez définir le filtre", "ID": "Veuillez spécifier la portée des ID qui doivent être affichés. Possibilité de marquer '<de> - <à>', '- <à>' ou '<de> -'. Exemple: '10 -20 '(tous les ID de 10 à 20); '-50' (tous les ID à 50).", "CATEGORY": "Veuillez sélectionner les catégories à afficher:", "PATH": "Veuillez entrer un terme de recherche qui sera vérifié par rapport au chemin de texte affiché:", "TEXT": "Veuillez entrer un terme de recherche qui sera vérifié par rapport à la description:", "ARTICLE": "Veuillez entrer un terme de recherche; celui-ci sera contrôlé contre le numéro de l'article (non: désignation de l'article). Si la désignation de l'article doit être contrôlée, veuillez introduire le terme de recherche, en transmettant la requête avec deux points ':':", "TIMELOSS": "Veuillez sélectionner les informations au sujet de la perte de temps qui seront affichées dans la liste:", "STATUS": "Veuillez sélectionner les statut qui doivent être affichées dans la liste:"}, "TITLE": {"PROBLREP": "Rapport d'erreur", "DESC": "Description", "PROPOSALS": "Propositions:", "CAT": "<PERSON><PERSON><PERSON><PERSON>", "TIMELOSS": "<PERSON><PERSON> de te<PERSON> (environ)", "ARTNO": "Numéro d'article", "ARTDESC": "Désignation", "PROBLREPNO": "Numéro de rapport d'erreur"}, "TEXT": {"DESC": "Veuillez ajouter au rapport d'erreur une description courte et significative; <PERSON><PERSON> pouvez le faire, le cas éch<PERSON>ant, en choississant parmi plusieurs propositions ou en entrant votre propre texte.", "CHOOSECAT": "Veuillez choisir une des catégories données correspondant au rapport d'erreur. Si vous avez perdu beaucoup de temps par la faute, entrez en sélectionnant un temps approprié.", "ARTICLE": "Si l'erreur concerne un article en particulier ou se rapportent à ce titre, vous pouvez ici spécifier le numéro de l'article et le nom.", "STTCHANGE": "Veuillez entrer un commentaire bref pour le changement d'un statut:"}, "BUTTON": {"USE": "Reprendre", "CANCEL": "<PERSON><PERSON><PERSON>", "SEND": "Envoyer", "CATEGORIES": "Editer catégories", "TEMPLATES": "Editer des propositions de textes", "EXPORT": "Exporter listes", "STT_12": "Commencer modification", "STT_21": "Annuler modification", "STT_25": "Terminer modification", "STT_52": "Recommencer modification", "STT_59": "Archiver", "CLOSE": "<PERSON><PERSON><PERSON>"}, "VIEW": {"LOCATION": "Chemin", "CATEGORY": "<PERSON><PERSON><PERSON><PERSON>", "ARTICLE": "Article", "TIMELOSS": "<PERSON><PERSON> de temps  ", "NOTEXT": "Indéterminé", "DESC": "Description", "ID": "<PERSON><PERSON><PERSON><PERSON>   ", "PATH": "Chemin", "TEXT": "Description", "STATUS": "Status"}, "TIMELOSS": {"15": "Environ 15 minutes", "30": "Environ 30 minutes", "60": "Environ 1 heure", "90": "Environ 1.5 heures", "120": "Environ 2 heures", "180": "Environ 3 heures", "240": "Plus que 4 heures"}, "ALERT": {"CATMISS": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> manque", "TEXT": "Une des catégories disponibles doit être choisie!"}, "DESCMISS": {"TITLE": "Description manque", "TEXT": "La description est manquante ou trop courte!"}, "THANKS": {"TITLE": "<PERSON><PERSON><PERSON> beaucoup!", "TEXT": "Le rapport d'erreur a été transmis et sera traité sous peu!"}, "CONFDEL": {"TITLE": "Vraiment effacer?", "TEXT1": "Vraiment supprimer l'entrée? La modification est irrévocable, les utilisations précédentes de cette entrée seront éventuellement modifiées.", "TEXT2": "Vraiment supprimer l'entrée? L'entrée sera supprimée de la liste et ne sera plus disponible pour l'utilisation future; toutefois, les anciennes utilisations restent inchangées."}}, "MODAL": {"EDITTEXT": {"TITLE": "Nouvelle entrée", "TEXT": "Veuillez éditer ici le texte pour l'entrée:"}, "NEWTEXT": {"TITLE": "Editer entr<PERSON>", "TEXT": "Veuillez saisir ici le texte pour l'entrée (au minimum trois caractères):"}, "SNIPPETCAT": {"TITLE": "Catégories de rapport d'erreur", "SNIPPET": "<PERSON><PERSON><PERSON><PERSON>"}, "SNIPPETDESC": {"TITLE": "Propositions pour rapport d'erreur", "SNIPPET": "Proposition de texte"}}, "STATUS": {"1": "<PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON><PERSON><PERSON>", "OPEN": "<b>soumis </b> von <b>{{ user }}</b> ({{ realname }}) am {{ time }}", "PROCESSED": "<b>modifié </b> durch <b>{{ user }}</b> ({{ realname }}) ab {{ time }}", "CLOSED": "<b>fermé </b> von <b>{{ user }}</b> ({{ realname }}) am {{ time }}", "ARCHIVED": "<b>archivé </b> am {{ time }}"}, "PATHTYPE": {"CHECKSTEP": "Etape de contrôle", "CHECKGENERAL": "Con<PERSON><PERSON><PERSON> (général)"}, "SEGMENT": {"MODEL": "<PERSON><PERSON><PERSON><PERSON> <b>{{ code }}</b>", "UNIT": "Unité <b>{{ code }}</b>", "CHECK": "Contr<PERSON>le <b>{{ id }}</b>", "PROCEDURE": "Procédure <b>{{ code }}</b>", "STEP": "Étape <b>{{ code }}</b>"}, "CORRECT": {"TITLE": "Correction", "DESCRIPTION": "Veuillez éditer une description:", "TIMELOSS": "Veuillez sélectionner une nouvelle valeur pour la perte de temps:", "CATEGORY": "<PERSON><PERSON><PERSON><PERSON>z sélectionner une nouvelle catégorie:", "ARTICLE1": "Veuillez éditer les numéros d'article (le nom du produit suit dans l'étape suivante):", "ARTICLE2": "Veuillez éditer la désignation d'article:"}}, "SNIPPET": {"TITLE": {"ADD": "ajouter  "}, "TOOLTIP": {"LOCK": "Ferme l'entrée. Utilisations antérieures restent conservées, cependant la sélection ne peut pas être continuée.", "UNLOCK": "Ouvre l'entrée: elle peut ensuite être à nouveau sélectionnée", "DELETE": "Supprime l'entrée après confirmation irrévocablement.", "EDIT": "Le texte de l'entrée peut ainsi être modifié.", "REORD": "L'entrée peut être ainsi ajouté ailleurs par Drag&Drop"}, "BUTTON": {"CLOSE": "<PERSON><PERSON><PERSON>"}}, "CONFIGTABLE": {"CONFIGTABLES": "Tables de configuration", "ALERT": {"CONFHEADER": "E<PERSON>-vous sûr?", "DELETE": "Vou<PERSON><PERSON>-vous désactiver cette entrée?"}, "EDITOR": {"TITLE": {"NEW": "Nouvelle entrée", "EDIT": "Modifier l'entrée"}}, "EDIT": {"TITLE": "Titre", "HEADER": "Rubrique", "BUTTON": {"CANCEL": "@:MEASURE.EDIT.BUTTON.CANCEL", "CLOSE": "@:MEASURE.EDIT.BUTTON.CLOSE"}}, "LIST": {"ENTRY": {"EDIT": "Modifier l'entrée", "DELETE": "Désactiver l'entrée", "UNDELETE": "Réactiver l'entrée", "BLOCKED": "Cette entrée est utilisée par un contrôle de flux."}, "RELATION": {"NONE": "pas d'attribution à un modèle", "MODEL": "suivant le modèle associé à:"}, "VERSION": "Version", "TOOLTIP": {"IMPORTTABLE": "Importer le tableau", "NEWTABLE": "Nouveau tableau", "GOTOUNITS": "Changer au modèle", "SHOWDEL": "Montrer les désactivés", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "CLONE": "<PERSON><PERSON><PERSON><PERSON> le tableau", "DELETE": "<PERSON>és<PERSON><PERSON> le tableau", "EDIT": "<PERSON><PERSON> le <PERSON>au", "REACTIVATE": "<PERSON><PERSON><PERSON><PERSON> le tableau"}, "TOPROW": ""}, "VIEW": {"CODE": "Code", "ACTIVE": "Actif", "DISABLED": "Désactivé"}, "CLONE": {"TITLE": "Dupliquer les tables de configuration", "TEXT": "Voulez-vous dupliquer la table de configuration {{ title }}?"}, "DELETE": {"TITLE": "Désactiver les tables de configuration", "TEXT": "Voulez-vous vraiment désactiver la table de configuration  {{ title }} ? Vous pouvez la réactiver à un moment ultérieur."}}, "SETTINGS": {"PROC": {"TAGS": {"TITLE": "<PERSON><PERSON><PERSON> attribut de procédure", "ADD": "Ajouter attributs", "DELETE": "Supprimer attribut", "EDIT": "Editer attribut", "COLLAPSE": "Collapse sub-attributes"}, "EDITTAG": {"TITLE": "Modifier attribut de procédure"}, "NEWTAG": {"TITLE": "Ajouter attribut de procédure"}, "TAGVALUE": {"ADD": "Ajouter valeur", "DELETE": "Supprimer valeur", "EDIT": "Editer valeur"}, "NEWTAGVALUE": {"TITLE": "Ajouter valeur"}, "EDITTAGVALUE": {"TITLE": "Modifier valeur"}}, "ALERT": {"CONFDEL": {"TITLE": "Supprimer un attribut de procédure", "TEXT": "Etes-vous sûr de vouloir supprimer l'attribut de procédure sélectionnée?"}}}, "MEASUREMENTERRORREPORT": {"TITLE": "Report Measurement errors", "MEASURES": {"PERIODFROM": "Choose the start of the period:", "PERIODTO": "Choose the end of the period:", "TEXT": "Choose the measurements. Only measurements which have been corrected will be displayed:"}, "SORT": {"TEXT": "Choose the sorting:", "ERRORCATEGORY": "Error category", "MEASURE": "Measure", "USER": "User", "UNIT": "Unit"}}}