{"FRAME": {"TITLE": "LeanLogic QA", "DASHBOARD": "Dashboard", "DASHBOARDS": "Dashboards", "MANAGE": {"TITLE": "Management", "MODEL": "Models", "UNIT": "Units", "PROCEDURE": "Procedures", "TOOL": "Tools", "DEVICETYPE": "Device types", "CHECKTYPE": "Check types", "USERS": "Users", "CHANGEPW": "Change Password", "NOTICES": "Problem reports", "CONFIGTABLE": "Configuration table", "SETTINGS": "Settings", "MEASUREMENTERRORCATEGORIES": "Measurement error categories", "MEASUREMENTERRORCATEGORIESREPORT": "Report Measurement errors"}, "LOGOUT": "Logout", "EDITDTYPE": {"TITLE": "@:FRAME.MANAGE.DEVICETYPE", "TEXT": "Add, edit and disable device types:"}, "EDITCTYPE": {"TITLE": "@:FRAME.MANAGE.CHECKTYPE", "TEXT": "Add, edit and disable check types:"}, "CHPWD": {"TITLE": "@:FRAME.MANAGE.CHANGEPW", "PW1": "Enter your new password:", "PW2": "Repeat password:", "NOMATCH": "Passwords do not match - operation cancelled!", "TOOSHORT": "Password too short - must be at least 3 characters long!", "OK": "Password changed."}, "GSEARCH": "Search"}, "DASHBOARD": {"TOOLTIP": {"PREVDB": "Previous Dashboard (Pressing the shift key while clicking will swap the current with the previous dashboard)", "NEXTDB": "Next Dashboard (Pressing the shift key while clicking will swap the current with the next dashboard)", "DELDB": "Remove Dashboard", "EDITDB": "Edit title of Dashboard", "ADDDB": "New Dashboard"}, "NEWBLOCK": {"TITLE": "New Dash", "TEXT": "Select the content of the new Dash below:"}, "WELCOME": "Welcome, {{ name }}!", "VERSION": "Version {{ version }}", "ADDDASH": {"BUTTON": "Add Dash"}, "ADDDB": {"TITLE": "Add new dashboard", "TEXT": "Please enter a title for the new dashboard. The title must be at least 3 characters long."}, "EDITDBNAME": {"TITLE": "Edit Dashboard name", "TEXT": "Please enter a new title for the dashboard. The title must be at least 3 characters long."}, "EDITBLKNAME": {"TITLE": "Edit Dashboard block name", "TEXT": "Please enter a new title for the block. The title must be at least 3 characters long."}, "DELETEDB": {"TITLE": "Are you sure?", "TEXT": "Really delete this dashboard?"}, "FILTERS": {"TITLE": "Edit block filters", "TEXT": "Please select an action:", "ACTENABLE": "Enable & Edit", "ACTEDIT": "Edit", "ACTDISABLE": "Disable", "TYPES": {"MODELSEL": {"NAME": "model selection filter", "TEXT": "Please select the models you want to use in this block"}}}, "TYPE": {"UCAM": {"TITLE": "Unfinished checks assigned to me", "LINET": "Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}", "LINEB": "Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>", "LINEBNS": "Model <b>{{ model.code }}: {{ model.title.en }}</b>"}, "UCMP": {"TITLE": "Unfinished checks that may be processed by me", "LINET": "Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}", "LINEB": "Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>", "LINEBNS": "Model <b>{{ model.code }}: {{ model.title.en }}</b>"}, "FCFR": {"TITLE": "Finished checks for review", "LINET": "Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}", "LINEB": "Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>", "LINEBNS": "Model <b>{{ model.code }}: {{ model.title.en }}</b>"}, "UWAC": {"TITLE": "Units without any checks", "LINET": "Unit <b>{{ code }}</b> ({{ customer }})", "LINEB": "Model <b>{{ model.code }}: {{ model.title.en }}</b>"}, "CPCH": {"TITLE": "Currently processed checks", "LINET": "Unit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}", "LINEB": "Model <b>{{ model.code }}: {{ model.title.en }}</b>, scheduled on <b>{{ scheduled }}</b>, due by <b>{{ dueby }}</b>", "LINEBNS": "Model <b>{{ model.code }}: {{ model.title.en }}</b>"}, "PMPC": {"TITLE": "Procedures and models with pending changes", "LINEPROC": "Procedure <b>{{ code }}</b>: {{ title.en }}", "LINEMOD": "Model <b>{{ code }}</b>: {{ title.en }}"}}, "TYPEF": {"TITLE": "Procedures and models with pending changes", "LINEPROC": "Procedure <b>{{ code }}</b>: {{ title.en }}"}}, "CHECK": {"INPUT": {"TITLE": {"EDIT": "Processing {{mname_en}}"}}, "OVERVIEW": {"ACTION": {"SCHEDULE": {"TITLE": "Schedule check", "TEXTSCHED": "Select the schedule date (earliest start time)", "TEXTDUE": "Select the due date (latest finishing time)", "TEXTASSIGN": "Select the assignment mode", "TEXTASSIGNTO": "Select the group or user to assign this check to", "TEXTADDASSIGN": "Select the group or user to add as assignment target"}, "CHANGEASS": {"TITLE": "Change assignment", "TEXT": "Select the new assignment for this block:"}}}, "ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "CANCEL": "Please confirm by typing in the check number.", "WRONGNUMBER": "Wrong number", "WRONGDELETEINPUT": "Your input does not match the number. The check has not been cancelled.", "DELETEASS": {"TITLE": "Do you really want to delete assignee {{ name }}?", "TEXT": "All assignments to this assignee will be reset."}, "DELETE": {"TITLE": "Please confirm by typing in the check number.", "WRONGDELETEINPUT": "Your input does not match the number. The check has not been deleted.", "TEXT": "@:UNIT.ALERT.DELETECHECK.TEXT"}, "SCHED": {"ASSFREE": "Free Assignment", "ASSFULL": "Preassign full check", "ASSDETAILED": "Assign detailed", "ASSPREALLOCFREE": "Predefined assignment, free assignment for remaining parts", "ASSPREALLOCDETAILED": "Predefined assignment, detailed assignment for remaining parts"}, "SCHEDTT": {"ASSFREE": "Check is free to be registered by any user", "ASSFULL": "The whole check is assigned to a single user or group", "ASSDETAILED": "Individual areas may be assigned to a user or group", "ASSPREALLOCFREE": "After assigning predefined parts, the remaining parts of the check are available to all groups or users", "ASSPREALLOCDETAILED": "After assigning predefined parts, the remaining parts of the check are not assigned and must be assigned manually"}, "REASSIGN": {"TITLE": "Do you really want to stop the process and reassign the workflow?", "TEXT": "The assigned users will be unable to work on this check while being stopped!"}, "REGISTER": {"TITLE": "Register", "TEXT": "Select the assignment you want to register to:"}, "UNREGISTER": {"TITLE": "Unregister", "TEXT": "Select the assignment you want to unregister from:"}, "STEPINPROC": "{{ nums }} step(s)/{{ nump }} procedure(s)", "REOPEN": {"TITLE": "Reopen", "TEXT": "Select the assignments you want to uncommit while reopening:"}, "COPY": {"TITLE": "Create copy", "TEXT": "Please select the procedures which will be copied to the new check including all measurements:", "NEWVERS": {"TITLE": "Update?", "TEXT": "There are newer versions of this check and you can optionally update the copy. Please note, however, that only procedures are copied that have not changed between the versions (regardless of the procedures you selected in the previous dialog) and that the copy may behave incorrectly in case of modified workflow rules.", "NOCHANGE": "Keep current version"}}, "SELPROC": {"TITLE": "Procedure selection", "TEXTSING": "Please select the procedure from this block for which you want to register:", "TEXTMULT": "Please select the procedures from this block for which you want to register:"}, "REGMODE": {"TITLE": "Registration mode", "TEXT": "Please select the mode of self registration:", "COMPLETE": "Only complete block", "MAYPART": "Partial registration possible", "MUSTPART": "Registration for single procedures only"}, "REGMODETT": {"COMPLETE": "Self registration is only possible for the complete block", "MAYPART": "When self registering, the procedures of a block may be selected individually (if there are multiple procedures in the block)", "MUSTPART": "When self registering, only a single procedure may be selected and registeres at a time (if there are multiple procedures in the block)"}, "TAKEOVER": {"INFO": "Take over!", "CONFIRM": "Do you really want to take over this assignment from the previous owner?"}}, "MODIFY": {"COMMENT": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "Add, edit or delete comments for this check"}}, "MAKEPDF": {"TTL": "Create report PDF", "TEXT": "Select all optional elements you want to include in the PDF:", "OPTV": "Procedure versions", "OPTADM": "Assignees and other user information", "OPTCOMM": "Comments on measurements", "OPTRAW": "Raw values for matrix calculations", "OPTTOOL": "Tools used for measurements", "OPTRULE": "Measure success rules", "TEXT2": "Please select the report type:", "CUSTOMER": {"TEXT": "Customer report", "TTIP": "Measures marked as 'intern' will be omitted; an alternative result string is shown in case of warnings."}, "INTERNAL": {"TEXT": "Internal report", "TTIP": "All measures are included."}}, "MAKEFILE": {"TTL": "Export report data", "TEXT": "Select the type of output for this report:", "CSV": "Comma Separated Values (CSV)", "JSON": "JavaScript Output Notation (JSON)", "XML": "Extensible Markup Language (XML)"}, "SELTOOL": {"TITLE": "Select tool unit", "MESSAGE": "Select the tool you are using for this step", "NOTOOLAVAILABLE": "There are no tools with active units available!"}}, "MEASUREMENT": {"INPUT": {"COMMENT": {"TITLE": "Edit comment", "TEXT": "Add, edit or delete comments for this unit"}}}, "MSRSTAT": {"SELMSR": {"TTL": "@:MSRSTAT.SELM.TTL", "TEXT": "Select the measures you want to include in the report:", "RUTEXT": "Enter the id-String you've copied from a previous report (last page of the PDF):"}, "SELCHK": {"TTL": "@:MSRSTAT.SELC.TTL", "S1TEXT": "Select the model of the unit you want to add checks from:", "S2TEXT": "Enter one or more short strings matching a part of the code or the customer name of the unit you want to add checks from:", "S3TEXT": "Select one or more check below:", "ERROR": {"TTL": "Check selection problem...", "NORES": "No checks found that use this procedure (in any version) or where the unit matches the given search strings.", "LIMIT": "You've reached the limit of 30 checks per report. Please remove checks from the list before adding new ones."}, "RUTEXT": "@:MSRSTAT.SELMSR.RUTEXT", "SUCCESS": {"TTL": "Export finished", "TEXT": "The data is being downloaded. Click 'Yes' to close this wizard, or 'No' if you wish to continue using this report generator. To reuse the measure or check selection later, you can copy the following id-strings now:", "MSTR": "Measures: \"{{ cstr }}\"", "CSTR": "Checks: \"{{ cstr }}\""}}, "TOOLTIP": {"REMCHK": "Remove check"}, "TITLE": "Statistical Report Generator", "SUBT": "Procedure {{ pcode }}", "SELM": {"TTL": "Select measures", "TXT": "Select the measures which will be included in the report. There is no limit of measures you can choose, each measure will generate one page. You can either reuse a previously copied selection or use the Select button to open the selection wizard.", "STATUS": "{{ msel }} measure(s) selected"}, "SELC": {"TTL": "Select checks", "TXT": "Select the checks which will be included in the report. The number of checks is limited to 30. You can use the Add button to open the check finder wizard, delete entries from the list with the red trashcan button or reuse a previously copied selection.", "STATUS": "{{ csel }} check(s) selected"}, "CLIST": "Check <b>{{ id }}</b> <span style='font-size:70%'>for Model <b>{{ mcode }}</b>, Unit <b>{{ ucode }}</b>; Checktype <b>{{ ctcode }}</b></span>", "BUTTON": {"SEL": "@:UI.BUTTONS.MEDITOR.SELECT", "REUSE": "Reuse", "ADD": "@:TLISTEDIT.BUTTON.ADD", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "GENERATE": "Generate Report", "EXPORT": "Export Data"}}, "MEDIAMGR": {"IMAGE": {"ALERT": {"CONFHEADER": "Are you sure?", "DELETE": "Do you really want to delete image {{fname}} from this set?"}, "EDIT": {"CAPTION": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "Add, edit or delete a caption for this image"}}}, "DOC": {"ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Do you really want to delete document {{fname}} from this set?"}, "EDIT": {"CAPTION": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "Add, edit or delete a caption for this document"}}}, "UPLOAD": {"FILTER": {"TITLE": "Upload impossible", "MESSAGE": "The upload of file {{ filename }} could not be started:", "UNSUPPORTED": "The file type ({{ type }}) is unknown or unsupported by the media manager.", "TOOBIG": "The file is too big; for this file type, the maximum size is set to {{ max }} MB, the file's size is {{ has }} MB."}, "HINT": "Drop media files in this pane for upload", "RESTR": "You may upload images (png, jpeg, tiff), text documents (pdf), packaged files (zip) and videos (mp4 only).", "STATUS": {"READY": "Ready to be uploaded", "WAITING": "Waiting...", "UPLOADING": "Uploading: {{ prog }}%", "CANCELLED": "File upload cancelled", "FINISHED": "@:WFLOW.INTRO.DATE.FIN", "FAILED": "Upload failed", "ERROR": "File upload error"}, "STARTALL": "Start all uploads", "DELALL": "Remove all finished uploads"}, "TOOLTIP": {"IMGSIZE": "Show image size", "IMGMETA": "Show image metadata", "EDITCAP": "Set or edit caption", "REMIMG": "Remove image", "VIEWDOC": "View Document", "DOCSIZE": "Show document size", "DOCMETA": "Show document metadata", "REMDOC": "Remove document", "UPLOAD": "Start upload", "REMUPL": "Remove upload from list", "CNCLUPL": "Cancel upload", "DOWNLOAD": "Download original file"}, "PDFPAGES": "{{ nump }} page(s)", "VIDINFO": "@:WFLOW.INTRO.VIDINFO", "TITLE": "Media Manager", "TAB": {"IMAGES": "Images", "VIDEOS": "Videos", "DOCS": "@:WFLOW.STEP.DOCS", "UPLOAD": "Upload"}}, "MODEL": {"EDITOR": {"TITLE": {"EDIT": "Editing {{modelname_en}}", "NEW": "Editing new model"}}, "ALERT": {"EDITVERSION": "Do you really want to edit a version of model {{code}}?", "EDITVERSIONDETAIL": "You are about to edit a finalized version of this model. If this version is used in any workflow, checks already planned or finished will be altered.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Please confirm the deletion of this model by typing in its code.", "WRONGCODE": "Wrong code", "WRONGDELETEINPUT": "Your input does not match the code. The model has not been deleted.", "DELETEVERSION": "You are about to remove a finalized version of this model. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.", "FINALIZE": "Do you really want to finalize model {{code}}?", "FINALIZEDETAIL": "The current data will be finalized into a new version. This version will be published and may then be used for new units and checks.", "RESET": {"TITLE": "Do you really want to reset all changes?", "TEXT": "All changes made to this object since the last finalization will be erased permanently."}, "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "SWITCHV": {"TITLE": "Select version", "MESSAGE": "Select the version to switch to from the following list:"}, "VIEW": {"ADDPROC": {"TITLE": "Add Procedures", "TEXT": "Select all procedures to add, then click the 'Add'-<PERSON><PERSON>"}, "ADDCTYPE": {"TITLE": "Add Checktypes", "TEXT": "Select all checktypes to add, then click the 'Add'-<PERSON><PERSON>"}, "ADDCONFIGTABLE": {"TITLE": "Relate configuration table", "TEXT": "Please choose a relation table to be related to the current model."}, "TOOLTIP": {"ACTREORD": "Activate reordering buttons", "ADDPROC": "Add active procedure", "UPDPROC": "Update procedure version", "REORDER": "Drag&Drop to reorder", "GOTOPROC": "Goto procedure", "DELPROC": "Remove procedure", "ADDCTYPE": "Add active check types", "DELCTYPE": "Remove check type", "SWITCHVER": "Switch version", "TESTVER": "Test version", "CHANGELOG": "View change log", "RESET": "Reset all changes to the last finalized version", "PREALLOC0": "Predefine assignments (currently no definitions)", "PREALLOC1": "Predefine assignments (currently all check types defined)", "PREALLOC2": "Predefine assignments (currently some check types defined)", "PREALLOC3": "Predefine assignments (defective definitions found)", "ADDCONFIGTABLE": "Relate configuration table", "DELCONFIGTABLE": "Delete relation to table"}, "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DTYPE": "@:WFLOWEDIT.OPT1.DT", "DESC": "@:TLISTEDIT.DESC", "MEDIA": "Media", "IMREG": "{{ imgnum }} Image(s) registered", "DOCREG": "{{ docnum }} Document(s) registered", "MEDMGR": "@:MEDIAMGR.TITLE", "BUTTON": {"EXPORT": "Export", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "FIN": "Finalize", "EDIT": "Edit", "UNIT": "Goto units", "CONFIGTABLE": "Goto configuration table"}, "PROC": {"STEPNUM": "Steps: {{ stepnum }}", "DISCONTINUED": "Discontinued!", "OLDVERS": "Using version {{ pvers }}"}, "ACTPROC": "Active procedures", "ACTCTYPE": "Active checktypes", "VERSIONTAG": "Version:", "UNITINFO": {"TTL": "@:FRAME.MANAGE.UNIT", "TXT": "<b>{{ ucnt }} unit(s) registered:</b><br>{{ ocnt }} open, {{ ccnt }} closed, {{ dcnt }} discarded."}, "CONFIGTABLE": {"TTL": "Configuration table", "NORELATION": "No configuration table related."}, "PREALLOC": {"TITLE": "Predefine assignments", "TEXT1": "Please select the check types for which the predefined assignments are valid:", "TEXT2": "Please select the group and/or user to be assigned to the procedure. An existing definition may be removed with 'remove assignment' (in group or user selection).", "ANY": "Any user or group", "GROUP": "Group:"}}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DTYPE": "@:WFLOWEDIT.OPT1.DT", "DESC": "@:TLISTEDIT.DESC", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"CLTF": "Clear text filter", "SHOWDEL": "Show or hide deleted models", "NEWMODEL": "New model", "IMPORTMODEL": "Import model", "GOTOUNITS": "Go to units"}, "TOPROW": "{{ dtypt }} ({{ dtypc }}), {{ unitnum }} unit(s)"}, "MODELS": "@:FRAME.MANAGE.MODEL", "TESTCHECK": {"TITLE": "Test model", "TEXT": "Please select the check type with which you want to test this version of the model:"}}, "STEP": {"MODEL": {"MEDIAMANAGER": "Model {{ mcode }}: {{ mtitle }}"}, "EDITOR": {"TITLE": {"EDIT": "Editing {{stepname_en}}", "NEW": "Editing new step"}}, "ALERT": {"EDITVERSION": "Do you really want to edit a version of step {{code}}?", "EDITVERSIONDETAIL": "You are about to edit a finalized version of this step. If this version is used in any workflow, checks already planned or finished will be altered.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Do you really want to delete step {{scode}} from procedure {{pcode}}?", "DELETECASCADING": "All measures ({{mcnt}}) defined for this step will be deleted as well!", "DELETEVERSION": "You are about to remove a finalized version of this step. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.", "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "VIEW": {"MEDIAMANAGER": "Procedure {{ pcode }}: {{ ptitle }}, Step {{ scode }}: {{ stitle }}", "FLOWEDITOR": "Step {{ scode }}: {{ stitle }}", "TOOLTIP": {"ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD", "UPLMEAS": "Upload measures", "CLNMEAS": "@:MEASURE.CLONE.TITLE", "NEWMEAS": "Create new measure", "REORDER": "@:MODEL.VIEW.TOOLTIP.REORDER", "EDITRULE": "Open workflow rule editor", "EXPMEAS": "Export measure", "EDITMEAS": "Edit measure", "TESTMEAS": "Test measure", "REMMEAS": "Remove measure"}, "VERSION": "@:PROCEDURE.VIEW.VERS", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESC": "@:TLISTEDIT.DESC", "MEDIA": "@:MODEL.VIEW.MEDIA", "IMREG": "@:MODEL.VIEW.IMREG", "DOCREG": "@:MODEL.VIEW.DOCREG", "BUTTON": {"MEDMGR": "@:MEDIAMGR.TITLE", "REDITOR": "@:PROCEDURE.VIEW.REDITOR", "EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"}, "RULES": "@:PROCEDURE.VIEW.RULES", "MEASURES": "@:WFLOW.INTRO.MEASURES"}, "CLONE": {"TITLE": "Clone steps", "PSELTEXT": "Select the procedure to clone steps from:", "TEXT": "Select the steps to clone:"}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "INSTRUCTION": "Instruction", "DESC": "@:TLISTEDIT.DESC", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}, "TOOLTIP": {"INSTRUCTION": "A step with type 'instruction' can not hold any measures"}}, "MATRIX": {"TITLE": "Setup matrix annotations", "HINT": "Select one of the titles in the matrix above, then edit in the textbox below. The smaller titles directly above the fields are later displayed in the top-left corner of the value field. Also, you can place a short text inside the value fields which will be used as a placeholder in the actual matrix.", "BUTTON": {"BACK": "Back"}}, "LIST": {"TOOLTIP": {"GOTOPROC": "@:WFLOW.STEP.TOOLTIP.GOTOPROC", "UPLOAD": "@:PROCEDURE.VIEW.TOOLTIP.UPLSTEP", "CLONE": "@:STEP.CLONE.TITLE", "ADDSTEP": "@:PROCEDURE.VIEW.TOOLTIP.NEWSTEP", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF"}, "BTMROW": "Measures: {{ mcnt }}, Images: {{ icnt }}, Docs: {{ dcnt }}"}, "STEP": "@:WFLOWEDIT.OPT1.S", "STEPS": "@:WFLOW.INTRO.STEPS", "FORPROC": "of procedure {{ proc }}"}, "PROCEDURE": {"EXPORT": {"TITLE": "Export procedure", "MESSAGESYSTEM": "Choose if the procedure should be exported for the same or for another system.", "MESSAGECONTENTS": "Choose which contents should be exported.", "OPTIONS": {"SAMESYSTEM": "For the same system", "OTHERSYSTEM": "For another system", "FILES": "Images and Documents", "TOOLS": "Tools", "RULES": "Workflow rules", "ENFORCEMENTS": "Workflow locks"}}, "EDITOR": {"TITLE": {"EDIT": "Editing {{procname_en}}", "NEW": "Editing new procedure"}}, "LIST": {"GOTOPROC": {"TITLE": "Usage view", "TEXT": "The following models use the selected procedure. You may choose one to go to the corresponding management page."}, "TOOLTIP": {"IMPORT": "Import procedures", "CLONE": "@:PROCEDURE.CLONE.TITLE", "ADD": "Create new procedure", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "SHOWDEL": "Show or hide deleted procedures", "GOTOMODEL": "Show and optionally go to models using this procedure", "PROCFILTER": "Filter by procedure tags"}, "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE", "BTMROW": "Steps: {{ stpcnt }}, Used by: {{ modcnt }} model(s)", "BTMROWUPD": ", updatable: {{ updcnt }}"}, "ALERT": {"EDITVERSION": "Do you really want to edit a version of procedure {{code}}?", "EDITVERSIONDETAIL": "You are about to edit a finalized version of this procedure. If this version is used in any workflow, checks already planned or finished will be altered.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Do you really want to delete procedure {{code}}?", "DELETECASCADING": "All steps ({{scnt}}) and their measures defined for this procedure will be deleted as well!", "DELETEVERSION": "You are about to remove a finalized version of this procedure. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.", "FINALIZE": {"TITLE": "Do you really want to finalize procedure {{code}}?", "TEXT": "The current data will be finalized into a new version. This version will be published and may then be used for new units and checks. Be sure to update and finalize all models using this procedure to use this new version."}, "FULLUPDATE": {"TITLE": "Do you really want to update pending models?", "TEXT": "Models using an older version of this procedure will be updated; this means, that a new version of each model will be created automatically. Other pending changes in the model's editing stage are not finalized."}, "RESET": {"TITLE": "Do you really want to reset all changes?", "TEXT": "All changes made to this object since the last finalization will be erased permanently."}, "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "SWITCHV": {"TITLE": "@:MODEL.SWITCHV.TITLE", "MESSAGE": "@:MODEL.SWITCHV.MESSAGE"}, "VIEW": {"FLOWEDITOR": "Procedure {{ pcode }}: {{ ptitle }}", "TOOLTIP": {"STAT": "Generate statistic report", "CHVERS": "@:MODEL.VIEW.TOOLTIP.SWITCHVER", "UPDMOD": "Update models to latest version", "GOTOSTEP": "@:WFLOW.INTRO.TOOLTIP.GOTOSTEP", "ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD", "UPLSTEP": "Upload steps", "CLNSTEP": "@:STEP.CLONE.TITLE", "NEWSTEP": "Create new step", "REORD": "@:MODEL.VIEW.TOOLTIP.REORDER", "EXPSTEP": "Export step", "REMSTEP": "Remove step", "TESTVER": "Test version", "CHANGELOG": "View change log", "RESET": "Reset all changes to the last finalized version", "ENFORCETOP": "The first entry may not be workflow locked", "ENFORCEINSTR": "An instruction may not be workflow locked", "ENFORCE0": "No workflow lock", "ENFORCE1": "Lock: May only be processed when the last entry has been completed", "ENFORCE2": "Full lock: May only be processed when all prior entries have been completed"}, "VERS": "Version", "UPDATEINFO": "There are {{ updcnt }} model(s) using an older version of this procedure.", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "PROCTIME": "Processing Time", "HOURS": "hours", "MINUTES": "minutes", "DESC": "@:TLISTEDIT.DESC", "RULES": "Workflow Rules", "REDITOR": "Rule Editor", "BUTTON": {"EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "FIN": "@:MODEL.VIEW.BUTTON.FIN", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT", "STATISTIC": "Statistics"}, "STEPS": "@:WFLOW.INTRO.STEPS", "MEASURES": "Measures: {{ msrcnt }}"}, "CLONE": {"TITLE": "Clone procedures", "TEXT": "Select one or more procedures to clone:"}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESC": "@:TLISTEDIT.DESC", "PROCTIME": "Processing Time", "HOURS": "hours", "MINUTES": "minutes", "WRONGPROCTIME": "Wrong processing time", "WRONGPROCTIMETXT": "The processing time has been entered incorrectly.", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "TESTCHECK": {"TITLE": "Test procedure", "TEXT": "Please select the check type with which you want to test this version of the procedure:"}, "FILTER": {"TITLE": "Global filter (procedure tags)", "PLEASECHOOSE": "Please choose", "CANCEL": "Clear filter", "APPLY": "Apply filter"}}, "MEASURE": {"EDITOR": {"TITLE": {"EDIT": "Editing {{mname_en}}", "NEW": "Editing new measure"}, "NOTOOL": "No tool used"}, "VIEW": {"FLOWEDITOR": "Measure {{ mcode }}: {{ mtitle }}"}, "ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Do you really want to delete measure {{mcode}} from step {{scode}} in procedure {{pcode}}?", "DELETEVERSION": "You are about to remove a finalized version of this measure. If this version is used in any workflow, the process will fail; also this action may produce unwanted results and data corruption.", "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Only text may be altered in a finalized version."}, "CLONE": {"TITLE": "Clone measures", "PSELTEXT": "Select the procedure to clone measures from:", "TEXT": "Select the measures to clone:"}, "EDIT": {"COMPARATOR": "Comparator", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "HINTS": "Hints", "TTYPE": "Tool type", "MTYPE": "Measure type", "MANDATORY": "Mandatory", "YES": "@:WFLOW.INPUT.YES", "NO": "@:WFLOW.INPUT.NO", "TARGET": "Target Value", "UNIT": "@:WFLOW.INTRO.UNIT", "THOLD": "@:WFLOW.INPUT.THRESH", "MIN": "Minimum", "MAX": "Maximum", "MINLEN": "<PERSON><PERSON>", "REGEXP": "Regular Expression", "EXP": "Expected", "ANY": "Any", "MATRIX": "Matrix", "BUTTON": {"SETUP": "Setup titles", "TEST": "Save & Test Measure", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "CLOSE": "Save & Close"}, "NUMCOL": "Number columns", "NUMROW": "Number rows", "FORMULA": "Formula", "FLOATFRM": {"TEXT": "Numeric format", "STD": "Standard", "INT": "No decimal places", "1DIGIT": "1 decimal place", "2DIGIT": "2 decimal places", "3DIGIT": "3 decimal places", "4DIGIT": "4 decimal places", "6DIGIT": "6 decimal places"}, "COMPLCODE": "Grouping", "INTERNAL": "Internal", "INTERNALEXTERNAL": "Internal & External", "CHOICE": "Number of choices", "CHOICEVAL": "Value {{ val }}"}, "TYPES": {"THRESHOLD": "@:WFLOW.INPUT.THRESH", "ABSOLUTE": "Absolute value", "ABSOLUTERNG": "Absolute value in range", "TEXT": "Random text", "REGEXP": "<PERSON><PERSON> checked text", "BOOL": "Flag (yes/no)", "RESCHECK": "Fixed result check", "THRESHOLDMATRIX": "Threshold, deviation input by matrix calc.", "ABSOLUTEMATRIX": "Absolute value, input by matrix calc.", "ABSOLUTERNGMATRIX": "Absolute value in range, input by matrix calc.", "STATISTICAL": "Statistical value", "STATISTICALMATRIX": "Statistical value, input by matrix calc.", "TIMERSTART": "Timer (start time)", "TIMERSTOP": "Timer (stop time)", "TIMERSTOPQ": "Checked timer (stop time)", "TIMERSTOPC": "Check uncorrected time measurement", "CHOICELIST": "Choice list"}}, "MEASUREMENTERRORCATEGORYEDITOR": {"TITLE": "Editing of a finished measurement", "TEXT": "This measurement has already been accomplished. Please choose one of the reasons for the editing below to continue."}, "TOOL": {"EDITOR": {"TITLE": {"EDIT": "Editing {{ tool }}", "NEW": "Editing new tool type"}}, "NEWUNIT": {"TITLE": "Create new unit", "TEXT": "Enter the serial number or any other identifying code in the text field below:"}, "EDITUNIT": {"TITLE": "Edit unit", "TEXT": "@:TOOL.NEWUNIT.TEXT"}, "COMMENTUNIT": {"TITLE": "Set comment", "TEXT": "Create, edit or delete the comment for this unit:"}, "ALERT": {"DELETEUNIT": "Do you really want to delete tool unit {{code}}?", "NODELETEUNIT": "You may not delete this tool {{code}}!", "DELETEUNITCASCADING": "It is already used in one or more measures. Please set it to 'disabled' instead.", "DELETELASTUNITCASCADING": "The tooltype to this unit is already used in some procedures; at least one unit must be available. Please set it to 'disabled' instead.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DENY": "Action not possible...", "DELETE": "Do you really want to delete tooltype {{code}}?", "NODELETE": "You may not delete this tool type {{code}}!", "DELETECASCADING": "One or more units are already defined for this type. Please set it to 'disabled' instead."}, "MEDIAMANAGER": "Tool type {{ ttcode }}: {{ tttitle }}", "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "HINTS": "@:TLISTEDIT.DESC", "DIS": {"TITLE": "@:TLISTEDIT.DISSET.TITLE", "TRUE": "The tool type is disabled and may not be used any longer.", "FALSE": "The tool type is active."}, "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"NEW": "Create new tool type", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "SHOWDEL": "Show deactivated tool types"}, "TTYPES": "Tool types", "BTMROW": "Measures: {{ mcnt }}, Tool units: {{ tcnt }}"}, "VIEW": {"TOOLTIP": {"NEWTUNIT": "Create new tool unit", "EDITTUNIT": "Edit tool unit", "REENABLE": "Reenable tool unit", "DISABLE": "Disable tool unit", "REMTUNIT": "Remove tool unit", "SHOWDEL": "Show deactivated tool units", "SETCOMMENT": "comment on this unit", "REPORT": "Generate tool unit usage report"}, "DISABLED": "@:TOOL.EDIT.DIS.TRUE", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESCRIPTION": "@:TLISTEDIT.DESC", "MEDIA": "@:MODEL.VIEW.MEDIA", "IMGREG": "{{ imgcnt }} Image(s) registered", "DOCREG": "{{ doccnt }} Document(s) registered", "BUTTON": {"MEDMGR": "@:MEDIAMGR.TITLE", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"}, "MEDINFO": "Note: only the first image/first document is accessible!", "TUNITS": "Tool units", "TOOLDEL": "Disabled", "TOOLUNITCNT": "Used in {{ ucnt }} measurements"}, "REPORT": {"TITLE": "Generate usage report", "DATE1": "You can select a date from which all uses of this tool unit will be included in the report. If you do not wish to select a date (include all uses from beginning), then click 'Save' without choosing a date.", "DATE2": "You can select a date until which all uses of this tool unit will be included in the report. If you do not wish to select a date (include all uses until now), then click 'Save' without choosing a date.", "SORT": {"TEXT": "Please choose:", "MODEL": "Sort by model, unit, check", "TIME": "Sort by timestamp"}}}, "CHECKTYPE": {"MEDIAMANAGER": "Devicetype {{ code }}: {{ title }}"}, "DEVICETYPE": {"MEDIAMANAGER": "Checktype {{ code }}: {{ title }}"}, "UNIT": {"EDITOR": {"TITLE": {"EDIT": "Editing {{ unit }}", "NEW": "Editing new unit"}}, "ARCHIVE": {"TTL": "Search in archive", "TXT": "Enter one or more short strings matching a part of the code or customer string of the unit(s) you are looking for.", "MANYRES": {"TTL": "Too many results", "TXT": "The search returned more than 100 entries, only the first 100 are shown. Please refine your search."}}, "NEWCHECK": {"TITLE": "Create new check", "TEXT": "Select the check type in the box below:", "CONFIGTABLE": {"NEW": "Adopt template configuration table from model", "TITLE": "Choose a template configuration table", "TEXT": "Please choose if you want to adopt the template configuration table from the model or from an earlier check:", "CHECK": "Check"}}, "ALERT": {"DELETECHECK": {"TITLE": "Do you really want to delete this check?", "TEXT": "All measures and gathered data will be deleted as well!"}, "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Please confirm the deletion of this unit by typing in its code.", "WRONGCODE": "Wrong code", "WRONGDELETEINPUT": "Your input does not match the code. The unit has not been deleted.", "DELETECASCADING": "All checks ({{ccnt}}) of this unit will be deleted as well! You may want to set the status of this unit to 'discarded' instead.", "STATUSCHG": {"TTL": "Change unit status", "TXT": "Select the new status of this unit:"}, "UNARCHIVE": {"TXT": "Really unarchive unit?"}}, "MODIFY": {"DATE": {"TITLE": "Set new date", "COMMISSIONED": "Select the date when this unit has been commissioned", "FINISHED": "Select the date when this unit has been finished", "DELIVERED": "Select the date when this unit has been delivered to the customer", "APPROVED": "Select the date when this unit has been approved by the customer"}, "COMMENT": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "@:MEASUREMENT.INPUT.COMMENT.TEXT"}}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "CUST": "Customer", "MODEL": "@:WFLOWEDIT.OPT1.MD", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"NEW": "@:TOOL.NEWUNIT.TITLE", "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "ARCHMODE": "Enter or leave archive mode", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "LOCKMODE": "Show or hide closed units"}, "UNITS": "@:FRAME.MANAGE.UNIT"}, "FORMODEL": "of model {{ mdl }}", "INARCHIVE": "in archive, matching '{{ pat }}'", "VIEW": {"TOOLTIP": {"UNITSTAT": "Set unit status", "UNARCH": "Unarchive (reactivate) unit", "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "EDITDATE": "Set or change date", "EDITCOMM": "Add or change comment", "ADDCHK": "@:UNIT.NEWCHECK.TITLE", "DELCHK": "@:MSRSTAT.TOOLTIP.REMCHK", "GOTOCHK": "Go to check"}, "MODEL": "@:WFLOWEDIT.OPT1.MD", "CODE": "@:TLISTEDIT.CODE", "CUST": "@:UNIT.EDIT.CUST", "CHECKS": "Checks", "CHK": "@:WFLOW.INTRO.MEDIAMANAGER", "DATES": "Dates", "COMM": {"ON": "Commissioned on", "NOT": "Not yet commissioned"}, "FIN": {"ON": "Finished on", "NOT": "Not yet finished"}, "DEL": {"ON": "Delivered on", "NOT": "Not yet delivered"}, "APPR": {"ON": "Approved on", "NOT": "Not yet approved"}, "COMMENT": "Comment", "BUTTON": {"DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT", "CHSTATUS": "Change status", "UNARCHIVE": "Unarchive"}, "CHECK": {"MODVERS": "using model version {{ mvers }}", "FAIL": "check failed!", "PASS": "check passed!", "FAILCLOSED": "Closed, check failed on", "PASSCLOSED": "Closed, check passed on", "PASSCANCEL": "Cancelled", "WARN": "Check passed with warnings!", "WARNCLOSED": "Closed, check passed with warnings on"}, "SCHED": {"NOT": "@:WFLOW.INTRO.DATE.NOSCHED", "ON": "Scheduled for"}, "DUE": {"ON": "due by", "NOT": "no due date"}, "START": {"ON": "@:WFLOW.INTRO.DATE.START"}, "STATUS": {"TTL": "@:WFLOW.INPUT.STATUS.TITLE", "OPEN": "OPEN", "CLOSED": "CLOSED", "DISCARDED": "DISCARDED", "ARCHIVED": "ARCHIVED", "CLOSEDARCH": "CLOSED (ARCHIVED)", "DISCARCH": "DISCARDED (ARCHIVED)"}}}, "USERS": {"ANYUSER": "Any user", "ANYGROUP": "Any group", "DELASSIGN": "Remove assignment", "TOOLTIP": {"NEWUSER": "Create new user", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "HIDEGRP": "Hide groups", "NEWGRP": "Create new group", "SHOWGRP": "Show groups", "DISUSER": "Deactivate user", "ENUSER": "Activate user", "EDITUNAM": "Edit user name", "EDITURN": "Edit user's real name", "EDITUCOMM": "Add or edit comments about the user", "REMGRP": "@:USERMGR.ACTION.DELETEGROUP.TITLE", "REACTGRP": "Enable group", "DISABLED": "Disabled", "SHOWDELGROUPS": "Show disabled groups", "CHGGRP": "Put user into another group", "EDITGNAME": "Edit group name", "EDITGLVL": "Edit group level", "EDITGDESC": "Edit group description", "ADDGPRV": "Add group privileges", "REMGPRV": "Remove group privilege", "ADDUPRV": "Add user privileges", "REMUPRV": "Remove user privilege", "SHOWDEL": "Show deactivated users", "ADDGROUP": "Add one or more groups to this user", "ADDUSER": "Add one or more users to this group", "ADDFIRSTGROUP": "Add one or more groups to this user", "ADDFIRSTUSER": "Add one or more users to this group", "REMOVEGROUP": "Remove this group from the user", "REMOVEUSER": "Remove this user from the group"}, "USERS": "@:FRAME.MANAGE.USERS", "GROUPS": "Groups", "USER": "@:MEDITOR.USER", "USERNAME": "@:LOGIN.USERNAME", "PASSWORD": {"TITLE": "@:LOGIN.PASSWORD", "SET": "Password set", "NOTSET": "Password not set"}, "BUTTON": {"CHPASSW": "@:FRAME.MANAGE.CHANGEPW"}, "REALNAME": "Realname", "COMMENT": "@:UNIT.VIEW.COMMENT", "GROUP": "@:MEDITOR.GROUP", "GNAME": "Group name", "LEVEL": "Level", "DESC": "@:TLISTEDIT.DESC", "PRIVILEGES": "Privileges", "PRIV": {"BYGROUP": "Granted by groups", "BYUSER": "Granted individually", "FORGROUP": "Group wide privileges"}, "RANK": "Level", "USERGROUPS": "Affiliated groups", "NOGROUPS": "User is not affiliated to any group", "GROUPUSERS": "Members", "NOUSERS": "No users are affiliated to this group", "ADDUSER": "Add user", "ADDGROUP": "Add group"}, "USERMGR": {"ACTION": {"ADDUSER": {"TITLE": "Add user"}, "EDITUSER": {"TITLE": "Edit user"}, "USER": {"USERNAME": "Enter a new username", "REALNAME": "Enter the user's real name", "COMMENT": "Edit or add a comment for the user", "PASSWORD": "Set a new password or leave empty to clear password", "GROUP": "Select the new group for this user"}, "ADDGROUP": {"TITLE": "Add group"}, "EDITGROUP": {"TITLE": "Edit group"}, "GROUP": {"NAME": "Enter a new group name", "LEVEL": "Enter a new group level", "DESCRIPTION": "Enter or add a description for this group"}, "DELETEGROUP": {"TITLE": "Disable group", "TEXT": "Do you really want to dsiable the group? Some users in this group may be not be affiliated to any group afterwards."}, "REACTIVATEGROUP": {"TITLE": "Enable group", "TEXT": "This group has been disabled, are you sure you want to enable it again?"}, "ADDGRANT": {"TITLE": "Add privileges", "TEXT": {"GROUP": "Select the privileges to add to the group:", "USER": "Select the privileges to add to the user:"}}, "ADDGRPTOUSER": {"TITLE": "Add groups", "TEXT": "Please select the groups to be added to the user:"}, "ADDUSERTOGRP": {"TITLE": "Add users", "TEXT": "Please select the users to be added to the group:"}}}, "WFLOW": {"INTRO": {"STATUS": {"INIT": "INITIALIZED", "SCHED": "SCHEDULED", "START": "STARTED", "FAIL": "FAILED", "PASS": "PASSED", "FAILC": "FAILED & CLOSED", "PASSC": "PASSED & CLOSED", "CANCEL": "CANCELLED", "WARN": "PASSED W/RES", "WARNC": "PASSWD W/RES & CLOSED"}, "MEDIAMANAGER": "Check", "FILEINFO": {"TITLE": "Upload information", "UPLOADDATE": "Date:", "UPLOADTIME": "Time:", "UPLOADEDBY": "By:"}, "TOOLTIP": {"EXPPDF": "Export check as PDF", "EXPFILE": "Export check data", "GOTOMODEL": "Go to model", "GOTOUNIT": "Go to unit", "IMGLEFT": "Previous image", "IMGRIGHT": "Next image", "VIEWDOC": "View document", "EDITCOMM": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "VIEWIMG": "View image", "REMASS": "Remove assignee", "ASSIGN": "Assign to selected items", "HIGHLASS": "Highlight assigned items", "ADDASS": "Add assignee", "SELASS": "Select for assignment", "GOTOSTEP": "Go to step", "CHANGEASSIGN": "Change assignment", "CONFIGTABLE": "Open configuration table"}, "MEDIAMGR": "Add, delete or add images and documents", "TOOEARLY": "The check is scheduled in the future; it may not be processed now!", "TOOLATE": "The check is due in the past; hurry up!", "MODEL": "@:WFLOWEDIT.OPT1.MD", "UNIT": "Unit", "PDFINFO": "{{ pnum }} page(s)", "VIDINFO": "{{ width }} x {{ height }}, {{ fps }} fps, runtime: {{ rtime }} sec", "IMGINFO": "{{ width }} x {{ height }}", "DATE": {"SCHED": "Scheduled", "DUE": "Due by", "NOSCHED": "Not scheduled yet", "NODUE": "No due date set yet", "START": "Started on", "FIN": "Finished", "NOSTART": "Not started yet", "NOFIN": "Not finished yet"}, "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "NOCOMMENT": "Check not commented yet; <PERSON><PERSON> here to add a comment", "STEPINPROC": "step(s) in", "PROCS": "procedure(s)", "NOASS": "No assignments yet", "ADDASS": "Add Assignment", "PROCSUNASS": "procedure(s) unassigned", "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE", "STEPS": "Steps", "INSTRUCTIONS": "Instructions", "MEASURES": "Measures", "STAT": {"TOTAL": "Total", "PASSED": "Passed", "UNFIN": "In Progress", "FAILED": "Not passed", "SKIPPED": "Optional"}, "BUTTON": {"DELETE": "Delete", "SCHEDULE": "Schedule", "RESCHEDULE": "Reschedule", "START": "Start", "REASS": "Stop check", "UNREG": "@:CHECK.ALERT.UNREGISTER.TITLE", "REG": "@:CHECK.ALERT.REGISTER.TITLE", "CONTINUE": "Continue", "COMMIT": "Commit", "CLOSE": "@:WFLOW.INPUT.BUTTON.CLOSE", "REOPEN": "@:CHECK.ALERT.REOPEN.TITLE", "CANCEL": "Cancel check", "COPY": "Create Copy"}, "FILTER": {"BUTTONS": {"COLLAPSE": "Collapse:", "PROC": "Proc.", "STEP": "Steps", "MEAS": "Meas.", "FILTER": "Filter", "FILTERPLUS": "Filter+"}, "TITLE": "Filter", "DISABLE": "Disable filter", "FILTTITLE": "Apply text filter on:", "FILTCODE": "codes", "FILTTEXT": "element titles", "FILTPROC": "procedures", "FILTSTEP": "steps", "FILTMEAS": "measures", "FILTMARKELEMS": "Mark elements:", "FILTUSER": "Mark all/only elements assigned to the current user (will reset filters above):", "FILTUNFIN": "Mark all/only unfinished elements", "FILTFAIL": "Mark all/only failed elements", "FILTSUCC": "Mark all/only suceeded elements", "HIDEINSTR": "Hide instruction steps:", "HIDEOMITTEDORSKIPPED": "Hide omitted or skipped elements:", "TOOLTIP": {"VIEWFILT": "Show filter dialog", "PROC": "Collapse all, show procedures only", "STEP": "Expand steps, collapse measures (default)", "MEAS": "Show all, everything is expanded", "FILTER": "Show everything marked via filters; collapse anything else", "FILTERPLUS": "Expand all elements marked via filter: collapse anything else", "RESET": "Reset all filters"}}, "VERSION": "Check is based on model version", "UNFINVERSION": "Check is based on the currently edited, not yet finalized model version", "TESTRUN": {"PROCEDURE": "Test run of a check for procedure:", "MODEL": "Test run of a check for model:"}}, "STEP": {"IMAGE": {"NEXT": "Next", "PREVIOUS": "Previous"}, "STATUS": {"TODO": "@:WFLOW.INTRO.STATUS.INIT", "PASS": "@:WFLOW.INTRO.STATUS.PASS", "FAIL": "@:WFLOW.INTRO.STATUS.FAIL", "OMIT": "OMITTED", "SKIP": "SKIPPED", "PASSNF": "(PASSED)", "FAILNF": "(FAILED)", "WARN": "@:WFLOW.INTRO.STATUS.WARN", "WARNNF": "(PASSED W/RES)"}, "TOOLTIP": {"GOTOMODEL": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "GOTOUNIT": "@:WFLOW.INTRO.TOOLTIP.GOTOUNIT", "GOTOPROC": "Go to procedure", "VIEWIMG": "@:WFLOW.INTRO.TOOLTIP.VIEWIMG", "VIEWDOC": "@:WFLOW.INTRO.TOOLTIP.VIEWDOC"}, "COMMITTER": "Step committed by:", "NOCOMMIT": "Not committed yet", "MEASURER": "Measurement {{ code }} by:", "NOMEASURE": "Not done yet", "PDFINFO": "@:WFLOW.INTRO.PDFINFO", "VIDINFO": "{{ width }} x {{ height }}, {{ fps }} fps, {{ rtime }} sec", "MODEL": "Model", "UNIT": "@:WFLOW.INTRO.UNIT", "PROCEDURE": "@:WFLOWEDIT.OPT1.P", "STEP": "Step", "BUTTON": {"BACK": "Back to Overview", "CONTINUE": "@:WFLOW.INTRO.BUTTON.CONTINUE", "FIN": "Finish!", "REWIND": "Last step", "FORWARD": "Next step", "PROCALL": "Process all"}, "MEASURES": "@:WFLOW.INTRO.MEASURES", "MEASUREINP": "Enter result...", "INPLOCKED": "Locked", "TOOLS": "@:FRAME.MANAGE.TOOL", "TOOLCHOOSE": "Choose tool...", "DOCS": "Documents", "VIEW": {"TIMER": {"TITLE": "Processing time", "FINISHED": "The processing time for this procedure is now over!"}, "INFO": "Description", "IMAGES": "Images ({{ numi }})", "DOCS": "Documents ({{ numd }})"}}, "INPUT": {"VALUECMP1": "Value must be less than {{ val }}", "VALUECMP2": "Value must be less than or equal to {{ val }}", "VALUECMP3": "Value must be equal to {{ val }}", "VALUECMP4": "Value must be greater than or equal to {{ val }}", "VALUECMP5": "Value must be greater than {{ val }}", "CHECKCMP1": "Check if your measured value is less than {{ val }}, then click 'Yes' or 'No' accordingly", "CHECKCMP2": "Check if your measured value is less than or equal to {{ val }}, then click 'Yes' or 'No' accordingly", "CHECKCMP3": "Check if your measured value is equal to  {{ val }}, then click 'Yes' or 'No' accordingly", "CHECKCMP4": "Check if your measured value is greater than or equal to {{ val }}, then click 'Yes' or 'No' accordingly", "CHECKCMP5": "Check if your measured value is greater than {{ val }}, then click 'Yes' or 'No' accordingly", "TARGET": "Target value", "STATISTIC": "Please enter the measured value; the value will not be checked", "THRESH": "<PERSON><PERSON><PERSON><PERSON>", "VALUERNG1": "Value must be between", "VALUERNG2": "and", "TEXTLEN1": "Text must be at least", "TEXTLEN2": "characters long", "TEXTPAT": "Text must match a specific pattern", "EXPNO": "'No' is expected", "EXPYES": "'Yes' is expected", "EXPBOTH": "'Yes' or 'No' will both be accepted", "OPTSKIP": "This measurement is optional. Skip?", "YES": "Yes", "NO": "No", "INPUT": {"VALUE": "Enter the measured value in the textbox below:", "TEXT": "Enter the required text in the textbox below:", "LIST": "Choose the appropriate value:", "BOOL": "Check either 'Yes' or 'No':", "MATRIX": "Fill out the matrix below, then hit 'Check & Save' to calculate the result:"}, "RESULT": "Result", "STATUS": {"TITLE": "Status", "TODO": "TODO", "PASS": "PASS", "FAIL": "FAIL", "SKIP": "SKIP", "INV": "INVALID", "ERROR": "ERROR", "WARN": "FAIL"}, "BUTTON": {"CLOSE": "Close", "CLEAR": "Clear", "CANCEL": "Cancel", "ADDCOMM": "Add Comment", "EDITCOMM": "Edit Comment", "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "CONTINUE": "Check & Continue"}, "TEXTTIMER": {"START": "Please enter the start time", "STOP": "Please enter the stop time", "STOPLT": "Please enter the stop time; a resulting time less than {{ time }} min is expected", "STOPGT": "Please enter the stop time; a resulting time greater than {{ time }} min is expected", "REDUCE": "reduce by", "MIN": "min"}, "NOINPUT": "The result of this measurement is calculated from other measurements; no input necessary."}}, "WFLOWEDIT": {"OPT1": {"CT": "Checktype", "MD": "Model", "DT": "Devicetype", "P": "Procedure", "S": "Step", "M": "Measure", "CO": "Config. table"}, "OPT2": {"SEL": "selected", "NSEL": "not selected", "QUEUED": "queued", "NQUEUED": "not queued", "FINPASS": "finished & passed", "FINFAIL": "finished & failed", "SKIP": "skipped", "NSKIP": "not skipped", "OMIT": "omitted", "NOMIT": "not omitted", "YES": "set to 'YES'", "NO": "set to 'NO'", "ACTIVE": "active", "NACTIVE": "not active"}, "OPT3": {"SKIP": "skip", "OMIT": "omit"}, "TOOLTIP": {"REMMOD": "Remove modifier", "ADDMOD": "Add new modifier"}, "LOADING": "Loading Editor...", "TITLE": "Workflow Rules Editor", "ACTIVER": "Active Rules", "ADDRULE": "Add new Rule", "RULE1": "If the", "RULE2": "with code", "RULE3": "is", "RULE4": ", then"}, "SERVER": {"ERROR": {"TITLE": "Error!", "VALIDATION": {"MEASUREMENTERRORCATEGORY": {"NAME": {"SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"}}, "USERGROUP": {"NAME": {"NOTNULL": "A group name must be provided", "UNIQUE": "The group name must be unique; no other group may have the same name", "MATCH": "The group name may only contain characters, numbers and the underscore; It must be at least 3 characters wide"}, "LEVEL": {"NOTNULL": "A level must be specified", "NUMBER": "The group level must be a positive integer number between 1 and 1000"}}, "USER": {"USERNAME": {"NOTNULL": "A username must be provided", "UNIQUE": "The username must be unique; no other user may have the same username", "MATCH": "The username may only contain characters, numbers and the underscore; It must be at least 3 characters wide"}, "PASSHASH": {"NOTNULL": "A passhash must be provided", "MATCH": "The passhash is not supported by the current password mechanism"}, "REALNAME": {"NOTNULL": "The user's real name must be provided", "MINLEN": "The real name must be at least 3 characters long"}, "USERGROUP_ID": {"NOTNULL": "The user's group must be set"}}, "TOOLTYPE": {"CODE": {"UNIQUE": "The code value must be unique; no other tool type may have the same code", "SHORT": "The code value must be at least 2 characters long", "INVALID": "The code value may only contain characters, numbers, dots, commas, underscores and hyphens"}, "TITLE": {"INVALID": "The language block for the title is invalid", "INCOMPLETE": "The language block for the title misses the language entry for '{{ lcode }}'", "SHORT": "The language entry '{{ lcode }}' for the title is too short (< {{ minlength }})"}, "DESCRIPTION": {"INVALID": "The language block for the decription is invalid"}}, "TOOLUNIT": {"CODE": {"UNIQUE": "The code value must be unique within the tool type; no other unit may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}}, "PROCEDURE": {"CODE": {"UNIQUE": "The code value must be unique; no other procedure may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "STEP": {"CODE": {"UNIQUE": "The code value must be unique; no other step for this procedure may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "MEASURE": {"CODE": {"UNIQUE": "The code value must be unique; no other measure for this step may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "CALCULATION": {"OPTMISS": "The optional field must be set either to Yes or No", "INVMTYPE": "The measuretype is invalid", "AINVVAL": "The target value and the threshold must have a valid number specified; the threshold must be positive.", "BINVVAL": "A valid number must be specified for the minimum and maximum value", "CINVVAL": "The minimum text length must be a positive integer value", "EMPTYVAL": "List values must not be empty", "DMISSING": "A regular expression must be specified", "DINVREGEXP": "The given regular expression is not valid", "EINVVAL": "The expected value is missing - it must be set to either Yes or No", "JINVVAL": "A valid number must be specified for the reference value", "JINVCMP": "The comparator must be specified properly", "HINVVAL": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL", "HINVCMP": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP", "OINVVAL": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL", "OINVCMP": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP", "XINVMTRX": "Invalid matrix definition; sizes are not given or are invalid", "XINVFORM": "No matrix formula specified", "XVARIOUS": "Unexpected error while verifiying matrix", "XSYNTAX": "Syntax error while verifying matrix formula; please check for typing errors", "XINVVAR": "Invalid variable name detected while verifying matrix formula", "XINVFUN": "Invalid method name detected while verifying matrix formula", "XINVUSE": "One ore more methods are used improperly (e.g. one instead of two arguments)", "MCODEMISSING": "The grouping code must be set for this measure type and exceed 3 characters"}}, "CHECKTYPE": {"CODE": {"UNIQUE": "The code value must be unique; no other checktype may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "DEVICETYPE": {"CODE": {"UNIQUE": "The code value must be unique; no other devicetype may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "MODEL": {"CODE": {"UNIQUE": "The code value must be unique; no other model may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "DEVICETYPE_ID": {"NULL": "The devicetype must be set", "NOREF": "The devicetype {{ refid}} is not valid or does not exist"}}, "UNIT": {"CODE": {"UNIQUE": "The code value must be unique; no other unit may have the same code", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "COMMENT": {"INVALID": "The language block for the customer is invalid"}, "MODEL_ID": {"NULL": "The model must be set", "NOREF": "The model {{ refid }} is not valid or does not exist"}}, "SETTINGS": {"VALUE": {"INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"}}, "TYPE": "The server reported one or more data validation errors:"}, "INVMOVIE": {"TYPE": "Uploaded video is not valid", "TEXT": "The uploaded video file was checked and found invalid. It can not be used in this application."}, "INVPDF": {"TYPE": "Uploaded PDF document is not valid", "TEXT": "The uploaded PDF document was checked and found invalid. It can not be used in this application."}, "CHECKACTION": {"TYPE": "An error occurred during this action", "INVALIDPHASE": "This action may not be performed in this phase", "REQFAILED": "Some requirements are not met for this action", "INVCOMMAND": "Invalid command"}, "STEPACTION": {"TYPE": "@:SERVER.ERROR.CHECKACTION.TYPE", "INVALIDPHASE": "@:SERVER.ERROR.CHECKACTION.INVALIDPHASE", "REQFAILED": "@:SERVER.ERROR.CHECKACTION.REQFAILED", "INVCOMMAND": "@:SERVER.ERROR.CHECKACTION.INVCOMMAND"}, "LOGIN": {"TYPE": "Login error", "USERUNKNOWN": "The username does not exist in the database", "NOTINITIALIZED": "The user has not yet been initialized", "WRONGPASSWORD": "The password does not match", "NOTLOGGEDIN": "The user is not logged in", "USERLOCKED": "The user is locked"}, "NOTFOUND": {"TYPE": "An object is missing", "TEXT": "The {{class}}-object with ID {{id}} could not be found in the database", "CHECKSTEXT": "The step #{{sid}} in check (ID: {{cid}}) could not be found in the database", "CHECKMTEXT": "The measure #{{mid}} in step #{{sid}} in check (ID: {{cid}}) could not be found in the database"}, "STEPTYPE": {"TYPE": "Error while trying to set step type", "HASMEASURES": "The type of step #{{id}} can't be set to 'instruction' as long as there are measures on it."}, "ACCDENIED": {"TYPE": "Access denied or insufficient", "TEXT": "Your access level or privileges are not sufficient for this action"}, "INVMEDIA": {"TYPE": "Uploaded media type invalid", "TEXT": "The uploaded media has an invalid type; the server was unable to store the data."}, "IMPORT": {"TYPE": "Import error", "WRONGTYPE": "The import file ({{ fname }}) has the wrong type. Requested was an import of type {{ req }}, the file contains type {{ is }}.", "BADCRC": "The import file ({{ fname }}) has been tampered with. Only original, unmodified files downloaded with the export function may be imported.", "INVALIDFILE": "The given file ({{ fname }}) is invalid; either it has been tampered with or you have selected a wrong file."}}}, "EDITOR": {"LOADING": "Editor loading...", "CODECHNG": {"TITLE": "Code changed!", "TEXT": "Changing the code value of any object should be avoided. It may alter existing workflows rulesets, rendering them useless or invalid. Are you sure you want to save?"}}, "VERSIONING": {"EDIT": {"VERSIONED": "Edit (V. {{ vers }})", "NEW": "Edit (new)"}, "VERSION": "Version {{ vers }}", "LASTCHG": "last change by {{ realname }} ({{ username }}) on {{ date }}"}, "WFMODIFIER": {"NOMODIFIERS": "No workflow modifiers set", "HASMODIFIERS": "{{ num }} workflow modifiers set:", "CT": {"SELECT": {"STD": "Omit if used in a check with checktype {{ code }}", "INV": "Omit if not used in a check with checktype {{ code }}"}}, "MD": {"SELECT": {"STD": "Omit if used for model {{ code }}", "INV": "Omit if not used for model {{ code }}"}}, "DT": {"SELECT": {"STD": "Omit if used for a model with devicetype {{ code }}", "INV": "Omit if not used for a model with devicetype {{ code }}"}}, "CO": {"SELECT": {"STD": "Omit if config table entry {{ code }} active", "INV": "Omit if config table entry {{ code }} not active"}}, "P": {"INQUEUE": {"STD": "Omit if procedure {{ code }} is also queued in the check", "INV": "Omit if procedure {{ code }} is not queued in the check"}, "PASS": {"OMIT": "Omit if procedure {{ code }} is finished and has passed", "SKIP": "Skip if procedure {{ code }} is finished and has passed"}, "FAIL": {"OMIT": "Omit if procedure {{ code }} is finished and has failed", "SKIP": "Skip if procedure {{ code }} is finished and has failed"}, "SKIP": {"OMIT": {"STD": "Omit if procedure {{ code }} is skipped", "INV": "Omit if procedure {{ code }} is not skipped"}, "SKIP": {"STD": "Skip if procedure {{ code }} is skipped", "INV": "Skip if procedure {{ code }} is not skipped"}}, "OMIT": {"OMIT": {"STD": "Omit if procedure {{ code }} is omitted", "INV": "Omit if procedure {{ code }} is not omitted"}, "SKIP": {"STD": "Skip if procedure {{ code }} is omitted", "INV": "Skip if procedure {{ code }} is not omitted"}}}, "S": {"PASS": {"OMIT": "Omit if step {{ code }} is finished and has passed", "SKIP": "Skip if step {{ code }} is finished and has passed"}, "FAIL": {"OMIT": "Omit if step {{ code }} is finished and has failed", "SKIP": "Skip if step {{ code }} is finished and has failed"}, "SKIP": {"OMIT": {"STD": "Omit if step {{ code }} is skipped", "INV": "Omit if step {{ code }} is not skipped"}, "SKIP": {"STD": "Skip if step {{ code }} is skipped", "INV": "Skip if step {{ code }} is not skipped"}}, "OMIT": {"OMIT": {"STD": "Omit if step {{ code }} is omitted", "INV": "Omit if step {{ code }} is not omitted"}, "SKIP": {"STD": "Skip if step {{ code }} is omitted", "INV": "Skip if step {{ code }} is not omitted"}}}, "M": {"PASS": {"OMIT": "Omit if step/measure {{ code }} is finished and has passed", "SKIP": "Skip if step/measure {{ code }} is finished and has passed"}, "FAIL": {"OMIT": "Omit if step/measure {{ code }} is finished and has failed", "SKIP": "Skip if step/measure {{ code }} is finished and has failed"}, "YES": {"OMIT": "Omit if step/measure {{ code }} has been set to 'Yes'", "SKIP": "Skip if step/measure {{ code }} has been set to 'Yes'"}, "NO": {"OMIT": "Omit if step/measure {{ code }} has been set to 'No'", "SKIP": "Skip if step/measure {{ code }} has been set to 'No'"}, "SKIP": {"OMIT": {"STD": "Omit if step/measure {{ code }} is skipped", "INV": "Omit if step/measure {{ code }} is not skipped"}, "SKIP": {"STD": "Skip if step/measure {{ code }} is skipped", "INV": "Skip if step/measure {{ code }} is not skipped"}}, "OMIT": {"OMIT": {"STD": "Omit if step/measure {{ code }} is omitted", "INV": "Omit if step/measure {{ code }} is not omitted"}, "SKIP": {"STD": "Skip if step/measure {{ code }} is omitted", "INV": "Skip if step/measure {{ code }} is not omitted"}}}}, "ERROR": {"CLIENT": {"TITLE": "A severe error has occurred...", "TOTTL": "Timeout...", "ADTTL": "Access denied...", "DETAILS": "Details following:", "BACK": "Back to login", "MSG": {"TIMEOUT": "The current session has been cancelled due to an timeout. Please log in again and continue.", "ACCDENY": "Access was denied because of missing rights or privileges. Please log in again and continue.", "SEVREST": "A severe server error has occurred; the process can not be continued in this stage. Please return to the login or use the title bar to select the next destination.", "HTTP404": "@:ERROR.CLIENT.MSG.SEVREST", "HTTP500": "@:ERROR.CLIENT.MSG.SEVREST"}}}, "LOGIN": {"TITLE": "<PERSON><PERSON>", "USERNAME": "Username", "PASSWORD": "Password", "BUTTON": "@:LOGIN.TITLE"}, "UI": {"BUTTONS": {"MEDITOR": {"RESET": "@:WFLOW.INPUT.BUTTON.CLEAR", "ADDMSEL": "Add {{ num }} item(s)", "ADDMSELTEST": "Test {{ num }} item(s)", "SAVE": "Save", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SELECT": "Select", "SELALL": "Select all", "DESELALL": "Clear all"}, "ALERT": {"OK": "OK", "YES": "@:WFLOW.INPUT.YES", "NO": "@:WFLOW.INPUT.NO"}}, "MEDITOR": {"MAXSEL": "You max select {{ num }} entry/ies", "NOMORESEL": "The maximum number of selected entries has been reached."}}, "VIEWER": {"IMAGE": {"TITLE": "Image viewer", "ZOOM": {"FULL": "Full view", "FIT": "Fit into window", "STEP": "Zoom {{ factor }}x", "MAX": "Original size"}}, "PDF": {"TITLE": "PDF viewer"}, "VIDEO": {"TITLE": "Video player"}}, "TLISTEDIT": {"TOOLTIP": {"DEL": "Delete entry", "MEDMGR": "Start media manage for this item", "EDIT": "Edit item", "NEW": "Create new item"}, "LOADING": "Loading editor...", "DISABLED": "Disabled!", "CNT": {"CHECK": "{{ cnt }} check(s)", "MODEL": "{{ cnt }} model(s)", "IMAGES": "{{ cnt }} image(s)", "DOCS": "{{ cnt }} document(s)"}, "NEW": "New entry", "CODE": "Code", "TITLE": "Title", "DESC": "Description", "DISSET": {"TITLE": "Disabled", "TRUE": "This entry is deleted and may not be used any longer.", "FALSE": "This entry is active."}, "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "ADD": "Add", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "MEDITOR": {"GROUP": "Group", "USER": "User"}, "DASHBLOCK": {"TOOLTIP": {"CLOSE": "Remove block", "GOTO": "Go to item", "SETFILTER": "Edit filter settings", "EDITTITLE": "Edit title of block"}}, "DROPBOX": {"INTRO": "Drag export files into this box for import!", "UPLOADING": "Uploading {{ num }} file(s)", "REMAINING": "{{ num }} file(s) remaining", "SUCCESS": "{{ num }} file(s) uploaded successfully", "ERRORS": "{{ num }} file(s) uploaded with errors"}, "PRV": {"MNGMUC": {"TTL": "Manage models/unit/checks", "DSC": "May use management views for models, units and checks"}, "MNGPSM": {"TTL": "Manage procedures", "DSC": "May use management views for procedures, as well as steps and measures"}, "MNGTOL": {"TTL": "Manage tooltypes", "DSC": "May use management views for tool types"}, "MNGPAR": {"TTL": "Manage parameters", "DSC": "May use management views for parameters, devicetypes and checktypes as well as managing optional settings"}, "MNGUSR": {"TTL": "Manage users", "DSC": "May use the user management console"}, "MNGALL": {"TTL": "Manage all", "DSC": "May use all management views (privilege collection)"}, "EDTMOD": {"TTL": "Edit models", "DSC": "May modify existing models"}, "EDTUNT": {"TTL": "Edit units", "DSC": "May modify existing units"}, "EDTPSM": {"TTL": "Edit procedures", "DSC": "May modify existing procedures, including steps and measures (which also may be deleted)"}, "EDTTTY": {"TTL": "Edit tooltypes", "DSC": "May modify existing tooltypes"}, "EDTALL": {"TTL": "Edit all", "DSC": "May modify all existing objects (privilege collection)"}, "CRTMOD": {"TTL": "Create models", "DSC": "May add new models"}, "CRTUNT": {"TTL": "Create units", "DSC": "May add new units"}, "CRTPSM": {"TTL": "Create procedures", "DSC": "May add new procedures"}, "CRTTOL": {"TTL": "Create tools", "DSC": "May add new tool units"}, "CRTTTY": {"TTL": "Create tool types", "DSC": "May add new tool types"}, "CRTCHK": {"TTL": "Create checks", "DSC": "May add new checks or delete checks not yet started"}, "CRTALL": {"TTL": "Create all", "DSC": "May add new object of any kind (privilege collection)"}, "DELMOD": {"TTL": "Delete models", "DSC": "May delete or disable models"}, "DELUNT": {"TTL": "Delete units", "DSC": "May delete units"}, "DELPRC": {"TTL": "Delete procedures", "DSC": "May delete or disable procedures"}, "DELTTY": {"TTL": "Delete tooltypes", "DSC": "May delete or disable tool types"}, "DELTOL": {"TTL": "Delete tools", "DSC": "May delete or disable tools"}, "DELCHK": {"TTL": "Delete checks", "DSC": "May delete or disable checks"}, "DELALL": {"TTL": "Delete all", "DSC": "May delete or disable all objects (privilege collection)"}, "USRMGO": {"TTL": "@:PRV.MNGUSR.TTL", "DSC": "May manage (add, change, disable) users of lower level"}, "USRMGA": {"TTL": "Manage all users", "DSC": "May manage (add, change, disable) users of all levels"}, "GRTPRO": {"TTL": "Grant own privileges", "DSC": "May grant privileges owned to individuals"}, "GRTPRA": {"TTL": "Grant all privileges", "DSC": "May grant all privileges to individuals"}, "GRTTOG": {"TTL": "Grant group privileges", "DSC": "May grant privileges also to groups (extending privilege)"}, "GRPCRT": {"TTL": "Create group", "DSC": "May create new groups"}, "WFLMNG": {"TTL": "Manage workflow", "DSC": "May manage check and check workflows, comment on checks, view supervisor information"}, "WFLREG": {"TTL": "Register to workflow", "DSC": "May register to workflows (if applicable) or unregister from workflows (if self-registered)"}, "CHGCOD": {"TTL": "Change code", "DSC": "May change the object code when editing (extending privilege)"}, "FINALZ": {"TTL": "Finalize version", "DSC": "May finalize current version of object (only available with privilege to edit object; extending privilege)"}, "MODVRS": {"TTL": "Modify version", "DSC": "May edit a finalized version of an object (spelling mistakes only)"}, "TRUSER": {"TTL": "Experienced user", "DSC": "The user is experienced and may use several higher level functions"}, "TKOVER": {"TTL": "Take over assigned tasks", "DSC": "The user may take over tasks self assigned to other users if applicable"}, "GLSRCH": {"TTL": "Access to global search", "DSC": "May use global search function"}, "MNGNTC": {"TTL": "View and process problem reports", "DSC": "May view, manage and process problem reports on the report page"}, "MNGCFG": {"TTL": "Show and edit configuration tables", "DSC": "The user may show and edit all configuration tables"}, "MNGCFE": {"TTL": "Edit configurations for checks", "DSC": "The user may configure tables on checks."}}, "PDF": {"MEASUREMENTERRORREPORT": {"TITLE": "Measurement error report", "PROCEDURE": "Procedure", "STEP": "Step", "MEASURE": "Measure", "MODEL": "Model", "UNIT": "Unit", "VALUE": "Value", "SAVEDBY": "Saved by", "ERRORCATEGORY": "Error category", "STATUS": {"TITLE": "State", "UNPROC": "Left out", "PASSED": "Passed", "FAILED": "Failed", "INVALID": "Invalid", "ERROR": "Error"}, "EXPINPUT": {"TARGET": "@:wflow_input_target", "THRESH": "@:WFLOW.INPUT.THRESH", "VALUERNG1": "@:WFLOW.INPUT.VALUERNG1", "VALUERNG2": "@:WFLOW.INPUT.VALUERNG2", "VALUECMP1": "@:WFLOW.INPUT.VALUECMP1", "VALUECMP2": "@:WFLOW.INPUT.VALUECMP2", "VALUECMP3": "@:WFLOW.INPUT.VALUECMP3", "VALUECMP4": "@:WFLOW.INPUT.VALUECMP4", "VALUECMP5": "@:WFLOW.INPUT.VALUECMP5", "STATISTIC": "Measured value, no check took place", "CHECKCMP1": "Check if measured value is less than {{ val }}", "CHECKCMP2": "Check if measured value ist less or equal to  {{ val }}", "CHECKCMP3": "Check if measured value ist equal to {{ val }}", "CHECKCMP4": "Check if measured value is greater or equal to  {{ val }}", "CHECKCMP5": "Check if measured value is greater than {{ val }}", "TEXTLEN1": "@:WFLOW.INPUT.TEXTLEN1", "TEXTLEN2": "@:WFLOW.INPUT.TEXTLEN2", "REGEXP": "Checked text:", "EXPNO": "@:WFLOW.INPUT.EXPNO", "EXPYES": "@:WFLOW.INPUT.EXPYES", "EXPBOTH": "@:WFLOW.INPUT.EXPBOTH", "TEXTTIMERSTART": "Entered start time", "TEXTTIMERSTOP": "Entered stop time", "TEXTTIMERSTOPLT": "Entered start time: exptected time to {{ val }} mins", "TEXTTIMERSTOPGT": "Entered start time: expected time greater than {{ val }} mins", "CHOICELIST": "Choice list"}}, "BOOL": {"YES": "YES", "NO": "NO"}, "MAINTTL": "Check Report", "STATUS": {"OPEN": "@:UNIT.VIEW.STATUS.OPEN", "FAILED": "@:WFLOW.INTRO.STATUS.FAIL", "PASSED": "@:WFLOW.INTRO.STATUS.PASS", "UNFIN": "@:WFLOW.INPUT.STATUS.TODO", "PASS": "@:WFLOW.INPUT.STATUS.PASS", "FAIL": "@:WFLOW.INPUT.STATUS.FAIL", "SKIP": "@:WFLOW.STEP.STATUS.SKIP", "CANCELLED": "CANCELLED", "WARNINT": "FAILED <sup>*)</sup>", "WARNEXT": "PASSED <sup>*)</sup>"}, "USTATUS": {"OPEN": "Open", "CLOSED": "Closed", "DISCARDED": "Discarded", "ACLOSED": "Closed & archived", "ADISCARDED": "Discarded & archived"}, "MODEL": "@:WFLOWEDIT.OPT1.MD", "UNIT": "@:WFLOW.INTRO.UNIT", "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "DATE": {"SCHED": "@:WFLOW.INTRO.DATE.SCHED", "DUE": "@:WFLOW.INTRO.DATE.DUE", "NOSCHED": "@:WFLOW.INTRO.DATE.NOSCHED", "NODUE": "@:WFLOW.INTRO.DATE.NODUE", "START": "@:WFLOW.INTRO.DATE.START", "FIN": "@:WFLOW.INTRO.DATE.FIN", "NOSTART": "@:WFLOW.INTRO.DATE.NOSTART", "NOFIN": "@:WFLOW.INTRO.DATE.NOFIN"}, "USER": {"NOONE": "No one yet", "ANY": "Anyone", "GRP": "User of group %s"}, "VERSION": "@:PROCEDURE.VIEW.VERS", "STEP": {"ASSNC": "<i>Assignee:</i> %s, <b>not committed yet</b>", "ASSC": "<i>Assignee:</i> %s, <i>committed by:</i> %s"}, "MEASURE": {"TOOL": "<i>Tool (type </i><b>%s</b>: %s) used:</i> <b>%s</b>", "TOOLNOSEL": "<i>Used tool (type </i><b>%s</b>: %s<i>) <b>not selected yet</b></i>", "REP": "<i>Measured by </i><b>%s</b> <i>at</i> <b>%s</b>", "COMM": "<i>Comment:</i> %s", "RAW": "<i>Matrix values:</i> %s"}, "MINPUT": {"VALUECMP": "Value must be %s %s", "CHECKCMP": "'Yes' if the measured value is %s %s", "COMP": {"T1": "less than", "T2": "less than or equal to", "T3": "equal to", "T4": "greater than or equal to", "T5": "greater than"}, "THRESH": "Target value: %s, Threshold %s", "VALUERNG": "Value must be between %s and %s", "TEXTLEN": "Text must be at least %d characters long", "TEXTPAT": "Text must match a specific pattern: %s", "CHOICE": "A value must be chosen from a list", "EXP": {"NO": "@:WFLOW.INPUT.EXPNO", "YES": "@:WFLOW.INPUT.EXPYES", "BOTH": "@:WFLOW.INPUT.EXPBOTH"}, "STATISTICAL": "For statistical analysis; any value is accepted", "TIMERA": "Time measurement (Start)", "TIMERS": "Time measurement (Stop) for statistical reasons", "TIMERQ": {"T1": "Time measurement (Stop); time span must be less than %d minutes", "T5": "Time measurement (Stop); time span must be more than %d minutes"}, "TIMERC": {"T1": "Post check time measurement; time span must be less than %d minutes", "T5": "Post check time measurement; time span must be less than %d minutes"}}, "STATREPORT": "Measurement Statistics", "PAGENO": "Page %d of %d", "STAT": {"CHECKINTRO": "For the statistical overview the following checks are used:", "CHECK": {"LINE1": "Check <b>#%s</b> (%s) for unit <b>%s</b> (%s), model <b>%s: %s", "LINE2": "Check Status: <b>%s</b>, unit status: <b>%s</b>"}, "MSR1": "Measure <b>%s: %s</b>", "MSR2": "Step <b>%s: %s</b>, Procedure <b>%s: %s</b>", "MSR2A": "Step <b>%s: %s</b>", "MSR2B": "Procedure <b>%s: %s</b>", "MSR3S": "Rule: <b>%s</b>", "MSR3O": "Rule: <b>%s</b> (optional, may be skipped)", "MSR4T": "Tooltype: <b>%s: %s</b>", "MSR4N": "Tooltype: <i>no tool used</i>", "OLDV": "<sup>*)</sup> An older version of the measure was used during the check; the results may not be compatible with the current version", "NODATA": "No data available", "HDR": {"STATUS": "@:WFLOW.INPUT.STATUS.TITLE", "UNIT": "Unit", "USER": "@:MEDITOR.USER", "TOOL": "Tool used", "VALUE": "Value"}, "FINAL": {"INTRO": "The following id-strings were used creating this report. You may use them to recreate this report or parts from it:", "CHECKS": "Checks:", "MEASURES": "Measures:"}, "TITLE": {"FRONT": "Overview", "STATS": "Measure %s"}}, "COMMENT": {"TOOLTIP": "Generate comment report", "TTL": "Comment report", "FOOTER": {"USERS": "All users", "TOOLTYPE": "Tooltype '%s' (%s)", "MODEL": "Model '%s' (%s)", "UNIT": "Unit %s", "CHECK": "Check #%d (unit %s, model %s)", "STEP": "Step '%s' V.%d (%s.%s)", "MEASURE": "Measure '%s' V.%d (%s.%s.%s)"}, "ELEMENT": {"USER": "User %s (%s)", "TOOLTYPE": "Tool type %s", "TOOL": "Tool unit %s", "UNIT": "Unit %s (%s)", "CHECK": "Check #%d (unit %s, model %s)", "MSMNT": "Measure %s in step %s of procedure %s", "STEP": "Step %s", "PROC": "Procedure %s", "MSMNTS": "Measure %s", "MEAS": "Measure %s"}}, "TUREPORT": {"HEADER": "Usage report toolunit", "FOOTER": "Tool type <b>%s</b>, unit <b>%s</b>", "TITLE": {"MODEL": "<b>Model %s</b>, Unit %s", "TIME": "%s.<b>%s</b>"}, "CONTINUED": "<i>(cont.)</i>", "ENTRY": {"MODEL": "Procedure <b>%s</b>, Step <b>%s</b>, Measure <b>%s</b>", "TIMET": "Model <b>%s</b>, Unit <b>%s</b>", "TIMEB": "<i>Procedure <b>%s</b>, Step <b>%s</b>, Measure <b>%s</b></i>"}}, "HINT": {"CHECKWARN": {"INTERNAL": "Some measurements have failed but do not or only slightly affect the proper functionality of the machine.", "CUSTOMER": "A measurement that does not affect the proper functionality of the machine is slightly outside the defined tolerance values."}}}, "CHANGELOG": {"ITEM": {"PROCEDURE": "Procedure", "STEP": "Step", "MEASURE": "Measure", "IMAGE": "Image", "DOCUMENT": "Document", "MODEL": "Model", "CHECKTYPE": "Check type"}, "TYPE": {"CREATE": "Created", "CHANGECODE": "Code changed from {{ oldcode }}", "CHANGEFIELD": "Field {{ field }} changed", "CHANGESEQ": "Order changed (multiple items affected)", "CHANGEPID": "Procedure updated to version {{ version }}", "FINALIZE": "Finalized", "DELETE": "Deleted", "CREATEATT": "Created and added to {{ tgt }}", "DELETEATT": "Deleted from {{ tgt }}", "CREATEACTP": "Added procedure to model", "DELETEACTP": "Removed procedure from model", "CREATEACTCT": "Added check type to model", "DELETEACTCT": "Removed check type from model", "PREALLOCATION": "Predefined assignments modified"}, "VIEWER": {"TITLE": "Change log", "EXTRODIRTY": "Entries marked blue are direct user changes and require a finalization to become active.", "EXTROPOSTFIN": "Entries marked red are changes to the item that has been made after finalization.", "NOENTRIES": "The change log is currently empty."}, "BUTTON": {"OPENDIALOG": "Change log", "CLOSE": "Close"}}, "GSEARCH": {"WIZARD": {"TITLE": "Global Search", "TEXT1": "Please specify the search term. The term must be at least three characters long, an asterisk ('*') is a wildcard for a maximum of 10 characters. A pipe symbol ('|') at the beginning or the end specifies, that the term must be at the beginning resp. the end of the text. Multiple terms can be specified by concatenating them with plus signs ('+'); all these terms must be found in the text. Case is ignored unless a term is prepended by an exclamation mark ('!').", "TEXT2": "Please select the fields to be processed during the search.", "TEXT3": "Please select the languages to be processed (in multilangual fields).", "TEXT4": "Please select the search mode on versionized objects:"}, "TYPE": {"CHECK": {"SELECT": "Checks", "DISPLAY": "<u>Check</u> <b>{{ object.id }}</b> (<i>{{ object.checktype.title.en }}</i>) for unit <b>{{ object.unit.code}}</b>, model <b>{{ object.unit.model.code }}</b>"}, "MEASUREMENT": {"SELECT": "Measurements", "DISPLAY": "<u>Measurement</u> in check <b>{{ object.check.id }}</b> (unit {{ object.check.unit.code }}, model {{ object.check.unit.model.code }}) for measure <b>{{ object.measure.code }}</b> in step <b>{{ object.measure.step.code }}</b>, procedure <b>{{ object.measure.step.procedure.code }}</b>"}, "CHECKTYPE": {"SELECT": "Checktypes", "DISPLAY": "<u>Checktype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"}, "DEVICETYPE": {"SELECT": "Devicetypes", "DISPLAY": "<u>Devicetype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)"}, "MODEL": {"SELECT": "Models", "DISPLAY": "<u>Model</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), version {{ object.version || '(edit)' }}"}, "UNIT": {"SELECT": "Units", "DISPLAY": "<u>Unit</u> <b>{{ object.code }}</b> in model <b>{{ object.model.code }}</b>"}, "MEASURE": {"SELECT": "Measures", "DISPLAY": "<u>Measure</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) in step <b>{{ object.step.code }}</b> / procedure <b>{{ object.step.procedure.code }}</b>, version {{ object.procedure.version || '(edit)' }}"}, "STEP": {"SELECT": "Steps", "DISPLAY": "<u>Step</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>) in procedure <b>{{ object.procedure.code }}</b>, v {{ object.procedure.version || '(edit)' }}"}, "PROCEDURE": {"SELECT": "Procedures", "DISPLAY": "<u>Procedure</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>), version {{ object.version || '(edit)' }}"}, "TOOLTYPE": {"SELECT": "Tooltypes", "DISPLAY": "<u>Tooltype</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)"}, "TOOLUNIT": {"SELECT": "Toolunits", "DISPLAY": "<u>Toolunit</u> <b>{{ object.code }}</b> in tooltype <b>{{ object.tooltype.code }}</b>"}, "USER": {"SELECT": "Users", "DISPLAY": "<u>User</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)"}, "NOTICE": {"SELECT": "Problem report", "DISPLAY": "<u>Problem report</u> <b>{{ object.id }}</b>"}, "CONFIGTABLE": {"SELECT": "Configuration tables", "DISPLAY": "<u>Configuration table</u> <b>{{ object.id }}</b>"}, "CONFIGENTRY": {"SELECT": "Configuration table entries", "DISPLAY": "<u>Entry</u> <b>{{ object.code_id }}</b> in configuration table <b>{{ object.configtable_id }}</b>"}}, "SCOPE": {"ALLALL": {"TEXT": "All objects (full search)", "TTIP": "All versions (including non finalized) are searched, all matching objects shown."}, "ALLRECENT": {"TEXT": "Full search, most recent found object only", "TTIP": "All versions (including non finalized) are searched, but only the most recent object is shown."}, "FINALL": {"TEXT": "Finalized objects", "TTIP": "All finalized versions are searched, all matching objects shown."}, "FINRECENT": {"TEXT": "Finalized objects, most recent found object only", "TTIP": "All finalized versions are searched, but only the most recent object is shown."}, "LATESTV": {"TEXT": "Latest version", "TTIP": "Only the latest version (finalized) is searched."}, "EDITV": {"TEXT": "Edit version only", "TTIP": "Only the most recent, non finalized versions are searched."}}, "FIELD": {"ID": "Number", "COMMENT": "Comment", "CODE": "Code", "TITLE": "Title", "DESCRIPTION": "Description/Hints", "CUSTOMER": "Customer", "USERNAME": "Username", "REALNAME": "Realname", "TEXT": "Description", "COL1": "First column", "COL2": "Second column", "COL3": "Third column", "COL4": "Fourth column", "COL5": "Fifth column", "COL6": "Sixth column", "DELETED": "Metainformation", "CALCULATION": "Choice list"}, "BUTTONS": {"NEWSEARCH": "New Search", "CLOSE": "Close"}, "RESULT": {"TITLE": "Search results", "TEXT": "The last search resulted in the following matches, sorted by date of change:", "TOOMUCH": "(Note: Only the first 50 matches are displayed)", "NOMATCH": "There are no matches"}}, "NOTICES": {"TOOLTIP": {"SHOW": "Show", "SHOWALL": "Show all", "HIDE": "<PERSON>de", "FILTER": "Filter this column", "NOFILTER": "Disable filter"}, "EXPORT": {"TTL": "Export problem reports", "TYPETEXT": "Please select the file type for export:", "FILTERTEXT": "Please select the scope:", "CSV": "Comma Separated Values (CSV)", "JSON": "JavaScript Output Notation (JSON)", "XML": "Extensible Markup Language (XML)", "UNARCHIVED": "All non archived reports", "ALL": "All (including archived) reports", "FILTERED": "All shown (filtered) reports"}, "FILTEREDIT": {"TITLE": "Please define the filter", "ID": "Please select the range of IDs to be shown. You can use '<from>-<to>', '-<to>' or '<from>-'. Example: '10-20' (all IDs from 10 to 20); '-50' (all IDs up to 50).", "CATEGORY": "Please select the categories to be shown:", "PATH": "Please enter a search term; this will be matched against the displayed path text:", "TEXT": "Please enter a search term; this will be matched against the description:", "ARTICLE": "Please enter a search term; this will be matched against the article number (not: article description). If you want to match against the article description, prefix the term with a colon ':'.", "TIMELOSS": "Please select the time loss values to be shown:", "STATUS": "Please select the statu types values to be shown:"}, "TITLE": {"PROBLREP": "Problem report", "DESC": "Description", "PROPOSALS": "Proposals:", "CAT": "Category", "TIMELOSS": "Time loss (ca.)", "ARTNO": "Article no.", "ARTDESC": "Article desc.", "PROBLREPNO": "Problem report no."}, "TEXT": {"DESC": "Please enter a short and meaningful description. You can choose from a small selection of proposals below the text box or enter your own text.", "CHOOSECAT": "Please select one of the given categories matching the problem report best. If you have lost time due to the problem, please select the best matching value in the time loss select box.", "ARTICLE": "If the problem report is related to a certain article you can enter this article by number and description here:", "STTCHANGE": "Please enter a short comment regarding the status change:"}, "BUTTON": {"USE": "Use", "CANCEL": "Cancel", "SEND": "Send", "CATEGORIES": "Edit categories", "TEMPLATES": "Edit text proposals", "EXPORT": "Export", "STT_12": "Process report", "STT_21": "Cancel processing", "STT_25": "Finish processing", "STT_52": "Restart processing", "STT_59": "Archive", "CLOSE": "Close"}, "VIEW": {"LOCATION": "Path", "CATEGORY": "Category", "ARTICLE": "Article", "TIMELOSS": "Time loss", "NOTEXT": "N/A", "DESC": "Description", "ID": "No.", "PATH": "Path", "TEXT": "Description", "STATUS": "Status"}, "TIMELOSS": {"15": "approx. 15 minutes", "30": "approx. 30 minutes", "60": "approx. 1 hour", "90": "approx. 1½ hour", "120": "approx. 2 hours", "180": "approx. 3 hours", "240": "more than 4 hours"}, "ALERT": {"CATMISS": {"TITLE": "Missing category", "TEXT": "You have to select on of the given categories!"}, "DESCMISS": {"TITLE": "Description missing", "TEXT": "The description is missing or too short!"}, "THANKS": {"TITLE": "Thank you!", "TEXT": "The problem report was sent and will be processed shortly!"}, "CONFDEL": {"TITLE": "Really delete?", "TEXT1": "Really delete this entry? The change can not be undone, earlier use of this entry may be modified or corrupted.", "TEXT2": "Really delete this entry? The entry will be removed from this list and may not be used in the future. Earlier uses of this entry remain valid, though."}}, "MODAL": {"EDITTEXT": {"TITLE": "New entry", "TEXT": "Please edit the text for this entry:"}, "NEWTEXT": {"TITLE": "Edit entry", "TEXT": "Please enter the text for the new entry (at least 3 characters):"}, "SNIPPETCAT": {"TITLE": "Problem report categories", "SNIPPET": "Category"}, "SNIPPETDESC": {"TITLE": "Text proposals for problem reports", "SNIPPET": "Text proposal"}}, "STATUS": {"1": "Submitted", "2": "Processed", "5": "Closed", "OPEN": "<b>submitted</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}", "PROCESSED": "<b>processed</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}", "CLOSED": "<b>closed</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}", "ARCHIVED": "<b>archived</b> on {{ time }}"}, "PATHTYPE": {"CHECKSTEP": "Check step", "CHECKGENERAL": "Check (general)"}, "SEGMENT": {"MODEL": "Model <b>{{ code }}</b>", "UNIT": "Unit <b>{{ code }}</b>", "CHECK": "Check <b>{{ id }}</b>", "PROCEDURE": "Procedure <b>{{ code }}</b>", "STEP": "Step <b>{{ code }}</b>"}, "CORRECT": {"TITLE": "Correction", "DESCRIPTION": "Please edit the description:", "TIMELOSS": "Please select a new value for time loss:", "CATEGORY": "Please choose a new category:", "ARTICLE1": "Step 1/2: Please edit the article number:", "ARTICLE2": "Step 2/2: Please edit the article description:"}}, "SNIPPET": {"TITLE": {"ADD": "add"}, "TOOLTIP": {"LOCK": "Closes the entry. Earlier uses of this entry are still valid, however the entry may not be chosen any longer.", "UNLOCK": "Reopens the entry; it may be chosen again.", "DELETE": "Deletes the entry after confirmation for good.", "EDIT": "Edit the text of the entry.", "REORD": "The entry may be moved to another position per drag & drop"}, "BUTTON": {"CLOSE": "Close"}}, "CONFIGTABLE": {"CONFIGTABLES": "Configuration tables", "ALERT": {"CONFHEADER": "Are you sure?", "DELETE": "Do you want to disable this entry?"}, "EDITOR": {"TITLE": {"NEW": "New entry", "EDIT": "Edit entry"}}, "EDIT": {"TITLE": "Title", "HEADER": "Header", "BUTTON": {"CANCEL": "@:MEASURE.EDIT.BUTTON.CANCEL", "CLOSE": "@:MEASURE.EDIT.BUTTON.CLOSE"}}, "LIST": {"ENTRY": {"EDIT": "Edit entry", "DELETE": "Disable entry", "UNDELETE": "Enable entry", "BLOCKED": "This entry is used by a workflow rule."}, "RELATION": {"NONE": "no model related", "MODEL": "related model:"}, "VERSION": "Version", "TOOLTIP": {"IMPORTTABLE": "Import table", "NEWTABLE": "New table", "GOTOUNITS": "Go to model", "SHOWDEL": "Show disabled", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "CLONE": "Clone table", "DELETE": "Disable table", "EDIT": "Edit table", "REACTIVATE": "Enable table"}, "TOPROW": ""}, "VIEW": {"CODE": "code", "ACTIVE": "active", "DISABLED": "disabled"}, "CLONE": {"TITLE": "Clone configuration table", "TEXT": "Do you want to clone the table {{ title }}?"}, "DELETE": {"TITLE": "Deactivate configuration table", "TEXT": "Do you really want to disable the table {{ title }}? You can enable it again later."}}, "SETTINGS": {"PROC": {"TAGS": {"TITLE": "Manage procedure tags", "ADD": "Add tags", "DELETE": "Delete tag", "EDIT": "Edit tag", "COLLAPSE": "Collapse sub-attributes"}, "EDITTAG": {"TITLE": "Edit procedure tag"}, "NEWTAG": {"TITLE": "Add procedure tag"}, "TAGVALUE": {"ADD": "Add value", "DELETE": "Delete value", "EDIT": "Edit value"}, "NEWTAGVALUE": {"TITLE": "Add value"}, "EDITTAGVALUE": {"TITLE": "Edit value"}}, "ALERT": {"CONFDEL": {"TITLE": "Deleting a procedure tag", "TEXT": "Are you sure about deleting the selected procedure tag?"}}}, "MEASUREMENTERRORCATEGORIES": {"TITLE": "Manage measurement error categories", "ADD": "Add measurement error category", "DELETE": "Delete measurement error categories", "EDIT": "Edit measurement error categories", "TOOLTIP": {"REORDER": "@:MODEL.VIEW.TOOLTIP.REORDER"}}, "MEASUREMENTERRORREPORT": {"TITLE": "Report Measurement errors", "MEASURES": {"PERIODFROM": "Choose the start of the period:", "PERIODTO": "Choose the end of the period:", "TEXT": "Choose the measurements. Only measurements which have been corrected will be displayed:"}, "SORT": {"TEXT": "Choose the sorting:", "ERRORCATEGORY": "Error category", "MEASURE": "Measure", "USER": "User", "UNIT": "Unit"}}}