{"FRAME": {"TITLE": "LeanLogic QA", "DASHBOARD": "Dashboard", "DASHBOARDS": "Dashboards", "MANAGE": {"TITLE": "Management", "MODEL": "<PERSON><PERSON>", "UNIT": "Einheiten", "PROCEDURE": "<PERSON><PERSON><PERSON>", "TOOL": "Werkzeuge", "DEVICETYPE": "Gerätetypen", "CHECKTYPE": "Prüfungsarten", "USERS": "<PERSON><PERSON><PERSON>", "CHANGEPW": "Passwort ändern", "NOTICES": "Fehlerberichte", "CONFIGTABLE": "Konfigurationstabellen", "SETTINGS": "Einstellungen", "MEASUREMENTERRORCATEGORIES": "Messungs-Fehlerkategorien", "MEASUREMENTERRORCATEGORIESREPORT": "Rapport Messungsfehler"}, "LOGOUT": "Logout", "EDITDTYPE": {"TITLE": "@:FRAME.MANAGE.DEVICETYPE", "TEXT": "Gerätetypen hinzufügen, editieren oder deaktivieren:"}, "EDITCTYPE": {"TITLE": "@:FRAME.MANAGE.CHECKTYPE", "TEXT": "Prüfungsar<PERSON> hinzufügen, editieren oder ausschalten:"}, "CHPWD": {"TITLE": "@:FRAME.MANAGE.CHANGEPW", "PW1": "Bitte das neue Passwort eingeben:", "PW2": "Bitte zur Überprüfung wiederholen:", "NOMATCH": "Passwörter stimmen nicht überein; Vorgang wird abgebrochen!", "TOOSHORT": "Das Passwort ist zu kurz; drei Zeichen sind Minimum!", "OK": "Passwort wurde geändert."}, "GSEARCH": "<PERSON><PERSON>"}, "DASHBOARD": {"TOOLTIP": {"PREVDB": "Vorheriges Dashboard (mit gehaltener Shift-taste wird das aktuelle Dashboard mit dem vorherigen in der Reihenfolge getauscht)", "NEXTDB": "Nächstes Dashboard (mit gehaltener Shift-taste wird das aktuelle Dashboard mit dem nächsten in der Reihenfolge getauscht)", "DELDB": "Dashboard entfernen", "EDITDB": "Dashboardtitel ändern", "ADDDB": "Neues Dashboard"}, "NEWBLOCK": {"TITLE": "Neuer Block", "TEXT": "Den Inhalt des Blocks festlegen:"}, "WELCOME": "<PERSON><PERSON><PERSON><PERSON>, {{ name }}!", "VERSION": "Version {{ version }}", "ADDDASH": {"BUTTON": "Neuer Block"}, "ADDDB": {"TITLE": "Neues Dashboard hinzufügen", "TEXT": "<PERSON>te geben Si<PERSON> einen Titel für das neue Dashboard ein. Der Titel muss mindestens 3 Zeichen lang sein."}, "EDITDBNAME": {"TITLE": "Dashboardtitel ändern", "TEXT": "<PERSON>te geben Si<PERSON> einen neuen Titel für das Dashboard ein. Der Titel muss mindestens 3 Zeichen lang sein."}, "EDITBLKNAME": {"TITLE": "Blocktitel ändern", "TEXT": "<PERSON>te geben Si<PERSON> einen neuen Titel für den Block ein. Der Titel muss mindestens 3 Zeichen lang sein."}, "DELETEDB": {"TITLE": "Sind Sie sicher?", "TEXT": "<PERSON><PERSON><PERSON> dieses Dashboard entfernen?"}, "FILTERS": {"TITLE": "Blockfilter editieren", "TEXT": "Bitte wählen Sie eine Aktion aus:", "ACTENABLE": "Verwenden:", "ACTEDIT": "Editieren:", "ACTDISABLE": "Ausschalten:", "TYPES": {"MODELSEL": {"NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TEXT": "Bitte wählen Sie die Modelle aus, die in diesem Block betrachtet werden:"}}}, "TYPE": {"UCAM": {"TITLE": "<PERSON> zu<PERSON>, nicht beendete Prüfungen", "LINET": "Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>", "LINEBNS": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>"}, "UCMP": {"TITLE": "Unbeendete Prüfungen, die von mir bearbeitet werden können", "LINET": "Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>", "LINEBNS": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>"}, "FCFR": {"TITLE": "Abgeschlossene Prüfungen zur Einsicht", "LINET": "Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>", "LINEBNS": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>"}, "UWAC": {"TITLE": "Einheiten ohne eingerichtete Prüfungen", "LINET": "Einheit <b>{{ code }}</b> ({{ customer }})", "LINEB": "Modell <b>{{ model.code }}: {{ model.title.en }}</b>"}, "CPCH": {"TITLE": "Prüfungen zur Zeit in Bearbeitung", "LINET": "Einheit <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.de }}", "LINEB": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>, zu prüfen ab <b>{{ scheduled }}</b>, fertigstellen bis <b>{{ dueby }}</b>", "LINEBNS": "Modell <b>{{ model.code }}: {{ model.title.de }}</b>"}, "PMPC": {"TITLE": "Prozeduren und Modelle mit Änderungen", "LINEPROC": "Prozedur <b>{{ code }}</b>: {{ title.de }}", "LINEMOD": "Modell <b>{{ code }}</b>: {{ title.de }}"}}}, "CHECK": {"INPUT": {"TITLE": {"EDIT": "Bearbeite {{mname_de}}"}}, "OVERVIEW": {"ACTION": {"SCHEDULE": {"TITLE": "Prüfung terminieren", "TEXTSCHED": "Wählen Sie den frühesten Startzeitpunkt", "TEXTDUE": "<PERSON><PERSON><PERSON><PERSON>, bis wann die Prüfung abgeschlossen sein muss", "TEXTASSIGN": "Wählen Sie den Modus der Zuweisung", "TEXTASSIGNTO": "Die Prüfung folgender Gruppe oder Benutzer zuweisen", "TEXTADDASSIGN": "Eine Gruppe oder einen Benutzer als Zuweisungsziel auswählen"}, "CHANGEASS": {"TITLE": "Zuweisung ändern", "TEXT": "Wählen Sie bitte die neue Zuweisung für diesen Block:"}}}, "ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "CANCEL": "Bitte bestätigen Sie das Abbrechen der Prüfung, indem Sie dessen Nummer eingeben.", "WRONGNUMBER": "Falsche Nummer", "WRONGDELETEINPUT": "Ihre Eingabe entspricht nicht der Nummer der Prüfung. Die Prüfung wurde nicht abgebrochen.", "DELETEASS": {"TITLE": "Wirk<PERSON> das Zuweisungsziel {{ name }} löschen?", "TEXT": "Alle Zuweisungen an dieses Ziel werden zurückgesetzt."}, "DELETE": {"TITLE": "Bitte bestätigen Sie das Löschen der Prüfung, indem Sie dessen Nummer eingeben.", "WRONGDELETEINPUT": "Ihre Eingabe entspricht nicht der Nummer der Prüfung. Die Prüfung wurde nicht gelöscht.", "TEXT": "@:UNIT.ALERT.DELETECHECK.TEXT"}, "SCHED": {"ASSFREE": "<PERSON><PERSON><PERSON>", "ASSFULL": "Ganze Prüfung vorab zu<PERSON>sen", "ASSDETAILED": "Detaillierte Zuweisung", "ASSPREALLOCFREE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sonst freie <PERSON>", "ASSPREALLOCDETAILED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sonst detail<PERSON><PERSON> Zuweisung"}, "SCHEDTT": {"ASSFREE": "Prüfung ist für alle Gruppen oder Benutzer verfügbar", "ASSFULL": "Prüfung wird vollständig einer Gruppe / einem Benutzer zugewiesen", "ASSDETAILED": "Einzelne Teile können verantwortlichen Benutzern / Gruppen zugewiesen werden", "ASSPREALLOCFREE": "Nach der Vorabzuweisung sind die restlichen Teile der Prüfung für alle Gruppen oder Benutzer verfügbar", "ASSPREALLOCDETAILED": "Nach der Vorabzuweisung sind die restlichen Teile der Prüfung nicht zugewiesen und müssen manuell bearbeitet werden"}, "REASSIGN": {"TITLE": "Wirklich den Prozess unterbrechen und die Prüfungsabschnitte neu zuweisen?", "TEXT": "Die momentan zugewiesenen Benutzer können an der Prüfung nicht weiter arbeiten, während diese gestoppt ist!"}, "REGISTER": {"TITLE": "Registrieren", "TEXT": "Bitte die Zuweisung auswählen, für die Sie sich registrieren möchten:"}, "UNREGISTER": {"TITLE": "Abmelden", "TEXT": "Bitte die Zuweisung auswählen, von denen <PERSON> sich abmelden möchten:"}, "STEPINPROC": "{{ nums }} <PERSON><PERSON><PERSON>(e)/{{ nump }} <PERSON><PERSON><PERSON>(en)", "REOPEN": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TEXT": "Bitte die Zuweisungen auswählen, die nach der Wiedereröffnung erneut geprüft werden sollen:"}, "COPY": {"TITLE": "<PERSON><PERSON>", "TEXT": "Bitte die Prozeduren auswählen, deren Messergebnisse in die Kopie übernommen werden sollen:", "NEWVERS": {"TITLE": "Aktualisieren?", "TEXT": "Es sind neuere Versionen dieser Prüfung vorhanden; <PERSON><PERSON>ö<PERSON> optional die Kopie aktualisieren. <PERSON><PERSON> Si<PERSON>te, dass nur Prozeduren kopiert werden, die sich nicht geändert haben (ungeachtet der Auswahl im vorherigen Dialog) und bei zwischenzeitlichen Änderungen in den Ablaufsteuerungen Teile der Prüfungskopie unkorrektes Verhalten aufweisen können.", "NOCHANGE": "Aktuelle Version beibehalten"}}, "SELPROC": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TEXTSING": "Bitte die Prozedur aus dem Block auswählen, für die Si<PERSON> sich registrieren möchten:", "TEXTMULT": "Bitte die Prozeduren aus dem Block auswählen, für die Sie sich registrieren möchten:"}, "REGMODE": {"TITLE": "Registrierungsmodus", "TEXT": "Bitte die Art der Selbstregistrierung auswählen:", "COMPLETE": "Registrierung nur komplett", "MAYPART": "Teilweise Registrierung möglich", "MUSTPART": "Registrierung nur für einzelne Prozeduren"}, "REGMODETT": {"COMPLETE": "Registrierung ist nur für den kompletten Block möglich", "MAYPART": "Bei der Registrierung können Prozeduren eines Blockes individuell ausgewählt werden (sofern mehrere Prozeduren in einem Block vorhanden sind)", "MUSTPART": "Bei der Registrierung wird je nur eine e<PERSON>, auszuwählende Prozedur zugewiesen (sofern mehrere Prozeduren in einem Block vorhanden sind)"}, "TAKEOVER": {"INFO": "Übernahme!", "CONFIRM": "Wollen Sie wirklich diesen Block vom früheren Bearbeiter übernehmen?"}}, "MODIFY": {"COMMENT": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "Kommentare für diese Prüfung hinzufügen, löschen oder editieren"}}, "MAKEPDF": {"TTL": "Prüfungsbericht als PDF generieren", "TEXT": "Bitte alle optionalen Elemente auswählen, die in dem PDF erscheinen sollen:", "OPTV": "Prozedurversionen", "OPTADM": "Zuweisungen und andere benutzerspezifische Informationen", "OPTCOMM": "<PERSON>mme<PERSON><PERSON> zu Messungen", "OPTRAW": "Rohwerte der Matrixkalkulationen", "OPTTOOL": "Bei Messungen verwendete Werkzeuge", "OPTRULE": "Messungsregeln", "TEXT2": "Bitte den Typ des Reports auswählen:", "CUSTOMER": {"TEXT": "Kundenreport", "TTIP": "Als 'intern' mark<PERSON>te Messungen auslassen; alternativer Ergebnistext."}, "INTERNAL": {"TEXT": "Interner Report", "TTIP": "Alle Messungen einfügen."}}, "MAKEFILE": {"TTL": "Reportdaten exportieren", "TEXT": "Bitte den Typ der Exportdaten für diesen Report festlegen:", "CSV": "Comma Separated Values (CSV)", "JSON": "JavaScript Output Notation (JSON)", "XML": "Extensible Markup Language (XML)"}, "SELTOOL": {"TITLE": "Werkzeug auswählen", "MESSAGE": "Bitte das Werkzeug auswählen, das für diesen Schritt verwendet wird", "NOTOOLAVAILABLE": "Es stehen keine Werkzeuge mit aktiven Einheiten zur Verfügung!"}}, "MEASUREMENT": {"INPUT": {"COMMENT": {"TITLE": "Kommentar editieren", "TEXT": "Kommentare für diese Einheit hinzufügen, löschen oder editieren"}}}, "MSRSTAT": {"SELMSR": {"TTL": "@:MSRSTAT.SELM.TTL", "TEXT": "Bitte wählen Sie die Messungen aus, die im Bericht erscheinen sollen:", "RUTEXT": "Bitte geben Sie den id-String ein, den <PERSON> von einem früheren Bericht kopiert haben (letzte Seite des PDF):"}, "SELCHK": {"TTL": "@:MSRSTAT.SELC.TTL", "S1TEXT": "Bitte wählen Sie das Modell der Einheit, von der Sie Prüfungen hinzufügen möchten:", "S2TEXT": "Bitte geben Si<PERSON> eine kurze Zeichenfolge ein, die auf den Code oder den Kundennamen der gewünschten Einheit passt:", "S3TEXT": "Bitte wählen Sie ein oder mehrere Prüfungen:", "ERROR": {"TTL": "Auswahlproblem...", "NORES": "<PERSON><PERSON> Prüfungen wurden gefunden, die diese Prozedur verwenden oder bei denen die Einheit auf die gegebenen Suchbegriffe passt.", "LIMIT": "Sie haben das Limit von 30 Prüfungen pro Report erreicht. Bitte löschen Sie erst Prüfungen aus der Liste, bevor Si<PERSON> neue hinzufügen."}, "RUTEXT": "@:MSRSTAT.SELMSR.RUTEXT", "SUCCESS": {"TTL": "Export beendet", "TEXT": "Die Daten werden heruntergeladen. Klicken Sie 'Ja' um diesen Dialog zu schliessen oder 'Nein' um fortzufahren. Um die Messungs- oder Prüfungsauswahl später wieder zu verwenden, können Sie folgende id-Strings kopieren:", "MSTR": "Messungen: '{{ cstr }}'", "CSTR": "Prüfungen: '{{ cstr }}'"}}, "TOOLTIP": {"REMCHK": "Prüfung entfernen"}, "TITLE": "Statistischer Report Generator", "SUBT": "Prozedur {{ pcode }}", "SELM": {"TTL": "Messungen auswählen", "TXT": "Bitte wählen Sie die Messungen, die im Report verwendet werden sollen. Es gibt keine Höchstanzahl, jede Me<PERSON>ung erzeugt eine neue Seite.", "STATUS": "{{ msel }} Messung(en) ausgewählt"}, "SELC": {"TTL": "Prüfungen auswählen", "TXT": "Bitte wählen Sie die Prüfungen, die im Report verwendet werden sollen.", "STATUS": "{{ csel }} Prüfung(en) ausgewählt"}, "CLIST": "Prüfung <b>{{ id }}</b> <span style='font-size:70%'>für Modell <b>{{ mcode }}</b>, Einheit <b>{{ ucode }}</b>; Prüfungsart <b>{{ ctcode }}</b></span>", "BUTTON": {"SEL": "@:UI.BUTTONS.MEDITOR.SELECT", "REUSE": "Wiederverwenden", "ADD": "@:TLISTEDIT.BUTTON.ADD", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "GENERATE": "Report generieren", "EXPORT": "Daten exportieren"}}, "MEDIAMGR": {"IMAGE": {"ALERT": {"CONFHEADER": "Sind Sie sicher?", "DELETE": "Möchten Sie wirklich das Bild {{fname}} aus dieser Zusammenstellung löschen?"}, "EDIT": {"CAPTION": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "Bildtitel ergänzen, ändern oder löschen"}}}, "DOC": {"ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Möchten Sie wirklich das Dockument {{fname}} aus dieser Zusammenstellung löschen?"}, "EDIT": {"CAPTION": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "Dokumenttitel ergänzen, ändern oder löschen"}}}, "UPLOAD": {"FILTER": {"TITLE": "Hochladen fehlgeschlagen", "MESSAGE": "Das Hochladen der Datei {{ filename }} konnte nicht gestartet werden:", "UNSUPPORTED": "Der Dateityp ({{ type }}) ist unbekannt oder wird vom Mediamanager nicht unterstützt.", "TOOBIG": "Die Datei ist zu gross; für diesen Dateityp ist die maximale Grösse auf {{ max }} MB festgelegt, die Datei ist {{ has }} MB gross."}, "HINT": "Mediendateien in diesem Feld ablegen um sie auf den <PERSON> ho<PERSON>zuladen", "RESTR": "<PERSON><PERSON> können hier Bildd<PERSON> (png, jpeg, tiff), Textdokumente (pdf), verpackte Date<PERSON> (zip) und Videos (nur mp4) hochladen.", "STATUS": {"READY": "Bereit zum Hochladen", "WAITING": "Bitte warten...", "UPLOADING": "Lade Datei hoch: {{ prog }}%", "CANCELLED": "Hochladen abgebrochen", "FINISHED": "<PERSON><PERSON>", "FAILED": "Hochladen fehlgeschlagen", "ERROR": "<PERSON><PERSON><PERSON>"}, "STARTALL": "Alle Dateien ho<PERSON>laden", "DELALL": "Alle fertigen Einträge entfernen"}, "TOOLTIP": {"IMGSIZE": "Bilddateigrösse anzeigen", "IMGMETA": "Bildmetadaten anzeigen", "EDITCAP": "Bildunterschrift hinzufügen oder ändern", "REMIMG": "Bild entfernen", "VIEWDOC": "<PERSON><PERSON><PERSON> an<PERSON>", "DOCSIZE": "Dateigrösse anzeigen", "DOCMETA": "Dokumentmetadaten anzeigen", "REMDOC": "Dokument entfernen", "UPLOAD": "Upload starten", "REMUPL": "Upload von der Liste entfernen", "CNCLUPL": "Upload abbrechen", "DOWNLOAD": "<PERSON>i im Original herunterladen"}, "PDFPAGES": "{{ nump }} Seiten", "VIDINFO": "@:WFLOW.INTRO.VIDINFO", "TITLE": "Media Manager", "TAB": {"IMAGES": "Bilder", "VIDEOS": "Videos", "DOCS": "@:WFLOW.STEP.DOCS", "UPLOAD": "Hochladen"}}, "MODEL": {"EDITOR": {"TITLE": {"EDIT": "Editiere {{modelname_de}}", "NEW": "Editiere neues Modell"}}, "ALERT": {"EDITVERSION": "Möchten Sie wirklich eine finalisierte Version des Modells {{code}} bearbeiten?", "EDITVERSIONDETAIL": "Dies ist eine finalisierte Version dieses Modells. Wird diese Version in Arbeitsabläufen eingesetzt, werden bereits eingerichtete oder bearbeitete Prüfungen verändert.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Bitte bestätigen Sie die Löschung des Modells, indem Sie dessen Code eingeben.", "WRONGCODE": "Falscher Code", "WRONGDELETEINPUT": "Ihre Eingabe stimmt nicht mit dem Code überein. Das Modell wurde nicht gelöscht.", "DELETEVERSION": "Dies ist eine finalisierte Version dieses Modells. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.", "FINALIZE": "Möchten Sie wirklich die aktuelle Version des Modells {{code}} finalisieren?", "FINALIZEDETAIL": "Die aktuellen Daten werden finalisiert und eine neue Version zum Editieren bereitgestellt. Die finalisierte Version kann dann für neue Einheiten und Prüfungen verwendet werden.", "RESET": {"TITLE": "Wirklich alle Änderungen zurücksetzen?", "TEXT": "Alle Änderungen, die an diesem Objekt seit der letzten Finalisierung vorgenommen wurden, werden unwiderruflich gelöscht."}, "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Es können nur Texte einer finalisierten Version editiert werden."}, "SWITCHV": {"TITLE": "Version auswählen", "MESSAGE": "Bitte die gewünschte Version aus der folgenden Liste auswählen:"}, "VIEW": {"ADDPROC": {"TITLE": "Prozeduren hinzufügen", "TEXT": "Bitte alle gewünschten Prozeduren auswählen, dann mit dem 'Hinzufügen'-<PERSON><PERSON> bestätigen"}, "ADDCTYPE": {"TITLE": "Prüfungsarten hinzufügen", "TEXT": "Bitte alle gewünschten Prüfungsarten auswählen, dann mit dem 'Hinzufügen'-<PERSON><PERSON> bestätigen"}, "ADDCONFIGTABLE": {"TITLE": "Konfigurationstabell<PERSON>", "TEXT": "Bitte die gewünschte Konfigurationstabelle auswählen, die mit dem aktuellen Modell verknüpft werden soll."}, "TOOLTIP": {"ACTREORD": "Icons zum Umsortieren anzeigen", "ADDPROC": "Aktive Prozedur hinzufügen", "UPDPROC": "Prozedurversion aktualisieren", "REORDER": "Zum Umsortieren halten und verschieben", "GOTOPROC": "<PERSON><PERSON>ehen", "DELPROC": "<PERSON><PERSON><PERSON>", "ADDCTYPE": "Aktive Prüfungsart hinzufügen", "DELCTYPE": "Prüfungsart entfernen", "SWITCHVER": "Version wechseln", "TESTVER": "Version testen", "CHANGELOG": "Änderungslog einsehen", "RESET": "Alle Änderungen zur letzten finalisierten Version zurücksetzen", "PREALLOC0": "Vorabzuweisungen definieren (zurzeit keine vorhanden)", "PREALLOC1": "Vorabzuweisungen definieren (zurzeit für alle Prüfungsarten definiert)", "PREALLOC2": "Vorabzuweisungen definieren (zurzeit für einige Prüfungsarten definiert)", "PREALLOC3": "Vorabzuweisungen definieren (zurzeit fehlerhafte Definitionen vorhanden)", "ADDCONFIGTABLE": "Konfigurationstabelle verknüpfen", "DELCONFIGTABLE": "Verknüpfung zu Tabelle lösen"}, "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DTYPE": "@:WFLOWEDIT.OPT1.DT", "DESC": "@:TLISTEDIT.DESC", "MEDIA": "Medien", "IMREG": "{{ imgnum }} Bilder registriert", "DOCREG": "{{ docnum }} Dokumente registriert", "MEDMGR": "@:MEDIAMGR.TITLE", "BUTTON": {"EXPORT": "Export", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "FIN": "Finalisieren", "EDIT": "Editieren", "UNIT": "<PERSON><PERSON> den Einheiten", "CONFIGTABLE": "<PERSON><PERSON><PERSON>bell<PERSON>"}, "PROC": {"STEPNUM": "Schritte: {{ stepnum }}", "DISCONTINUED": "Stillgelegt!", "OLDVERS": "Verwendet Version {{ pvers }}"}, "ACTPROC": "Aktive Prozeduren", "ACTCTYPE": "Aktive Prüfungsarten", "VERSIONTAG": "Version:", "UNITINFO": {"TTL": "@:FRAME.MANAGE.UNIT", "TXT": "<b>{{ ucnt }} Einheit(en) registriert:</b><br>{{ ocnt }} offen, {{ ccnt }} geschlossen, {{ dcnt }} verworfen."}, "CONFIGTABLE": {"TTL": "Konfigurationstabelle", "NORELATION": "<PERSON><PERSON>gu<PERSON>tabelle zugewiesen."}, "PREALLOC": {"TITLE": "Vorabzuweisungen definieren", "TEXT1": "Bitte wählen Sie die Prüfungsarten aus, für welche die Vorabzuweisung gelten soll:", "TEXT2": "Bitte wählen Sie Gruppe und/oder Benutzer aus, der vorab der Prozedur zugewiesen werden soll. Eine vorhandene Zuweisung kann auch mit 'Zuweisung aufheben' (in der Gruppen- oder Benutzerauswahl) entfernt werden.", "ANY": "<PERSON><PERSON>er/Gruppe", "GROUP": "Gruppe:"}}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DTYPE": "@:WFLOWEDIT.OPT1.DT", "DESC": "@:TLISTEDIT.DESC", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"CLTF": "<PERSON><PERSON><PERSON> leeren", "SHOWDEL": "Gelöschte Modelle zeigen oder verbergen", "NEWMODEL": "Neues Modell", "IMPORTMODEL": "Modell importieren", "GOTOUNITS": "Zu den Einheiten des Modells gehen"}, "TOPROW": "{{ dtypt }} ({{ dtypc }}), {{ unitnum }} Einheit(en)"}, "MODELS": "@:FRAME.MANAGE.MODEL", "TESTCHECK": {"TITLE": "<PERSON>l testen", "TEXT": "Bitte wählen Sie die Prüfungsart, mit der Sie diese Version des Modells testen wollen:"}}, "STEP": {"MODEL": {"MEDIAMANAGER": "Modell {{ mcode }}: {{ mtitle }}"}, "EDITOR": {"TITLE": {"EDIT": "Editiere {{stepname_de}}", "NEW": "Editiere neuen Schritt"}}, "ALERT": {"EDITVERSION": "Möchten Si<PERSON> wirklich eine finalisierte Version des Schritts {{code}} bearbeiten?", "EDITVERSIONDETAIL": "Dies ist eine finalisierte Version dieses Schritts. Wird diese Version in Arbeitsabläufen eingesetzt, werden bereits eingerichtete oder bearbeitete Prüfungen verändert.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "<PERSON>öchten Si<PERSON> wirklich <PERSON>hritt {{scode}} von <PERSON> {{pcode}} löschen?", "DELETECASCADING": "Alle Messungen ({{mcnt}}), die für diesen Schritt definiert sind, werden dann auch gelöscht!", "DELETEVERSION": "Dies ist eine finalisierte Version dieses Schritts. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.", "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Es können nur Texte einer finalisierten Version editiert werden."}, "VIEW": {"MEDIAMANAGER": "Prozedur {{ pcode }}: {{ ptitle }}, <PERSON><PERSON><PERSON> {{ scode }}: {{ stitle }}", "FLOWEDITOR": "Schritt {{ scode }}: {{ stitle }}", "TOOLTIP": {"ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD", "UPLMEAS": "Messungen hochladen", "CLNMEAS": "@:MEASURE.CLONE.TITLE", "NEWMEAS": "Neue Messung erstellen", "REORDER": "@:MODEL.VIEW.TOOLTIP.REORDER", "EDITRULE": "Editor für Ablaufregeln öffnen", "EXPMEAS": "Messung exportieren", "EDITMEAS": "Messung editieren", "TESTMEAS": "Messung testen", "REMMEAS": "Messung entfernen"}, "VERSION": "@:PROCEDURE.VIEW.VERS", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESC": "@:TLISTEDIT.DESC", "MEDIA": "@:MODEL.VIEW.MEDIA", "IMREG": "@:MODEL.VIEW.IMREG", "DOCREG": "@:MODEL.VIEW.DOCREG", "BUTTON": {"MEDMGR": "@:MEDIAMGR.TITLE", "REDITOR": "@:PROCEDURE.VIEW.REDITOR", "EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"}, "RULES": "@:PROCEDURE.VIEW.RULES", "MEASURES": "@:WFLOW.INTRO.MEASURES"}, "CLONE": {"TITLE": "<PERSON><PERSON><PERSON>", "PSELTEXT": "Bitte die Prozedur auswählen, von der Si<PERSON> einzelne Schritte klonen möchten:", "TEXT": "<PERSON><PERSON><PERSON> zum Klonen auswählen:"}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "INSTRUCTION": "Anleitung", "DESC": "@:TLISTEDIT.DESC", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}, "TOOLTIP": {"INSTRUCTION": "Ein Schritt mit Typ Anleitung kann keine Messungen enthalten"}}, "MATRIX": {"TITLE": "Matrixbeschriftung einrichten", "HINT": "Einen der Titel in der Matrix auswählen, dann in der Textbox unten bearbeiten. Die kleineren Titel über den Wertfeldern werden später in der oberen linken Ecke der Wertfelder selbst angezeigt. Ebenfalls ist es möglich, einen kurzen Text in ein Wertfeld selbst einzugeben; dieser wird später in der Matrix als Platzhalter angezeigt.", "BUTTON": {"BACK": "Zurück"}}, "LIST": {"TOOLTIP": {"GOTOPROC": "@:WFLOW.STEP.TOOLTIP.GOTOPROC", "UPLOAD": "@:PROCEDURE.VIEW.TOOLTIP.UPLSTEP", "CLONE": "@:STEP.CLONE.TITLE", "ADDSTEP": "@:PROCEDURE.VIEW.TOOLTIP.NEWSTEP", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF"}, "BTMROW": "Messungen: {{ mcnt }}, Bilder: {{ icnt }}, Dokumente: {{ dcnt }}"}, "STEP": "@:WFLOWEDIT.OPT1.S", "STEPS": "@:WFLOW.INTRO.STEPS", "FORPROC": "<PERSON> {{ proc }}"}, "PROCEDURE": {"EXPORT": {"TITLE": "Prozedur exportieren", "MESSAGESYSTEM": "<PERSON><PERSON><PERSON><PERSON> Sie aus, ob die Prozedur für das selbe oder für ein anderes System exportiert werden soll.", "MESSAGECONTENTS": "<PERSON><PERSON><PERSON>en Si<PERSON> aus, welche Inhalte mitexportiert werden sollen.", "OPTIONS": {"SAMESYSTEM": "Für das selbe System", "OTHERSYSTEM": "Für ein anderes System", "FILES": "Bilder und Dokumente", "TOOLS": "Werkzeuge", "RULES": "Workflowregeln", "ENFORCEMENTS": "Ablaufzwänge"}}, "EDITOR": {"TITLE": {"EDIT": "Editiere {{procname_de}}", "NEW": "Editiere neue Prozedur"}}, "LIST": {"GOTOPROC": {"TITLE": "Verwendungsansicht", "TEXT": "Die folgenden Modelle verwenden die gewählte Prozedur. Sie können eine auswählen um zur entsprechenden Bearbeitungsseite zu wechseln."}, "TOOLTIP": {"IMPORT": "Prozeduren importieren", "CLONE": "@:PROCEDURE.CLONE.TITLE", "ADD": "Neue Prozedur erstellen", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "SHOWDEL": "Gelöschte Prozeduren zeigen oder verbergen", "GOTOMODEL": "<PERSON><PERSON>, die diese Prozedur verwenden anzeigen und optional dorthin gehen", "PROCFILTER": "Nach kundenspezifischen Attributen filtern"}, "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE", "BTMROW": "Schritte: {{ stpcnt }}, ver<PERSON><PERSON> von {{ modcnt }} <PERSON>l(en)", "BTMROWUPD": ", alte Vers.: {{ updcnt }}"}, "ALERT": {"EDITVERSION": "Möchten Sie wirklich eine finalisierte Version der Prozedur {{code}} bearbeiten?", "EDITVERSIONDETAIL": "Dies ist eine finalisierte Version dieser Prozedur. Wird diese Version in Arbeitsabläufen eingesetzt, werden bereits eingerichtete oder bearbeitete Prüfungen verändert.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Möchten Sie wirklich Prozedur {{code}} löschen?", "DELETECASCADING": "Alle Schritte ({{scnt}}) und Messungen, die für diese Prozedur definiert sind, werden dann auch gelöscht!", "DELETEVERSION": "Dies ist eine finalisierte Version dieser Prozedur. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.", "FINALIZE": {"TITLE": "Möchten Sie wirklich die aktuelle Version der Prozedur {{code}} finalisieren?", "TEXT": "Die aktuellen Daten werden finalisiert und eine neue Version zum Editieren bereitgestellt. Die finalisierte Version kann dann für neue Einheiten und Prüfungen verwendet werden. <PERSON><PERSON><PERSON>, dass Si<PERSON> alle Modelle, die diese Prozedur verwenden, aktualisieren und finalisieren um diese Version verwenden zu können."}, "FULLUPDATE": {"TITLE": "<PERSON>öchten Si<PERSON> wirklich alle Modelle, die diese Prozedur verwenden, automatisch aktualisieren?", "TEXT": "<PERSON><PERSON>, die eine ältere Version dieser Prozedur verwenden, werden aktualisiert; dies bedeutet, dass eine neue Version jedes Modells automatisch erzeugt wird. Allfällige andere Änderungen an den Modellen werden dabei nicht finalisiert."}, "RESET": {"TITLE": "Wirklich alle Änderungen zurücksetzen?", "TEXT": "Alle Änderungen, die an diesem Objekt seit der letzten Finalisierung vorgenommen wurden, werden unwiderruflich gelöscht."}, "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Es können nur Texte einer finalisierten Version editiert werden."}, "SWITCHV": {"TITLE": "@:MODEL.SWITCHV.TITLE", "MESSAGE": "@:MODEL.SWITCHV.MESSAGE"}, "VIEW": {"FLOWEDITOR": "Prozedur {{ pcode }}: {{ ptitle }}", "TOOLTIP": {"STAT": "Statistischen Report generieren", "CHVERS": "@:MODEL.VIEW.TOOLTIP.SWITCHVER", "UPDMOD": "Modelle auf neueste Version aktualisieren", "GOTOSTEP": "@:WFLOW.INTRO.TOOLTIP.GOTOSTEP", "ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD", "UPLSTEP": "<PERSON><PERSON><PERSON>n", "CLNSTEP": "@:STEP.CLONE.TITLE", "NEWSTEP": "Neuen Schritt er<PERSON>llen", "REORD": "@:MODEL.VIEW.TOOLTIP.REORDER", "EXPSTEP": "Schritt exportieren", "REMSTEP": "<PERSON><PERSON><PERSON>", "TESTVER": "Version testen", "CHANGELOG": "Änderungslog einsehen", "RESET": "Alle Änderungen zur letzten finalisierten Version zurücksetzen", "ENFORCETOP": "Der erste Eintrag kann keinem Ablaufzwang unterliegen", "ENFORCEINSTR": "Eine Anleitung kann keinem Ablaufzwang unterliegen", "ENFORCE0": "<PERSON><PERSON>", "ENFORCE1": "Kann erst nach dem vorherigen Eintrag bearbeitet werden", "ENFORCE2": "Kann erst bearbeitet werden, wenn alle vorherigen Einträge abgeschlossen sind"}, "VERS": "Version", "UPDATEINFO": "<PERSON>s gibt {{ updcnt }} <PERSON><PERSON>, die eine ältere Version dieser Prozedur verwenden.", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "PROCTIME": "Bearbeitungszeit", "HOURS": "Stunden", "MINUTES": "Minuten", "DESC": "@:TLISTEDIT.DESC", "RULES": "Workflowregeln", "REDITOR": "Regeleditor", "BUTTON": {"EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "FIN": "@:MODEL.VIEW.BUTTON.FIN", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT", "STATISTIC": "Statistik"}, "STEPS": "@:WFLOW.INTRO.STEPS", "MEASURES": "Messungen: {{ msrcnt }}"}, "CLONE": {"TITLE": "<PERSON><PERSON><PERSON> klonen", "TEXT": "Bitte wählen Sie die zu klonenden Prozeduren:"}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESC": "@:TLISTEDIT.DESC", "PROCTIME": "Bearbeitungszeit", "HOURS": "Stunden", "MINUTES": "Minuten", "WRONGPROCTIME": "Falsche Bearbeitungszeit", "WRONGPROCTIMETXT": "Die Bearbeitungszeit wurde falsch eingegeben.", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "TESTCHECK": {"TITLE": "<PERSON><PERSON><PERSON> testen", "TEXT": "Bitte wählen Sie die Prüfungsart, mit der Sie diese Version der Prozedur testen wollen:"}, "FILTER": {"TITLE": "<PERSON><PERSON>lter (Prozedur-Attribute)", "PLEASECHOOSE": "Bitte auswählen", "CANCEL": "<PERSON><PERSON>", "APPLY": "<PERSON><PERSON> anwenden"}}, "MEASURE": {"EDITOR": {"TITLE": {"EDIT": "Editiere {{mname_de}}", "NEW": "Editiere neue Messung"}, "NOTOOL": "<PERSON><PERSON> Werkzeug <PERSON>"}, "VIEW": {"FLOWEDITOR": "Messung {{ mcode }}: {{ mtitle }}"}, "ALERT": {"CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Möchten Sie wirklich die Messung {{ mcode }} von <PERSON> {{ scope }} in Prozedur {{ pcode }} löschen?", "DELETEVERSION": "Dies ist eine finalisierte Version dieser Messung. Wird diese Version in Abreitsabläufen eingesetzt, wird dieser Vorgang fehlschlagen; ebenso kann der Vorgang unerwünschte Datenmutationen oder -korruption verursachen.", "DENYHEADER": "@:TOOL.ALERT.DENY", "DENYMODIFICATION": "Es können nur Texte einer finalisierten Version editiert werden."}, "CLONE": {"TITLE": "Messungen klonen", "PSELTEXT": "Bitte wählen Sie die Prozedur, von der Si<PERSON> einzelne Messungen klonen möchten:", "TEXT": "Bitte wählen Sie die zu klonenden Messungen:"}, "EDIT": {"COMPARATOR": "Vergleichsoperator", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "HINTS": "<PERSON><PERSON><PERSON><PERSON>", "TTYPE": "Werkzeugtyp", "MTYPE": "Messungstyp", "MANDATORY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "YES": "@:WFLOW.INPUT.YES", "NO": "@:WFLOW.INPUT.NO", "TARGET": "Zielwert", "UNIT": "@:WFLOW.INTRO.UNIT", "THOLD": "@:WFLOW.INPUT.THRESH", "MIN": "Minimum", "MAX": "Maximum", "MINLEN": "<PERSON><PERSON>", "REGEXP": "Regulärer Ausdruck", "EXP": "<PERSON><PERSON><PERSON><PERSON>", "ANY": "<PERSON><PERSON>", "MATRIX": "Matrix", "BUTTON": {"SETUP": "Matrix bearbeiten", "TEST": "Speichern & testen", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "CLOSE": "Speichern & schliessen"}, "NUMCOL": "Spalten", "NUMROW": "<PERSON><PERSON><PERSON>", "FORMULA": "Formel", "FLOATFRM": {"TEXT": "Zahlenformat", "STD": "Standard", "INT": "<PERSON><PERSON>", "1DIGIT": "1 Nachkommastelle", "2DIGIT": "2 Nachkommastellen", "3DIGIT": "3 Nachkommastellen", "4DIGIT": "4 Nachkommastellen", "6DIGIT": "6 Nachkommastellen"}, "COMPLCODE": "Gruppierung", "INTERNAL": "Intern", "INTERNALEXTERNAL": "Intern & Extern", "CHOICE": "<PERSON><PERSON><PERSON>", "CHOICEVAL": "{{ val }}. Wert"}, "TYPES": {"THRESHOLD": "@:WFLOW.INPUT.THRESH", "ABSOLUTE": "Absoluter Wert", "ABSOLUTERNG": "Absoluter Wert in einem Bereich", "TEXT": "Beliebiger Text", "REGEXP": "Geprüfter Text", "BOOL": "<PERSON><PERSON>/<PERSON><PERSON>-<PERSON><PERSON>", "RESCHECK": "Prüfung eines vorgegebenen Wertes", "THRESHOLDMATRIX": "Abweichung per Matrixber<PERSON>nung", "ABSOLUTEMATRIX": "Absoluter Wert per Matrixberechnung", "ABSOLUTERNGMATRIX": "Abs. Wert in einem Bereich per Matrixberechnung", "STATISTICAL": "Statistischer Wert", "STATISTICALMATRIX": "Statistischer Wert per Matrixberechnung", "TIMERSTART": "Zeiterfassung (Startzeit)", "TIMERSTOP": "Zeiterfassung (Stopzeit)", "TIMERSTOPQ": "Geprüfte Zeiterfassung (Stopzeit)", "TIMERSTOPC": "Prüfung der unkorrigierten Zeiterfassung", "CHOICELIST": "Auswahlliste"}}, "MEASUREMENTERRORCATEGORYEDITOR": {"TITLE": "Bearbeitung einer abgeschlossenen Messung", "TEXT": "Diese Messung wurde bereits durchgeführt. Bitte wählen Si<PERSON> einen der untenstehenden Gründe für die Bearbeitung aus, um fortzufahren:"}, "TOOL": {"EDITOR": {"TITLE": {"EDIT": "Editiere {{ tool }}", "NEW": "Editiere neuen Werkzeugtyp"}}, "NEWUNIT": {"TITLE": "Neue Werkzeugeinheit", "TEXT": "Bitte geben Sie die Seriennummer oder einen anderen identifizierenden Wert in das Textfeld ein:"}, "EDITUNIT": {"TITLE": "Editiere Werkzeugeinheit", "TEXT": "@:TOOL.NEWUNIT.TEXT"}, "COMMENTUNIT": {"TITLE": "Kommentieren", "TEXT": "<PERSON><PERSON> er<PERSON>llen, ändern oder löschen Sie hier den Kommentar für diese Einheit:"}, "ALERT": {"DELETEUNIT": "Möchten Sie wirklich die Werkzeugeinheit {{code}} löschen?", "NODELETEUNIT": "Werkzeugeinheit {{code}} kann nicht gelöscht werden!", "DELETEUNITCASCADING": "Die Einheit wird bereits in einigen Prüfungen verwendet; bitte setzen Sie diese Einheit auf den Status 'deaktiviert'.", "DELETELASTUNITCASCADING": "Der Werkzeugtyp zu dieser Einheit wird bereits in einigen Prozeduren verwendet; es muss mindestens eine Einheit vorhanden sein. <PERSON>te setzen Sie diese Einheit auf den Status 'deaktiviert'.", "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DENY": "Aktion nicht möglich...", "DELETE": "Möchten Sie wirklich den Werkzeugtyp {{code}} löschen?", "NODELETE": "Werkzeugtyp {{code}} kann nicht gelöscht werden!", "DELETECASCADING": "<PERSON><PERSON><PERSON> den Typ sind bereits einige Einheiten definiert; bitte setzen Sie den Typ daher auf den Status 'deaktiviert'."}, "MEDIAMANAGER": "Werkzeugtyp {{ ttcode }}: {{ tttitle }}", "EDIT": {"CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "HINTS": "@:TLISTEDIT.DESC", "DIS": {"TITLE": "@:TLISTEDIT.DISSET.TITLE", "TRUE": "Der Werkzeugtyp ist deaktiviert und darf nicht länger verwendet werden.", "FALSE": "Der Werkzeugtyp ist aktiv."}, "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"NEW": "Neuen Werkzeugtyp erstellen", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "SHOWDEL": "Deaktivierte Werkzeugtypen zeigen"}, "TTYPES": "Werkzeugtypen", "BTMROW": "Messungen: {{ mcnt }}, Werkzeugeinheiten: {{ tcnt }}"}, "VIEW": {"TOOLTIP": {"NEWTUNIT": "Neue Werkzeugeinheit erstellen", "EDITTUNIT": "Werkzeugeinheit editieren", "REENABLE": "Werkzeugeinheit wieder aktivieren", "DISABLE": "Werkzeugeinheit deaktivieren", "REMTUNIT": "Werkzeugeinheit entfernen", "SHOWDEL": "Deaktivierte Werkzeugeinheiten zeigen", "SETCOMMENT": "Diese Einheit kommentieren", "REPORT": "Werkzeugverwendungsreport generieren"}, "DISABLED": "@:TOOL.EDIT.DIS.TRUE", "CODE": "@:TLISTEDIT.CODE", "TITLE": "@:TLISTEDIT.TITLE", "DESCRIPTION": "@:TLISTEDIT.DESC", "MEDIA": "@:MODEL.VIEW.MEDIA", "IMGREG": "{{ imgcnt }} Bilder registriert", "DOCREG": "{{ doccnt }} Dokumente registriert", "BUTTON": {"MEDMGR": "@:MEDIAMGR.TITLE", "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"}, "MEDINFO": "Hin<PERSON>s: <PERSON><PERSON> jew<PERSON> das erste Bild bzw. Dokument ist verfügbar!", "TUNITS": "Werkzeugeinheiten", "TOOLDEL": "Deaktiviert", "TOOLUNITCNT": "<PERSON><PERSON><PERSON><PERSON> in {{ ucnt }} Messungen"}, "REPORT": {"TITLE": "Verwendungsreport generieren", "DATE1": "Sie können hier das Datum festlegen, ab dem Verwendungen der Werkzeugeinheit in dem Report aufgenommen werden. Wenn Sie kein Datum festlegen möchten (alle Verwendungen seit Anfang), dann betätigen Sie einfach 'Sichern' ohne ein Datum auszuwählen.", "DATE2": "Sie können hier das Datum festlegen, bis zu dem Verwendungen der Werkzeugeinheit in dem Report aufgenommen werden. Wenn Sie kein Datum festlegen möchten (alle Verwendungen bis jetzt), dann betätigen Sie einfach 'Sichern' ohne ein Datum auszuwählen.", "SORT": {"TEXT": "Bitte wählen Sie aus, nach welchem Kriterium der Report sortiert sein soll:", "MODEL": "Nach Model, Einheit, Prüfung", "TIME": "<PERSON><PERSON> Zeitstempel"}}}, "CHECKTYPE": {"MEDIAMANAGER": "Gerätetype {{ code }}: {{ title }}"}, "DEVICETYPE": {"MEDIAMANAGER": "Prüfungsart {{ code }}: {{ title }}"}, "UNIT": {"EDITOR": {"TITLE": {"EDIT": "Editiere {{ unit }}", "NEW": "Editiere neue Einheit"}}, "ARCHIVE": {"TTL": "In Archiv suchen", "TXT": "Bitte eine oder mehrere Suchbegriffe e<PERSON>ben, die mit einem Teil des Codes oder des Kundennamens übereinstimmen, nach dem <PERSON> suchen.", "MANYRES": {"TTL": "Zu viele Ergebnisse", "TXT": "Die Suche ergab mehr als 100 Einträge, nur die ersten 100 werden angezeigt. Bitte verfeinern Sie ihre Suche."}}, "NEWCHECK": {"TITLE": "Neue Prüfung erstellen", "TEXT": "Bitte die Prüfungsart wählen:", "CONFIGTABLE": {"NEW": "Tabelle aus Vorlage Modell übernehmen", "TITLE": "Vorlage Konfigurationstabelle wählen", "TEXT": "<PERSON>te wählen sie, ob die Konfigurationstabelle nach Vorlage Modell oder von einer vorherigen Prüfung übernommen werden soll:", "CHECK": "Prüfung"}}, "ALERT": {"DELETECHECK": {"TITLE": "Möchten Sie wirklich diese Prüfung löschen?", "TEXT": "Alle Messungen und weiteren gesammelten Daten gehen hierbei ebenfalls verloren!"}, "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER", "DELETE": "Bitte bestätigen Sie die Löschung der Einheit, indem Sie dessen Code eingeben.", "WRONGCODE": "Falscher Code", "WRONGDELETEINPUT": "Ihre Eingabe stimmt nicht mit dem Code überein. Die Einheit wurde nicht gelöscht.", "DELETECASCADING": "Alle Prüfungen dieser Einheit ({{ccnt}}) werden inklusive aller Messergebnisse auch gelöscht!", "STATUSCHG": {"TTL": "Status der Einheit ändern", "TXT": "Bitte wählen Sie den neuen Status dieser Einheit:"}, "UNARCHIVE": {"TXT": "Wirklich die Einheit aus dem Archiv nehmen?"}}, "MODIFY": {"DATE": {"TITLE": "<PERSON><PERSON><PERSON>", "COMMISSIONED": "Bitte das Datum wählen, an dem diese Einheit in Auftrag gegeben wurde", "FINISHED": "Bitte das Datum wählen, an dem diese Einheit fertiggestellt wurde", "DELIVERED": "Bitte das Datum wählen, an dem diese Einheit an den Kunden ausgeliefert wurde", "APPROVED": "Bitte das Datum wählen, an dem diese Einheit vom Kunden abgenommen wurde"}, "COMMENT": {"TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "TEXT": "@:MEASUREMENT.INPUT.COMMENT.TEXT"}}, "EDIT": {"CODE": "@:TLISTEDIT.CODE", "CUST": "Kunde", "MODEL": "@:WFLOWEDIT.OPT1.MD", "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "LIST": {"TOOLTIP": {"NEW": "Neue Einheit erfassen", "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "ARCHMODE": "Archivmodus starten oder verlassen", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "LOCKMODE": "Abgeschlossene Einheiten anzeigen oder verbergen"}, "UNITS": "@:FRAME.MANAGE.UNIT"}, "FORMODEL": "des Modells {{ mdl }}", "INARCHIVE": "im Archiv, passend auf '{{ pat }}'", "VIEW": {"TOOLTIP": {"UNITSTAT": "Status der Einheit ändern", "UNARCH": "Einheit aus Archiv nehmen (reaktivieren)", "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "EDITDATE": "<PERSON><PERSON> setzen oder ändern", "EDITCOMM": "Kommentar hinzufügen oder ändern", "ADDCHK": "@:UNIT.NEWCHECK.TITLE", "DELCHK": "@:MSRSTAT.TOOLTIP.REMCHK", "GOTOCHK": "Zur Prüfung gehen"}, "MODEL": "@:WFLOWEDIT.OPT1.MD", "CODE": "@:TLISTEDIT.CODE", "CUST": "@:UNIT.EDIT.CUST", "CHECKS": "Prüfungen", "CHK": "@:WFLOW.INTRO.MEDIAMANAGER", "DATES": "Daten", "COMM": {"ON": "In Auftrag gegeben am", "NOT": "Noch nicht in Auftrag gegeben"}, "FIN": {"ON": "Fertiggestellt am", "NOT": "Noch nicht fertiggestellt"}, "DEL": {"ON": "Ausgeliefert am", "NOT": "Noch nicht ausgeliefert"}, "APPR": {"ON": "Abgenommen am", "NOT": "Noch nicht abgenommen"}, "COMMENT": "Kommentar", "BUTTON": {"DELETE": "@:WFLOW.INTRO.BUTTON.DELETE", "EDIT": "@:MODEL.VIEW.BUTTON.EDIT", "CHSTATUS": "Status ändern", "UNARCHIVE": "Dearchivieren"}, "CHECK": {"MODVERS": "verwendet Modellversion {{ mvers }}", "FAIL": "Prüfung fehlgeschlagen!", "WARN": "Prüfung mit Warnungen bestanden!", "PASS": "Prüfung bestanden!", "FAILCLOSED": "Geschlossen, Prüfung fehlgeschlagen am", "WARNCLOSED": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Prüfung mit Warnungen bestanden am", "PASSCLOSED": "Geschlossen, Prüfung bestanden am", "PASSCANCEL": "Abgebrochen"}, "SCHED": {"NOT": "@:WFLOW.INTRO.DATE.NOSCHED", "ON": "Vorgesehen für"}, "DUE": {"ON": "abzuschliessen bis", "NOT": "<PERSON><PERSON>"}, "START": {"ON": "@:WFLOW.INTRO.DATE.START"}, "STATUS": {"TTL": "@:WFLOW.INPUT.STATUS.TITLE", "OPEN": "OFFEN", "CLOSED": "GESCHLOSSEN", "DISCARDED": "VERWORFEN", "ARCHIVED": "ARCHIVIERT", "CLOSEDARCH": "GESCHLOSSEN (ARCHIVIERT)", "DISCARCH": "VERWORFEN (ARCHIVIERT)"}}}, "USERS": {"ANYUSER": "<PERSON><PERSON><PERSON><PERSON>", "ANYGROUP": "Irgendeine Gruppe", "DELASSIGN": "Zuweisung aufheben", "TOOLTIP": {"NEWUSER": "Neuen Benutzer anlegen", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "HIDEGRP": "Gruppen verbergen", "NEWGRP": "Neue Gruppe anlegen", "SHOWGRP": "Gruppen anzeigen", "DISUSER": "<PERSON><PERSON><PERSON>akti<PERSON>", "ENUSER": "Benutzer aktivieren", "EDITUNAM": "Benutername editieren", "EDITURN": "Vollen Namen des Benutzers editieren", "EDITUCOMM": "Kommentare zum Benutzer hinzufügen oder editieren", "REMGRP": "@:USERMGR.ACTION.DELETEGROUP.TITLE", "REACTGRP": "Gruppe reaktivieren", "DISABLED": "Deaktiviert", "SHOWDELGROUPS": "Deaktivierte Gruppen anzeigen", "CHGGRP": "Benutzer in andere Gruppe verlegen", "EDITGNAME": "Gruppenname editieren", "EDITGLVL": "Gruppenstufe editieren", "EDITGDESC": "Gruppenbeschreibung editieren", "ADDGPRV": "Gruppenprivilegien hinzufügen", "REMGPRV": "Gruppenprivileg entfernen", "ADDUPRV": "Benutzerprivilegien hinzufügen", "REMUPRV": "Benutzerprivileg entfernen", "SHOWDEL": "Deaktivierte Benutzer anzeigen", "ADDGROUP": "Diesem Benutzer eine oder mehrere Gruppen zuordnen", "ADDUSER": "Dieser Gruppe einen oder mehrere Benutzer zuordnen", "ADDFIRSTGROUP": "Diesem Benutzer eine oder mehrere Gruppen zuordnen", "ADDFIRSTUSER": "Dieser Gruppe einen oder mehrere Benutzer zuordnen", "REMOVEGROUP": "Diese Gruppe vom Benutzer entfernen", "REMOVEUSER": "<PERSON>sen Benutzer aus der Gruppe entfernen"}, "USERS": "@:FRAME.MANAGE.USERS", "GROUPS": "Gruppen", "USER": "@:MEDITOR.USER", "USERNAME": "@:LOGIN.USERNAME", "PASSWORD": {"TITLE": "@:LOGIN.PASSWORD", "SET": "Passwort gesetzt", "NOTSET": "Passwort nicht gesetzt"}, "BUTTON": {"CHPASSW": "@:FRAME.MANAGE.CHANGEPW"}, "REALNAME": "<PERSON>chter Name", "COMMENT": "@:UNIT.VIEW.COMMENT", "GROUP": "@:MEDITOR.GROUP", "GNAME": "Gruppenname", "LEVEL": "Level", "DESC": "@:TLISTEDIT.DESC", "PRIVILEGES": "Privilegien", "PRIV": {"BYGROUP": "erhalten durch die Gruppen", "BYUSER": "individuell erhalten", "FORGROUP": "Gruppenweite Privilegien"}, "RANK": "Level", "USERGROUPS": "Gruppenzugehörigkeit", "NOGROUPS": "Benutzer gehört keiner Gruppe an", "GROUPUSERS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NOUSERS": "<PERSON><PERSON> sind dieser Gruppe zugeordnet", "ADDUSER": "Benutzer hinzufügen", "ADDGROUP": "Gruppen zuordnen"}, "USERMGR": {"ACTION": {"ADDUSER": {"TITLE": "Benutzer hinzufügen"}, "EDITUSER": {"TITLE": "<PERSON><PERSON><PERSON>"}, "USER": {"USERNAME": "Bitte neuen Benutzernamen eingeben", "REALNAME": "Bitte den echten Namen des Benutzers eingeben", "COMMENT": "Bitte einen Kommentar zu dem Benutzer hinzufügen oder editieren", "PASSWORD": "Bitte neues Passwort für den Benutzer eingeben oder das Feld leer lassen um das Passwort zu löschen", "GROUP": "Bitte die neue Gruppe für den Benutzer wählen"}, "ADDGROUP": {"TITLE": "Gruppe hinzufügen"}, "EDITGROUP": {"TITLE": "Editiere Gruppe"}, "GROUP": {"NAME": "Bitte den neuen Gruppennamen eingeben", "LEVEL": "Bitte den neuen Level der Gruppe eingeben (Zahl zwischen 1 und 1000)", "DESCRIPTION": "Bitte die Beschreibung für diese Gruppe eingeben oder ändern"}, "DELETEGROUP": {"TITLE": "Gruppe deaktivieren", "TEXT": "Möchten Sie wirklich die Gruppe deaktivieren? Die Benutzer in dieser Gruppe sind danach unter Umständen keiner Gruppe mehr angehörig."}, "REACTIVATEGROUP": {"TITLE": "Gruppe aktivieren", "TEXT": "Diese Gruppe wurde deaktiviert. Möchten Sie sie wieder reaktivieren?"}, "ADDGRANT": {"TITLE": "Privilegien hinzufügen", "TEXT": {"GROUP": "Bitte wählen Sie die Privilegien, die der Gruppe hinzugefügt werden sollen:", "USER": "Bitte wählen Sie die Privilegien, die dem Benutzer hinzugefügt werden sollen:"}}, "ADDGRPTOUSER": {"TITLE": "Gruppen hinzufügen", "TEXT": "Bitte wählen Sie die Gruppen aus, die Sie dem Benutzer hinzufügen wollen:"}, "ADDUSERTOGRP": {"TITLE": "Benutzer hinzufügen", "TEXT": "Bitte wählen Sie die Benutzer aus, die Sie der Gruppe hinzufügen wollen:"}}}, "WFLOW": {"INTRO": {"STATUS": {"INIT": "INITIALISIERT", "SCHED": "EINGERICHTET", "START": "GESTARTET", "FAIL": "FEHLGESCHLAGEN", "WARN": "BESTANDEN M.E.", "PASS": "BESTANDEN", "FAILC": "FEHLGESCHLAGEN & GESCHLOSSEN", "WARNC": "BESTANDEN M.E. & GESCHLOSSEN", "PASSC": "BESTANDEN & GESCHLOSSEN", "CANCEL": "ABGEBROCHEN"}, "MEDIAMANAGER": "Prüfung", "FILEINFO": {"TITLE": "Uploadinformationen", "UPLOADDATE": "Datum:", "UPLOADTIME": "Zeit:", "UPLOADEDBY": "Von:"}, "TOOLTIP": {"EXPPDF": "Prüfung als PDF exportieren", "EXPFILE": "Prüfungsdaten exportieren", "GOTOMODEL": "<PERSON><PERSON> gehen", "GOTOUNIT": "Zur Einheit gehen", "IMGLEFT": "Vorhergehendes Bild", "IMGRIGHT": "Nächstes Bild", "VIEWDOC": "<PERSON><PERSON><PERSON> an<PERSON>", "EDITCOMM": "@:MEASUREMENT.INPUT.COMMENT.TITLE", "VIEWIMG": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "REMASS": "Zuweisungsziel entfernen", "ASSIGN": "Alle ausgewählten Elemente zuweisen", "HIGHLASS": "Zugewiesene Elemente hervorheben", "ADDASS": "Zuweisungsziel hinzufügen", "SELASS": "<PERSON><PERSON><PERSON> die Zuweisung auswählen", "GOTOSTEP": "<PERSON><PERSON>hr<PERSON> gehen", "CHANGEASSIGN": "Zuweisung ändern", "CONFIGTABLE": "Konfigurationstabelle ö<PERSON>nen"}, "MEDIAMGR": "Bilder und Dokumente hinzufügen, löschen oder ändern", "TOOEARLY": "Die Prüfung ist für einen zukünftigen Zeitpunkt vorgesehen, sie kann noch nicht bearbeitet werden.", "TOOLATE": "Der Abschluss der Prüfung ist überfällig, bitte beeilen!", "MODEL": "@:WFLOWEDIT.OPT1.MD", "UNIT": "Einheit", "PDFINFO": "{{ pnum }} Seiten", "VIDINFO": "{{ width }} x {{ height }}, {{ fps }} bps, Laufzeit: {{ rtime }} sek", "IMGINFO": "{{ width }} x {{ height }}", "DATE": {"SCHED": "Vorgesehen für", "DUE": "Fertigzustellen bis", "NOSCHED": "<PERSON><PERSON><PERSON> nicht geplant", "NODUE": "<PERSON><PERSON> g<PERSON>", "START": "Gestartet am", "FIN": "Beendet am", "NOSTART": "Noch nicht gestartet", "NOFIN": "Noch nicht beendet"}, "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "NOCOMMENT": "Prüfung ist noch nicht kommentiert", "STEPINPROC": "<PERSON><PERSON><PERSON>(e) in", "PROCS": "<PERSON><PERSON><PERSON>(en)", "NOASS": "<PERSON>ch keine Zuweisungen", "ADDASS": "Zuweisung hinzufügen", "PROCSUNASS": "Prozedur(en) nicht zugewiesen", "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE", "STEPS": "<PERSON><PERSON><PERSON>", "INSTRUCTIONS": "Anleitungen", "MEASURES": "Messungen", "STAT": {"TOTAL": "Total", "PASSED": "<PERSON><PERSON><PERSON>", "UNFIN": "In Bearbeitung", "FAILED": "<PERSON>cht bestanden", "SKIPPED": "Optional"}, "BUTTON": {"DELETE": "Löschen", "SCHEDULE": "Einrichten", "RESCHEDULE": "Umplanen", "START": "Starten", "REASS": "Prüfung stoppen / <PERSON><PERSON>", "UNREG": "@:CHECK.ALERT.UNREGISTER.TITLE", "REG": "@:CHECK.ALERT.REGISTER.TITLE", "CONTINUE": "Fortsetzen", "COMMIT": "Bestätigen", "CLOSE": "@:WFLOW.INPUT.BUTTON.CLOSE", "REOPEN": "@:CHECK.ALERT.REOPEN.TITLE", "CANCEL": "Prüfung abbrechen", "COPY": "<PERSON><PERSON>"}, "FILTER": {"BUTTONS": {"COLLAPSE": "Ausklappen:", "PROC": "Proz.", "STEP": "<PERSON>hr.", "MEAS": "Mess.", "FILTER": "Filter", "FILTERPLUS": "Filter+"}, "TITLE": "Filter", "DISABLE": "<PERSON><PERSON>", "FILTTITLE": "<PERSON><PERSON>lter anwenden an:", "FILTCODE": "Codes", "FILTTEXT": "Titel", "FILTPROC": "<PERSON><PERSON><PERSON>", "FILTSTEP": "<PERSON><PERSON><PERSON>", "FILTMEAS": "Messungen", "FILTMARKELEMS": "Markiere Elemente:", "FILTUSER": "Nur Elemente markieren, die dem aktuellen Benutzer zugewiesen sind (wird obige Filter zurücksetzen):", "FILTUNFIN": "<PERSON>ur Elemente markieren, die noch nicht vollständig bearbeitet sind", "FILTFAIL": "Nur Elemente markieren, die fehlgeschlagen sind", "FILTSUCC": "<PERSON>ur Elemente markieren, die vollständig bearbeitet und fehlerfrei sind", "HIDEINSTR": "Anleitungsschritte ausblenden:", "HIDEOMITTEDORSKIPPED": "Verstecke ausgelassene und übersprungene Elemente:", "TOOLTIP": {"VIEWFILT": "Filterdialog anzeigen", "PROC": "Alles einklappen und nur Prozeduren zeigen", "STEP": "<PERSON><PERSON><PERSON> ausk<PERSON>, Messungen einklappen", "MEAS": "Alles anzeigen, Schritte und Messungen ausklappen", "FILTER": "Alles anzeigen was per Filter markiert ist, alles andere e<PERSON>pen", "FILTERPLUS": "Per <PERSON>lter markierte Elemente ausklappen, alles andere einklappen", "RESET": "Alle Filter zurücksetzen"}}, "VERSION": "Prü<PERSON><PERSON> basiert auf Modellversion", "UNFINVERSION": "Prüfung basiert auf der aktuell editierten, noch nicht finalisierten Modellversion", "TESTRUN": {"PROCEDURE": "Testlauf einer Prüfung für Prozedur:", "MODEL": "Testlauf einer Prüfung für Modell:"}}, "STEP": {"IMAGE": {"NEXT": "Nächstes", "PREVIOUS": "Vorheriges"}, "STATUS": {"TODO": "@:WFLOW.INTRO.STATUS.INIT", "PASS": "@:WFLOW.INTRO.STATUS.PASS", "WARN": "@:WFLOW.INTRO.STATUS.WARN", "FAIL": "@:WFLOW.INTRO.STATUS.FAIL", "OMIT": "AUSGELASSEN", "SKIP": "ÜBERSPRUNGEN", "PASSNF": "(BESTANDEN)", "FAILNF": "(FEHLGESCHLAGEN)", "WARNNF": "(BESTANDEN M.E.)"}, "TOOLTIP": {"GOTOMODEL": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL", "GOTOUNIT": "@:WFLOW.INTRO.TOOLTIP.GOTOUNIT", "GOTOPROC": "<PERSON><PERSON>ehen", "VIEWIMG": "@:WFLOW.INTRO.TOOLTIP.VIEWIMG", "VIEWDOC": "@:WFLOW.INTRO.TOOLTIP.VIEWDOC"}, "COMMITTER": "<PERSON><PERSON><PERSON> von:", "NOCOMMIT": "<PERSON>ch nicht bestätigt", "MEASURER": "Messung {{ code }} von:", "NOMEASURE": "Noch nicht bearbeitet", "PDFINFO": "@:WFLOW.INTRO.PDFINFO", "VIDINFO": "{{ width }} x {{ height }}, {{ fps }} bps, {{ rtime }} sek", "MODEL": "<PERSON><PERSON>", "UNIT": "@:WFLOW.INTRO.UNIT", "PROCEDURE": "@:WFLOWEDIT.OPT1.P", "STEP": "<PERSON><PERSON><PERSON>", "BUTTON": {"BACK": "Zurück zur Übersicht", "CONTINUE": "@:WFLOW.INTRO.BUTTON.CONTINUE", "FIN": "Beenden!", "REWIND": "<PERSON><PERSON><PERSON>", "FORWARD": "<PERSON><PERSON><PERSON>", "PROCALL": "Alle bearbeiten"}, "MEASURES": "@:WFLOW.INTRO.MEASURES", "MEASUREINP": "Ergebnis eintragen...", "INPLOCKED": "<PERSON><PERSON><PERSON><PERSON>", "TOOLS": "@:FRAME.MANAGE.TOOL", "TOOLCHOOSE": "Werkzeug wählen...", "DOCS": "Dokumente", "VIEW": {"TIMER": {"TITLE": "Bearbeitungszeit", "FINISHED": "Die Bearbeitungszeit für diese Prozedur ist nun abgelaufen!"}, "INFO": "Beschreibung", "IMAGES": "Bilder ({{ numi }})", "DOCS": "Dokumente ({{ numd }})"}}, "INPUT": {"VALUECMP1": "Wert muss kleiner sein als {{ val }}", "VALUECMP2": "Wert muss kleiner oder gleich sein als {{ val }}", "VALUECMP3": "Wert muss gleich sein wie {{ val }}", "VALUECMP4": "Wert muss grösser oder gleich sein als {{ val }}", "VALUECMP5": "Wert muss gr<PERSON><PERSON> sein als {{ val }}", "CHECKCMP1": "<PERSON>te prüfen Si<PERSON>, ob Ihr gemessener Wert kleiner ist als {{ val }}, dann wählen Sie entsprechend '<PERSON><PERSON>' o<PERSON> '<PERSON><PERSON>'", "CHECKCMP2": "<PERSON>te prüfen Si<PERSON>, ob Ihr gemessener Wert kleiner oder gleich ist als {{ val }}, dann wählen Sie entsprechend '<PERSON><PERSON>' o<PERSON> '<PERSON><PERSON>'", "CHECKCMP3": "<PERSON>te prüfen Si<PERSON>, ob Ihr gemessener Wert gleich ist wie {{ val }}, dann wählen Sie entsprechend '<PERSON><PERSON>' o<PERSON> '<PERSON><PERSON>'", "CHECKCMP4": "<PERSON>te prüfen Sie, ob Ihr gemessener Wert grösser oder gleich ist als {{ val }}, dann wählen Sie entsprechend '<PERSON><PERSON>' o<PERSON> '<PERSON><PERSON>'", "CHECKCMP5": "<PERSON>te prüfen Sie, ob Ihr gemessener Wert grösser ist als {{ val }}, dann wählen Sie entsprechend '<PERSON><PERSON>' o<PERSON> '<PERSON><PERSON>'", "TARGET": "Zielwert", "STATISTIC": "Bitte den gemessenen Wert e<PERSON>ben; es findet keine Prüfung des Wertes statt.", "THRESH": "<PERSON>bwei<PERSON><PERSON>", "VALUERNG1": "<PERSON>rt liegt zwischen", "VALUERNG2": "und", "TEXTLEN1": "Text ist mindestens", "TEXTLEN2": "<PERSON><PERSON><PERSON> lang", "TEXTPAT": "Text passt in ein festgelegtes Muster", "EXPNO": "'N<PERSON>' wird erwartet", "EXPYES": "'Ja' wird erwartet", "EXPBOTH": "'<PERSON><PERSON>' oder '<PERSON><PERSON>' wird erwartet", "OPTSKIP": "Diese Messung ist optional. Überspringen?", "YES": "<PERSON>a", "NO": "<PERSON><PERSON>", "INPUT": {"VALUE": "Bitte den gemessenen Wert unten eintragen:", "TEXT": "Bitte den einzugebenden Text unten eintragen:", "LIST": "Bitte den passenden Wert auswählen:", "BOOL": "<PERSON><PERSON> entwed<PERSON> '<PERSON><PERSON>' <PERSON><PERSON> '<PERSON><PERSON>' w<PERSON>hlen:", "MATRIX": "Bitte die Matrix unten ausfüllen, dann 'Prüfen' anklicken um das Ergebnis zu berechnen:"}, "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "STATUS": {"TITLE": "Status", "TODO": "LEER", "PASS": "OK", "WARN": "NICHT OK", "FAIL": "NICHT OK", "SKIP": "ÜBERSPRUNGEN", "INV": "UNGÜLTIG", "ERROR": "FEHLER"}, "BUTTON": {"CLOSE": "<PERSON><PERSON><PERSON><PERSON>", "CLEAR": "Löschen", "CANCEL": "Abbrechen", "ADDCOMM": "Kommentar", "EDITCOMM": "Kommentar", "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "CONTINUE": "Prüfen & weiter"}, "TEXTTIMER": {"START": "Bitte die Startzeit eingeben", "STOP": "Bitte die Stopzeit eingeben", "STOPLT": "Bitte die Stopzeit eingeben; es wird eine Zeit bis {{ time }} min erwartet", "STOPGT": "Bitte die Stopzeit eingeben; es wird eine Zeit grösser als {{ time }} min erwartet", "REDUCE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MIN": "min"}, "NOINPUT": "Das Ergebnis dieser Messung berechnet sich aus anderen Messungen."}}, "WFLOWEDIT": {"OPT1": {"CT": "Prüfungsart", "MD": "<PERSON><PERSON>", "DT": "Gerätetyp", "P": "<PERSON><PERSON><PERSON>", "S": "<PERSON><PERSON><PERSON>", "M": "Messung", "CO": "Konfig.-<PERSON><PERSON><PERSON>"}, "OPT2": {"SEL": "gewählt", "NSEL": "nicht gewählt", "QUEUED": "vorgesehen", "NQUEUED": "nicht vorgesehen", "FINPASS": "beendet & bestanden", "FINFAIL": "beendet & fehlg.", "SKIP": "übersprungen", "NSKIP": "nicht übersprungen", "OMIT": "ausgelassen", "NOMIT": "nicht ausgelassen", "YES": "au<PERSON> '<PERSON><PERSON><PERSON><PERSON>t", "NO": "auf '<PERSON><PERSON>' g<PERSON>tzt", "ACTIVE": "aktiv", "NACTIVE": "nicht aktiv"}, "OPT3": {"SKIP": "überspringen", "OMIT": "auslassen"}, "TOOLTIP": {"REMMOD": "Regel entfernen", "ADDMOD": "Regel hinzufügen"}, "LOADING": "Lade Editor...", "TITLE": "Editor für Ablaufregeln", "ACTIVER": "Aktive Regeln", "ADDRULE": "Neue Regel hinzufügen", "RULE1": "<PERSON><PERSON>", "RULE2": "mit Code", "RULE3": "ist", "RULE4": ", dann"}, "SERVER": {"ERROR": {"TITLE": "<PERSON><PERSON>!", "VALIDATION": {"MEASUREMENTERRORCATEGORY": {"NAME": {"SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"}}, "USERGROUP": {"NAME": {"NOTNULL": "Ein Gruppenname muss angegeben werden", "UNIQUE": "Der Gruppenname muss eindeutig sein; keine andere Gruppe darf dense<PERSON>ben Namen haben", "MATCH": "Der Gruppenname darf nur Buchstaben, Nummern und den Unterstrich beinhalten; er muss mindestend drei Zeichen lang sein"}, "LEVEL": {"NOTNULL": "Ein Level muss angegeben werden", "NUMBER": "Der Gruppenlevel muss eine positive Gan<PERSON><PERSON> zwischen 1 und 1000 sein"}}, "USER": {"USERNAME": {"NOTNULL": "Ein Benutzername muss angegeben werden", "UNIQUE": "Der Benutzername muss eindeutig sein; kein and<PERSON>er darf <PERSON>ben Name<PERSON> haben", "MATCH": "Der Benutzername darf nur Buchstaben, Nummern und den Unterstrich beinhalten; er muss mindestend drei Zeichen lang sein"}, "PASSHASH": {"NOTNULL": "Ein Passhash muss angegeben werden", "MATCH": "Der Passhash wird vom aktuellen Passwortmechanismus nicht unterstützt"}, "REALNAME": {"NOTNULL": "Der echte Namen des Benutzers muss angegeben werden", "MINLEN": "Der echte Name des Benutzers muss mindestens drei Zeichen lang sein"}, "USERGROUP_ID": {"NOTNULL": "Die Benutzergruppe muss gesetzt sein"}}, "TOOLTYPE": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; kein anderer Werkzeugtyp darf denselben Code haben", "SHORT": "Der Code muss mindestens zwei Zeichen lang sein", "INVALID": "Der Code darf nur Buchstaben, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Unterstriche und Bindestriche beinhalten"}, "TITLE": {"INVALID": "Der Sprachenblock für den Titel ist ungültig", "INCOMPLETE": "<PERSON>m Sprachenblock für den Titel fehlt der Eintrag für '{{ lcode }}'", "SHORT": "Der Spracheintrag '{{ lcode }}' für den Titel ist zu kurz (< {{ minlength }})"}, "DESCRIPTION": {"INVALID": "Der Sprachenblock für die Beschreibung ist ungültig"}}, "TOOLUNIT": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; keine andere Werkzeugeinheit darf innerhalb des Werkzeugtyps denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}}, "PROCEDURE": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; keine andere Prozedur darf denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "STEP": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; kein and<PERSON> Schritt darf innerhalb der Prozedur denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "MEASURE": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; keine andere Messung darf innerhalb des Schritts denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "CALCULATION": {"OPTMISS": "Das 'Erforderlich'-<PERSON><PERSON> muss entweder auf '<PERSON><PERSON>' <PERSON><PERSON> '<PERSON><PERSON>' gesetzt sein", "INVMTYPE": "Der Messungstyp ist ungültig", "AINVVAL": "<PERSON><PERSON><PERSON> den Zielwert und die Abweichung müssen gültige Nummern spezifiziert sein; die Abweichung muss positiv sein", "BINVVAL": "Gültige Zahlen für den Minimal- und Maximalwert müssen angegeben sein", "CINVVAL": "Die minimal Textlänge muss eine positive Ganzzahl oder 0 sein", "EMPTYVAL": "Die eingegebenen Listenwerte dürfen nicht leer sein", "DMISSING": "Ein regulärer Ausdruck als Muster muss gegeben sein", "DINVREGEXP": "Der reguläre Ausdruck ist nicht gültig", "EINVVAL": "Der erwartete Wert ist nicht gültig; es muss entweder '<PERSON><PERSON>', '<PERSON><PERSON>' oder '<PERSON><PERSON>' gewählt sein", "JINVVAL": "Eine gültige Zahl muss als Referenzzahl angegeben werden", "JINVCMP": "Ein gültiger Vergleichsoperator muss angegeben werden", "HINVVAL": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL", "HINVCMP": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP", "OINVVAL": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL", "OINVCMP": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP", "XINVMTRX": "Ungültige Matrixdefinition; die Grösse ist nicht korrekt angegeben oder ungültig", "XINVFORM": "<PERSON>ine Matrixformel spezifiziert", "XVARIOUS": "Unbekannter Fehler beim Prüfen der Matrixformel", "XSYNTAX": "Syntaxfehler beim Prüfen der Matrixformel; bitte auf Tippfehler achten", "XINVVAR": "Ungültiger Variablenname beim Prüfen der Matrixformel gefunden", "XINVFUN": "Unbekannte Methode beim Prüfen der Matrixformel gefunden", "XINVUSE": "Eine oder mehr Methoden werden ungültig verwendet (z.B. einer statt zwei Argumente)", "MCODEMISSING": "Der Gruppierungscode muss für diesen Messtyp gesetzt und mindestens drei Zeichen lang sein"}}, "CHECKTYPE": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; keine andere Prüfungsart darf denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "DEVICETYPE": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; kein anderer Gerätetyp darf denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}}, "MODEL": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; kein anderes Modell darf denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "TITLE": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID", "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "DEVICETYPE_ID": {"NULL": "Der Gerätetyp muss gesetzt sein", "NOREF": "Der Gerätetyp {{ refid}} ist nicht gültig oder existiert nicht"}}, "UNIT": {"CODE": {"UNIQUE": "Der Code muss eindeutig sein; keine andere Einheit darf denselben Code haben", "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT", "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"}, "DESCRIPTION": {"INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"}, "COMMENT": {"INVALID": "Der Sprachblock für den Kunden ist ungültig"}, "MODEL_ID": {"NULL": "Das Modell muss gesetzt sein", "NOREF": "Das Modell {{ refid }} ist nicht gültig oder existiert nicht"}}, "SETTINGS": {"VALUE": {"INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"}}, "TYPE": "Der Server meldet ein oder mehrere Datenvalidierungsfehler:"}, "INVMOVIE": {"TYPE": "Das hochgeladene Video ist ungültig", "TEXT": "Das hochgeladene Video wurde geprüft und für ungültig bewertet. E<PERSON> kann in dieser Applikation nicht verwendet werden."}, "INVPDF": {"TYPE": "Das hochgeladene PDF Dokument ist ungültig", "TEXT": "Das hochgeladene PDF Dokument wurde geprüft und für ungültig bewertet. E<PERSON> kann in dieser Applikation nicht verwendet werden."}, "CHECKACTION": {"TYPE": "Ein Fehler ist während dieser Aktion aufgetreten", "INVALIDPHASE": "Diese Aktion darf in dieser Phase nicht ausgeführt werden", "REQFAILED": "Einige Bedingungen für diese Aktion sind nicht erfüllt", "INVCOMMAND": "Ungültiges Kommando"}, "STEPACTION": {"TYPE": "@:SERVER.ERROR.CHECKACTION.TYPE", "INVALIDPHASE": "@:SERVER.ERROR.CHECKACTION.INVALIDPHASE", "REQFAILED": "@:SERVER.ERROR.CHECKACTION.REQFAILED", "INVCOMMAND": "@:SERVER.ERROR.CHECKACTION.INVCOMMAND"}, "LOGIN": {"TYPE": "<PERSON><PERSON>", "USERUNKNOWN": "Der Benutzername existiert nicht in der Datenbank", "NOTINITIALIZED": "Der Benutzer nicht initialisiert", "WRONGPASSWORD": "Das Passwort stimmt nicht", "NOTLOGGEDIN": "Der Benuter ist nicht eingeloggt", "USERLOCKED": "Der Benutzer ist deaktiviert"}, "NOTFOUND": {"TYPE": "Ein Objekt wurde nicht gefunden", "TEXT": "Das {{class}}-Object mit ID {{id}} konnte in der Datenbank nicht gefunden werden", "CHECKSTEXT": "Der Schritt #{{sid}} in der Prüfung (ID: {{cid}}) konnte in der Datenbank nicht gefunden werden", "CHECKMTEXT": "Die Messung #{{mid}} in <PERSON><PERSON><PERSON> #{{sid}} in Prüfung (ID: {{cid}}) konnte in der Datenbank nicht gefunden werden"}, "STEPTYPE": {"TYPE": "Fehler beim setzen des Schritt-Typs", "HASMEASURES": "Der Typ vom Schritt #{{id}} kann nicht auf 'Anleitung' gesetzt werden, solange Messungen auf dem Schritt erfasst sind."}, "ACCDENIED": {"TYPE": "Zugriff abgel<PERSON>nt", "TEXT": "Ihre Zugriffsrechte sind für diese Aktion nicht ausreichend."}, "INVMEDIA": {"TYPE": "Hochgeladener Medientyp ungültig oder unbekannt", "TEXT": "Die hochgeladene Mediendatei hat einen ungültigen Typ; der Server kann die Daten nicht abspeichern"}, "IMPORT": {"TYPE": "I<PERSON>rt<PERSON>hler", "WRONGTYPE": "Die Importdatei ({{ fname }}) hat den falschen Typ. Es wurde ein Import vom Typ {{ req }} gefordert, die Datei beinhaltet den Typ {{ is }}.", "BADCRC": "Die Importdatei ({{ fname }}) wurde modifiziert. Nur originale, unmodifizierte Dateien dürfen importiert werden.", "INVALIDFILE": "Die Datei ({{ fname }}) ist invalid; sie wurde entweder verändert oder Sie haben die falsche Datei ausgesucht."}}}, "EDITOR": {"LOADING": "Editor lädt...", "CODECHNG": {"TITLE": "Code geändert!", "TEXT": "Den Code eines Objektes zu verändern sollte vermieden werden, da hierdurch Ablaufregeln verändert und ungültig werden können. Bitte fahren Si<PERSON> nur fort, wenn das Objekt sicher noch nicht in Arbeitsabläufen verwendet wird."}}, "VERSIONING": {"EDIT": {"VERSIONED": "Edit (V. {{ vers }})", "NEW": "Edit (neu)"}, "VERSION": "Version {{ vers }}", "LASTCHG": "letzte Änderung von {{ realname }} ({{ username }}) am {{ date }}"}, "WFMODIFIER": {"NOMODIFIERS": "<PERSON><PERSON> Ablaufanweisungen festgelegt", "HASMODIFIERS": "{{ num }} Ablaufanweisungen festgelegt:", "CT": {"SELECT": {"STD": "<PERSON><PERSON><PERSON> wenn in einer Prüfung mit Prüfungsart {{ code }} verwendet", "INV": "<PERSON><PERSON><PERSON> wenn nicht in einer Prüfung mit Prüfungsart {{ code }} verwendet"}}, "MD": {"SELECT": {"STD": "Auslassen wenn für Modell {{ code }} eingesetzt", "INV": "Auslassen wenn nicht für Modell {{ code }} eingesetzt"}}, "DT": {"SELECT": {"STD": "Auslassen wenn für ein Modell mit Gerätetyp {{ code }} verwendet", "INV": "Auslassen wenn nicht für ein Modell mit Gerätetyp {{ code }} verwendet"}}, "CO": {"SELECT": {"STD": "Auslassen wenn Konfigtabellen-Eintrag {{ code }} aktiv", "INV": "Auslassen wenn Konfigtabellen-Eintrag {{ code }} nicht aktiv"}}, "P": {"INQUEUE": {"STD": "Au<PERSON><PERSON> wenn die Prozedur {{ code }} auch in der Prüfung verwendet wird", "INV": "Auslassen wenn die Prozedur {{ code }} nicht in der Prüfung verwendet wird"}, "PASS": {"OMIT": "Au<PERSON>ssen wenn die Prozedur {{ code }} beendet und bestanden wurde", "SKIP": "Überspringen wenn ie Prozedur {{ code }} beendet und bestanden wurde"}, "FAIL": {"OMIT": "Auslassen wenn die Prozedur {{ code }} beendet und wurde und fehlgeschlagen ist", "SKIP": "Überspringen wenn die Prozedur {{ code }} beendet und wurde und fehlgeschlagen ist"}, "SKIP": {"OMIT": {"STD": "Auslassen wenn die Prozedur {{ code }} übersprungen wurde", "INV": "Auslassen wenn die Prozedur {{ code }} nicht übersprungen wurde"}, "SKIP": {"STD": "Überspringen wenn die Prozedur {{ code }} übersprungen wurde", "INV": "Überspringen wenn die Prozedur {{ code }} nicht übersprungen wurde"}}, "OMIT": {"OMIT": {"STD": "Auslassen wenn die Prozedur {{ code }} ausgelassen wurde", "INV": "Auslassen wenn die Prozedur {{ code }} nicht ausgelassen wurde"}, "SKIP": {"STD": "Überspringen wenn die Prozedur {{ code }} ausgelassen wurde", "INV": "Überspringen wenn die Prozedur {{ code }} nicht ausgelassen wurde"}}}, "S": {"PASS": {"OMIT": "Auslassen wenn der Schritt {{ code }} beendet und bestanden wurde", "SKIP": "Überspringen wenn der Schritt {{ code }} beendet und bestanden wurde"}, "FAIL": {"OMIT": "Auslassen wenn der Schritt {{ code }} beendet wurde und fehlgeschlagen ist", "SKIP": "Überspringen wenn der Schritt {{ code }} beendet wurde und fehlgeschlagen ist"}, "SKIP": {"OMIT": {"STD": "Auslassen wenn der Schritt {{ code }} übersprungen wurde", "INV": "Auslassen wenn der Schritt {{ code }} nicht übersprungen wurde"}, "SKIP": {"STD": "Überspringen wenn der Schritt {{ code }} übersprungen wurde", "INV": "Überspringen wenn der Schritt {{ code }} nicht übersprungen wurde"}}, "OMIT": {"OMIT": {"STD": "Auslassen wenn der Schritt {{ code }} ausgelassen wurde", "INV": "Auslassen wenn der Schritt {{ code }} nicht ausgelassen wurde"}, "SKIP": {"STD": "Überspringen wenn der Schritt {{ code }} ausgelassen wurde", "INV": "Überspringen wenn der Schritt {{ code }} nicht ausgelassen wurde"}}}, "M": {"PASS": {"OMIT": "Auslassen wenn der Schritt/Messung {{ code }} beendet und bestanden wurde", "SKIP": "Überspringen wenn der Schritt/Messung {{ code }} beendet und bestanden wurde"}, "FAIL": {"OMIT": "Auslassen wenn der Schritt/Messung {{ code }} beendet wurde und fehlgeschlagen ist", "SKIP": "Überspringen wenn der Schritt/Messung {{ code }} beendet wurde und fehlgeschlagen ist"}, "YES": {"OMIT": "Auslassen wenn der Schritt/Messung {{ code }} mit '<PERSON><PERSON>' beantwortet wurde", "SKIP": "Überspringen wenn der Schritt/Messung {{ code }} mit '<PERSON><PERSON>' beantwortet wurde"}, "NO": {"OMIT": "Auslassen wenn der Schritt/Messung {{ code }} mit 'Nein' beantwortet wurde", "SKIP": "Überspringen wenn der Schritt/Messung {{ code }} mit 'Nein' beantwortet wurde"}, "SKIP": {"OMIT": {"STD": "Auslassen wenn der Schritt/Messung {{ code }} übersprungen wurde", "INV": "Auslassen wenn der Schritt/Messung {{ code }} nicht übersprungen wurde"}, "SKIP": {"STD": "Überspringen wenn der Schritt/Messung {{ code }} übersprungen wurde", "INV": "Überspringen wenn der Schritt/Messung {{ code }} nicht übersprungen wurde"}}, "OMIT": {"OMIT": {"STD": "Auslassen wenn der Schritt/Messung {{ code }} ausgelassen wurde", "INV": "Auslassen wenn der Schritt/Messung {{ code }} nicht ausgelassen wurde"}, "SKIP": {"STD": "Überspringen wenn der Schritt/Messung {{ code }} ausgelassen wurde", "INV": "Überspringen wenn der Schritt/Messung {{ code }} nicht ausgelassen wurde"}}}}, "ERROR": {"CLIENT": {"TITLE": "Ein schwerer Fehler trat auf...", "TOTTL": "Zeitüberschreitung...", "ADTTL": "Zugriff verweigert...", "DETAILS": "Details:", "BACK": "<PERSON><PERSON><PERSON> zum Login", "MSG": {"TIMEOUT": "Die aktuelle Sitzung wurde wegen eines Timeout beendet. Bitte erneut einloggen und fortfahren.", "ACCDENY": "Der Zugriff wurde auf Grund fehlender Rechte oder Privilegien nicht erlaubt. Bitte erneut einloggen und fortfahren.", "SEVREST": "Ein schwerer Serverfehler trat auf; der Prozess kann nicht fortgeführt werden. Bitte zurück zum Login gehen oder in der Titelzeile zu einer anderen Seite wechseln.", "HTTP404": "@:ERROR.CLIENT.MSG.SEVREST", "HTTP500": "@:ERROR.CLIENT.MSG.SEVREST"}}}, "LOGIN": {"TITLE": "<PERSON><PERSON>", "USERNAME": "<PERSON><PERSON><PERSON><PERSON>", "PASSWORD": "Passwort", "BUTTON": "@:LOGIN.TITLE"}, "UI": {"BUTTONS": {"MEDITOR": {"RESET": "@:WFLOW.INPUT.BUTTON.CLEAR", "ADDMSEL": "{{ num }} Eintrag/äge hinzufügen", "ADDMSELTEST": "Test {{ num }} item(s)", "SAVE": "<PERSON><PERSON><PERSON>", "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "SELECT": "Auswählen", "SELALL": "Alles auswählen", "DESELALL": "Z<PERSON>ücksetzen"}, "ALERT": {"OK": "OK", "YES": "@:WFLOW.INPUT.YES", "NO": "@:WFLOW.INPUT.NO"}}, "MEDITOR": {"MAXSEL": "Es dürfen noch {{ num }} Eintrag/äge ausgewählt werden", "NOMORESEL": "Die maximale Anzahl ausgewählter Einträge ist erreicht"}}, "VIEWER": {"IMAGE": {"TITLE": "<PERSON>il<PERSON><PERSON><PERSON><PERSON><PERSON>", "ZOOM": {"FULL": "Vollansicht", "FIT": "Eingepasst", "STEP": "Zoom {{ factor }}x", "MAX": "Originale Grösse"}}, "PDF": {"TITLE": "PDF Anzeige"}, "VIDEO": {"TITLE": "Video player"}}, "TLISTEDIT": {"TOOLTIP": {"DEL": "Eintrag entfernen", "MEDMGR": "Mediamanager für diesen Eintrag starten", "EDIT": "Eintrag editieren", "NEW": "Neuen Eintrag erstellen"}, "LOADING": "Lade Editor...", "DISABLED": "Deaktiviert!", "CNT": {"CHECK": "{{ cnt }} P<PERSON><PERSON><PERSON><PERSON>(en)", "MODEL": "{{ cnt }} Modell(e)", "IMAGES": "{{ cnt }} Bild(er)", "DOCS": "{{ cnt }} Dokument(e)"}, "NEW": "<PERSON><PERSON><PERSON> Eintrag", "CODE": "Code", "TITLE": "Titel", "DESC": "Beschreibung", "DISSET": {"TITLE": "Deaktiviert", "TRUE": "Dieser Eintrag ist deaktiviert und kann nicht mehr eingesetzt werden.", "FALSE": "Dieser Eintrag ist aktiv."}, "BUTTON": {"CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL", "ADD": "Hinzufügen", "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"}}, "MEDITOR": {"GROUP": "Gruppe", "USER": "<PERSON><PERSON><PERSON>"}, "DASHBLOCK": {"TOOLTIP": {"CLOSE": "Block entfernen", "GOTO": "Zum Eintrag gehen", "SETFILTER": "Filtereinstellungen editieren", "EDITTITLE": "Blocktitel ändern"}}, "DROPBOX": {"INTRO": "<PERSON><PERSON>r den Import eine Exportdatei in dieses Feld ziehen!", "UPLOADING": "Lade {{ num }} Datei(en) hoch", "REMAINING": "{{ num }} <PERSON><PERSON>(en) in der Warteschlange", "SUCCESS": "{{ num }} Datei(en) erfolgreich hochgeladen", "ERRORS": "{{ num }} <PERSON><PERSON>(en) mit <PERSON><PERSON>n"}, "PRV": {"MNGMUC": {"TTL": "<PERSON>le/Einheiten/Checks bearbeiten", "DSC": "<PERSON>f die Bearbeitungsansicht für Modelle, Einheiten und Prüfungen aufrufen"}, "MNGPSM": {"TTL": "<PERSON><PERSON><PERSON> bearbeiten", "DSC": "<PERSON>f die Bearbeitungsansicht für Prozeduren, sowie Schritte und Messungen aufrufen"}, "MNGTOL": {"TTL": "Werkzeugtypen bearbeiten", "DSC": "<PERSON><PERSON> die Bearbeitungsansicht für Werkzeugtypen aufrufen"}, "MNGPAR": {"TTL": "Parameter bearbeiten", "DSC": "<PERSON>f die Bearbeitungsansicht für Parameter, Gerätetypen und Prüfungsarten aufrufen, sowie die optionalen Einstellungen bearbeiten"}, "MNGUSR": {"TTL": "<PERSON><PERSON><PERSON> bearbeiten", "DSC": "Darf die Benutzermanagementansicht aufrufen"}, "MNGALL": {"TTL": "<PERSON><PERSON> bear<PERSON>ten", "DSC": "Darf alle Bearbeitungsansichten aufrufen (Privilegiensammlung)"}, "EDTMOD": {"TTL": "<PERSON><PERSON> editier<PERSON>", "DSC": "<PERSON><PERSON> Modelle editieren"}, "EDTUNT": {"TTL": "Einheiten editieren", "DSC": "<PERSON><PERSON>ende Einheiten editieren"}, "EDTPSM": {"TTL": "Prozeduren editieren", "DSC": "<PERSON><PERSON>, einschliesslich deren Schritte und Messungen editieren (Schritte und Messungen dürfen auch gelöscht werden)"}, "EDTTTY": {"TTL": "Werkzeugtypen editieren", "DSC": "<PERSON><PERSON> exist<PERSON>ende Werkzeugtypen editieren"}, "EDTALL": {"TTL": "Alles editieren", "DSC": "Darf alle existierenden Objekte editieren (Privilegiensammlung)"}, "CRTMOD": {"TTL": "<PERSON><PERSON>", "DSC": "<PERSON><PERSON> ne<PERSON> er<PERSON>llen"}, "CRTUNT": {"TTL": "Einheiten erstellen", "DSC": "<PERSON><PERSON> neue Einheiten erstellen"}, "CRTPSM": {"TTL": "<PERSON><PERSON><PERSON> er<PERSON>", "DSC": "<PERSON><PERSON> neue Prozeduren er<PERSON>llen"}, "CRTTOL": {"TTL": "Werkzeuge erstellen", "DSC": "Darf neue Werkzeuge unter existierenden Werkzeugtypen eintragen"}, "CRTTTY": {"TTL": "Werkzeugtypen erstellen", "DSC": "<PERSON><PERSON> neue Werkzeugtypen erstellen"}, "CRTCHK": {"TTL": "Prüfungen erstellen", "DSC": "<PERSON><PERSON> neue Prüfungen einrichten oder Prüfungen löschen, die noch nicht gestartet sind"}, "CRTALL": {"TTL": "Alles erstellen", "DSC": "<PERSON><PERSON> neue Objekte jeden <PERSON> erstellen (Privilegiensammlung)"}, "DELMOD": {"TTL": "Modelle löschen", "DSC": "<PERSON><PERSON>le löschen oder deaktivieren"}, "DELUNT": {"TTL": "Einheiten löschen", "DSC": "<PERSON><PERSON>he<PERSON>n löschen oder deaktivieren"}, "DELPRC": {"TTL": "Prozeduren löschen", "DSC": "Dark Prozeduren löschen oder deaktivieren"}, "DELTTY": {"TTL": "Werkzeugtypen löschen", "DSC": "Darf Werkzeugtypen löschen oder deaktivieren"}, "DELTOL": {"TTL": "Werkzeuge löschen", "DSC": "Darf Werkzeuge löschen oder deaktivieren"}, "DELCHK": {"TTL": "Prüfungen löschen", "DSC": "Darf Prüfungen löschen"}, "DELALL": {"TTL": "Alles löschen", "DSC": "<PERSON><PERSON> aller Typen löschen oder deaktivieren (Privilegiensammlung)"}, "USRMGO": {"TTL": "@:PRV.MNGUSR.TTL", "DSC": "<PERSON><PERSON> niedrigerer Stufen bearbeiten (hinzufügen, ändern, deaktivieren)"}, "USRMGA": {"TTL": "Alle Benutzer bearbeiten", "DSC": "<PERSON><PERSON> alle Benutzer bearbeiten (hinzufügen, ändern, deaktivieren)"}, "GRTPRO": {"TTL": "Eigene Privilegien vergeben", "DSC": "<PERSON><PERSON> sel<PERSON>t besessene Privilegien an andere Benutzer vergeben"}, "GRTPRA": {"TTL": "Alle Privilegien vergeben", "DSC": "<PERSON><PERSON> alle verfügbaren Privilegien an andere Benutzer vergeben"}, "GRTTOG": {"TTL": "Gruppenprivilegien vergeben", "DSC": "Darf Privilegien auch an Gruppen vergeben (erweiterndes Privileg)"}, "GRPCRT": {"TTL": "Gruppen erstellen", "DSC": "Darf Gruppen erstellen und editieren"}, "WFLMNG": {"TTL": "Arbeitsablauf verwalten", "DSC": "Darf <PERSON>rüfungen und Prüfungsabläufe verwalten, Prüfungen kommentieren und erweiterte Informationen einsehen"}, "WFLREG": {"TTL": "An Arbeitsablauf anmelden", "DSC": "<PERSON><PERSON> sich an verfügbare und passende Arbeitsabläufe anmelden, bzw. von selbst-registrierten Arbeitsabläufen abmelden"}, "CHGCOD": {"TTL": "Codewert ändern", "DSC": "<PERSON><PERSON> den <PERSON>wert beim Ändern verschiedener Objekte editieren (erweiterndes Privileg)"}, "FINALZ": {"TTL": "Version finalisieren", "DSC": "Darf die aktuellen Versionen der Objekte finalisieren (nur verfüg<PERSON>, wenn auch das Editierrecht für das Objekt besessen ist; erweiterndes Privileg)"}, "MODVRS": {"TTL": "Version veränden", "DSC": "Darf eine finalisierte Version eines Objektes verändern (nur Rechtschreibfehler)"}, "TRUSER": {"TTL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DSC": "Der Benutzer ist in dem System erfahren und darf zusätzlich einige beschleunigende Funktionen verwenden"}, "TKOVER": {"TTL": "Zugewiesene Aufgaben übernehmen", "DSC": "Der Benutzer kann bei Eignung Aufgaben übernehmen, für die sich andere Benutzer bereits registriert haben."}, "GLSRCH": {"TTL": "Zugriff auf global Suche", "DSC": "Der Benutzer darf die globale Suche verwenden."}, "MNGNTC": {"TTL": "Fehlerberichte anzeigen und bearbeiten", "DSC": "Der Benutzer darf Fehlerberichte einsehen und bearbeiten."}, "MNGCFG": {"TTL": "Konfigurationstabellen anzeigen und bearbeiten", "DSC": "Der Benutzer darf Konfigurationstabellen einsehen und bearbeiten."}, "MNGCFE": {"TTL": "Konfigurationen für Prüfungen bearbeiten", "DSC": "Der Benutzer darf Konfigurationen auf Prüfungen vornehmen."}}, "PDF": {"MEASUREMENTERRORREPORT": {"TITLE": "Rapport Messungsfehler", "PROCEDURE": "<PERSON><PERSON><PERSON>", "STEP": "<PERSON><PERSON><PERSON>", "MEASURE": "Messung", "MODEL": "Model", "UNIT": "Einheit", "VALUE": "Wert", "SAVEDBY": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "ERRORCATEGORY": "Fehlerkategorie", "STATUS": {"TITLE": "Status", "UNPROC": "Ausgelassen", "PASSED": "<PERSON><PERSON><PERSON>", "FAILED": "Fehlgeschlagen", "INVALID": "Invalid", "ERROR": "<PERSON><PERSON>"}, "EXPINPUT": {"TARGET": "@:WFLOW.INPUT.TARGET", "THRESH": "@:WFLOW.INPUT.THRESH", "VALUERNG1": "@:WFLOW.INPUT.VALUERNG1", "VALUERNG2": "@:WFLOW.INPUT.VALUERNG2", "VALUECMP1": "@:WFLOW.INPUT.VALUECMP1", "VALUECMP2": "@:WFLOW.INPUT.VALUECMP2", "VALUECMP3": "@:WFLOW.INPUT.VALUECMP3", "VALUECMP4": "@:WFLOW.INPUT.VALUECMP4", "VALUECMP5": "@:WFLOW.INPUT.VALUECMP5", "STATISTIC": "Gemessener Wert, keine Prüfung stattgefunden", "CHECKCMP1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ob gemessener Wert kleiner ist als {{ val }}", "CHECKCMP2": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ob gemessener Wert kleiner oder gleich ist wie {{ val }}", "CHECKCMP3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ob gemessener Wert gleich ist wie {{ val }}", "CHECKCMP4": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ob gemessener Wert grösser oder gleich ist wie {{ val }}", "CHECKCMP5": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ob gemessener Wert grösser ist als {{ val }}", "TEXTLEN1": "@:WFLOW.INPUT.TEXTLEN1", "TEXTLEN2": "@:WFLOW.INPUT.TEXTLEN2", "REGEXP": "Geprüfter Text:", "EXPNO": "@:WFLOW.INPUT.EXPNO", "EXPYES": "@:WFLOW.INPUT.EXPYES", "EXPBOTH": "@:WFLOW.INPUT.EXPBOTH", "TEXTTIMERSTART": "Eingegebene Startzeit", "TEXTTIMERSTOP": "Eingegebene Stopzeit", "TEXTTIMERSTOPLT": "Eingegebene Stopzeit: erwartete Zeit bis {{ val }} min", "TEXTTIMERSTOPGT": "Eingegebene Stopzeit: erwartete Zeit grösser als {{ val }} min", "CHOICELIST": "Auswahlliste"}}, "BOOL": {"YES": "JA", "NO": "NEIN"}, "MAINTTL": "Prüfungsbericht", "STATUS": {"OPEN": "OFFEN", "FAILED": "FEHLG.", "WARNINT": "FEHLG. <sup>*)</sup>", "WARNEXT": "OK <sup>*)</sup>", "PASSED": "OK", "UNFIN": "ZU ERL.", "PASS": "OK", "FAIL": "FEHLG.", "SKIP": "AUSGEL.", "CANCELLED": "ABGEBR."}, "HINT": {"CHECKWARN": {"INTERNAL": "Die fehlgeschlagenen Messungsresultate sind für die Funktion der Maschine nicht oder nur geringfügig von Bedeutung.", "CUSTOMER": "Ein Messungsresultat, das die Funktion der Maschine nicht beeinflusst, liegt geringfügig ausserhalb der festgelegten Toleranzen."}}, "USTATUS": {"OPEN": "<PERSON>en", "CLOSED": "Geschlossen", "DISCARDED": "Verworfen", "ACLOSED": "Geschlossen & archiviert", "ADISCARDED": "Verworfen & archiviert"}, "MODEL": "@:WFLOWEDIT.OPT1.MD", "UNIT": "@:WFLOW.INTRO.UNIT", "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER", "DATE": {"SCHED": "@:WFLOW.INTRO.DATE.SCHED", "DUE": "@:WFLOW.INTRO.DATE.DUE", "NOSCHED": "@:WFLOW.INTRO.DATE.NOSCHED", "NODUE": "@:WFLOW.INTRO.DATE.NODUE", "START": "@:WFLOW.INTRO.DATE.START", "FIN": "@:WFLOW.INTRO.DATE.FIN", "NOSTART": "@:WFLOW.INTRO.DATE.NOSTART", "NOFIN": "@:WFLOW.INTRO.DATE.NOFIN"}, "USER": {"NOONE": "<PERSON><PERSON> ni<PERSON>d", "ANY": "<PERSON>cht festgelegt", "GRP": "Benutzer der Gruppe %s"}, "VERSION": "@:PROCEDURE.VIEW.VERS", "STEP": {"ASSNC": "<i><PERSON><PERSON>wiese<PERSON> an:</i> %s, <b>noch nicht bestätigt</b>", "ASSC": "<i><PERSON><PERSON><PERSON><PERSON>n an:</i> %s, <i>best<PERSON><PERSON>t von:</i> %s"}, "MEASURE": {"TOOL": "<i>Werkzeug (typ </i><b>%s</b>: %s) verwendet:</i> <b>%s</b>", "TOOLNOSEL": "<i>Verwendetes Werkzeug (typ </i><b>%s</b>: %s<i>) <b>noch nicht eingetragen</b></i>", "REP": "<i><PERSON><PERSON><PERSON><PERSON> von </i><b>%s</b> <i>am</i> <b>%s</b>", "COMM": "<i>Kommentar:</i> %s", "RAW": "<i>Matrixwerte:</i> %s"}, "MINPUT": {"VALUECMP": "Der Wert muss %s %s sein", "CHECKCMP": "'J<PERSON>' wenn der gemessene Wert %s %s ist", "COMP": {"T1": "kleiner als", "T2": "kleiner als oder gleich", "T3": "gle<PERSON>", "T4": "g<PERSON><PERSON><PERSON> als oder gleich", "T5": "<PERSON><PERSON><PERSON><PERSON>s"}, "THRESH": "Zielwert: %s, Abweichung %s", "VALUERNG": "Wert muss zwischen %s und %s sein", "TEXTLEN": "Der Text muss mindestens %d <PERSON><PERSON><PERSON> lang sein", "TEXTPAT": "Der Text muss auf ein bestimmtes Muster passen: %s", "CHOICE": "Ein Wert muss aus der Liste ausgewählt werden", "EXP": {"NO": "@:WFLOW.INPUT.EXPNO", "YES": "@:WFLOW.INPUT.EXPYES", "BOTH": "@:WFLOW.INPUT.EXPBOTH"}, "STATISTICAL": "Für statistische Auswertungen; jeder Wert wird akzeptiert", "TIMERA": "Zeitmessung (Start)", "TIMERS": "Zeitmessung (Ende) für statistische Auswertungen", "TIMERQ": {"T1": "Zeitmessung (Ende); gemessene Zeit muss unter %d Minuten liegen", "T5": "Zeitmessung (Ende); gemessene Zeit muss über %d Minuten liegen"}, "TIMERC": {"T1": "Prüfung Zeitmessung; gemessene Zeit muss unter %d Minuten liegen", "T5": "Prüfung Zeitmessung; gemessene Zeit muss über %d Minuten liegen"}}, "STATREPORT": "Messungsstatistik", "PAGENO": "Seite %d von %d", "STAT": {"CHECKINTRO": "Für die Statistik werden folgende Prüfungen verwendet:", "CHECK": {"LINE1": "Prüfung <b>#%s</b> (%s) für Einheit <b>%s</b> (%s), Modell <b>%s: %s", "LINE2": "Prüfungsstatus: <b>%s</b>, Status der Einheit: <b>%s</b>"}, "MSR1": "Messung <b>%s: %s</b>", "MSR2": "<PERSON><PERSON>itt <b>%s: %s</b>, Prozedur <b>%s: %s</b>", "MSR2A": "<PERSON>hritt <b>%s: %s</b>", "MSR2B": "Prozedur <b>%s: %s</b>", "MSR3S": "Regel: <b>%s</b>", "MSR3O": "Regel: <b>%s</b> (optional, kann übersprungen werden)", "MSR4T": "Werkzeugtyp: <b>%s: %s</b>", "MSR4N": "Werkzeugtyp: <i>Kein Werkzeug verwendet</i>", "OLDV": "<sup>*)</sup> Eine ältere Version der Messung wurde während der Prüfung verwendet; die Ergebnisse können u.U. nicht kompatibel sein.", "NODATA": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "HDR": {"STATUS": "@:WFLOW.INPUT.STATUS.TITLE", "UNIT": "Einheit", "USER": "@:MEDITOR.USER", "TOOL": "Werkzeug", "VALUE": "Wert"}, "FINAL": {"INTRO": "Die folgenden id-Strings wurden bei der Generierung des Reports verwendet. Sie können sie verwenden, um den Report noch einmal ganz oder modifiziert zu generieren:", "CHECKS": "Prüfungen:", "MEASURES": "Messungen:"}, "TITLE": {"FRONT": "Übersicht", "STATS": "Messung %s"}}, "COMMENT": {"TOOLTIP": "Kommentarreport anzeigen", "TTL": "Kommentarreport", "FOOTER": {"USERS": "<PERSON><PERSON>", "TOOLTYPE": "Werkzeugtyp '%s' (%s)", "MODEL": "Modell '%s' (%s)", "UNIT": "Einheit %s", "CHECK": "Prüfung #%d (Einheit %s, Modell %s)", "STEP": "Schritt '%s' V.%d (%s.%s)", "MEASURE": "Messung '%s' V.%d (%s.%s.%s)"}, "ELEMENT": {"USER": "Benutzer %s (%s)", "TOOL": "Werkzeugeinheit %s", "TOOLTYPE": "Werkzeugtyp %s", "UNIT": "Einheit %s (%s)", "CHECK": "Prüfung #%d (Einheit %s, Modell %s)", "MSMNT": "Messung %s in Schritt %s, Prozedur %s", "STEP": "Schritt %s", "PROC": "Prozedur %s", "MSMNTS": "Messung %s", "MEAS": "Messung %s"}}, "TUREPORT": {"HEADER": "Verwendungsreport Werkzeug", "FOOTER": "Werkzeugtyp <b>%s</b>, Einheit <b>%s</b>", "TITLE": {"MODEL": "<b>Modell %s</b>, Einheit %s", "TIME": "%s.<b>%s</b>"}, "CONTINUED": "<i>(fortg.)</i>", "ENTRY": {"MODEL": "Prozedur <b>%s</b>, <PERSON><PERSON><PERSON> <b>%s</b>, <PERSON><PERSON>ung <b>%s</b>", "TIMET": "Modell <b>%s</b>, Einheit <b>%s</b>", "TIMEB": "<i>Prozedur <b>%s</b>, <PERSON><PERSON><PERSON> <b>%s</b>, <PERSON><PERSON>ung <b>%s</b></i>"}}}, "CHANGELOG": {"ITEM": {"PROCEDURE": "<PERSON><PERSON><PERSON>", "STEP": "<PERSON><PERSON><PERSON>", "MEASURE": "Messung", "IMAGE": "Bild", "DOCUMENT": "Dokument", "MODEL": "<PERSON><PERSON>", "CHECKTYPE": "Prüfungsart"}, "TYPE": {"CREATE": "<PERSON><PERSON>", "CHANGECODE": "Code geändert (ursprünglich: {{ oldcode }})", "CHANGEFIELD": "Feld '{{ field }}' ge<PERSON><PERSON><PERSON>", "CHANGESEQ": "Reihenfolge geändert (mehrere Objekte betroffen)", "CHANGEPID": "Prozedur auf Version {{ version }} aktualisiert", "FINALIZE": "<PERSON><PERSON><PERSON>", "DELETE": "Gelöscht", "CREATEATT": "<PERSON><PERSON> zu {{ tgt }} hinzugefügt", "DELETEATT": "Von {{ tgt }} entfernt", "CREATEACTP": "<PERSON><PERSON><PERSON> zu <PERSON> hinzugefügt", "DELETEACTP": "<PERSON><PERSON><PERSON>", "CREATEACTCT": "<PERSON><PERSON>ü<PERSON><PERSON><PERSON><PERSON> zu <PERSON>l hinzugefügt", "DELETEACTCT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PREALLOCATION": "Vorabzuweisungen modifiziert"}, "VIEWER": {"TITLE": "Änderungsliste", "EXTRODIRTY": "Blau markierte Einträge sind direkte Benutzeränderungen und benötigen eine Finalisierung um aktiv genutzt zu werden.", "EXTROPOSTFIN": "Rot markierte Einträge sind Änderungen, die nach der Finalisierung am Objekt vorgenommen wurden.", "NOENTRIES": "Die Änderungsliste ist momentan leer."}, "BUTTON": {"OPENDIALOG": "Änderungsliste", "CLOSE": "<PERSON><PERSON><PERSON><PERSON>"}}, "GSEARCH": {"WIZARD": {"TITLE": "Globale Suche", "TEXT1": "Bitte geben Sie den Suchbegriff ein. Der Begriff muss mindestens drei Zei<PERSON> lang sein, ein Asterisk ('*') gilt al<PERSON>, ein Pipesymbol ('|') am Anfang oder Ende des Begriffs spezifizieren, dass der Text mit dem gesuchten Begriff anfangen bzw. enden muss. Mehrere Suchbegriffe können per Plus-Zeichen ('+') zusammengefügt werden, diese müssen alle im Text vorkommen. Gross-/Kleinschreibung wird nur beachtet, wenn ein Begriff mit einem Ausrufezeichen ('!') vorangestellt wird.", "TEXT2": "Bitte wählen Sie die Felder aus, über die nach dem Begriff gesucht werden soll.", "TEXT3": "Bitte wählen Sie die Sprachen aus, über die der Begriff gesucht werden soll (in mehrsprachigen Feldern).", "TEXT4": "Bitte wählen Sie, welche Einschränkung bei versionierten Objekten gelten soll:"}, "SCOPE": {"ALLALL": {"TEXT": "<PERSON><PERSON> (Vollsuche)", "TTIP": "Alle Versionen (inkl. nicht finalisierter) werden durchsucht, alle gefundenen Ergebnisse angezeigt."}, "ALLRECENT": {"TEXT": "Vollsuche, nur aktuellstes Element", "TTIP": "Alle Versionen (inkl. nicht finalisierter) werden durchsucht, jedoch nur das aktuellste (zuletzt geänderte) Objekt angezeigt."}, "FINALL": {"TEXT": "Finalisierte Objekte", "TTIP": "Alle finalisierten Versionen werden durchsucht, alle gefundenen Ergebnisse angezeigt."}, "FINRECENT": {"TEXT": "Finalisierte Objekte, nur aktuellstes Element", "TTIP": "Alle finalisierten Versionen werden durchsucht, jedoch nur das aktuellste (zuletzt geänderte) Objekt angezeigt."}, "LATESTV": {"TEXT": "Letzte Version", "TTIP": "Nur die jeweils letzte finalisierte Version wird durchsucht."}, "EDITV": {"TEXT": "Editierversion", "TTIP": "Nur die aktuellsten, noch nicht finalisierten Versionen werden durchsucht."}}, "TYPE": {"CHECK": {"SELECT": "Prüfungen", "DISPLAY": "<u>Prüfung</u> <b>{{ object.id }}</b> (<i>{{ object.checktype.title.de }}</i>) für Einheit <b>{{ object.unit.code}}</b>, Modell <b>{{ object.unit.model.code }}</b>"}, "MEASUREMENT": {"SELECT": "Messeintragungen", "DISPLAY": "<u>Messeintragung</u> in Prüfung <b>{{ object.check.id }}</b> (Einheit {{ object.check.unit.code }}, Modell {{ object.check.unit.model.code }}) für Messung <b>{{ object.measure.code }}</b> in Schritt <b>{{ object.measure.step.code }}</b>, Prozedur <b>{{ object.measure.step.procedure.code }}</b>"}, "CHECKTYPE": {"SELECT": "Prüfungstypen", "DISPLAY": "<u>Prüfungstyp</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"}, "DEVICETYPE": {"SELECT": "Gerätetypen", "DISPLAY": "<u>Gerätetyp</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"}, "MODEL": {"SELECT": "<PERSON><PERSON>", "DISPLAY": "<u>Modell</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>), Version {{ object.version || '(Edit)' }}"}, "UNIT": {"SELECT": "Einheiten", "DISPLAY": "<u>Einheit</u> <b>{{ object.code }}</b> in Modell <b>{{ object.model.code }}</b>"}, "MEASURE": {"SELECT": "Messungen", "DISPLAY": "<u>Messung</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>) in Schritt <b>{{ object.step.code }}</b> / Prozedur <b>{{ object.step.procedure.code }}</b>, Version {{ object.procedure.version || '(Edit)' }}"}, "STEP": {"SELECT": "<PERSON><PERSON><PERSON>", "DISPLAY": "<u><PERSON><PERSON><PERSON></u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>) in Prozedur <b>{{ object.procedure.code }}</b>, Version {{ object.procedure.version || '(Edit)' }}"}, "PROCEDURE": {"SELECT": "<PERSON><PERSON><PERSON>", "DISPLAY": "<u>Prozedur</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>), Version {{ object.version || '(Edit)' }}"}, "TOOLTYPE": {"SELECT": "Werkzeugtypen", "DISPLAY": "<u>Werkzeugtyp</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"}, "TOOLUNIT": {"SELECT": "Werkzeugeinheiten", "DISPLAY": "<u>Werkzeugeinheit</u> <b>{{ object.code }}</b> in Werkzeugtyp <b>{{ object.tooltype.code }}</b>"}, "USER": {"SELECT": "<PERSON><PERSON><PERSON>", "DISPLAY": "<u><PERSON><PERSON><PERSON></u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)"}, "NOTICE": {"SELECT": "Fehlerbericht", "DISPLAY": "<u>Fehlerbericht</u> <b>{{ object.id }}</b>"}, "CONFIGTABLE": {"SELECT": "Konfigurationstabellen", "DISPLAY": "<u>Konfigurationstabelle</u> <b>{{ object.id }}</b>"}, "CONFIGENTRY": {"SELECT": "Konfigurationstabellen-Einträge", "DISPLAY": "<u>Eintrag</u> <b>{{ object.code_id }}</b> in Konfigurationstabelle <b>{{ object.configtable_id }}</b>"}}, "FIELD": {"ID": "<PERSON><PERSON><PERSON>", "COMMENT": "Kommentar", "CODE": "Code", "TITLE": "Titel", "DESCRIPTION": "Beschreibung/Hinweis", "CUSTOMER": "Kunde", "USERNAME": "<PERSON><PERSON><PERSON><PERSON>", "REALNAME": "<PERSON>chter Name", "TEXT": "Beschreibung", "COL1": "<PERSON><PERSON><PERSON>", "COL2": "Zweite Spalte", "COL3": "<PERSON><PERSON>", "COL4": "<PERSON><PERSON><PERSON>e", "COL5": "Fünfte Spalte", "COL6": "Sechste Spalte", "DELETED": "Metainformationen", "CALCULATION": "Auswahlliste"}, "BUTTONS": {"NEWSEARCH": "Neue Suche", "CLOSE": "<PERSON><PERSON><PERSON><PERSON>"}, "RESULT": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TEXT": "Die letzte Suche ergab folgende Ergebnisse, nach Änderungsdatum sortiert:", "TOOMUCH": "(Hinweis: <PERSON><PERSON> werden nur die ersten 50 Treffer angezeigt)", "NOMATCH": "<PERSON><PERSON> gab keine <PERSON>"}}, "NOTICES": {"TOOLTIP": {"SHOW": "Anzeigen", "SHOWALL": "Alle anzeigen", "HIDE": "Ausblenden", "FILTER": "Nach diesem Wert filtern", "NOFILTER": "<PERSON><PERSON> au<PERSON>"}, "EXPORT": {"TTL": "Fehlerberichte exportieren", "TYPETEXT": "Bitte wählen Sie den Dateityp, in dem die Liste exportiert werden soll:", "FILTERTEXT": "Bitte geben Sie den Umfang der Liste an:", "CSV": "Comma Separated Values (CSV)", "JSON": "JavaScript Output Notation (JSON)", "XML": "Extensible Markup Language (XML)", "UNARCHIVED": "Alle nicht archivierten Berichte", "ALL": "Alle (auch archivierte) Berichte", "FILTERED": "Alle in der gefilterten Liste enthaltenen Berichte"}, "FILTEREDIT": {"TITLE": "<PERSON><PERSON> den Filter definieren", "ID": "Bitte geben Sie die Reichweite der IDs an, die angezeigt werden sollen. Sie können dabei '<von>-<bis>', '-<bis>' oder '<von>-' angeben. Beispiel: '10-20' (alle IDs von 10 bis 20); '-50' (alle IDs bis 50).", "CATEGORY": "Bitte wählen Sie die Kategorien aus, die angezeigt werden sollen:", "PATH": "Bitte geben Sie einen Suchbegriff ein; dieser wird gegen den angezeigten Pfadtext geprüft:", "TEXT": "Bitte geben Sie einen Suchbegriff ein; dieser wird gegen die Beschreibung geprüft:", "ARTICLE": "Bitte geben Sie einen Suchbegriff ein; dieser wird gegen die Artikelnummer (nicht: Artikelbezeichnung) geprüft. Soll statt dessen gegen die Artikelbezeichnung geprüft werden, leiten Sie bitte den Suchbegriff mit einem Doppelpunkt ':' ein:", "TIMELOSS": "Bitte wählen Sie die Angaben zum Zeitverlust aus, die in der Liste angezeigt werden sollen:", "STATUS": "Bitte wählen Sie die Stati aus, die in der Liste angezeigt werden sollen:"}, "TITLE": {"PROBLREP": "Fehlerbericht", "DESC": "Beschreibung", "PROPOSALS": "Vorschläge:", "CAT": "<PERSON><PERSON><PERSON>", "TIMELOSS": "<PERSON>eitverlust (ca.)", "ARTNO": "Artikelnr.", "ARTDESC": "Bezeichnung", "PROBLREPNO": "Fehlerbericht nr."}, "TEXT": {"DESC": "Bitte geben Sie zu dem Fehlerbericht eine kurze und aussagekräftige Beschreibung; <PERSON><PERSON> können da<PERSON>, falls passend, aus einigen Vorschlägen auswählen oder einen eigenen Text eingeben.", "CHOOSECAT": "Bitte wählen Sie aus den gegebenen Kategorien eine aus, die auf den Fehlerbericht passt. Falls Sie durch den Fehler beträchtliche Zeit verloren haben, geben Sie dies durch Auswahl einer passenden Zeit an.", "ARTICLE": "Falls der Fehler einen bestimmten Artikel betrifft oder damit im Zusammenhang steht, können Si<PERSON> hier den Artikel mit Nummer und Bezeichnung angeben.", "STTCHANGE": "Bitte geben Sie einen kurzen Kommentar zu der Statusänderung ein:"}, "BUTTON": {"USE": "Übernehmen", "CANCEL": "Abbrechen", "SEND": "Senden", "CATEGORIES": "<PERSON><PERSON><PERSON> edit<PERSON>", "TEMPLATES": "Textvorschläge editieren", "EXPORT": "Liste exportieren", "STT_12": "Bearbeitung starten", "STT_21": "Bearbeitung abbrechen", "STT_25": "<PERSON><PERSON><PERSON><PERSON> beenden", "STT_52": "Bearbeitung wieder aufnehmen", "STT_59": "Archivieren", "CLOSE": "<PERSON><PERSON><PERSON><PERSON>"}, "VIEW": {"LOCATION": "Pfad", "CATEGORY": "<PERSON><PERSON><PERSON>", "ARTICLE": "Artikel", "TIMELOSS": "Zeitverlust", "NOTEXT": "<PERSON><PERSON>", "DESC": "Beschreibung", "ID": "<PERSON><PERSON><PERSON>", "PATH": "Pfad", "TEXT": "Beschreibung", "STATUS": "Status"}, "TIMELOSS": {"15": "ca. 15 Minuten", "30": "ca. 30 Minuten", "60": "ca. 1 Stunde", "90": "ca. 1½ Stunden", "120": "ca. 2 Stunden", "180": "ca. 3 Stunden", "240": "mehr als 4 Stunden"}, "ALERT": {"CATMISS": {"TITLE": "<PERSON><PERSON><PERSON> fehlt", "TEXT": "Es muss eine der verfügbaren Kategorien ausgewählt werden!"}, "DESCMISS": {"TITLE": "Beschreibung fehlt", "TEXT": "Die Beschreibung fehlt oder ist zu kurz!"}, "THANKS": {"TITLE": "Vielen Dank!", "TEXT": "Der Fehlerbericht wurde übertragen und wird in Kürze bearbeitet!"}, "CONFDEL": {"TITLE": "Wirklich löschen?", "TEXT1": "Den Eintrag wirklich löschen? Die Änderung ist unwiderruflich, frühere Verwendungen dieses Eintrages werden möglicherweise modifiziert.", "TEXT2": "Den Eintrag wirklich löschen? Der Eintrag wird aus der Liste entfernt und steht nicht mehr für zukünftige Nutzung zur Verfügung; frühere Nutzungen bleiben jedoch unverändert."}}, "MODAL": {"EDITTEXT": {"TITLE": "<PERSON><PERSON><PERSON> Eintrag", "TEXT": "<PERSON>te hier den Text für den Eintrag editieren:"}, "NEWTEXT": {"TITLE": "Eintrag editieren", "TEXT": "Bitte hier den Text für den Eintrag eingeben (mindestens drei Zeichen):"}, "SNIPPETCAT": {"TITLE": "Fehlerberichtkategorien", "SNIPPET": "<PERSON><PERSON><PERSON>"}, "SNIPPETDESC": {"TITLE": "Textvorschläge für Fehlerbericht", "SNIPPET": "Textvorschlag"}}, "STATUS": {"OPEN": "<b>e<PERSON><PERSON><PERSON></b> von <b>{{ user }}</b> ({{ realname }}) am {{ time }}", "PROCESSED": "<b>bearbeitet</b> durch <b>{{ user }}</b> ({{ realname }}) ab {{ time }}", "CLOSED": "<b>ges<PERSON><PERSON>en</b> von <b>{{ user }}</b> ({{ realname }}) am {{ time }}", "ARCHIVED": "<b>archiv<PERSON>t</b> am {{ time }}", "1": "Eingereicht", "2": "Bearbeitet", "5": "Geschlossen"}, "PATHTYPE": {"CHECKSTEP": "Prüfungsschritt", "CHECKGENERAL": "Prüfung (allgemein)"}, "SEGMENT": {"MODEL": "Modell <b>{{ code }}</b>", "UNIT": "Einheit <b>{{ code }}</b>", "CHECK": "Prüfung <b>{{ id }}</b>", "PROCEDURE": "Prozedur <b>{{ code }}</b>", "STEP": "<PERSON><PERSON>itt <b>{{ code }}</b>"}, "CORRECT": {"TITLE": "Korrektur", "DESCRIPTION": "Bitte editieren Sie die Beschreibung:", "TIMELOSS": "Bitte wählen Sie einen neuen Wert für den Zeitverlust:", "CATEGORY": "Bitte wählen Sie eine neue Kategorie:", "ARTICLE1": "Bitte editieren Sie die Artikelnummer (Artikelbezeichnung folgt im nächsten Schritt):", "ARTICLE2": "Bitte editieren Sie die Artikelbezeichnung:"}}, "SNIPPET": {"TITLE": {"ADD": "hinzufügen"}, "TOOLTIP": {"LOCK": "Schliesst den Eintrag. Frühere Verwendungen bleiben erhalten, es kann jedoch nicht weiter ausgewählt werden.", "UNLOCK": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Eintrag; er kann danach wieder ausgewählt werden.", "DELETE": "Löscht den Eintrag nach Rückfrage unwiderruflich.", "EDIT": "Der Text des Eintrages kann hiermit geändert werden.", "REORD": "<PERSON> Eintrag kann hiermit per Drag&Drop an anderer Stelle einfügt werden"}, "BUTTON": {"CLOSE": "<PERSON><PERSON><PERSON><PERSON>"}}, "CONFIGTABLE": {"CONFIGTABLES": "Konfigurationstabellen", "ALERT": {"CONFHEADER": "Sind Sie sicher?", "DELETE": "Möchten Sie diesen Eintrag deaktivieren?"}, "EDITOR": {"TITLE": {"NEW": "<PERSON><PERSON><PERSON> Eintrag", "EDIT": "Eintrag bearbeiten"}}, "EDIT": {"TITLE": "Titel", "HEADER": "Überschrift", "BUTTON": {"CANCEL": "@:MEASURE.EDIT.BUTTON.CANCEL", "CLOSE": "@:MEASURE.EDIT.BUTTON.CLOSE"}}, "LIST": {"ENTRY": {"EDIT": "Eintrag bearbeiten", "DELETE": "Eintrag deaktivieren", "UNDELETE": "Eintrag reaktivieren", "BLOCKED": "Dieser Eintrag wird von einer Ablaufregel verwendet."}, "RELATION": {"NONE": "kein <PERSON>l zugeordnet", "MODEL": "folgendem Modell zugeordnet:"}, "VERSION": "Version", "TOOLTIP": {"IMPORTTABLE": "Tabelle importieren", "NEWTABLE": "Neue Tabelle", "GOTOUNITS": "<PERSON><PERSON> <PERSON> we<PERSON>", "SHOWDEL": "Deaktivierte anzeigen", "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF", "CLONE": "<PERSON><PERSON><PERSON> k<PERSON>n", "DELETE": "<PERSON><PERSON><PERSON>", "EDIT": "<PERSON><PERSON><PERSON> edit<PERSON>", "REACTIVATE": "<PERSON><PERSON><PERSON>"}, "TOPROW": ""}, "VIEW": {"CODE": "Code", "ACTIVE": "Aktiv", "DISABLED": "Deaktiviert"}, "CLONE": {"TITLE": "Konfigurationstabellen klonen", "TEXT": "Wollen Sie die Konfigurationstabelle {{ title }} klonen?"}, "DELETE": {"TITLE": "Konfigurationstabellen deaktivieren", "TEXT": "Wollen Sie die Konfigurationstabelle {{ title }} wirklich deaktivieren? Sie können sie zu einem späteren Zeitpunkt wieder reaktivieren."}}, "SETTINGS": {"PROC": {"TAGS": {"TITLE": "Prozedur-Attribute verwalten", "ADD": "Attribute hinzufügen", "DELETE": "Attribut löschen", "EDIT": "Attribut editieren", "COLLAPSE": "Subattribute ein/ausklappen"}, "EDITTAG": {"TITLE": "Prozedur-Attribut bearbeiten"}, "NEWTAG": {"TITLE": "Prozedur-Attribut hinzufügen"}, "TAGVALUE": {"ADD": "<PERSON><PERSON> hinz<PERSON>", "DELETE": "<PERSON>rt l<PERSON>", "EDIT": "<PERSON>rt editieren"}, "NEWTAGVALUE": {"TITLE": "<PERSON><PERSON> hinz<PERSON>"}, "EDITTAGVALUE": {"TITLE": "<PERSON>rt bearbeiten"}}, "ALERT": {"CONFDEL": {"TITLE": "Löschen eines Prozedur-Attributes", "TEXT": "Sind sie sicher das das ausgewählte Prozedur-Attribut gelöscht werden soll?"}}}, "MEASUREMENTERRORCATEGORIES": {"TITLE": "Messungs-Fehlerkategorien verwalten", "ADD": "Messungs-Fehlerkategorie hinzufügen", "DELETE": "Messungs-Fehlerkategorie löschen", "EDIT": "Messungs-Fehlerkategorie bearbeiten", "TOOLTIP": {"REORDER": "@:MODEL.VIEW.TOOLTIP.REORDER"}}, "MEASUREMENTERRORREPORT": {"TITLE": "Rapport Messungsfehler", "MEASURES": {"PERIODFROM": "Wählen Sie den gewünschten Start des Zeitraums:", "PERIODTO": "Wählen Sie das gewünschte Ende des Zeitraums:", "TEXT": "Wählen Sie die gewünschten Messungen aus. Es werden nur Messungen angezeigt, die korrigiert wurden:"}, "SORT": {"TEXT": "Wählen Sie die gewünschte Sortierung aus:", "ERRORCATEGORY": "Fehlerkategorie", "MEASURE": "Messung", "USER": "<PERSON><PERSON><PERSON>", "UNIT": "Einheit"}}}