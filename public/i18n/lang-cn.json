{
  "FRAME": {
    "TITLE": "LeanLogic QA",
    "DASHBOARD": "仪表板",
    "MANAGE": {
      "TITLE": "管理人员",
      "MODEL": "型号",
      "UNIT": "单位",
      "PROCEDURE": "流程",
      "TOOL": "工具",
      "DEVICETYPE": "设备类型",
      "CHECKTYPE": "检查类型",
      "USERS": "多用户",
      "CHANGEPW": "更改密码",
      "CONFIGTABLE": "Configuration table",
      "NOTICES": "问题报告",
      "SETTINGS": "Settings",
      "MEASUREMENTERRORCATEGORIES": "Measurement error categories",
      "MEASUREMENTERRORCATEGORIESREPORT": "Report Measurement errors"
    },
    "LOGOUT": "注销",
    "EDITDTYPE": {
      "TITLE": "@:FRAME.MANAGE.DEVICETYPE",
      "TEXT": "增加, 编辑和禁用设备类型"
    },
    "EDITCTYPE": {
      "TITLE": "@:FRAME.MANAGE.CHECKTYPE",
      "TEXT": "增加, 编辑和禁用检查类型"
    },
    "CHPWD": {
      "TITLE": "@:FRAME.MANAGE.CHANGEPW",
      "PW1": "输入你的新密码",
      "PW2": "重复密码",
      "NOMATCH": "密码不符-操作取消",
      "TOOSHORT": "密码太短-至少需3个字符长",
      "OK": "密码已修改"
    },
    "DASHBOARDS": "控制面板",
    "GSEARCH": "搜索"
  },
  "DASHBOARD": {
    "NEWBLOCK": {
      "TITLE": "新破折号",
      "TEXT": "选择新破折号下面的内容"
    },
    "WELCOME": "欢迎, {{ name }}!",
    "VERSION": "版本 {{ version }}",
    "ADDDASH": {
      "BUTTON": "增加破折号"
    },
    "TYPEA": {
      "TITLE": "未完成的检查指定到我",
      "LINET": "{{ checktype.title.cn }} - 单位 <b>{{ unit.code }}</b> ({{ unit.customer }})",
      "LINEB": "型号 <b>{{ model.code }}: {{ model.title.cn }}</b>, 计划于 <b>{{ scheduled }}</b>, 交付 <b>{{ dueby }}</b>"
    },
    "TYPEB": {
      "TITLE": "未完成的检查也许将由我处理",
      "LINET": "@:DASHBOARD.TYPEA.LINET",
      "LINEB": "@:DASHBOARD.TYPEA.LINEB"
    },
    "TYPEC": {
      "TITLE": "复审已完成的检查",
      "LINET": "@:DASHBOARD.TYPEA.LINET",
      "LINEB": "@:DASHBOARD.TYPEA.LINEB"
    },
    "TYPED": {
      "TITLE": "单位缺少任何检查",
      "LINET": "单元 <b>{{ code }}</b> ({{ customer }})",
      "LINEB": "型号 <b>{{ model.code }}: {{ model.title.cn }}</b>"
    },
    "TYPEE": {
      "TITLE": "当前处理检查",
      "LINET": "@:DASHBOARD.TYPEA.LINET",
      "LINEB": "@:DASHBOARD.TYPEA.LINEB"
    },
    "TYPEF": {
      "TITLE": "更改处于待定状态的程序和型号",
      "LINEPROC": "程序 <b>{{ code }}</b>: {{ title.cn }}",
      "LINEMOD": "型号 <b>{{ code }}</b>: {{ title.cn }}"
    },
    "TOOLTIP": {
      "PREVDB": "前一个控制面板（点击并按shift键可实现当前和前一个控制面板的切换）",
      "NEXTDB": "下一个控制面板（点击并按shift键可实现当前和下一个控制面板的切换）",
      "DELDB": "移除控制面板",
      "EDITDB": "编辑控制面板名称",
      "ADDDB": "新控制面板"
    },
    "ADDDB": {
      "TITLE": "新增控制面板",
      "TEXT": "请为新控制面板输入一个名称。名称长度不少于3个字符。"
    },
    "EDITDBNAME": {
      "TITLE": "编辑控制面板名称",
      "TEXT": "请为新控制面板输入一个名称。名称长度不少于3个字符。"
    },
    "EDITBLKNAME": {
      "TITLE": "编辑控制面板块名",
      "TEXT": "请为块输入一个新名称。名称长度不少于3个字符。"
    },
    "DELETEDB": {
      "TITLE": "确定？",
      "TEXT": "确定删除控制面板？"
    },
    "FILTERS": {
      "TITLE": "编辑块波滤器",
      "TEXT": "请选择一个动作",
      "ACTENABLE": "启用&编辑",
      "ACTEDIT": "编辑",
      "ACTDISABLE": "禁用",
      "TYPES": {
        "MODELSEL": {
          "NAME": "型号选择过滤器",
          "TEXT": "请选择要在该块中使用的型号"
        }
      }
    },
    "TYPE": {
      "UCAM": {
        "TITLE": "未完成的验盘分配给我",
        "LINET": "单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}",
        "LINEB": " scheduled on <b>{{ scheduled }}</b>",
        "LINEBNS": "型号<b>{{ model.code }}: {{ model.title.en }}</b>"
      },
      "UCMP": {
        "TITLE": "未完成的验盘可能将由我处理",
        "LINET": "单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}",
        "LINEB": " scheduled on <b>{{ scheduled }}</b>",
        "LINEBNS": "型号<b>{{ model.code }}: {{ model.title.en }}</b>"
      },
      "FCFR": {
        "TITLE": "已完成的验盘供检查",
        "LINET": "单元<b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}",
        "LINEB": " scheduled on <b>{{ scheduled }}</b>",
        "LINEBNS": "型号<b>{{ model.code }}: {{ model.title.en }}</b>"
      },
      "UWAC": {
        "TITLE": "Units without any set up checks",
        "LINET": "单元<b>{{ code }}</b> ({{ customer }})",
        "LINEB": "型号 <b>{{ model.code }}: {{ model.title.en }}</b>"
      },
      "CPCH": {
        "TITLE": "正在执行的验盘",
        "LINET": "单元 <b>{{ unit.code }}</b> ({{ unit.customer }}) - {{ checktype.title.en }}",
        "LINEB": " scheduled on <b>{{ scheduled }}</b>",
        "LINEBNS": "型号<b>{{ model.code }}: {{ model.title.en }}</b>"
      },
      "PMPC": {
        "TITLE": "更改处于待定状态的程序和型号",
        "LINEPROC": "程序<b>{{ code }}</b>: {{ title.en }}",
        "LINEMOD": "型号<b>{{ code }}</b>: {{ title.en }}"
      }
    }
  },
  "CHECK": {
    "INPUT": {
      "TITLE": {
        "EDIT": "处理中 {{mname_en}}"
      }
    },
    "OVERVIEW": {
      "ACTION": {
        "SCHEDULE": {
          "TITLE": "进度检查",
          "TEXTSCHED": "选择计划日期 (最早开始时间)",
          "TEXTDUE": "选择到期日(最迟完成时间)",
          "TEXTASSIGN": "选择作业模式",
          "TEXTASSIGNTO": "选择群组或用户分配检查",
          "TEXTADDASSIGN": "选择群组或用户添加任务目标"
        },
        "CHANGEASS": {
          "TITLE": "更改任务",
          "TEXT": "为此区块选择新任务:"
        }
      }
    },
    "ALERT": {
      "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
      "CANCEL": "Please confirm by typing in the check number.",
      "WRONGNUMBER": "Wrong number",
      "WRONGDELETEINPUT": "Your input does not match the number. The check has not been cancelled.",
      "DELETEASS": {
        "TITLE": "你真的要删除代理人 {{ name }}?",
        "TEXT": "代理人将所有任务重置"
      },
      "DELETE": {
        "TITLE": "Please confirm by typing in the check number.",
        "WRONGDELETEINPUT": "Your input does not match the number. The check has not been deleted.",
        "TEXT": "@:UNIT.ALERT.DELETECHECK.TEXT"
      },
      "SCHED": {
        "ASSFREE": "自由任务",
        "ASSFULL": "预先完整检查",
        "ASSDETAILED": "预先指定细节",
        "ASSPREALLOCFREE": " free assignment for remaining parts\"",
        "ASSPREALLOCDETAILED": " detailed assignment for remaining parts\""
      },
      "REASSIGN": {
        "TITLE": "你真的要停止工序及重新分配工作流程?",
        "TEXT": "指定用户将无法检查这次工作在一会儿后停止"
      },
      "REGISTER": {
        "TITLE": "注册",
        "TEXT": "选择你要注册的任务:"
      },
      "UNREGISTER": {
        "TITLE": "注销注册",
        "TEXT": "选择你要注销注册的任务:"
      },
      "STEPINPROC": "{{ nums }} 步骤(s)/{{ nump }} 流程(s)",
      "REOPEN": {
        "TITLE": "重新开始",
        "TEXT": "选择你要未被授权的任务一会儿重新开放:"
      },
      "SCHEDTT": {
        "ASSFREE": "任何用户均可自由注册检查",
        "ASSFULL": "整个检查被分配给单个用户或组",
        "ASSDETAILED": "各个区域可以被分配给一个用户或组",
        "ASSPREALLOCFREE": " the remaining parts of the check are available to all groups or users\"",
        "ASSPREALLOCDETAILED": " the remaining parts of the check are not assigned and must be assigned manually\""
      },
      "COPY": {
        "TITLE": "创建副本",
        "TEXT": "请选择程序，这些程序将被复制到包括所有测量的新的检查:",
        "NEWVERS": {
          "TITLE": "更新?",
          "TEXT": "有此检查的新版本，您可以选择是否更新副本。请注意，只有在不同版本中未做更改的程序会被复制（不论您在之前对话中选择什么样的程序）在工作程序规则更改的情况下，复制可能出错.",
          "NOCHANGE": "保留现有版本"
        }
      },
      "SELPROC": {
        "TITLE": "程序选择",
        "TEXTSING": "请选择您要注册区块的一个程序:",
        "TEXTMULT": "请选择您要注册区块的多个程序:"
      },
      "REGMODE": {
        "TITLE": "注册方式",
        "TEXT": "请选择自注册的方式:",
        "COMPLETE": "只有完整区块",
        "MAYPART": "可部分注册",
        "MUSTPART": "仅限单一程序注册"
      },
      "REGMODETT": {
        "COMPLETE": "自注册只能在完整区块进行",
        "MAYPART": "自注册时，区块程序可以被单独选择（如果区块存在多重程序的话）",
        "MUSTPART": "自注册时，一次只有一个单一程序被选择和注册（如果区块存在多重程序的话）"
      },
      "TAKEOVER": {
        "INFO": "兼并!",
        "CONFIRM": "您真的要从之前的主人处兼并此任务吗？"
      }
    },
    //<<<<<<< HEAD
    "MODIFY": {
      "COMMENT": {
        "TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE",
        "TEXT": "增加, 编辑或删除该检查的评论"
      }
    },
    "MAKEPDF": {
      "TTL": "创建PDF(便携文档格式)报告",
      "TEXT": "选择所有可选的元素要包含在PDF(便携文档格式):",
      "OPTV": "流程版本",
      "OPTADM": "代理人和其他用户信息",
      "OPTCOMM": "评论测量",
      "OPTRAW": "矩阵计算的原始值",
      "OPTTOOL": "用于测量工具",
      "OPTRULE": "路径成功标准",
      "TEXT2": "请选择报告类型",
      "CUSTOMER": {
        "TEXT": "客户报告",
        "TTIP": "标记为“实习”的路径将会忽略掉；在警告情形下，会由一个替代结果字符串表示"
      },
      "INTERNAL": {
        "TEXT": "内部报告",
        "TTIP": "包括所有路径"
      }
    },
    "MAKEFILE": {
      "TTL": "输出报告数据",
      "TEXT": "选择输出的类型在这份报告:",
      "CSV": "逗号分隔值",
      "JSON": "Java描述语言输出符号",
      "XML": "可扩展的标记语言"
    },
    "SELTOOL": {
      "TITLE": "选择工具单位",
      "MESSAGE": "选择您使用此步骤的工具",
      "NOTOOLAVAILABLE": "There are no tools with active units available!"
    }
  },
  "MEASUREMENT": {
    "INPUT": {
      "COMMENT": {
        "TITLE": "编辑意见",
        "TEXT": "增加, 编辑或删除该单位意见"
      }
    }
  },
  "MSRSTAT": {
    "SELMSR": {
      "TTL": "@:MSRSTAT.SELM.TTL",
      "TEXT": "选择你要包含在报告的测量:",
      "RUTEXT": "输入你先前报告中已复制的ID字符串(PDF(便携文档格式)的最后一页):"
    },
    "SELCHK": {
      "TTL": "@:MSRSTAT.SELC.TTL",
      "S1TEXT": "选择您要添加检查的单位型号:",
      "S2TEXT": "输入一个或多个短字符串的一部分代码或你想要添加的检查单位客户名称匹配:",
      "S3TEXT": "选择以下一个或多个检查:",
      "ERROR": {
        "TTL": "检查选择问题…",
        "NORES": "没有检查发现,使用此程序(在任何版本)或所在单位匹配给定的搜索字符串",
        "TOOMANY": "太多的单位发现于给定的搜索字符串. 只有前25个已经过有效的检查测试. 请修改搜索,如果你想添加的检查是不上市.",
        "LIMIT": "你已经达到每30次检查报告的限制. 请先从列表中移除检查在添加新的检查"
      },
      "RUTEXT": "@:MSRSTAT.SELMSR.RUTEXT",
      "SUCCESS": {
        "TTL": "输出成品",
        "TEXT": "数据已经下载中. 单击\"是\"以关闭此向导, 或\"否\"如果你想继续使用这个报告生成器. 重新测量或稍候尝试选项,你现在可以复制下面的ID字符串:",
        "MSTR": "测量: {{cstr}}\"\"",
        "CSTR": "检查: {{cstr}}\"\""
      }
    },
    "TOOLTIP": {
      "REMCHK": "移除检查"
    },
    "TITLE": "统计报表生成器",
    "SUBT": "流程 {{ pcode }}",
    "SELM": {
      "TTL": "选择测量",
      "TXT": "选择此次测量将包含在报告中. 你可以选择没有限制的测量,每一次测量将生成一页. 你可以重新使用以前复制的选择或使用\"\"选择\"\"按钮打开选择向导.",
      "STATUS": "{{ msel }} 测量(s) 选定"
    },
    "SELC": {
      "TTL": "选择检查",
      "TXT": "选择此次检查将包含在报告中.",
      "STATUS": "{{ csel }} 检查 (s) 选定"
    },
    "CLIST": "检查 <b>{{ id }}</b> <span style='font-size:70%'>型号 <b>{{ mcode }}</b>, 单元 <b>{{ ucode }}</b>; 检查类型 <b>{{ ctcode }}</b></span>",
    "BUTTON": {
      "SEL": "@:UI.BUTTONS.MEDITOR.SELECT",
      "REUSE": "重新使用",
      "ADD": "@:TLISTEDIT.BUTTON.ADD",
      "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
      "GENERATE": "生成报告",
      "EXPORT": "输出数据"
    },
    "MEDIAMGR": {
      "IMAGE": {
        "ALERT": {
          "CONFHEADER": "你确定吗?",
          "DELETE": "你真的想从从这设置删除图像{{fname}}?"
        },
        "EDIT": {
          "CAPTION": {
            "TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE",
            "TEXT": "增加, 编辑或删除这图像的标题"
          }
        }
      },
      "MSRSTAT": {
        "SELMSR": {
          "TTL": "@:MSRSTAT.SELM.TTL",
          "TEXT": "选择你要包含在报告的测量:",
          "RUTEXT": "输入你先前报告中已复制的ID字符串(PDF(便携文档格式)的最后一页):"
        },
        "SELCHK": {
          "TTL": "@:MSRSTAT.SELC.TTL",
          "S1TEXT": "选择您要添加检查的单位型号:",
          "S2TEXT": "输入一个或多个短字符串的一部分代码或你想要添加的检查单位客户名称匹配:",
          "S3TEXT": "选择以下一个或多个检查:",
          "ERROR": {
            "TTL": "检查选择问题…",
            "NORES": "没有检查发现,使用此程序(在任何版本)或所在单位匹配给定的搜索字符串",
            "LIMIT": "你已经达到每30次检查报告的限制. 请先从列表中移除检查在添加新的检查"
          },
          "RUTEXT": "@:MSRSTAT.SELMSR.RUTEXT",
          "SUCCESS": {
            "TTL": "输出成品",
            "TEXT": "数据已经下载中. 单击\"是\"以关闭此向导, 或\"否\"如果你想继续使用这个报告生成器. 重新测量或稍候尝试选项,你现在可以复制下面的ID字符串:",
            "MSTR": "测量: {{cstr}}\"\"",
            "CSTR": "检查: {{cstr}}\"\""
          }
        },
        "TOOLTIP": {
          "REMCHK": "移除检查"
        },
        "TITLE": "统计报表生成器",
        "SUBT": "流程 {{ pcode }}",
        "SELM": {
          "TTL": "选择测量",
          "TXT": "选择此次测量将包含在报告中. 你可以选择没有限制的测量,每一次测量将生成一页. 你可以重新使用以前复制的选择或使用\"\"选择\"\"按钮打开选择向导.",
          "STATUS": "{{ msel }} 测量(s) 选定"
        },
        "SELC": {
          "TTL": "选择检查",
          "TXT": "选择此次检查将包含在报告中. 检查的数量限制为30. 您可以使用\"\"添加\"\"按钮打开检查查询向导,从列表中删除条目与红色垃圾桶按钮或重新使用先前复制的选择.",
          "STATUS": "{{ csel }} 检查 (s) 选定"
        },
        "CLIST": "检查 <b>{{ id }}</b> <span style='font-size:70%'>型号 <b>{{ mcode }}</b>, 单元 <b>{{ ucode }}</b>; 检查类型 <b>{{ ctcode }}</b></span>",
        "BUTTON": {
          "SEL": "@:UI.BUTTONS.MEDITOR.SELECT",
          "REUSE": "重新使用",
          "ADD": "@:TLISTEDIT.BUTTON.ADD",
          "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
          "GENERATE": "生成报告",
          "EXPORT": "输出数据"
        }
      }
    },
    "DOC": {
      "ALERT": {
        "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
        "DELETE": "你真的想从从这设置删除文档 {{fname}}?"
      },
      "EDIT": {
        "CAPTION": {
          "TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE",
          "TEXT": "增加, 编辑或删除这文件的标题"
        }
      }
    },
    "UPLOAD": {
      "FILTER": {
        "TITLE": "无法上传",
        "MESSAGE": "上传的档案 {{ filename }} 无法启动:",
        "UNSUPPORTED": "此类型档案 ({{ type }}) 为未知或不被支持此媒体管理",
        "TOOBIG": "此档案太大;此档案类型,最大的尺寸设置为 {{ max }} MB,此档案的尺寸为 {{ has }}MB."
      },
      "HINT": "终止媒体档案上传此窗格",
      "RESTR": "你可以上传图片 (png, jpeg, tiff),文字文档(pdf), packaged files (zip) 和影像数据 (mp4 only)",
      "STATUS": {
        "READY": "准备上传",
        "WAITING": "等待…",
        "UPLOADING": "上传: {{ prog }}%",
        "CANCELLED": "档案上传已取消",
        "FINISHED": "@:WFLOW.INTRO.DATE.FIN",
        "FAILED": "上传失败",
        "ERROR": "档案上传错误"
      },
      "STARTALL": "开始所有上传",
      "DELALL": "移除所有完成上传"
    },
    "TOOLTIP": {
      "IMGSIZE": "显示图像的大小",
      "IMGMETA": "显示图像的元数据",
      "EDITCAP": "设定或编辑标题",
      "REMIMG": "移除图像",
      "VIEWDOC": "查看文档",
      "DOCSIZE": "显示文档大小",
      "DOCMETA": "显示文档的元数据",
      "REMDOC": "移除文档",
      "UPLOAD": "开始上传",
      "REMUPL": "从列表中移除上传",
      "CNCLUPL": "取消上传",
      "DOWNLOAD": "下载原始文件"
    },
    "PDFPAGES": "{{ nump }} 页(s)",
    "VIDINFO": "@:WFLOW.INTRO.VIDINFO",
    "TITLE": "媒体管理",
    "TAB": {
      "IMAGES": "图像",
      "VIDEOS": "影像数据",
      "DOCS": "@:WFLOW.STEP.DOCS",
      "UPLOAD": "上传"
    }
  },
  "MODEL": {
    "EDITOR": {
      "TITLE": {
        "EDIT": "编辑 {{modelname_en}}",
        "NEW": "编辑新的流程"
      }
    },
    "ALERT": {
      "EDITVERSION": "你真的想要编辑此版本的型号 {{code}}?",
      "EDITVERSIONDETAIL": "你即将编辑此型号最终版本. 如果此版本被用在任何工作流程,检查已计划或完成将会改变.",
      "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
      "DELETE": "Please confirm the deletion of this model by typing in its code.",
      "WRONGCODE": "Wrong code",
      "WRONGDELETEINPUT": "Your input does not match the code. The model has not been deleted.",
      "DELETEVERSION": "你即将移除此型号最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏\"",
      "FINALIZE": "你真的想要完成型号 {{code}}?",
      "FINALIZEDETAIL": "当前的数据将最终形成一个新版本. 这个版本将被发表,随后可将其用于新的单位和检查.",
      "RESET": {
        "TITLE": "您真的要重置所有更改吗？",
        "TEXT": "至上次审定时对此对象所做的所有更改都将被永久删除."
      },
      "DENYHEADER": "@:TOOL.ALERT.DENY",
      "DENYMODIFICATION": "Only text may be altered in a finalized version."
    },
    "SWITCHV": {
      "TITLE": "选择版本",
      "MESSAGE": "从下面的列表中选择要切换到的版本:"
    },
    "VIEW": {
      "ADDPROC": {
        "TITLE": "增加流程",
        "TEXT": "选择要增加的所有流程,然后单击“增加”按钮"
      },
      "ADDCTYPE": {
        "TITLE": "增加检查类型",
        "TEXT": "选择要增加的所有检查类型,然后单击“增加”按钮"
      },
      "ADDCONFIGTABLE": {
        "TITLE": "Relate configuration table",
        "TEXT": "Please choose a relation table to be related to the current model."
      },
      "TOOLTIP": {
        "ACTREORD": "激活重新排序按钮",
        "ADDPROC": "增加有用的流程",
        "UPDPROC": "更新流程版本",
        "REORDER": "拖放重新排序",
        "GOTOPROC": "转到流程",
        "DELPROC": "移除流程",
        "ADDCTYPE": "增加有用的检查类型",
        "DELCTYPE": "移除检查类型",
        "SWITCHVER": "切换版本",
        "TESTVER": "测试版本",
        "CHANGELOG": "查看更改日志",
        "RESET": "重置所有更改至上次审定版本",
        "PREALLOC0": "预先定义任务（目前无定义）",
        "PREALLOC1": "预先定义任务（目前所有验盘类型确定）",
        "PREALLOC2": "预先定义任务（目前部分验盘类型确定）",
        "PREALLOC3": "预先定义任务（发现有缺陷的定义）",
        "ADDCONFIGTABLE": "Relate configuration table",
        "DELCONFIGTABLE": "Delete relation to table"
      },
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "DTYPE": "@:WFLOWEDIT.OPT1.DT",
      "DESC": "@:TLISTEDIT.DESC",
      "MEDIA": "媒体",
      "IMREG": "{{ imgnum }} 图像(s) 已注册的",
      "DOCREG": "{{ docnum }}文档(s) 已注册的",
      "MEDMGR": "@:MEDIAMGR.TITLE",
      "BUTTON": {
        "EXPORT": "输出",
        "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE",
        "FIN": "最终的",
        "EDIT": "编辑",
        "UNIT": "转到单位",
        "CONFIGTABLE": "Goto configuration table"
      },
      "PROC": {
        "STEPNUM": "步骤: {{ stepnum }}",
        "DISCONTINUED": "停止！",
        "OLDVERS": "使用版本 {{ pvers }}"
      },
      "ACTPROC": "有用的流程",
      "ACTCTYPE": "有用的检查类型",
      "VERSIONTAG": "版本:",
      "UNITINFO": {
        "TTL": "@:FRAME.MANAGE.UNIT",
        "TXT": "<b>{{ ucnt }}单位(s) 已注册的:</b><br>{{ ocnt }} 开启, {{ ccnt }} 关闭, {{ dcnt }} 丢弃."
      },
      "CONFIGTABLE": {
        "TTL": "Configuration table",
        "NORELATION": "No configuration table related."
      },
      "PREALLOC": {
        "TITLE": "预先定义任务",
        "TEXT1": "请选择有效的预先定义任务的验盘类型",
        "TEXT2": "请选择分配给该程序的组或用户。“移除任务”可移除一个现存的定义（在组或用户选择上）",
        "ANY": "任何用户或组",
        "GROUP": "组"
      }
    },
    "EDIT": {
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "DTYPE": "@:WFLOWEDIT.OPT1.DT",
      "DESC": "@:TLISTEDIT.DESC",
      "BUTTON": {
        "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
        "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"
      }
    },
    "LIST": {
      "TOOLTIP": {
        "CLTF": "清除文字过滤",
        "SHOWDEL": "显示或隐藏删除的型号",
        "NEWMODEL": "新型号",
        "IMPORTMODEL": "输入型号",
        "GOTOUNITS": "切换到单位"
      },
      "TOPROW": "{{ dtypt }} ({{ dtypc }}), {{ unitnum }} 单位(s)"
    },
    "MODELS": "@:FRAME.MANAGE.MODEL",
    "TESTCHECK": {
      "TITLE": "测试型号",
      "TEXT": "请选择您想要用于测试此版本型号的检查类型:"
    }
  },
  "STEP": {
    "MODEL": {
      "MEDIAMANAGER": "型号 {{ mcode }}: {{ mtitle }}"
    },
    "EDITOR": {
      "TITLE": {
        "EDIT": "编辑 {{stepname_en}}",
        "NEW": "编辑新的步骤"
      }
    },
    "ALERT": {
      "EDITVERSION": "你真的想要编辑此版本的步骤 {{code}}?",
      "EDITVERSIONDETAIL": "你即将编辑此步骤的最终版本. 如果此版本被用在任何工作流程,检查已计划或完成将会改变.",
      "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
      "DELETE": "你真的想要删除步骤 {{scode}} 从流程 {{pcode}}?",
      "DELETECASCADING": "所有测量 ({{mcnt}}) 定义此步骤也将被删除!",
      "DELETEVERSION": "你即将编辑此步骤的最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏\"",
      "DENYHEADER": "@:TOOL.ALERT.DENY",
      "DENYMODIFICATION": "Only text may be altered in a finalized version."
    },
    "VIEW": {
      "MEDIAMANAGER": "流程 {{ pcode }}: {{ ptitle }}, 步骤 {{ scode }}: {{ stitle }}",
      "FLOWEDITOR": "步骤 {{ scode }}: {{ stitle }}",
      "TOOLTIP": {
        "ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD",
        "UPLMEAS": "上传测量",
        "CLNMEAS": "@:MEASURE.CLONE.TITLE",
        "NEWMEAS": "创建新测量",
        "REORDER": "@:MODEL.VIEW.TOOLTIP.REORDER",
        "EDITRULE": "开启工作流程规则编辑器",
        "EXPMEAS": "输出测量",
        "EDITMEAS": "编辑测量",
        "TESTMEAS": "测试测量",
        "REMMEAS": "移除测量"
      },
      "VERSION": "@:PROCEDURE.VIEW.VERS",
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "DESC": "@:TLISTEDIT.DESC",
      "MEDIA": "@:MODEL.VIEW.MEDIA",
      "IMREG": "@:MODEL.VIEW.IMREG",
      "DOCREG": "@:MODEL.VIEW.DOCREG",
      "BUTTON": {
        "MEDMGR": "@:MEDIAMGR.TITLE",
        "REDITOR": "@:PROCEDURE.VIEW.REDITOR",
        "EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT",
        "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE",
        "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"
      },
      "RULES": "@:PROCEDURE.VIEW.RULES",
      "MEASURES": "@:WFLOW.INTRO.MEASURES"
    },
    "CLONE": {
      "TITLE": "复制步骤",
      "PSELTEXT": "从此流程中选择复制步骤:",
      "TEXT": "从此步骤中选择复制:"
    },
    "EDIT": {
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "INSTRUCTION": "Instruction",
      "DESC": "@:TLISTEDIT.DESC",
      "BUTTON": {
        "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
        "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"
      },
      "TOOLTIP": {
        "INSTRUCTION": "A step with type 'instruction' can not hold any measures"
      }
    },
    "MATRIX": {
      "TITLE": "设置矩阵的注解",
      "HINT": "小标题字段的正上方为后来显示在左上角的该字段值. 另外,你可以把一个短文字放置在该字段值这将用作为 在实际的矩阵一个预留位置",
      "BUTTON": {
        "BACK": "返回"
      }
    },
    "LIST": {
      "TOOLTIP": {
        "GOTOPROC": "@:WFLOW.STEP.TOOLTIP.GOTOPROC",
        "UPLOAD": "@:PROCEDURE.VIEW.TOOLTIP.UPLSTEP",
        "CLONE": "@:STEP.CLONE.TITLE",
        "ADDSTEP": "@:PROCEDURE.VIEW.TOOLTIP.NEWSTEP",
        "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF"
      },
      "BTMROW": "测量: {{ mcnt }}, 图像: {{ icnt }}, 文档: {{ dcnt }}"
    },
    "STEP": "@:WFLOWEDIT.OPT1.S",
    "STEPS": "@:WFLOW.INTRO.STEPS",
    "FORPROC": "程序 {{ proc }}"
  },
  "PROCEDURE": {
    "EXPORT": {
      "TITLE": "Export procedure",
      "MESSAGESYSTEM": "Choose if the procedure should be exported for the same or for another system.",
      "MESSAGECONTENTS": "Choose which contents should be exported.",
      "OPTIONS": {
        "SAMESYSTEM": "For the same system",
        "OTHERSYSTEM": "For another system",
        "FILES": "Images and Documents",
        "TOOLS": "Tools",
        "RULES": "Workflow rules",
        "ENFORCEMENTS": "Workflow locks"
      }
    },
    "EDITOR": {
      "TITLE": {
        "EDIT": "编辑 {{procname_en}}",
        "NEW": "@:MODEL.EDITOR.TITLE.NEW"
      }
    },
    "LIST": {
      "GOTOPROC": {
        "TITLE": "查看使用情况",
        "TEXT": "以下型号选定使用流程. 你可以去选择一个去转到相应的管理页面."
      },
      "TOOLTIP": {
        "IMPORT": "输入流程",
        "CLONE": "@:PROCEDURE.CLONE.TITLE",
        "ADD": "创建新流程",
        "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF",
        "SHOWDEL": "显示或隐藏删除的流程",
        "GOTOMODEL": "使用此流程显示和转到可选择地型号",
        "PROCFILTER": "Filter by procedure tags"
      },
      "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE",
      "BTMROW": "步骤: {{ stpcnt }}, 经由 : {{ modcnt }} 型号(s)",
      "BTMROWUPD": ",可更新的: {{ updcnt }}"
    },
    "ALERT": {
      "EDITVERSION": "你真的想要编辑此版本的流程 {{code}}?",
      "EDITVERSIONDETAIL": "你即将编辑此流程的最终版本. 如果此版本被用在任何工作流程,检查已计划或完成将会改变.",
      "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
      "DELETE": "你真的想要删除此版本的流程 {{code}}?",
      "DELETECASCADING": "所有步骤 ({{scnt}}) 和他们为此流程测量定义也将被删除!",
      "DELETEVERSION": "你即将移除此流程的最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏",
      "FINALIZE": {
        "TITLE": "你真的想要完成流程 {{code}}?",
        "TEXT": "当前的数据将最终形成一个新版本. 这个版本将被发表,随后可将其用于新的单位和检查. 确保更新并且最终所有型号用此流程来使用这个新的版本."
      },
      "FULLUPDATE": {
        "TITLE": "你真的想更新搁置的型号?",
        "TEXT": "使用旧版本的这个流程的型号将被更新; 这意味着,每个模型将自动创建为新的版本. 其它搁置变更,在此模型的编辑阶段没有做最后确定."
      },
      "RESET": {
        "TITLE": "您真的要重置所有更改吗？",
        "TEXT": "至上次审定时对此对象所做的所有更改都将被永久删除."
      },
      "DENYHEADER": "@:TOOL.ALERT.DENY",
      "DENYMODIFICATION": "Only text may be altered in a finalized version."
    },
    "SWITCHV": {
      "TITLE": "@:MODEL.SWITCHV.TITLE",
      "MESSAGE": "@:MODEL.SWITCHV.MESSAGE"
    },
    "VIEW": {
      "FLOWEDITOR": "流程 {{ pcode }}: {{ ptitle }}",
      "TOOLTIP": {
        "STAT": "生成统计数据报告",
        "CHVERS": "@:MODEL.VIEW.TOOLTIP.SWITCHVER",
        "UPDMOD": "最新版本的更新型号",
        "GOTOSTEP": "@:WFLOW.INTRO.TOOLTIP.GOTOSTEP",
        "ACTREORD": "@:MODEL.VIEW.TOOLTIP.ACTREORD",
        "UPLSTEP": "上传步骤",
        "CLNSTEP": "@:STEP.CLONE.TITLE",
        "NEWSTEP": "创建新步骤",
        "REORD": "@:MODEL.VIEW.TOOLTIP.REORDER",
        "EXPSTEP": "输出步骤",
        "REMSTEP": "移除步骤",
        "TESTVER": "测试版本",
        "CHANGELOG": "查看更改日志",
        "RESET": "重置所有更改至上次审定版本",
        "ENFORCETOP": "首个进入点可能不是锁定的工作流",
        "ENFORCEINSTR": "An instruction may not be workflow locked",
        "ENFORCE0": "无锁定工作流",
        "ENFORCE1": "锁定：最后一个进入点完成后方可执行",
        "ENFORCE2": "全锁：所有先前的进入点完成后方可执行"
      },
      "VERS": "版本",
      "UPDATEINFO": "有 {{ updcnt }} 型号(s) 使用此流程的旧版本.",
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "PROCTIME": "Processing Time",
      "HOURS": "hours",
      "MINUTES": "minutes",
      "DESC": "@:TLISTEDIT.DESC",
      "RULES": "工作流程规则",
      "REDITOR": "规则编辑器",
      "BUTTON": {
        "EXPORT": "@:MODEL.VIEW.BUTTON.EXPORT",
        "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE",
        "FIN": "@:MODEL.VIEW.BUTTON.FIN",
        "EDIT": "@:MODEL.VIEW.BUTTON.EDIT",
        "STATISTIC": "统计数据"
      },
      "STEPS": "@:WFLOW.INTRO.STEPS",
      "MEASURES": "测量: {{ msrcnt }}"
    },
    "CLONE": {
      "TITLE": "复制流程",
      "TEXT": "选择复制一个或多个流程:"
    },
    "EDIT": {
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "DESC": "@:TLISTEDIT.DESC",
      "PROCTIME": "Processing Time",
      "HOURS": "hours",
      "MINUTES": "minutes",
      "WRONGPROCTIME": "Wrong processing time",
      "WRONGPROCTIMETXT": "The processing time has been entered incorrectly.",
      "BUTTON": {
        "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
        "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"
      }
    },
    "TESTCHECK": {
      "TITLE": "测试程序",
      "TEXT": "请选择您想要用于测试此版本程序的检查类型:"
    },
    "FILTER": {
      "TITLE": "Global filter (procedure tags)",
      "PLEASECHOOSE": "Please choose",
      "CANCEL": "Clear filter",
      "APPLY": "Apply filter"
    }
  },
  "MEASURE": {
    "EDITOR": {
      "TITLE": {
        "EDIT": "编辑 {{mname_en}}",
        "NEW": "编辑新的测量"
      },
      "NOTOOL": "没有使用的工具"
    },
    "VIEW": {
      "FLOWEDITOR": "测量 {{ mcode }}: {{ mtitle }}"
    },
    "ALERT": {
      "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
      "DELETE": "你真的想要删除测量 {{mcode}} 来自步骤 {{scode}} 流程中 {{pcode}}?",
      "DELETEVERSION": "你即将移除此流程的最终版本. 如果此版本被用在任何工作流程,这个过程将失败;同时这动作可能产生不必要的结果和数据损坏.",
      "DENYHEADER": "@:TOOL.ALERT.DENY",
      "DENYMODIFICATION": "Only text may be altered in a finalized version."
    },
    "CLONE": {
      "TITLE": "复制测量",
      "PSELTEXT": "从此流程中选择复制测量:",
      "TEXT": "从此测量中选择复制:"
    },
    "EDIT": {
      "COMPARATOR": "比较仪",
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "HINTS": "提示",
      "TTYPE": "工具类型",
      "MTYPE": "测量类型",
      "MANDATORY": "强制型",
      "YES": "@:WFLOW.INPUT.YES",
      "NO": "@:WFLOW.INPUT.NO",
      "TARGET": "目标值",
      "UNIT": "@:WFLOW.INTRO.UNIT",
      "THOLD": "@:WFLOW.INPUT.THRESH",
      "MIN": "最小量",
      "MAX": "最大量",
      "MINLEN": "最小量文字长度",
      "REGEXP": "正规表达式",
      "EXP": "预期的",
      "ANY": "任何的",
      "MATRIX": "矩阵",
      "BUTTON": {
        "SETUP": "设置标题",
        "TEST": "存储和测试测量",
        "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
        "CLOSE": "存储和关闭"
      },
      "NUMCOL": "列数",
      "NUMROW": "行数",
      "FORMULA": "公式",
      "FLOATFRM": {
        "TEXT": "数字格式",
        "STD": "标准",
        "INT": "没有小数位",
        "1DIGIT": "一个小数位",
        "2DIGIT": "两个小数位",
        "3DIGIT": "三个小数位",
        "4DIGIT": "四个小数位",
        "6DIGIT": "六个小数位"
      },
      "COMPLCODE": "分组",
      "INTERNAL": "内部的",
      "INTERNALEXTERNAL": "Internal & External",
      "CHOICE": "Number of choices",
      "CHOICEVAL": "Value {{ val }}"
    },
    "TYPES": {
      "THRESHOLD": "@:WFLOW.INPUT.THRESH",
      "ABSOLUTE": "绝对值",
      "ABSOLUTERNG": "绝对值范围",
      "TEXT": "随机文字",
      "REGEXP": "选中的文本模式",
      "BOOL": "标记(yes/no)",
      "RESCHECK": "固定的检查结果",
      "THRESHOLDMATRIX": "临界值,通过矩阵计算输入误差",
      "ABSOLUTEMATRIX": "绝对值,通过矩阵计算输入",
      "ABSOLUTERNGMATRIX": "绝对值范围,通过矩阵计算输入",
      "STATISTICAL": "统计值",
      "STATISTICALMATRIX": "统计值, 通过矩阵计算输入",
      "TIMERSTART": "计时 (开始时间)",
      "TIMERSTOP": "计时 (停止时间)",
      "TIMERSTOPQ": "检查后的计时器（终止时间）",
      "TIMERSTOPC": "检查未纠正的时间测定",
      "CHOICELIST": "Choice list"
    }
  },
  "MEASUREMENTERRORCATEGORYEDITOR": {
    "TITLE": "Editing of a finished measurement",
    "TEXT": "This measurement has already been accomplished. Please choose one of the reasons for the editing below to continue."
  },
  "TOOL": {
    "EDITOR": {
      "TITLE": {
        "EDIT": "编辑 {{ tool }}",
        "NEW": "编辑新的工具类型"
      }
    },
    "NEWUNIT": {
      "TITLE": "创建新单位",
      "TEXT": "在下面的文字段输入序号或任何其他识别码:"
    },
    "EDITUNIT": {
      "TITLE": "编辑单位",
      "TEXT": "@:TOOL.NEWUNIT.TEXT"
    },
    "ALERT": {
      "DELETEUNIT": "你真的想要删除工具单位 {{code}}?",
      "NODELETEUNIT": "您不能删除此工具类型 {{code}}!",
      "DELETEUNITCASCADING": "它已经应用于一个或多个测量. 请将它设置为'禁用'.",
      "DELETELASTUNITCASCADING": "The tooltype to this unit is already used in some procedures; at least one unit must be available. Please set it to 'disabled' instead.",
      "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
      "DENY": "动作不可能的",
      "DELETE": "你真的想要删除工具类型 {{code}}?",
      "NODELETE": "@:TOOL.ALERT.NODELETEUNIT",
      "DELETECASCADING": "@:TOOL.ALERT.DELETEUNITCASCADING"
    },
    "MEDIAMANAGER": "工具类型 {{ ttcode }}: {{ tttitle }}",
    "EDIT": {
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "HINTS": "@:MEASURE.EDIT.HINTS",
      "DIS": {
        "TITLE": "@:TLISTEDIT.DISSET.TITLE",
        "TRUE": "此工具类型已被删除且不得再使用",
        "FALSE": "此工具类型已启动"
      },
      "BUTTON": {
        "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
        "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"
      }
    },
    "LIST": {
      "TOOLTIP": {
        "NEW": "创建新的工具类型",
        "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF",
        "SHOWDEL": "显示停用的工具类型"
      },
      "TTYPES": "工具类型",
      "BTMROW": "测量: {{ mcnt }}, 工具单位: {{ tcnt }}"
    },
    "VIEW": {
      "TOOLTIP": {
        "NEWTUNIT": "创建新的工具单位",
        "EDITTUNIT": "编辑工具单位",
        "REENABLE": "重新启用工具单位",
        "DISABLE": "禁用工具单位",
        "REMTUNIT": "移除工具单位",
        "SHOWDEL": "显示停用的工具单位",
        "SETCOMMENT": "该单元的注释",
        "REPORT": "生成工具单元使用报告"
      },
      "DISABLED": "@:TOOL.EDIT.DIS.TRUE",
      "CODE": "@:TLISTEDIT.CODE",
      "TITLE": "@:TLISTEDIT.TITLE",
      "DESCRIPTION": "@:TLISTEDIT.DESC",
      "MEDIA": "@:MODEL.VIEW.MEDIA",
      "IMGREG": "{{ imgcnt }}图像(s) 已注册的",
      "DOCREG": "{{ doccnt }} 文档(s) 已注册的",
      "BUTTON": {
        "MEDMGR": "@:MEDIAMGR.TITLE",
        "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE",
        "EDIT": "@:MODEL.VIEW.BUTTON.EDIT"
      },
      "MEDINFO": "注解: 只有第一个图像/第一个文档是可存取的",
      "TUNITS": "工具单位",
      "TOOLDEL": "刪除",
      "TOOLUNITCNT": "用于 {{ ucnt }}測量值"
    },
    "COMMENTUNIT": {
      "TITLE": "可做注释",
      "TEXT": " edit or delete the comment for this unit:\""
    },
    "REPORT": {
      "TITLE": "生成使用报告",
      "DATE1": " then click 'Save' without choosing a date.\"",
      "DATE2": " then click 'Save' without choosing a date.\"",
      "SORT": {
        "TEXT": "请选择：",
        "MODEL": " unit",
        "TIME": "按照时间标记分类"
      }
    }
  },
  "CHECKTYPE": {
    "MEDIAMANAGER": "设备类型{{ code }}: {{ title }}"
  },
  "DEVICETYPE": {
    "MEDIAMANAGER": "检查类型 {{ code }}: {{ title }}"
  },
  "UNIT": {
    "EDITOR": {
      "TITLE": {
        "EDIT": "编辑 {{ unit }}",
        "NEW": "编辑新单位"
      }
    },
    "ARCHIVE": {
      "TTL": "搜索存档",
      "TXT": "输入一个或多个短字符串的一部分代码或你在寻找客户的单位(s):",
      "MANYRES": {
        "TTL": "太多的结果",
        "TXT": "此搜寻返回超过100次项目,只有显示前100. 请修改您的搜索."
      }
    },
    "NEWCHECK": {
      "TITLE": "创建新检查",
      "TEXT": "选择下面的检查键入框中:",
      "CONFIGTABLE": {
        "NEW": "Adopt template configuration table from model",
        "TITLE": "Choose a template configuration table",
        "TEXT": "Please choose if you want to adopt the template configuration table from the model or from an earlier check:",
        "CHECK": "Check"
      }
    },
    "ALERT": {
      "DELETECHECK": {
        "TITLE": "你真的想要删除这次检查?",
        "TEXT": "所有测量和收集的数据也将被删除！"
      },
      "CONFHEADER": "@:MEDIAMGR.IMAGE.ALERT.CONFHEADER",
      "DELETE": "Please confirm the deletion of this unit by typing in its code.",
      "WRONGCODE": "Wrong code",
      "WRONGDELETEINPUT": "Your input does not match the code. The unit has not been deleted.",
      "DELETECASCADING": "所有检查 ({{ccnt}}) 这个单位也将被删除！您可能希望将此单元的状态设置为'丢弃'.",
      "STATUSCHG": {
        "TTL": "更改单位状态",
        "TXT": "选择这个单位的状态:"
      },
      "UNARCHIVE": {
        "TXT": "真的解档单位?"
      }
    },
    "MODIFY": {
      "DATE": {
        "TITLE": "设置新日期",
        "COMMISSIONED": "选择已调试的单位日期",
        "FINISHED": "选择已完成的单位日期",
        "DELIVERED": "选择已交付给客户的单位日期",
        "APPROVED": "选择客户已核准的单位日期"
      },
      "COMMENT": {
        "TITLE": "@:MEASUREMENT.INPUT.COMMENT.TITLE",
        "TEXT": "@:MEASUREMENT.INPUT.COMMENT.TEXT"
      }
    },
    "EDIT": {
      "CODE": "@:TLISTEDIT.CODE",
      "CUST": "客户",
      "MODEL": "@:WFLOWEDIT.OPT1.MD",
      "BUTTON": {
        "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
        "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"
      }
    },
    "LIST": {
      "TOOLTIP": {
        "NEW": "@:TOOL.NEWUNIT.TITLE",
        "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL",
        "ARCHMODE": "进入或离开存档模式",
        "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF",
        "LOCKMODE": "显示或隐藏关闭的单位"
      },
      "UNITS": "@:FRAME.MANAGE.UNIT"
    },
    "FORMODEL": "型号 {{ mdl }}",
    "INARCHIVE": "存档,匹配 '{{ pat }}'",
    "VIEW": {
      "TOOLTIP": {
        "UNITSTAT": "设置单位状态",
        "UNARCH": "解档(reactivate)单位",
        "GOTOMOD": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL",
        "EDITDATE": "设置或更改日期",
        "EDITCOMM": "增加或更改评论",
        "ADDCHK": "@:UNIT.NEWCHECK.TITLE",
        "DELCHK": "@:MSRSTAT.TOOLTIP.REMCHK",
        "GOTOCHK": "转到检查"
      },
      "MODEL": "@:WFLOWEDIT.OPT1.MD",
      "CODE": "@:TLISTEDIT.CODE",
      "CUST": "@:UNIT.EDIT.CUST",
      "CHECKS": "检查",
      "CHK": "@:WFLOW.INTRO.MEDIAMANAGER",
      "DATES": "日期",
      "COMM": {
        "ON": "调试于",
        "NOT": "尚未调试"
      },
      "FIN": {
        "ON": "结束于",
        "NOT": "尚未结束"
      },
      "DEL": {
        "ON": "交货于",
        "NOT": "尚未交货"
      },
      "APPR": {
        "ON": "核准于",
        "NOT": "尚未核准"
      },
      "COMMENT": "评论",
      "BUTTON": {
        "DELETE": "@:WFLOW.INTRO.BUTTON.DELETE",
        "EDIT": "@:MODEL.VIEW.BUTTON.EDIT",
        "CHSTATUS": "更改状态",
        "UNARCHIVE": "解档"
      },
      "CHECK": {
        "MODVERS": "使用型号版本",
        "FAIL": "检查失败!",
        "PASS": "检查通过!",
        "FAILCLOSED": "关闭, 检查失败",
        "PASSCLOSED": "关闭, 检查通过",
        "PASSCANCEL": "取消",
        "WARN": "验盘通过，带警告语！",
        "WARNCLOSED": "closed, check passed with warnings on"
      },
      "SCHED": {
        "NOT": "@:WFLOW.INTRO.DATE.NOSCHED",
        "ON": "计划于"
      },
      "DUE": {
        "ON": "截止日期",
        "NOT": "无截止日期"
      },
      "START": {
        "ON": "@:WFLOW.INTRO.DATE.START"
      },
      "STATUS": {
        "TTL": "@:WFLOW.INPUT.STATUS.TITLE",
        "OPEN": "开启",
        "CLOSED": "关闭",
        "DISCARDED": "丢弃",
        "ARCHIVED": "存档",
        "CLOSEDARCH": "关闭 (ARCHIVED)",
        "DISCARCH": "丢弃 (ARCHIVED)"
      }
    }
  },
  "USERS": {
    "ANYUSER": "任何用户",
    "ANYGROUP": "任何群组",
    "TOOLTIP": {
      "NEWUSER": "创建新用户",
      "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF",
      "HIDEGRP": "隐藏群组, 显示所有用户",
      "NEWGRP": "创建新群组",
      "SHOWGRP": "显示群组, 通过群组筛选用户",
      "DISUSER": "停用用户",
      "ENUSER": "启动用户",
      "EDITUNAM": "编辑用户名",
      "EDITURN": "编辑用户的真实姓名",
      "EDITUCOMM": "增加或编辑评论此用户",
      "REMGRP": "@:USERMGR.ACTION.DELETEGROUP.TITLE",
      "REACTGRP": "Enable group",
      "DISABLED": "Disabled",
      "SHOWDELGROUPS": "Show disabled groups",
      "CHGGRP": "将用户加入另一个群组",
      "EDITGNAME": "编辑群组名",
      "EDITGLVL": "编辑群组层级",
      "EDITGDESC": "编辑群组描述",
      "ADDGPRV": "增加群组权限",
      "REMGPRV": "移除群组权限",
      "ADDUPRV": "增加用户权限",
      "REMUPRV": "移除用户权限",
      "SHOWDEL": "显示停用的用户",
      "ADDGROUP": "添加一个或多个组到这个用户",
      "ADDUSER": "添加一个或多个用户到该组",
      "ADDFIRSTGROUP": "添加一个或多个组到这个用户",
      "ADDFIRSTUSER": "添加一个或多个用户到该组",
      "REMOVEGROUP": "从该用户中移除该组",
      "REMOVEUSER": "从该组移除该用户"
    },
    "USERS": "@:FRAME.MANAGE.USERS",
    "GROUPS": "群组",
    "USER": "@:MEDITOR.USER",
    "USERNAME": "@:LOGIN.USERNAME",
    "PASSWORD": {
      "TITLE": "@:LOGIN.PASSWORD",
      "SET": "密码设置",
      "NOTSET": "密码未设置"
    },
    "BUTTON": {
      "CHPASSW": "@:FRAME.MANAGE.CHANGEPW"
    },
    "REALNAME": "真实名",
    "COMMENT": "@:UNIT.VIEW.COMMENT",
    "GROUP": "@:MEDITOR.GROUP",
    "GNAME": "群组名",
    "LEVEL": "层级",
    "DESC": "@:TLISTEDIT.DESC",
    "PRIVILEGES": "权限",
    "PRIV": {
      "BYGROUP": "授予群组",
      "BYUSER": "授予个别地",
      "FORGROUP": "组广泛权限"
    },
    "DELASSIGN": "移除任务",
    "RANK": "等级",
    "USERGROUPS": "附属组",
    "NOGROUPS": "用户不从属于任何组",
    "GROUPUSERS": "成员",
    "NOUSERS": "该组没有任何附属用户",
    "ADDUSER": "添加用户",
    "ADDGROUP": "添加组"
  },
  "USERMGR": {
    "ACTION": {
      "ADDUSER": {
        "TITLE": "增加用户"
      },
      "EDITUSER": {
        "TITLE": "编辑用户"
      },
      "USER": {
        "USERNAME": "输入一个新的用户名",
        "REALNAME": "输入此用户真实名",
        "COMMENT": "编辑或增加一个评论此用户",
        "PASSWORD": "设置新的密码或腾让出来以清除密码",
        "GROUP": "选择一个新群组给此用户"
      },
      "ADDGROUP": {
        "TITLE": "增加群组"
      },
      "EDITGROUP": {
        "TITLE": "编辑群组"
      },
      "GROUP": {
        "NAME": "输入一个新的群组名",
        "LEVEL": "输入一个新的群组层级",
        "DESCRIPTION": "输入或增加描述给此群组"
      },
      "DELETEGROUP": {
        "TITLE": "Disable group",
        "TEXT": "Do you really want to dsiable the group? Some users in this group may be not be affiliated to any group afterwards."
      },
      "REACTIVATEGROUP": {
        "TITLE": "Enable group",
        "TEXT": "This group has been disabled, are you sure you want to enable it again?"
      },
      "ADDGRANT": {
        "TITLE": "增加权限",
        "TEXT": {
          "GROUP": "选择要添加到该群组此权限:",
          "USER": "选择要添加到该用户此权限:"
        }
      },
      "ADDGRPTOUSER": {
        "TITLE": "添加多个组",
        "TEXT": "请选择要添加到该用户的所有组"
      },
      "ADDUSERTOGRP": {
        "TITLE": "增加用户",
        "TEXT": "请选择要添加到该组的所有用户"
      }
    }
  },
  "WFLOW": {
    "INTRO": {
      "STATUS": {
        "INIT": "初始化",
        "SCHED": "计划于",
        "START": "开始",
        "FAIL": "失败",
        "PASS": "通过",
        "FAILC": "失败及关闭",
        "PASSC": "通过及关闭",
        "CANCEL": "取消",
        "WARN": "输出对象通过",
        "WARNC": "输出对象通过&关闭"
      },
      "MEDIAMANAGER": "检查",
      "FILEINFO": {
        "TITLE": "Upload information",
        "UPLOADDATE": "Date:",
        "UPLOADTIME": "Time:",
        "UPLOADEDBY": "By:"
      },
      "TOOLTIP": {
        "EXPPDF": "检查输出为PDF",
        "EXPFILE": "输出检查数据",
        "GOTOMODEL": "转到型号",
        "GOTOUNIT": "转到单位",
        "IMGLEFT": "上一个图像",
        "IMGRIGHT": "下一个图像",
        "VIEWDOC": "查看文档",
        "EDITCOMM": "@:MEASUREMENT.INPUT.COMMENT.TITLE",
        "VIEWIMG": "查看图像",
        "REMASS": "移除代理人",
        "ASSIGN": "指定到选定的项目",
        "HIGHLASS": "强调分配项目",
        "ADDASS": "增加代理人",
        "SELASS": "分配任务",
        "GOTOSTEP": "转到步骤",
        "CHANGEASSIGN": "更改任务",
        "CONFIGTABLE": "Open configuration table"
      },
      "MEDIAMGR": "增加, 删除或增加图像和文文件",
      "TOOEARLY": "在未来的检查计划; 它现在可能无法处理",
      "TOOLATE": "在过去此检查已到期;赶快!",
      "MODEL": "@:WFLOWEDIT.OPT1.MD",
      "UNIT": "单位",
      "PDFINFO": "{{ pnum }} 页面 (s)",
      "VIDINFO": "{{ width }} x {{ height }}, {{ fps }}英尺/秒, 运行时间 : {{ rtime }} 秒",
      "IMGINFO": "{{ 宽度}} x {{ 高度 }}",
      "DATE": {
        "SCHED": "计划于",
        "DUE": "交付",
        "NOSCHED": "计划尚未排定",
        "NODUE": "没有截止日期尚未确定",
        "START": "开始",
        "FIN": "已完成",
        "NOSTART": "还未开始",
        "NOFIN": "还没完工"
      },
      "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER",
      "NOCOMMENT": "检查尚未评论;单击此处增加评论",
      "STEPINPROC": "插入",
      "PROCS": "流程(s)",
      "NOASS": "尚未分配",
      "ADDASS": "增加分配",
      "PROCSUNASS": "流程(s) 未分配",
      "PROCEDURES": "@:FRAME.MANAGE.PROCEDURE",
      "STEPS": "步骤",
      "INSTRUCTIONS": "Instructions",
      "MEASURES": "测量",
      "STAT": {
        "TOTAL": "Total⌥",
        "PASSED": "已通过的",
        "UNFIN": "未完成的",
        "FAILED": "已失败的",
        "SKIPPED": "已跳过的"
      },
      "BUTTON": {
        "DELETE": "删除",
        "SCHEDULE": "计划",
        "RESCHEDULE": "重新计划",
        "START": "开始",
        "REASS": "停止及再分配",
        "UNREG": "@:CHECK.ALERT.UNREGISTER.TITLE",
        "REG": "@:CHECK.ALERT.REGISTER.TITLE",
        "CONTINUE": "继续",
        "COMMIT": "提交",
        "CLOSE": "@:WFLOW.INPUT.BUTTON.CLOSE",
        "REOPEN": "@:CHECK.ALERT.REOPEN.TITLE",
        "CANCEL": "取消检查",
        "COPY": "创建副本"
      },
      "FILTER": {
        "BUTTONS": {
          "COLLAPSE": "崩溃:",
          "PROC": "程序.",
          "STEP": "步骤",
          "MEAS": "测量.",
          "FILTER": "筛选",
          "FILTERPLUS": "筛选+"
        },
        "TITLE": "Filter",
        "DISABLE": "Disable filter",
        "FILTTITLE": "Apply text filter on:",
        "FILTCODE": "codes",
        "FILTTEXT": "element titles",
        "FILTPROC": "procedures",
        "FILTSTEP": "steps",
        "FILTMEAS": "measures",
        "FILTMARKELEMS": "Mark elements:",
        "FILTUSER": "Mark all/only elements assigned to the current user (will reset filters above):",
        "FILTUNFIN": "Mark all/only unfinished elements",
        "FILTFAIL": "Mark all/only failed elements",
        "FILTSUCC": "Mark all/only suceeded elements",
        "HIDEINSTR": "Hide instruction steps:",
        "HIDEOMITTEDORSKIPPED": "Hide omitted or skipped elements:",
        "TOOLTIP": {
          "VIEWFILT": "Show filter dialog",
          "PROC": "关闭所有，仅显示程序",
          "STEP": "展开步骤，关闭测量（默认）",
          "MEAS": "显示所有，展开所有",
          "FILTER": "显示所有通过筛选标记的项，关闭其他",
          "FILTERPLUS": "展开所有通过筛选标记的元素，关闭其他",
          "RESET": "重置所有筛选"
        }
      },
      "VERSION": "检查基于型号版本",
      "UNFINVERSION": "检查基于当前编辑尚未审定的型号版本",
      "TESTRUN": {
        "PROCEDURE": "为程序测试运行检查:",
        "MODEL": "为型号测试运行检查:"
      }
    },
    "STEP": {
      "IMAGE": {
        "NEXT": "Next",
        "PREVIOUS": "Previous"
      },
      "STATUS": {
        "TODO": "@:WFLOW.INTRO.STATUS.INIT",
        "PASS": "@:WFLOW.INTRO.STATUS.PASS",
        "FAIL": "@:WFLOW.INTRO.STATUS.FAIL",
        "OMIT": "忽略的",
        "SKIP": "已跳过的",
        "PASSNF": "通过",
        "FAILNF": "失败",
        "WARNNF": "（输出对象通过）"
      },
      "TOOLTIP": {
        "GOTOMODEL": "@:WFLOW.INTRO.TOOLTIP.GOTOMODEL",
        "GOTOUNIT": "@:WFLOW.INTRO.TOOLTIP.GOTOUNIT",
        "GOTOPROC": "转到流程",
        "VIEWIMG": "@:WFLOW.INTRO.TOOLTIP.VIEWIMG",
        "VIEWDOC": "@:WFLOW.INTRO.TOOLTIP.VIEWDOC"
      },
      "COMMITTER": "步骤通过提交:",
      "NOCOMMIT": "尚未通过提交",
      "MEASURER": "测量{{ code }}通过:",
      "NOMEASURE": "还没有完成",
      "PDFINFO": "@:WFLOW.INTRO.PDFINFO",
      "VIDINFO": "{{ width }} x {{ height }}, {{ fps }} 英尺/秒, {{ rtime }}秒",
      "MODEL": "给型号",
      "UNIT": "@:WFLOW.INTRO.UNIT",
      "PROCEDURE": "@:WFLOWEDIT.OPT1.P",
      "STEP": "@:WFLOWEDIT.OPT1.S",
      "BUTTON": {
        "BACK": "返回概况",
        "CONTINUE": "@:WFLOW.INTRO.BUTTON.CONTINUE",
        "FIN": "完成!",
        "REWIND": "上一步",
        "FORWARD": "Zum nächsten Schritt",
        "PROCALL": "处理全部"
      },
      "MEASURES": "@:WFLOW.INTRO.MEASURES",
      "MEASUREINP": "输入结果…",
      "TOOLS": "@:FRAME.MANAGE.TOOL",
      "TOOLCHOOSE": "选择工具…",
      "DOCS": "文檔",
      "INPLOCKED": "封闭",
      "VIEW": {
        "TIMER": {
          "TITLE": "Processing time",
          "FINISHED": "The processing time for this procedure is now over!"
        },
        "INFO": "描述",
        "IMAGES": "图片 ({{ numi }})",
        "DOCS": "文件 ({{ numd }})"
      }
    },
    "INPUT": {
      "VALUECMP1": "值必须小于{{ val }}",
      "VALUECMP2": "值必须小于或等于{{ val }}",
      "VALUECMP3": "值必须等于{{ val }}",
      "VALUECMP4": "值必须大于或等于{{ val }}",
      "VALUECMP5": "值必须大于{{ val }}",
      "CHECKCMP1": "检查如果你的测量值为小于 {{ val }}, 然后于是单击'是'或'不是'",
      "CHECKCMP2": "检查如果你的测量值为小于或等于 {{ val }}, 然后于是单击'是'或'不是'",
      "CHECKCMP3": "检查如果你的测量值为等于 {{ val }}, 然后于是单击'是'或'不是'",
      "CHECKCMP4": "检查如果你的测量值为大于或等于 {{ val }}, 然后于是单击'是'或'不是'",
      "CHECKCMP5": "检查如果你的测量值为大于{{ val }}, 然后于是单击'是'或'不是'",
      "TARGET": "目标值",
      "STATISTIC": "请输入一个值用于统计目的",
      "THRESH": "临界值",
      "VALUERNG1": "值必须介于",
      "VALUERNG2": "和",
      "TEXTLEN1": "至少文字必须",
      "TEXTLEN2": "字符长",
      "TEXTPAT": "文字必须与特定模式匹配",
      "EXPNO": "No' 预计",
      "EXPYES": "Yes' 预计",
      "EXPBOTH": "Yes' 或 'No' 都将被接受",
      "OPTSKIP": "此测量是可选的. 跳过?",
      "YES": "是",
      "NO": "否",
      "INPUT": {
        "VALUE": "输入测量值在以下文字框中:",
        "TEXT": "输入请求在以下文字框中:",
        "LIST": "Choose the appropriate value:",
        "BOOL": "检查'是'或'不是'",
        "MATRIX": "填写以下的矩阵, 然后点击'检查及保存'来计算结果:"
      },
      "RESULT": "结果",
      "STATUS": {
        "TITLE": "状态",
        "TODO": "待办事项",
        "PASS": "通过",
        "FAIL": "失败",
        "SKIP": "跳过",
        "INV": "无效的",
        "ERROR": "错误",
        "WARN": "失败"
      },
      "BUTTON": {
        "CLOSE": "关闭",
        "CLEAR": "清除",
        "CANCEL": "取消",
        "ADDCOMM": "增加评论",
        "EDITCOMM": "编辑评论",
        "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER",
        "CONTINUE": "检查及继续"
      },
      "TEXTTIMER13": "请输入开始时间",
      "TEXTTIMER14": "请输入结束时间",
      "TEXTTIMER": {
        "START": "请输入开始时间",
        "STOP": "请输入停止时间",
        "STOPLT": "请输入停止时间；实际停止时间可能少于 {{ time }} 分钟",
        "STOPGT": "请输入停止时间；实际停止时间可能多于 {{ time }} 分钟",
        "REDUCE": "减少",
        "MIN": "分钟"
      },
      "NOINPUT": "该路径结果由其他路径手段推算；无必要输入"
    }
  },
  "WFLOWEDIT": {
    "OPT1": {
      "CT": "检查类型",
      "MD": "型号",
      "DT": "设备类型",
      "P": "流程",
      "S": "步骤",
      "M": "测量",
      "CO": "Config. table"
    },
    "OPT2": {
      "SEL": "已选择的",
      "NSEL": "未选择的",
      "QUEUED": "已队列的",
      "NQUEUED": "未队列的",
      "FINPASS": "完成的及通过的",
      "FINFAIL": "完成的及失败的",
      "SKIP": "跳过的",
      "NSKIP": "未跳过的",
      "OMIT": "忽略的",
      "NOMIT": "未忽略的",
      "YES": "设置为'YES'",
      "NO": "设置为'NO'",
      "ACTIVE": "active",
      "NACTIVE": "not active"
    },
    "OPT3": {
      "SKIP": "跳过",
      "OMIT": "忽略"
    },
    "TOOLTIP": {
      "REMMOD": "移除修改者",
      "ADDMOD": "增加新修改者"
    },
    "LOADING": "加载编辑器…",
    "TITLE": "工作流程规则编辑器",
    "ACTIVER": "主动规则",
    "ADDRULE": "增加新规则",
    "RULE1": "如果这个",
    "RULE2": "使用代码",
    "RULE3": "是",
    "RULE4": ",然后"
  },
  "SERVER": {
    "ERROR": {
      "TITLE": "错误!",
      "VALIDATION": {
        "MEASUREMENTERRORCATEGORY": {
          "NAME": {
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT",
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"
          }
        },
        "USERGROUP": {
          "NAME": {
            "NOTNULL": "群组名称必须提供",
            "UNIQUE": "群组名称必须是唯一的; 没有其他群组可以有相同的名称",
            "MATCH": "组名只能包含字符, 数字和下划线; 它必须至少3个字符宽"
          },
          "LEVEL": {
            "NOTNULL": "此级别必须指定",
            "NUMBER": "群组级别必须是正整数1和1000之间"
          }
        },
        "USER": {
          "USERNAME": {
            "NOTNULL": "用户名必须提供",
            "UNIQUE": "用户名称必须是唯一的; 没有其他用户可以有相同的用户名称",
            "MATCH": "用户名称只能包含字符, 数字和下划线; 它必须至少3个字符宽"
          },
          "PASSHASH": {
            "NOTNULL": "验证登陆必须提供",
            "MATCH": "验证登陆当前不支持密码机制"
          },
          "REALNAME": {
            "NOTNULL": "用户真实名称必须提供",
            "MINLEN": "用户真实名称必须至少3个字符长"
          },
          "USERGROUP_ID": {
            "NOTNULL": "用户的群组必须设置"
          }
        },
        "TOOLTYPE": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他工具类型可以有相同的代码",
            "SHORT": "代码值必须至少3个字符长",
            "INVALID": "代码值只能包含字符, 数字,点,逗号, 下划线和连字符"
          },
          "TITLE": {
            "INVALID": "标题语言块是无效的",
            "INCOMPLETE": "标题语言块遗漏了语言输入 '{{ lcode }}'",
            "SHORT": "语言输入 '{{ lcode }}' 对于此标题太短 (< {{ minlength }})"
          },
          "DESCRIPTION": {
            "INVALID": "标题语言块的描述是无效的"
          }
        },
        "TOOLUNIT": {
          "CODE": {
            "UNIQUE": "代码值在此工具类型必须是唯一的; 没有其他单位可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          }
        },
        "PROCEDURE": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他流程可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          },
          "TITLE": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID",
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"
          },
          "DESCRIPTION": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"
          }
        },
        "STEP": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他此流程步骤可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          },
          "TITLE": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID",
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"
          },
          "DESCRIPTION": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"
          }
        },
        "MEASURE": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他此测量步骤可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          },
          "TITLE": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID",
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"
          },
          "DESCRIPTION": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"
          },
          "CALCULATION": {
            "OPTMISS": "可选字段必须设置为 是(Yes)或 否(No)",
            "INVMTYPE": "测量类型是无效的",
            "AINVVAL": "目标值和临界值必须指定一个有效的数字; 临界值必须是正数",
            "BINVVAL": "必须指定一个有效数字的最小值和最大值",
            "CINVVAL": "最小量文字长度必须是正整数值",
            "EMPTYVAL": "List values must not be empty",
            "DMISSING": "正规表达式必须是指定的",
            "DINVREGEXP": "给予的正则表达式是无效的",
            "EINVVAL": "缺少预期值-它必须设置为Yes或No",
            "JINVVAL": "必须指定一个有效的数字参考值",
            "JINVCMP": "必须指定比较器",
            "HINVVAL": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVVAL",
            "HINVCMP": "@:SERVER.ERROR.VALIDATION.MEASURE.CALCULATION.JINVCMP",
            "XINVMTRX": "无效的矩阵定义; 尺寸不给予或无效",
            "XINVFORM": "没有指定矩阵公式",
            "XVARIOUS": "未预期的错误,同时验证矩阵",
            "XSYNTAX": "语法错误,同时验证矩阵公式; 检查是否输入错误",
            "XINVVAR": "检测到无效的变量名称,同时验证矩阵公式",
            "XINVFUN": "检测到无效的方法名称,同时验证矩阵公式",
            "XINVUSE": "一个或多个方法使用不当 (列如: 一个代替两个参数)",
            "MCODEMISSING": "必须设置此测量类型的分组代码，必须超过3个字符"
          },
          "TOOL": "<i>工具 (type </i><b>%s</b>: %s)使用:</i> <b>%s</b>",
          "TOOLNOSEL": "<i>使用工具 (type </i><b>%s</b>: %s<i>) <b>尚未选择</b></i>",
          "REP": "<i>测量由 </i><b>%s</b> <i>at</i> <b>%s</b>",
          "COMM": "<i>评论:</i> %s",
          "RAW": "<i>矩阵值:</i> %s"
        },
        "MINPUT": {
          "VALUECMP": "值必须是 %s %s",
          "CHECKCMP": " '是' 如果测量值是 %s %s",
          "COMP": {
            "T1": "少于",
            "T2": "少于或等于",
            "T3": "等于",
            "T4": "大于或等于",
            "T5": "大于"
          },
          "THRESH": "目标值: %s, 临界值 %s",
          "VALUERNG": "值必须介于 %s 及 %s",
          "TEXTLEN": "文字必须至少 %d 字符长",
          "TEXTPAT": "文字必须与特定模式匹配: %s",
          "CHOICE": "A value must be chosen from a list",
          "EXP": {
            "NO": "@:WFLOW.INPUT.EXPNO",
            "YES": "@:WFLOW.INPUT.EXPYES",
            "BOTH": "@:WFLOW.INPUT.EXPBOTH"
          },
          "STATISTICAL": "用于统计分析;接受任何值",
          "TIMER": "时间测量",
          "TIMERA": "时间测定（开始）",
          "TIMERS": "时间测定（停止）由于统计原因",
          "TIMERQ": {
            "T1": "时间测定（停止）；时间长度必须少于%d分钟",
            "T5": "时间测定（停止）；时间长度必须多于%d分钟"
          },
          "TIMERC": {
            "T1": "暂停时间测定检查；时间长度必须少于%d分钟",
            "T5": "暂停时间测定检查；时间长度必须少于%d分钟"
          }
        },
        "STATREPORT": "计量统计法",
        "PAGENO": "页 %d 的 %d",
        "STAT": {
          "CHECKINTRO": "使用下列检查此统计概况:",
          "CHECK": {
            "LINE1": "检查 <b>#%s</b> (%s) 单元 <b>%s</b> (%s), 型号 <b>%s: %s",
            "LINE2": "检查状态: <b>%s</b>, 单元状态: <b>%s</b>"
          },
          "MSR1": "测量 <b>%s: %s</b>",
          "MSR2": "步骤 <b>%s: %s</b>, 流程 <b>%s: %s</b>",
          "MSR2A": "步骤 <b>%s: %s</b>",
          "MSR2B": "流程 <b>%s: %s</b>",
          "MSR3S": "规则: <b>%s</b>",
          "MSR3O": "规则: <b>%s</b> (任选的, 可以跳过)",
          "MSR4T": "工具类型: <b>%s: %s</b>",
          "MSR4N": "工具类型: <i>没有工具使用</i>",
          "OLDV": "<sup>*)</sup> 在检查期间是使用此测量的旧版本; 结果可能与当前版本不兼容",
          "NODATA": "无可用数据",
          "HDR": {
            "STATUS": "@:WFLOW.INPUT.STATUS.TITLE",
            "UNIT": "Unit",
            "USER": "@:MEDITOR.USER",
            "TOOL": "工具使用",
            "VALUE": "值"
          },
          "FINAL": {
            "INTRO": "下面的ID自符串被用来创建这个报告. 你可以使用它们来创建这个报告或使用部分:",
            "CHECKS": "检查:",
            "MEASURES": "测量:"
          },
          "TITLE": {
            "FRONT": "概况",
            "STATS": "测量 %s"
          }
        },
        "CHECKTYPE": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他测试类型可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          },
          "TITLE": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID",
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"
          },
          "DESCRIPTION": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"
          }
        },
        "DEVICETYPE": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他设备类型可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          },
          "TITLE": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID",
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"
          },
          "DESCRIPTION": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"
          }
        },
        "MODEL": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他型号可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          },
          "TITLE": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INVALID",
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.SHORT"
          },
          "DESCRIPTION": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"
          },
          "DEVICETYPE_ID": {
            "NULL": "设备类型必须设置",
            "NOREF": "设备类型 {{ refid}} 为无效或不存在"
          }
        },
        "UNIT": {
          "CODE": {
            "UNIQUE": "代码值必须是唯一的; 没有其他单位可以有相同的代码",
            "SHORT": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.SHORT",
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.CODE.INVALID"
          },
          "DESCRIPTION": {
            "INVALID": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.DESCRIPTION.INVALID"
          },
          "COMMENT": {
            "INVALID": "标题语言块给客户是无效的"
          },
          "MODEL_ID": {
            "NULL": "型号必须设置",
            "NOREF": "型號 {{ refid }}为无效或不存在"
          }
        },
        "SETTINGS": {
          "VALUE": {
            "INCOMPLETE": "@:SERVER.ERROR.VALIDATION.TOOLTYPE.TITLE.INCOMPLETE"
          }
        },
        "TYPE": "服务器报告一个或多个数据验证错误:"
      },
      "INVMOVIE": {
        "TYPE": "上传视频為无效的",
        "TEXT": "上传的视频文件，经检查发现无效. 它不能用于此应用."
      },
      "INVPDF": {
        "TYPE": "上传的PDF文档无效",
        "TEXT": "上传的PDF文档，经检查发现无效. 它不能用于此应用."
      },
      "CHECKACTION": {
        "TYPE": "此操作期间出现错误",
        "INVALIDPHASE": "在这个阶段可能无法执行此操作",
        "REQFAILED": "此操作不符合某些要求",
        "INVCOMMAND": "无效命令"
      },
      "STEPACTION": {
        "TYPE": "@:SERVER.ERROR.CHECKACTION.TYPE",
        "INVALIDPHASE": "@:SERVER.ERROR.CHECKACTION.INVALIDPHASE",
        "REQFAILED": "@:SERVER.ERROR.CHECKACTION.REQFAILED",
        "INVCOMMAND": "@:SERVER.ERROR.CHECKACTION.INVCOMMAND"
      },
      "LOGIN": {
        "TYPE": "登入错误",
        "USERUNKNOWN": "用户名不存在数据库中",
        "NOTINITIALIZED": "用户尚未初始化",
        "WRONGPASSWORD": "密码不匹配",
        "NOTLOGGEDIN": "用户没有登录",
        "USERLOCKED": "The user is locked"
      },
      "NOTFOUND": {
        "TYPE": "缺少一个对象",
        "TEXT": "此 {{class}}- 有ID的对象 {{id}} 在数据库中找不到",
        "CHECKSTEXT": "此步骤 #{{sid}} 在确认 (ID: {{cid}}) 在数据库中找不到",
        "CHECKMTEXT": "此测量 #{{mid}} 在步骤 #{{sid}} 在确认 (ID: {{cid}}) 在数据库中找不到"
      },
      "STEPTYPE": {
        "TYPE": "Error while trying to set step type",
        "HASMEASURES": "The type of step #{{id}} can't be set to 'instruction' as long as there are measures on it."
      },
      "INVMEDIA": {
        "TYPE": "上传的媒体类型无效",
        "TEXT": "上传的媒体具有无效的类型; 服务器无法存储数据"
      },
      "IMPORT": {
        "TYPE": "输入错误",
        "WRONGTYPE": "输入文件 ({{ fname }}) 的类型错误. 要求输入的类型 {{ req }}, 此文件包含类型{{ is }}.",
        "BADCRC": "输入文件 ({{ fname }}) 已被篡改. 只有原始的,未经修改的文件下载输出功能可能被输入.\"",
        "INVALIDFILE": "给定的文件 ({{ fname }}) 是无效的; 它要么已被篡改或您选择了一个错误的文件"
      },
      "ACCDENIED": {
        "TYPE": "拒绝访问或不足",
        "TEXT": "你的访问级别或权限不足以实施该动作"
      }
    }
  },
  "EDITOR": {
    "LOADING": "编辑器加载…",
    "CODECHNG": {
      "TITLE": "代码更改!",
      "TEXT": "应避免更改任何对象的代码值。它可能会改变现有的工作流规则设置，使它们失效或无效。你确定要储存吗?"
    }
  },
  "VERSIONING": {
    "EDIT": {
      "VERSIONED": "编辑 (V. {{ vers }})",
      "NEW": "编辑 (new)"
    },
    "VERSION": "版本 {{ vers }}",
    "LASTCHG": "上次更改由 {{ realname }} ({{ username }}) 在 {{ date }}"
  },
  "WFMODIFIER": {
    "NOMODIFIERS": "没有工作流程修改设定",
    "HASMODIFIERS": "{{ num }} 工作流程修改设定:",
    "CT": {
      "SELECT": {
        "STD": "省略,如果用于检查和检查类型{{ code }}",
        "INV": "省略,如果不是用于检查和检查类型 {{ code }}"
      },
      "GSEARCH": {
        "WIZARD": {
          "TITLE": "全局搜索",
          "TEXT1": " an asterisk ('*') is a wildcard for a maximum of 10 characters. A pipe symbol ('|') at the beginning or the end specifies",
          "TEXT2": "搜索执行过程中请选择范围",
          "TEXT3": "请选择要执行的语言（在多个范围里）",
          "TEXT4": "请版本化的对象上选择搜索型号"
        },
        "SCOPE": {
          "ALLALL": {
            "TEXT": "所有对象（全面搜索）",
            "TTIP": " all matching objects shown.\""
          },
          "ALLRECENT": {
            "TEXT": " most recent found object only\"",
            "TTIP": " but only the most recent object is shown.\""
          },
          "FINALL": {
            "TEXT": "选定对象",
            "TTIP": " all matching objects shown.\""
          },
          "FINRECENT": {
            "TEXT": " most recent found object only\"",
            "TTIP": " but only the most recent object is shown.\""
          },
          "LATESTV": {
            "TEXT": "最新版本",
            "TTIP": "只搜索最新的选定的对象"
          },
          "EDITV": {
            "TEXT": "只编辑版本",
            "TTIP": " non finalized versions are searched.\""
          }
        },
        "TYPE": {
          "CHECK": {
            "SELECT": "验盘",
            "DISPLAY": " model <b>{{ object.unit.model.code }}</b>\""
          },
          "MEASUREMENT": {
            "SELECT": "路径",
            "DISPLAY": " model {{ object.check.unit.model.code }}) for measure <b>{{ object.measure.code }}</b> in step <b>{{ object.measure.step.code }}</b>"
          },
          "CHECKTYPE": {
            "SELECT": "验盘类型",
            "DISPLAY": "<u>验盘类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"
          },
          "DEVICETYPE": {
            "SELECT": "装置类型",
            "DISPLAY": "<u>装置类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)"
          },
          "MODEL": {
            "SELECT": "型号",
            "DISPLAY": " version {{ object.version || '(edit)' }}\""
          },
          "UNIT": {
            "SELECT": "单元",
            "DISPLAY": "<u>单元</u> <b>{{ object.code }}</b> 在型号el <b>{{ object.model.code }}</b>"
          },
          "MEASURE": {
            "SELECT": "路径",
            "DISPLAY": " version {{ object.procedure.version || '(edit)' }}\""
          },
          "STEP": {
            "SELECT": "步骤",
            "DISPLAY": " v {{ object.procedure.version || '(edit)' }}\""
          },
          "PROCEDURE": {
            "SELECT": "程序",
            "DISPLAY": " version {{ object.version || '(edit)' }}\""
          },
          "TOOLTYPE": {
            "SELECT": "工具类型",
            "DISPLAY": "<u>工具类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)"
          },
          "TOOLUNIT": {
            "SELECT": "工具单元",
            "DISPLAY": "<u>工具单元</u> <b>{{ object.code }}</b> 在工具类型 <b>{{ object.tooltype.code }}</b>"
          },
          "USER": {
            "SELECT": "用户",
            "DISPLAY": "<u>用户</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)"
          },
          "NOTICE": {
            "SELECT": "问题报告",
            "DISPLAY": "<u>问题报告</u> <b>{{ object.id }}</b>"
          },
          "CONFIGTABLE": {
            "SELECT": "Configuration tables",
            "DISPLAY": "<u>Configuration table</u> <b>{{ object.id }}</b>"
          },
          "CONFIGENTRY": {
            "SELECT": "Configuration table entries",
            "DISPLAY": "<u>Entry</u> <b>{{ object.code_id }}</b> in configuration table <b>{{ object.configtable_id }}</b>"
          }
        },
        "FIELD": {
          "ID": "Number",
          "COMMENT": "注释",
          "CODE": "编码",
          "TITLE": "名称",
          "DESCRIPTION": "描述/线索",
          "CUSTOMER": "客户",
          "USERNAME": "用户名",
          "REALNAME": "真实姓名",
          "TEXT": "描述",
          "COL1": "First column",
          "COL2": "Second column",
          "COL3": "Third column",
          "COL4": "Fourth column",
          "COL5": "Fifth column",
          "COL6": "Sixth column",
          "DELETED": "Metainformation",
          "CALCULATION": "Choice list"
        },
        "BUTTONS": {
          "NEWSEARCH": "新搜索",
          "CLOSE": "关闭"
        },
        "RESULT": {
          "TITLE": "搜索结果",
          "TEXT": " sorted by date of change:\"",
          "TOOMUCH": "（备注：只显示前50个符合条件的对象）",
          "NOMATCH": "没有符合条件的对象"
        }
      },
      "MD": {
        "SELECT": {
          "STD": "省略,如果用于型号 {{ code }}",
          "INV": "省略,如果不是用于型号 {{ code }}"
        }
      },
      "DT": {
        "SELECT": {
          "STD": "省略,如果用于设备类型的型号{{ code }}",
          "INV": "省略,如果不是用于设备类型的型号{{ code }}"
        }
      },
      "CO": {
        "SELECT": {
          "STD": "Omit if config table entry {{ code }} active",
          "INV": "Omit if config table entry {{ code }} not active"
        }
      },
      "P": {
        "INQUEUE": {
          "STD": "省略,如果流程 {{ code }} 也在排队检查",
          "INV": "省略,如果流程 {{ code }} 不排队检查"
        },
        "PASS": {
          "OMIT": "省略,如果流程 {{ code }} 完成并通过了",
          "SKIP": "跳过,如果流程 {{ code }} 完成并通过了"
        },
        "FAIL": {
          "OMIT": "省略,如果流程 {{ code }} 完成并失败了",
          "SKIP": "跳过,如果流程 {{ code }} 完成并失败了"
        },
        "SKIP": {
          "OMIT": {
            "STD": "省略,如果流程 {{ code }} 被跳过",
            "INV": "省略,如果流程 {{ code }} 未跳过"
          },
          "SKIP": {
            "STD": "跳过,如果流程 {{ code }} 被跳过",
            "INV": "跳过,如果流程 {{ code }} 未跳过"
          }
        },
        "OMIT": {
          "OMIT": {
            "STD": "省略,如果流程 {{ code }} 被省略",
            "INV": "省略,如果流程 {{ code }} 未省略"
          },
          "SKIP": {
            "STD": "跳过,如果流程 {{ code }} 被省略",
            "INV": "跳过,如果流程 {{ code }} 未省略"
          }
        }
      },
      "S": {
        "PASS": {
          "OMIT": "省略,如果步骤 {{ code }} 完成并通过了",
          "SKIP": "跳过,如果步骤 {{ code }} 完成并通过了"
        },
        "FAIL": {
          "OMIT": "省略,如果步骤 {{ code }} 完成并失败了",
          "SKIP": "跳过,如果步骤 {{ code }} 完成并失败了"
        },
        "SKIP": {
          "OMIT": {
            "STD": "省略,如果步骤 {{ code }} 被跳过",
            "INV": "省略,如果步骤 {{ code }} 未跳过"
          },
          "SKIP": {
            "STD": "跳过,如果步骤 {{ code }} 被跳过",
            "INV": "跳过,如果步骤 {{ code }} 未跳过"
          }
        },
        "OMIT": {
          "OMIT": {
            "STD": "省略,如果步骤 {{ code }} 被省略",
            "INV": "省略,如果步骤 {{ code }} 未省略"
          },
          "SKIP": {
            "STD": "跳过,如果步骤 {{ code }} 被省略",
            "INV": "跳过,如果步骤 {{ code }} 未省略"
          }
        }
      },
      "M": {
        "PASS": {
          "OMIT": "省略,如果步骤/测量 {{ code }} 完成并通过了",
          "SKIP": "跳过,如果步骤/测量 {{ code }} 完成并通过了"
        },
        "FAIL": {
          "OMIT": "省略,如果步骤/测量 {{ code }} 完成并失败了",
          "SKIP": "跳过,如果步骤/测量 {{ code }} 完成并失败了"
        },
        "YES": {
          "OMIT": "省略,如果步骤/测量 {{ code }} 已设置为“是”",
          "SKIP": "跳过,如果步骤/测量 {{ code }} 已设置为“是”"
        },
        "NO": {
          "OMIT": "省略,如果步骤/测量 {{ code }} 已设置为“否”",
          "SKIP": "跳过,如果步骤/测量 {{ code }} 已设置为“否”"
        },
        "SKIP": {
          "OMIT": {
            "STD": "省略,如果步骤/测量 {{ code }} 被跳过",
            "INV": "省略,如果步骤/测量 {{ code }} 未跳过"
          },
          "SKIP": {
            "STD": "跳过,如果步骤/测量 {{ code }} 被跳过",
            "INV": "跳过,如果步骤/测量 {{ code }} 未跳过"
          }
        },
        "OMIT": {
          "OMIT": {
            "STD": "省略,如果步骤/测量 {{ code }} 被省略",
            "INV": "省略,如果步骤/测量 {{ code }} 未省略"
          },
          "SKIP": {
            "STD": "跳过,如果步骤/测量 {{ code }} 被省略",
            "INV": "跳过,如果步骤/测量 {{ code }} 未省略"
          }
        }
      }
    },
    "ERROR": {
      "CLIENT": {
        "TITLE": "发生了一个严重错误…",
        "TOTTL": "逾时…",
        "DETAILS": "细节如下:",
        "BACK": "回到登录",
        "MSG": {
          "TIMEOUT": "由于逾时当前工作段已被取消.请重新登录并继续.",
          "SEVREST": "发生了严重的服务器错误;在这一阶段的过程无法继续. 请返回到登录或使用标题栏选择下一个目的地.",
          "HTTP404": "@:ERROR.CLIENT.MSG.SEVREST",
          "HTTP500": "@:ERROR.CLIENT.MSG.SEVREST",
          "ACCDENY": "由于权限丢失，访问被拒绝。请重新登入并继续。"
        },
        "ADTTL": "拒绝访问"
      }
    },
    "LOGIN": {
      "TITLE": "登录",
      "USERNAME": "用户名",
      "PASSWORD": "密码",
      "BUTTON": "@:LOGIN.TITLE"
    },
    "UI": {
      "BUTTONS": {
        "MEDITOR": {
          "RESET": "@:WFLOW.INPUT.BUTTON.CLEAR",
          "ADDMSEL": "增加 {{ num }} 项目(s)",
          "ADDMSELTEST": "测试 {{ num }} 项目(s)",
          "SAVE": "储存",
          "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
          "SELECT": "选择",
          "SELALL": "选择全部",
          "DESELALL": "清除全部"
        },
        "ALERT": {
          "OK": "好",
          "YES": "@:WFLOW.INPUT.YES",
          "NO": "@:WFLOW.INPUT.NO"
        }
      },
      "MEDITOR": {
        "MAXSEL": "你可以选择{{ num }} 进入点",
        "NOMORESEL": "所选择的进入点已达到限制"
      }
    },
    "VIEWER": {
      "IMAGE": {
        "TITLE": "图像浏览"
      },
      "PDF": {
        "TITLE": "PDF浏览"
      },
      "VIDEO": {
        "TITLE": "影像拨放器"
      }
    },
    "TLISTEDIT": {
      "TOOLTIP": {
        "DEL": "删除条目",
        "MEDMGR": "启动媒体管理这个项目",
        "EDIT": "编辑项目",
        "NEW": "创建新项目"
      },
      "LOADING": "加载编辑器…",
      "DISABLED": "禁用!",
      "CNT": {
        "CHECK": "{{ cnt }} 检查(s)",
        "MODEL": "{{ cnt }} 型号(s)",
        "IMAGES": "{{ cnt }} 图像(s)",
        "DOCS": "{{ cnt }} 文檔(s)"
      },
      "NEW": "新条目",
      "CODE": "代码",
      "TITLE": "标题",
      "DESC": "描述",
      "DISSET": {
        "TITLE": "禁用",
        "TRUE": "这个条目被删除,不得再使用.",
        "FALSE": "这个条目是启动."
      },
      "BUTTON": {
        "CANCEL": "@:WFLOW.INPUT.BUTTON.CANCEL",
        "ADD": "增加",
        "SAVE": "@:UI.BUTTONS.MEDITOR.SAVE"
      }
    },
    "MEDITOR": {
      "GROUP": "群组",
      "USER": "@:FRAME.MANAGE.USER"
    },
    "DASHBLOCK": {
      "TOOLTIP": {
        "CLOSE": "移除区块",
        "GOTO": "转到项目",
        "SETFILTER": "编辑波滤器设置",
        "EDITTITLE": "编辑块的名称"
      }
    },
    "DROPBOX": {
      "INTRO": "拖拽流程输出文件到此框输入!",
      "UPLOADING": "上传 {{ num }} 文件(s)",
      "REMAINING": "{{ num }} 文件(s) 还需处理的",
      "SUCCESS": "{{ num }} 文件(s) 上传成功",
      "ERRORS": "{{ num }} 文件(s) 上传错误"
    },
    "PRV": {
      "MNGMUC": {
        "TTL": "管理 型号/单位/检查",
        "DSC": "型号可以使用管理查看,单位和检查"
      },
      "MNGPSM": {
        "TTL": "管理流程",
        "DSC": "流程可以使用管理查看,以及步骤和测量"
      },
      "MNGTOL": {
        "TTL": "管理工具类型",
        "DSC": "工具类型可以使用管理查看"
      },
      "MNGPAR": {
        "TTL": "管理参数",
        "DSC": "May use management views for parameters, devicetypes and checktypes as well as managing optional settings"
      },
      "MNGUSR": {
        "TTL": "管理用户",
        "DSC": "用户可以使用管理控制台"
      },
      "MNGALL": {
        "TTL": "管理全部",
        "DSC": "可以使用所有管理查看(权限采集)"
      },
      "EDTMOD": {
        "TTL": "编辑型号",
        "DSC": "可以修改现有型号"
      },
      "EDTUNT": {
        "TTL": "编辑单元",
        "DSC": "可以修改现有单元"
      },
      "EDTPSM": {
        "TTL": "编辑流程",
        "DSC": "可以修改现有流程,包括步骤和测量(这也可以被删除)"
      },
      "EDTTTY": {
        "TTL": "编辑工具类型",
        "DSC": "可以修改现有工具类型"
      },
      "EDTALL": {
        "TTL": "编辑全部",
        "DSC": "可以修改现有的所有对象(权限采集)"
      },
      "CRTMOD": {
        "TTL": "创建型号",
        "DSC": "可以添加新型号"
      },
      "CRTUNT": {
        "TTL": "创建单元",
        "DSC": "可以添加新单元"
      },
      "CRTPSM": {
        "TTL": "创建流程",
        "DSC": "可以添加新流程"
      },
      "CRTTOL": {
        "TTL": "创建工具",
        "DSC": "可以添加新工具单元"
      },
      "CRTTTY": {
        "TTL": "创建工具类型",
        "DSC": "可以添加新工具类型"
      },
      "CRTCHK": {
        "TTL": "创建检查",
        "DSC": "可以新增新的检查或删除尚未开始的检查"
      },
      "CRTALL": {
        "TTL": "创建全部",
        "DSC": "可以新增任何一种新的对象(权限采集)"
      },
      "DELMOD": {
        "TTL": "删除型号",
        "DSC": "可以删除或禁用型号"
      },
      "DELUNT": {
        "TTL": "删除单元",
        "DSC": "可以删除单元"
      },
      "DELPRC": {
        "TTL": "删除流程",
        "DSC": "可以删除或禁用流程"
      },
      "DELTTY": {
        "TTL": "删除工具类型",
        "DSC": "可以删除或禁用工具类型"
      },
      "DELTOL": {
        "TTL": "删除工具类型",
        "DSC": "可以删除或禁用工具类型"
      },
      "DELCHK": {
        "TTL": "删除检查",
        "DSC": "可以删除或禁用检查"
      },
      "DELALL": {
        "TTL": "删除全部",
        "DSC": "可以删除或禁用所有对象(权限采集)"
      },
      "USRMGO": {
        "TTL": "@:PRV.MNGUSR.TTL",
        "DSC": "可以管理 (新增,更改,禁用) 较低级别的用户"
      },
      "USRMGA": {
        "TTL": "管理全部用户",
        "DSC": "可以管理 (新增,更改,禁用) 所有级别的用户"
      },
      "GRTPRO": {
        "TTL": "授予自己的权限",
        "DSC": "可以授予个人拥有的权限"
      },
      "GRTPRA": {
        "TTL": "授予所有权限",
        "DSC": "可以个人授予所有权限"
      },
      "GRTTOG": {
        "TTL": "授予群组权限",
        "DSC": "也可以授予权限给群组(延伸权限)"
      },
      "GRPCRT": {
        "TTL": "创建群组",
        "DSC": "可以创建新群组"
      },
      "WFLMNG": {
        "TTL": "管理工作流程",
        "DSC": "可以管理检查和检查工作流程,评论检查, 查看主管信息"
      },
      "WFLREG": {
        "TTL": "注册工作流程",
        "DSC": "可以注册工作流程 (如果适用)或从工作流程注销 (如果自助注册)"
      },
      "CHGCOD": {
        "TTL": "更改代码",
        "DSC": "当编辑时,可以更改对象的代码(延伸权限)"
      },
      "FINALZ": {
        "TTL": "最终版本",
        "DSC": "可以确定对象的当前版本(唯一可用的权限去编辑对象;延伸权限)"
      },
      "MODVRS": {
        "TTL": "修改版本",
        "DSC": "May edit a finalized version of an object (spelling mistakes only)"
      },
      "TRUSER": {
        "TTL": "有经验用户",
        "DSC": "用户经验丰富，可能使用几种高级功能"
      },
      "TKOVER": {
        "TTL": "合并分配的任务",
        "DSC": "如适用，用户可以合并自分配给其他用户的任务"
      },
      "GLSRCH": {
        "TTL": "访问全局搜索",
        "DSC": "可使用全局搜索功能"
      },
      "MNGNTC": {
        "TTL": "查看并操作问题报告",
        "DSC": " manage and process problem reports on the report page\""
      },
      "MNGCFG": {
        "TTL": "Show and edit configuration tables",
        "DSC": "The user may show and edit all configuration tables"
      },
      "MNGCFE": {
        "TTL": "Edit configurations for checks",
        "DSC": "The user may configure tables on checks."
      }
    },
    "PDF": {
      "MEASUREMENTERRORREPORT": {
        "TITLE": "Measurement error report",
        "PROCEDURE": "Procedure",
        "STEP": "Step",
        "MEASURE": "Measure",
        "MODEL": "Model",
        "UNIT": "Unit",
        "VALUE": "Value",
        "SAVEDBY": "Saved by",
        "ERRORCATEGORY": "Error category",
        "STATUS": {
          "TITLE": "State",
          "UNPROC": "Left out",
          "PASSED": "Passed",
          "FAILED": "Failed",
          "INVALID": "Invalid",
          "ERROR": "Error"
        },
        "EXPINPUT": {
          "TARGET": "@:wflow_input_target",
          "THRESH": "@:WFLOW.INPUT.THRESH",
          "VALUERNG1": "@:WFLOW.INPUT.VALUERNG1",
          "VALUERNG2": "@:WFLOW.INPUT.VALUERNG2",
          "VALUECMP1": "@:WFLOW.INPUT.VALUECMP1",
          "VALUECMP2": "@:WFLOW.INPUT.VALUECMP2",
          "VALUECMP3": "@:WFLOW.INPUT.VALUECMP3",
          "VALUECMP4": "@:WFLOW.INPUT.VALUECMP4",
          "VALUECMP5": "@:WFLOW.INPUT.VALUECMP5",
          "STATISTIC": "Measured value, no check took place",
          "CHECKCMP1": "Check if measured value is less than {{ val }}",
          "CHECKCMP2": "Check if measured value ist less or equal to  {{ val }}",
          "CHECKCMP3": "Check if measured value ist equal to {{ val }}",
          "CHECKCMP4": "Check if measured value is greater or equal to  {{ val }}",
          "CHECKCMP5": "Check if measured value is greater than {{ val }}",
          "TEXTLEN1": "@:WFLOW.INPUT.TEXTLEN1",
          "TEXTLEN2": "@:WFLOW.INPUT.TEXTLEN2",
          "REGEXP": "Checked text:",
          "EXPNO": "@:WFLOW.INPUT.EXPNO",
          "EXPYES": "@:WFLOW.INPUT.EXPYES",
          "EXPBOTH": "@:WFLOW.INPUT.EXPBOTH",
          "TEXTTIMERSTART": "Entered start time",
          "TEXTTIMERSTOP": "Entered stop time",
          "TEXTTIMERSTOPLT": "Entered start time: exptected time to {{ val }} mins",
          "TEXTTIMERSTOPGT": "Entered start time: expected time greater than {{ val }} mins",
          "CHOICELIST": "Choice list"
        }
      },
      "BOOL": {
        "YES": "是",
        "NO": "否"
      },
      "MAINTTL": "检查报告",
      "STATUS": {
        "OPEN": "@:UNIT.VIEW.STATUS.OPEN",
        "FAILED": "@:WFLOW.INTRO.STATUS.FAIL",
        "PASSED": "@:WFLOW.INTRO.STATUS.PASS",
        "UNFIN": "@:WFLOW.INPUT.STATUS.TODO",
        "PASS": "@:WFLOW.INPUT.STATUS.PASS",
        "FAIL": "@:WFLOW.INPUT.STATUS.FAIL",
        "SKIP": "@:WFLOW.STEP.STATUS.SKIP",
        "CANCELLED": "取消",
        "WARNINT": "失败<sup>*)</sup>",
        "WARNEXT": "通过<sup>*)</sup>"
      },
      "USTATUS": {
        "OPEN": "开启",
        "CLOSED": "关闭",
        "DISCARDED": "丢弃",
        "ACLOSED": "关闭及存档",
        "ADISCARDED": "丢弃及存档"
      },
      "MODEL": "@:WFLOWEDIT.OPT1.MD",
      "UNIT": "@:WFLOW.INTRO.UNIT",
      "CHECK": "@:WFLOW.INTRO.MEDIAMANAGER",
      "DATE": {
        "SCHED": "@:WFLOW.INTRO.DATE.SCHED",
        "DUE": "@:WFLOW.INTRO.DATE.DUE",
        "NOSCHED": "@:WFLOW.INTRO.DATE.NOSCHED",
        "NODUE": "@:WFLOW.INTRO.DATE.NODUE",
        "START": "@:WFLOW.INTRO.DATE.START",
        "FIN": "@:WFLOW.INTRO.DATE.FIN",
        "NOSTART": "@:WFLOW.INTRO.DATE.NOSTART",
        "NOFIN": "@:WFLOW.INTRO.DATE.NOFIN"
      },
      "USER": {
        "NOONE": "还没有人",
        "ANY": "任何",
        "GRP": "群组的用户 %s"
      },
      "VERSION": "@:PROCEDURE.VIEW.VERS",
      "STEP": {
        "ASSNC": "<i>代理人:</i> %s, <b>尚未提交</b>",
        "ASSC": "<i>代理人:</i> %s, <i>提交由:</i> %s"
      },
      "MEASURE": {
        "TOOL": "<i>工具 (type </i><b>%s</b>: %s)使用:</i> <b>%s</b>",
        "TOOLNOSEL": "<i>使用工具 (type </i><b>%s</b>: %s<i>) <b>尚未选择</b></i>",
        "REP": "<i>测量由 </i><b>%s</b> <i>at</i> <b>%s</b>",
        "COMM": "<i>评论:</i> %s",
        "RAW": "<i>矩阵值:</i> %s"
      },
      "MINPUT": {
        "VALUECMP": "值必须是 %s %s",
        "CHECKCMP": " '是' 如果测量值是 %s %s",
        "COMP": {
          "T1": "少于",
          "T2": "少于或等于",
          "T3": "等于",
          "T4": "大于或等于",
          "T5": "大于"
        },
        "THRESH": "目标值: %s, 临界值 %s",
        "VALUERNG": "值必须介于 %s 及 %s",
        "TEXTLEN": "文字必须至少 %d 字符长",
        "TEXTPAT": "文字必须与特定模式匹配: %s",
        "CHOICE": "A value must be chosen from a list",
        "EXP": {
          "NO": "@:WFLOW.INPUT.EXPNO",
          "YES": "@:WFLOW.INPUT.EXPYES",
          "BOTH": "@:WFLOW.INPUT.EXPBOTH"
        },
        "STATISTICAL": "用于统计分析;接受任何值",
        "TIMER": "时间测量",
        "TIMERA": "时间测定（开始）",
        "TIMERS": "时间测定（停止）由于统计原因",
        "TIMERQ": {
          "T1": "时间测定（停止）；时间长度必须少于%d分钟",
          "T5": "时间测定（停止）；时间长度必须多于%d分钟"
        },
        "TIMERC": {
          "T1": "暂停时间测定检查；时间长度必须少于%d分钟",
          "T5": "暂停时间测定检查；时间长度必须少于%d分钟"
        }
      },
      "STATREPORT": "计量统计法",
      "PAGENO": "页 %d 的 %d",
      "STAT": {
        "CHECKINTRO": "使用下列检查此统计概况:",
        "CHECK": {
          "LINE1": "检查 <b>#%s</b> (%s) 单元 <b>%s</b> (%s), 型号 <b>%s: %s",
          "LINE2": "检查状态: <b>%s</b>, 单元状态: <b>%s</b>"
        },
        "MSR1": "测量 <b>%s: %s</b>",
        "MSR2": "步骤 <b>%s: %s</b>, 流程 <b>%s: %s</b>",
        "MSR2A": "步骤 <b>%s: %s</b>",
        "MSR2B": "流程 <b>%s: %s</b>",
        "MSR3S": "规则: <b>%s</b>",
        "MSR3O": "规则: <b>%s</b> (任选的, 可以跳过)",
        "MSR4T": "工具类型: <b>%s: %s</b>",
        "MSR4N": "工具类型: <i>没有工具使用</i>",
        "OLDV": "<sup>*)</sup> 在检查期间是使用此测量的旧版本; 结果可能与当前版本不兼容",
        "NODATA": "无可用数据",
        "HDR": {
          "STATUS": "@:WFLOW.INPUT.STATUS.TITLE",
          "USER": "@:MEDITOR.USER",
          "TOOL": "工具使用",
          "VALUE": "值"
        },
        "FINAL": {
          "INTRO": "下面的ID自符串被用来创建这个报告. 你可以使用它们来创建这个报告或使用部分:",
          "CHECKS": "检查:",
          "MEASURES": "测量:"
        },
        "TITLE": {
          "FRONT": "概况",
          "STATS": "测量 %s"
        }
      },
      "COMMENT": {
        "TTL": "评论报告",
        "FOOTER": {
          "USERS": "全部用户",
          "TOOLTYPE": "工具类型 '%s' (%s)",
          "MODEL": "型号 '%s' (%s)",
          "UNIT": "单位 %s",
          "CHECK": "检查 #%d (unit %s, model %s)",
          "STEP": "步骤 '%s' V.%d (%s.%s)",
          "MEASURE": "测量 '%s' V.%d (%s.%s.%s)"
        },
        "ELEMENT": {
          "USER": "用户 %s (%s)",
          "TOOL": "工具单位 %s",
          "UNIT": "单位 %s (%s)",
          "CHECK": "检查 #%d (unit %s, model %s)",
          "MSMNT": "测量 %s 在步骤 %s 程序 %s",
          "STEP": "步骤 %s",
          "PROC": "程序 %s",
          "MSMNTS": "测量 %s",
          "MEAS": "测量 %s",
          "TOOLTYPE": "工具类型%s"
        },
        "TOOLTIP": "生成注释报告"
      },
      "HINT": {
        "CHECKWARN": {
          "INTERNAL": "一些路径失败，但是对机器的正常功能不产生影响",
          "CUSTOMER": "不影响机器正常功能的路径在所定义公差的范围之外"
        }
      },
      "TUREPORT": {
        "HEADER": "用户报告工具单元",
        "FOOTER": " unit <b>%s</b>\"",
        "TITLE": {
          "MODEL": " Unit %s\"",
          "TIME": "%s.<b>%s</b>"
        },
        "CONTINUED": "<i>(cont.)</i>",
        "ENTRY": {
          "MODEL": " Step <b>%s</b>",
          "TIMET": " Unit <b>%s</b>\"",
          "TIMEB": " Step <b>%s</b>"
        }
      }
    },
    "CHANGELOG": {
      "ITEM": {
        "PROCEDURE": "程序 %s",
        "STEP": "步骤",
        "MEASURE": "测量",
        "IMAGE": "图像",
        "DOCUMENT": "文档",
        "MODEL": "型号 ",
        "CHECKTYPE": "检查类型"
      },
      "TYPE": {
        "CREATE": "已创建",
        "CHANGECODE": "代码更改由 {{ oldcode}}",
        "CHANGEFIELD": "字段 {{ field }} 更改",
        "CHANGESEQ": "顺序更改 (影响到多个项目)",
        "CHANGEPID": "程序更新到版本 {{ version }}",
        "FINALIZE": "审定",
        "DELETE": "已删除",
        "CREATEATT": "已创建并添加到 {{ tgt }}",
        "DELETEATT": "已从 {{ tgt }}删除",
        "CREATEACTP": "已添加到型号的程序",
        "DELETEACTP": "已从型号删除的程序",
        "CREATEACTCT": "已添加到型号的检查类型",
        "DELETEACTCT": "已从型号删除的检查类型",
        "PREALLOCATION": "预先定义的任务已修改"
      },
      "VIEWER": {
        "TITLE": "更改日志",
        "EXTRODIRTY": "蓝色标记条目为用户直接更改，需要审定才能激活.",
        "EXTROPOSTFIN": "红色标记条目为审定后项目的更改.",
        "NOENTRIES": "更改日志目前是空的"
      },
      "BUTTON": {
        "OPENDIALOG": "更改日志",
        "CLOSE": "关闭"
      }
    },
    "GSEARCH": {
      "WIZARD": {
        "TITLE": "全局搜索",
        "TEXT1": " an asterisk ('*') is a wildcard for a maximum of 10 characters. A pipe symbol ('|') at the beginning or the end specifies",
        "TEXT2": "搜索执行过程中请选择范围",
        "TEXT3": "请选择要执行的语言（在多个范围里）",
        "TEXT4": "请版本化的对象上选择搜索型号"
      },
      "SCOPE": {
        "ALLALL": {
          "TEXT": "所有对象（全面搜索）",
          "TTIP": " all matching objects shown.\""
        },
        "ALLRECENT": {
          "TEXT": " most recent found object only\"",
          "TTIP": " but only the most recent object is shown.\""
        },
        "FINALL": {
          "TEXT": "选定对象",
          "TTIP": " all matching objects shown.\""
        },
        "FINRECENT": {
          "TEXT": " most recent found object only\"",
          "TTIP": " but only the most recent object is shown.\""
        },
        "LATESTV": {
          "TEXT": "最新版本",
          "TTIP": "只搜索最新的选定的对象"
        },
        "EDITV": {
          "TEXT": "只编辑版本",
          "TTIP": " non finalized versions are searched.\""
        }
      },
      "TYPE": {
        "CHECK": {
          "SELECT": "验盘",
          "DISPLAY": " model <b>{{ object.unit.model.code }}</b>\""
        },
        "MEASUREMENT": {
          "SELECT": "路径",
          "DISPLAY": " model {{ object.check.unit.model.code }}) for measure <b>{{ object.measure.code }}</b> in step <b>{{ object.measure.step.code }}</b>"
        },
        "CHECKTYPE": {
          "SELECT": "验盘类型",
          "DISPLAY": "<u>验盘类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.de }}'</i>)"
        },
        "DEVICETYPE": {
          "SELECT": "装置类型",
          "DISPLAY": "<u>装置类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)"
        },
        "MODEL": {
          "SELECT": "型号",
          "DISPLAY": " version {{ object.version || '(edit)' }}\""
        },
        "UNIT": {
          "SELECT": "单元",
          "DISPLAY": "<u>单元</u> <b>{{ object.code }}</b> 在型号el <b>{{ object.model.code }}</b>"
        },
        "MEASURE": {
          "SELECT": "路径",
          "DISPLAY": " version {{ object.procedure.version || '(edit)' }}\""
        },
        "STEP": {
          "SELECT": "步骤",
          "DISPLAY": " v {{ object.procedure.version || '(edit)' }}\""
        },
        "PROCEDURE": {
          "SELECT": "程序",
          "DISPLAY": " version {{ object.version || '(edit)' }}\""
        },
        "TOOLTYPE": {
          "SELECT": "工具类型",
          "DISPLAY": "<u>工具类型</u> <b>{{ object.code }}</b> (<i>'{{ object.title.en }}'</i>)"
        },
        "TOOLUNIT": {
          "SELECT": "工具单元",
          "DISPLAY": "<u>工具单元</u> <b>{{ object.code }}</b> 在工具类型 <b>{{ object.tooltype.code }}</b>"
        },
        "USER": {
          "SELECT": "用户",
          "DISPLAY": "<u>用户</u> <b>{{ object.username }}</b> (<i>{{ object.realname }}</i>)"
        },
        "NOTICE": {
          "SELECT": "问题报告",
          "DISPLAY": "<u>问题报告</u> <b>{{ object.id }}</b>"
        },
        "CONFIGTABLE": {
          "SELECT": "Configuration tables",
          "DISPLAY": "<u>Configuration table</u> <b>{{ object.id }}</b>"
        },
        "CONFIGENTRY": {
          "SELECT": "Configuration table entries",
          "DISPLAY": "<u>Entry</u> <b>{{ object.code_id }}</b> in configuration table <b>{{ object.configtable_id }}</b>"
        }
      },
      "FIELD": {
        "COMMENT": "注释",
        "CODE": "编码",
        "TITLE": "名称",
        "DESCRIPTION": "描述/线索",
        "CUSTOMER": "客户",
        "USERNAME": "用户名",
        "REALNAME": "真实姓名",
        "TEXT": "描述",
        "COL1": "First column",
        "COL2": "Second column",
        "COL3": "Third column",
        "COL4": "Fourth column",
        "COL5": "Fifth column",
        "COL6": "Sixth column",
        "DELETED": "Metainformation",
        "CALCULATION": "Choice list"
      },
      "BUTTONS": {
        "NEWSEARCH": "新搜索",
        "CLOSE": "关闭"
      },
      "RESULT": {
        "TITLE": "搜索结果",
        "TEXT": " sorted by date of change:\"",
        "TOOMUCH": "（备注：只显示前50个符合条件的对象）",
        "NOMATCH": "没有符合条件的对象"
      }
    },
    "NOTICES": {
      "TOOLTIP": {
        "SHOW": "显示",
        "SHOWALL": "显示所有",
        "HIDE": "隐藏",
        "FILTER": "过滤该栏",
        "NOFILTER": "禁用过滤"
      },
      "EXPORT": {
        "TTL": "发送问题报告",
        "TYPETEXT": "请选择发送报告的文件类型",
        "FILTERTEXT": "请选择范围：",
        "CSV": "逗号分隔值",
        "JSON": "基于JavaScript语言的轻量级的数据交换格式",
        "XML": " 可扩展标示语言",
        "UNARCHIVED": "所有未存档的报告",
        "ALL": "所有报告（包括存档报告）",
        "FILTERED": "所有显示的（波滤过的）报告"
      },
      "FILTEREDIT": {
        "TITLE": "请定义波滤器",
        "ID": " '-<to>' or '<from>-'. Example: '10-20' (all IDs from 10 to 20); '-50' (all IDs up to 50).\"",
        "CATEGORY": "请选择要显示的类别",
        "PATH": "请输入一个搜索名称；该名称将和展示的路径文字相符",
        "TEXT": "请输入一个搜索名称；该名称将和描述相一致",
        "ARTICLE": " prefix the term with a colon ':'.\"",
        "TIMELOSS": "请选择要显示的时间损失值",
        "STATUS": "请选择要显示的状态类型值"
      },
      "TITLE": {
        "PROBLREP": "问题报告",
        "DESC": "描述",
        "PROPOSALS": "建议：",
        "CAT": "类别",
        "TIMELOSS": "时间消耗",
        "ARTNO": "文件编号",
        "ARTDESC": "文件描述",
        "PROBLREPNO": "问题报告编号"
      },
      "TEXT": {
        "DESC": "请输入一个简短有效的描述。可从以下文字框中的几项建议选择或者自己输入文本。",
        "CHOOSECAT": " please select the best matching value in the time loss select box.\"",
        "ARTICLE": "如果该问题报告和某个文件相关，你可在此处输入文件编号和文件描述。",
        "STTCHANGE": "关于状态更改请输入一个简短注释"
      },
      "BUTTON": {
        "USE": "使用",
        "CANCEL": "取消",
        "SEND": "发送",
        "CATEGORIES": "编辑类别",
        "TEMPLATES": "编辑文本建议",
        "EXPORT": "发送",
        "STT_12": "处理报告",
        "STT_21": "取消处理",
        "STT_25": "完成处理",
        "STT_52": "重新处理",
        "STT_59": "存档",
        "CLOSE": "关闭"
      },
      "VIEW": {
        "LOCATION": "路径",
        "CATEGORY": "类别",
        "ARTICLE": "文件",
        "TIMELOSS": "时间损耗",
        "NOTEXT": "无效",
        "DESC": "描述",
        "ID": "编号",
        "PATH": "路径",
        "TEXT": "描述",
        "STATUS": "状态"
      },
      "TIMELOSS": [
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "大概15分钟",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "大概30分钟",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "大概1个小时",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "大概1个多小时",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "大概2个小时",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "大概3个小时",
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "超过4个小时"
      ],
      "ALERT": {
        "CATMISS": {
          "TITLE": "类别不存在",
          "TEXT": "你必须在给定类别里选择"
        },
        "DESCMISS": {
          "TITLE": "描述不存在",
          "TEXT": "描述不存在或太短！"
        },
        "THANKS": {
          "TITLE": "谢谢！",
          "TEXT": "问题报告将会尽快发送并处理！"
        },
        "CONFDEL": {
          "TITLE": "确定删除？",
          "TEXT1": " earlier use of this entry may be modified or corrupted.\"",
          "TEXT2": " though.\""
        }
      },
      "MODAL": {
        "EDITTEXT": {
          "TITLE": "新进入点",
          "TEXT": "请编辑新进入点的文本"
        },
        "NEWTEXT": {
          "TITLE": "编辑进入点",
          "TEXT": "请为新进入点输入文本（至少3个字符）"
        },
        "SNIPPETCAT": {
          "TITLE": "问题报告类别",
          "SNIPPET": "类别"
        },
        "SNIPPETDESC": {
          "TITLE": "问题报告的文本建议",
          "SNIPPET": "文本建议"
        }
      },
      "STATUS": {
        "1": "提交",
        "2": "执行",
        "5": "关闭",
        "OPEN": "<b>已提交</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}",
        "PROCESSED": "<b>已执行</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}",
        "CLOSED": "<b>已关闭</b> by <b>{{ user }}</b> ({{ realname }}) on {{ time }}",
        "ARCHIVED": "<b>已存档</b> on {{ time }}"
      },
      "PATHTYPE": {
        "CHECKSTEP": "检查步骤",
        "CHECKGENERAL": "一般检查"
      },
      "SEGMENT": {
        "MODEL": "型号<b>{{ code }}</b>",
        "UNIT": "单元 <b>{{ code }}</b>",
        "CHECK": "检查<b>{{ id }}</b>",
        "PROCEDURE": "程序 <b>{{ code }}</b>: {{ title.en }}",
        "STEP": "步骤 <b>{{ code }}</b>"
      },
      "CORRECT": {
        "TITLE": "更正",
        "DESCRIPTION": "请编辑描述",
        "TIMELOSS": "请给时间损耗选择一个时间",
        "CATEGORY": "请选择一个新类别",
        "ARTICLE1": "步骤 1/2：请编辑文件编号：",
        "ARTICLE2": "步骤 2/2：请编辑文件描述"
      }
    },
    "SNIPPET": {
      "TITLE": {
        "ADD": "添加"
      },
      "TOOLTIP": {
        "LOCK": " however the entry may not be chosen any longer.\"",
        "UNLOCK": "重新打开进入点，该进入点可能又被关闭",
        "DELETE": "确定后删除进入点",
        "EDIT": "编辑进入点的文本。",
        "REORD": "该进入点可能在每次拖放的过程中移动到其他位置。"
      },
      "BUTTON": {
        "CLOSE": "关闭"
      }
    },
    "CONFIGTABLE": {
      "CONFIGTABLES": "Configuration tables",
      "ALERT": {
        "CONFHEADER": "Are you sure?",
        "DELETE": "Do you want to disable this entry?"
      },
      "EDITOR": {
        "TITLE": {
          "NEW": "New entry",
          "EDIT": "Edit entry"
        }
      },
      "EDIT": {
        "TITLE": "Title",
        "HEADER": "Header",
        "BUTTON": {
          "CANCEL": "@:MEASURE.EDIT.BUTTON.CANCEL",
          "CLOSE": "@:MEASURE.EDIT.BUTTON.CLOSE"
        }
      },
      "LIST": {
        "ENTRY": {
          "EDIT": "Edit entry",
          "DELETE": "Disable entry",
          "UNDELETE": "Enable entry",
          "BLOCKED": "This entry is used by a workflow rule."
        },
        "RELATION": {
          "NONE": "no model related",
          "MODEL": "related model:"
        },
        "VERSION": "Version",
        "TOOLTIP": {
          "IMPORTTABLE": "Import table",
          "NEWTABLE": "New table",
          "GOTOUNITS": "Go to model",
          "SHOWDEL": "Show disabled",
          "CLTF": "@:MODEL.LIST.TOOLTIP.CLTF",
          "CLONE": "Clone table",
          "DELETE": "Disable table",
          "EDIT": "Edit table",
          "REACTIVATE": "Reactivate table"
        },
        "TOPROW": ""
      },
      "VIEW": {
        "CODE": "code",
        "ACTIVE": "active",
        "DISABLED": "disabled"
      },
      "CLONE": {
        "TITLE": "Clone configuration table",
        "TEXT": "Do you want to clone the table {{ title }}?"
      },
      "DELETE": {
        "TITLE": "Disable configuration table",
        "TEXT": "Do you really want to disable the table {{ title }}? You can enable it again later."
      }
    },
    "SETTINGS": {
      "PROC": {
        "TAGS": {
          "TITLE": "Manage procedure tags",
          "ADD": "Add tags",
          "DELETE": "Delete tag",
          "EDIT": "Edit tag",
          "COLLAPSE": "Collapse sub-attributes"
        },
        "EDITTAG": {
          "TITLE": "Edit procedure tag"
        },
        "NEWTAG": {
          "TITLE": "Add procedure tag"
        },
        "TAGVALUE": {
          "ADD": "Add value",
          "DELETE": "Delete value",
          "EDIT": "Edit value"
        },
        "NEWTAGVALUE": {
          "TITLE": "Add value"
        },
        "EDITTAGVALUE": {
          "TITLE": "Edit value"
        }
      },
      "ALERT": {
        "CONFDEL": {
          "TITLE": "Deleting a procedure tag",
          "TEXT": "Are you sure about deleting the selected procedure tag?"
        }
      }
    },
    "MEASUREMENTERRORREPORT": {
      "TITLE": "Report Measurement errors",
      "MEASURES": {
        "PERIODFROM": "Choose the start of the period:",
        "PERIODTO": "Choose the end of the period:",
        "TEXT": "Choose the measurements. Only measurements which have been corrected will be displayed:"
      },
      "SORT": {
        "TEXT": "Choose the sorting:",
        "ERRORCATEGORY": "Error category",
        "MEASURE": "Measure",
        "USER": "User",
        "UNIT": "Unit"
      }
    }
  }
}