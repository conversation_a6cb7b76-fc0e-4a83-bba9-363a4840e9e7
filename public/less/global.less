@import "flex";

// Content / content panes

.ll-topview {
  height: 100%;
  min-width: 1000px;
  width: 100%;
}
.ll-mainview {
  height: calc(~"100% - 51px");
  //min-width: 1000px;
  margin-top: 51px;
  box-sizing: border-box;
  overflow: hidden;
  .clearfix();
}
.ll-addfooter {
  height: calc(~"100% - 102px");
  margin-bottom: 51px;
}

.ll-mainpane {
  padding:0px;
  height:100%;
  overflow: hidden;
}
.ll-stdborders {
  padding:5px;
}

.ll-divider-vertical {
  border-right: 2px solid @theme-color;
}

// Extensions Bootstrap

.input-xs {
  height: 24px;
  padding: 5px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.input-xxs {
  height: 20px;
  padding: 2px 2px;
  font-weight: 300;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 2px;
}

.modal-sm {
  width:350px;
}

.modal-pdf {
  width: 60% !important;
  height: 90% !important;
}

.modal-pdf > .modal-content {
  height:100%;
}

// Miscellaneous

.ll-scroll {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}
.ll-fullheight {
  height:100%;
}
.ll-fullwidth {
  width:100%;
}
.ll-mh500 {
  max-height:500px;
}


// Dropdown menus

.ll-dropdown {
  box-shadow: none;
  padding: 0;
  border-radius: 7px;
}
.ll-dropdown > li {
  border-top: 1px solid @list-group-border;
}
.ll-dropdown > li:first-child {
  border-top: 0 none;
}
.ll-dropdown > li > a {
  height: 41px;
  padding-top: 10px;
}

// Pointers

.ll-ptrstd {
  cursor: default;
}
.ll-ptrhand {
  cursor: pointer;
}
.ll-ptrhelp {
  cursor: help;
}

// Debug

.debugr {
  border:1px solid #ff0000;
  background-color:#ffc0c0;
}
.debugg {
  border:1px solid #008000;
  background-color:#c0ffc0;
}
.debugb {
  border:1px solid #0000ff;
  background-color:#c0c0ff;
}
.debugc {
  border:1px solid #00ffff;
  background-color:#c0ffff;
}
.debugm {
  border:1px solid #ff00ff;
  background-color:#ffc0ff;
}
.debugy {
  border:1px solid #ffff00;
  background-color:#ffffc0;
}

// Textsizes

.ll-textnormal {
  // TODO
}

//

.ll-loadingshade {
  position:fixed;
  top: 0px;
  left: 0px;
  bottom: 0px;
  right: 0px;
  //opacity: 0;
  background-color: #ffffffff;
  z-index: 100002;
}

.ll-btn-default {
  border-color: #666;
}