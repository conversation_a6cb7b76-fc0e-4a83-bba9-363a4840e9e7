.ll-editpanel .ll-body {
  .ll-measurehint {
    font-size:110%;
    font-weight:500;
    margin-bottom:10px;
    border:2px solid lighten(@theme-color,60%);
    background-color: lighten(@theme-color,65%);
    padding: 0px 5px 0px 5px;
  }
  .ll-measureparameter {
    font-size:120%;
    background-color: lighten(@theme-color,60%);
    padding: 0px 5px 0px 5px;
    font-weight:200;
    .ll-measureparbold {
      font-weight:400;
    }
  }
  .ll-measureskip {
    font-size:120%;
    color:@theme-color;
    font-weight:200;
    font-style:italic;
    margin-bottom:10px;
    .ll-measureskipbtns {
      font-style:normal;
    }
  }
  .ll-measureinputintro {
    font-size:120%;
    color:@theme-color;
    font-weight:200;
    font-style:italic;
  }
  .ll-measureinputfield {
    font-size:140%;
    font-weight:300;
    padding-top:2px;
  }
  .ll-measureinputfieldnum {
    font-size:140%;
    font-weight:300;
    text-align:right;
    padding-top:2px;
  }
  .ll-measureinputfieldunit {
    font-size:140%;
    color:@theme-color;
    font-weight:300;
    font-style:italic;
    padding-top:4px;
  }
  .ll-measureinputbutton {
    width:200px;
  }
  .ll-measurestatus {
    font-size:160%;
    border-top: 2px solid @theme-color;
    padding: 5px 0px 0px 0px;
    font-weight:200;
    .ll-measurestatusbold {
      font-weight:400;
    }
  }
}

.ll-wflowinfo {
  padding: 5px 10px 10px 10px;
  .ll-toptitlesml {
    font-size:100%;
    font-style:oblique;
    color: @theme-color;
    font-weight:200;
    margin: 0;
    padding: 0;
    .ll-emph {
      font-weight:400;
    }
  }
  .ll-subtitlesml {
    font-size:90%;
    font-style:oblique;
    color: @theme-color;
    font-weight:200;
    height: 20px;
  }
  .ll-title {
    padding: 0px;
    border-bottom: 2px solid @theme-color;
    color: @theme-color;
    font-size:180%;
    font-style:normal;
    font-weight:200;
    margin: 0;
    .ll-emph {
      font-weight:400;
    }
    .ll-statusicon {
      font-size:90%;
      font-weight:600;
    }
    .ll-status {
      font-size:80%;
      font-weight:500;
    }
    .ll-subtitle {
      font-size:60%;
      font-style:oblique;
      padding:0px;
      margin:0px;
      .ll-emph {
        font-weight:400;
      }
    }
  }
  .ll-testrunbar {
    padding: 0px;
    border-bottom: 2px solid @theme-color;
    color: @alert-danger-text;
    background-color: lighten(@alert-danger-text, 30%);
    font-size:80%;
    font-style:oblique;
    font-weight:400;
    text-align: center;
    margin: 0;
  }
  .ll-infogroup {
    margin-bottom:20px;
    .ll-icon {
      font-size:250%;
      color: lighten(@theme-color, 60%);
    }
    .ll-text {
      font-size:100%;
      font-weight:300;
      color:black;
    }
    .ll-emph {
      font-weight:500;
    }
    .ll-italemph {
      font-weight:500;
      font-style:italic;
    }
    .ll-deemph {
      font-weight:200;
      font-style:italic;
    }
  }
  .ll-highlight {
    background-color: lighten(@theme-color, 60%);
    border: 5px solid lighten(@theme-color, 60%);
    left: -5px;
    position: relative;

  }
  .ll-warning {
    font-size:110%;
    font-style:italic;
    font-weight:300;
  }
  .ll-buttonbar {
    .ll-button {
      margin-left:10px;
    }
  }
  .ll-highlight {
    color:red;
  }
  .ll-table {
    border-right:1px solid lighten(@theme-color,40%);
    border-bottom:1px solid lighten(@theme-color,40%);
  }

  .ll-imgsel {
    font-size:30pt;
    margin: 0px 15px 0px 15px;
    .text-primary;
  }
  .ll-imagebox {
    width:320px;
    height:250px;
    .ll-image {
      border:2px solid lighten(@theme-color,60%);
      background-color:lighten(@theme-color,60%);
    }
  }
  .ll-procedure-timer {
    position: relative;
    cursor: pointer;
    margin: 5px -10px -10px -10px;
    height: 20px;

    timer {
      height: 100%;
      width: 100%;

      .ll-timer-bar {
        height: 100%;
      }
      .ll-timer-bar-stopped {
        background-color: #af4645;
      }
      .ll-timer-bar-running {
        background-color: #42af43;
      }
    }

    span {
      position: absolute;
      width: 100%;
      text-align: center;
    }

    .progress {
      border-radius: 0;
    }
  }
}
.ll-wflowlist {
  border-left:3px solid @theme-color;
  font-weight:200;
  .ll-procedureblock {
    border-bottom:2px solid @theme-color;
    .ll-procedure {
      padding:4px 5px 4px 5px;
      background-color: lighten(@theme-color, 55%);
      font-size:120%;
      border-bottom:1px solid @theme-color;
    }
  }
  .ll-stepblock {
    border-bottom:1px solid @theme-color;
    .ll-step {
      padding:2px 5px 2px 5px;
      background-color: lighten(@theme-color, 64%);
      font-size:100%;
    }
  }
  .ll-measure {
    padding:0px 5px 0px 5px;
    font-size:90%;
  }
  .ll-title {
    font-weight:400;
  }

  .ll-assno {
    color: lighten(@theme-color, 40%);
  }
  .ll-asssel {
    color: red;
    text-shadow: -1px -1px 0 black, 1px -1px 0 black, -1px 1px 0 black, 1px 1px 0 black;
  }
  .ll-highlight {
    color: red;
  }

  .ll-filter {
    border-top:3px solid @theme-color;
    background-color: @theme-color;
    font-weight:400;
    font-size:90%;
    color: lighten(@theme-color, 60%);
    input {
      padding: 0px 5px 0px 5px;
      color: black;
      border: 1px solid black;
    }
    .ll-tbutton {
      font-weight:300;
      .ll-ptrhand();
      color: lighten(@theme-color, 65%);
      text-align: center;
      .ll-selected {
        font-weight:500;
        color: white;
        text-shadow: -1px -1px 0 black, 1px -1px 0 black, -1px 1px 0 black, 1px 1px 0 black;
      }
    }
  }
  .ll-filtermob {
    border-top:3px solid @theme-color;
    background-color: @theme-color;
    font-weight:400;
    font-size:120%;
    color: lighten(@theme-color, 60%);
    padding: 0px 5px 0px 5px;
    input {
      padding: 0px 5px 0px 5px;
      color: black;
      border: 1px solid black;
    }
    .ll-tbutton {
      font-weight:300;
      .flexitem-1;
      .ll-ptrhand();
      color: lighten(@theme-color, 65%);
      text-align: center;
      .ll-selected {
        font-weight:500;
        color: white;
        text-shadow: -1px -1px 0 black, 1px -1px 0 black, -1px 1px 0 black, 1px 1px 0 black;
      }
    }
  }
}

.ll-wflowsublist {
  border-top:3px solid @theme-color;
  .ll-listheader {
    padding:5px;
    font-size:120%;
    font-weight:400;
    color:black;
    background-color:lighten(@theme-color,40%);
    .ll-button {
      background-color:lighten(@theme-color,50%);
      font-weight:300;
      padding: 0px 2px 0px 2px;
      border: 1px solid lighten(@theme-color,30%);
    }
  }
  .ll-listitem {
    font-size:120%;
    font-weight:300;
    padding:2px 5px 2px 5px;
    .ll-starticon {
      font-size:120%;
    }
    .ll-addicon {
      margin-left:10px;
    }
    .ll-tiny {
      font-size:70%;
      font-weight:200;
    }
    .ll-emph {
      font-weight:400;
    }
    .ll-deemph {
      font-weight:200;
      font-style:italic;
    }
  }
}

.ll-viewbar {
  border-bottom:2px solid @theme-color;
  background-color: lighten(@theme-color, 55%);
  padding: 3px 5px 3px 5px;
  font-size: 120%;
  margin-bottom: 10px;
  .ll-viewitem {
    text-align: center;
  }
  .ll-selected {
    background-color: lighten(@theme-color, 65%);
  }
  .ll-disabled {
    background-color: lighten(@theme-color, 55%);
    color: lighten(@theme-color, 45%);
  }
}

.ll-docgalbodymob {
  padding:5px;
  font-size:100%;
  font-weight:300;
  color:black;
  .ll-data {
    font-size: 80%;
    font-weight: 200;
    font-style: italic;
  }
}

.ll-tngallerymob {
  display:flex;
  display:-webkit-flex;
  flex-flow:row wrap;
  -webkit-flex-flow:row wrap;
  justify-content:center;
  -webkit-justify-content:center;
  -webkit-align-content:flex-start;
  align-content:flex-start;
  .ll-thumbnail {
    -webkit-flex:0 0 114px;
    flex:0 0 114px;
    height:120px;
    border:1px solid lighten(@theme-color,60%);
    white-space:nowrap;
    overflow:hidden;
    margin:2px;
    background-color:lighten(@theme-color,60%);
  }
}

.ll-measurebar {
  border-left:3px solid @theme-color;
  margin-top: -3px;
  -webkit-flex:0 0 450px;
  flex:0 0 450px;
  white-space: nowrap;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 90%;
}

.ll-mediabar {
  border-left:3px solid @theme-color;
  .ll-imagebox {
    padding:10px;
    .ll-image {
      border:2px solid lighten(@theme-color,60%);
      background-color:lighten(@theme-color,60%);
    }
  }
  .ll-caption {
    padding:2px 5px 2px 10px;
    font-weight:300;
    font-size:100%;
    background-color: lighten(@theme-color,60%);
  }
  .ll-tngallery {
    border-top:3px solid @theme-color;
    display:flex;
    display:-webkit-flex;
    flex-flow:row wrap;
    -webkit-flex-flow:row wrap;
    justify-content:center;
    -webkit-justify-content:center;
    -webkit-align-content:flex-start;
    align-content:flex-start;
    .ll-thumbnail {
      -webkit-flex:0 0 114px;
      flex:0 0 114px;
      height:120px;
      border:1px solid lighten(@theme-color,60%);
      white-space:nowrap;
      overflow:hidden;
      margin:2px;
      background-color:lighten(@theme-color,60%);
    }
  }
  .ll-docgallery {
    border-top:3px solid @theme-color;
    .ll-docgaltitle {
      padding:5px;
      font-size:120%;
      font-weight:400;
      color:black;
      background-color:lighten(@theme-color,40%);
    }
    .ll-docgalbody {
      padding:5px;
      font-size:90%;
      font-weight:300;
      color:black;
      .ll-data {
        font-size: 80%;
        font-weight: 200;
        font-style: italic;
      }
    }
  }
}

