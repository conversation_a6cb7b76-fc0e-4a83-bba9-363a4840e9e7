// Languageinfo

.ll-langinfo {
  font-size: 10pt;
  font-weight: 200;
  margin-top: 3px;
  margin-right: 30px;
  .ll-lang {
    border: 1px solid lighten(@theme-color, 60%);
    margin-left: 5px;
    padding: 0px 2px 0px 2px;
  }
  .ll-langsel {
    background-color: lighten(@theme-color, 65%);
  }
  .ll-langfkey {
    font-size: 70%;
    font-weight: 300;

  }
}

// TextAngular

.ll-ta-btn-toolbar {
  background-color: #d0d0d0;
  padding: 2px;
}
.ll-ta-focussed {
  background-color: #66afe9;
}
.ll-ta-btn-group {
}
.ll-ta-btn {
  padding: 2px 5px 2px 5px;
}
.ll-ta-btn-active {
}
.ll-ta-disabled {
}
.ll-ta-text-editor {
  border-radius: 0;
  max-height: 410px;
  white-space: normal;
}
.ll-ta-html-editor {
  border-radius: 0;
  max-height: 410px;
  white-space: normal;
}

// UserMgt

.ll-viewpanel {
  .ll-info {
    .ll-header {
      .ll-desc {
        font-weight: 200;
      }
    }
  }
  .ll-list {
    .ll-body {
      .ll-microheader {
        margin: 0px 5px 0px 5px;
        padding: 2px;
        font-size: large;
        font-weight: 400;
        font-style: italic;
        border-bottom: 1px solid lighten(@theme-color, 30%);
        background-color: lighten(@theme-color, 60%);
        .ll-actions {
          font-size: 100%;
          font-style: normal;
          font-weight: 100;
          cursor: pointer;
        }
      }
      .ll-item {
        .ll-grantcode {
          font-size: large;
          font-weight: 300;
        }
        .ll-grantname {
          font-size: medium;
          font-weight: 200;
          font-style: italic;
          .flexcenter;
        }
        .ll-multigrant {
          font-weight: 500;
        }
      }
    }
  }
}


