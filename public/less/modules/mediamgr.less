.ll-mediamgr {
  .ll-mediapane {
    height:600px;
    border:1px solid #dddddd;
    border-top:none;
    padding:5px;
    .ll-imagepreview {
      .ll-image {
        margin:5px;
        border:3px solid @theme-color;
      }
    }
    .ll-imagelist {
      overflow-y: auto !important;
      overflow-x: hidden !important;
      border-left:2px solid @theme-color;
      .ll-imageitem {
        font-size:normal;
        padding:5px 5px 5px 5px;
        .ll-imagename {
          font-weight:300;
        }
        .ll-imagedata {
          font-weight:200;
        }
      }
    }
    .ll-imagethumbnailgallery {
      overflow-x: auto !important;
      overflow-y: hidden !important;
      border-top:2px solid @theme-color;
      .ll-thumbnailblock {
        border: 2px solid lighten(@theme-color, 60%);
        margin:5px;
        .ll-thumbnail {
          border:2px solid @theme-color;
        }
      }
    }

    .ll-doclist {
      overflow-y: auto !important;
      overflow-x: hidden !important;
      .ll-docitem {
        font-size:normal;
        padding:5px 5px 5px 5px;
        .ll-docname {
          font-weight:300;
        }
        .ll-docdata {
          font-weight:200;
        }
      }
    }

    .ll-uploadheader {
      font-size:x-large;
      font-weight:300;
    }
    .ll-uploaddescription {
      font-size:normal;
      font-weight:200;
    }
    .ll-uploadqueue {
      overflow-y: auto !important;
      overflow-x: hidden !important;

      .ll-uploaditem {
        font-size:normal;
        padding:5px 5px 5px 5px;
        .ll-itemname {
          font-weight:300;
        }
        .ll-itemsize {
          font-weight:200;
        }
        .ll-itemstatus {
          font-weight:300;
          font-style:italic;
        }
      }
    }

    .ll-listbgodd {
      background-color: fade(lighten(@theme-color, 60%),50%);
    }
    .ll-listbgeven {
      background-color: fade(#ffffff, 50%);
    }

    .ll-action {
      font-size:140%;
      font-style:normal;
      font-weight:100;
      cursor: pointer;
    }
    .ll-dragaction {
      .ll-action();
      cursor: move;
    }
    .ll-draggeditem {
      padding: 2px;
      margin: 5px 10px 5px 10px;
      background-color: lighten(@theme-color, 30%);
    }
  }
}