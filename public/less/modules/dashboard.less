.ll-dashboard {
  .ll-welcome {
    background-color:  fadeout(lighten(@theme-color, 30%),80%);
    padding:5px 25px 5px 25px;
    margin:20px 0px 10px 0px;
    font-size: 60px;
    font-weight: 300;
    font-style: italic;
    .ll-version {
      font-size: 40px;
      font-weight: 200;
      text-align: right;
      font-style: normal;
    }
    .ll-adddash {
      font-size: 20px;
      font-weight: 200;
      text-align: right;
      .text-primary();
      display:block;
    }
  }
  .ll-dbtitle {
    background-color:  fadeout(lighten(@theme-color, 30%),80%);
    padding:2px 5px 2px 5px;
    margin:5px 0px 5px 0px;
    font-size: 14px;
    font-weight: 300;
    font-style: normal;
    b {
      font-weight: 500;
    }
    .ll-editpen {
      .flextop;
      vertical-align: super;
      font-size: 60%;
    }
    .ll-icon {
      .flexitem-fix20;
      .text-primary();
    }
  }
  .ll-dbtitlemob {
    background-color:  fadeout(lighten(@theme-color, 30%),80%);
    padding:2px 5px 2px 5px;
    margin:5px 0px 5px 0px;
    font-size: 18px;
    font-weight: 300;
    font-style: normal;
    b {
      font-weight: 500;
    }
    .ll-editpen {
      .flextop;
      vertical-align: super;
      font-size: 60%;
    }
    .ll-icon {
      .flexitem-fix40;
      .text-primary();
      i {
        background-color: lighten(@theme-color, 55%);
        padding: 4px 10px 2px 10px;
      }
    }
  }
  .ll-logo {
    position:fixed;
    top:50px;
    left:20px;
    z-index:-1;
  }
}
.ll-dashblock {
  margin:10px;
  border:3px solid @theme-color;
  background-color: fadeout(#ffffff, 30%);
  .ll-header {
    padding:5px;
    margin:0px;
    color: white;
    background-color:  fadeout(lighten(@theme-color, 30%),30%);
    font-size:120%;
    font-style:normal;
    font-weight:300;
    .ll-quickfilter {
      //background-color: lighten(@theme-color, 60%);
      border: none;
      //font: normal;
      //font-size: normal;
      outline: none;
      height: 80%;
      color: black;
      vertical-align: middle;
    }
    .ll-filterused {
    }
    .ll-nofilter {
      color: fadeout(lighten(@theme-color, 30%),30%);
      text-shadow: -1px -1px 0 white, 1px -1px 0 white, -1px 1px 0 white, 1px 1px 0 white;
    }
    .ll-editpen {
      .flextop;
      vertical-align: super;
      font-size: 50%;
    }
    .ll-icon {}
  }
  .ll-headermob {
    padding:5px;
    margin:0px;
    color: white;
    background-color:  fadeout(lighten(@theme-color, 30%),30%);
    font-size:120%;
    font-style:normal;
    font-weight:300;
    .ll-filterused {
    }
    .ll-nofilter {
      color: @theme-color;
    }
    .ll-editpen {
      .flextop;
      vertical-align: super;
      font-size: 50%;
    }
    .ll-icon {
      .flexitem-fix40;
      font-size: 18px;
      i {
        background-color: lighten(@theme-color, 20%);
        padding: 4px 10px 2px 10px;
      }
    }

  }
  .ll-list {
    padding: 5px;
    .ll-item {
      border-bottom:1px solid @theme-color;
      .ll-ptrhand;
      .ll-topline {
        font-size:100%;
        font-style:normal;
        font-weight:300;
        b {
          font-weight:500;
        }
      }
      .ll-bottomline {
        font-size:90%;
        font-style:normal;
        font-weight:200;
        font-style:italic;
        b {
          font-style:normal;
          font-weight:400;
        }
      }
      .ll-progress {
        margin: 0px 0px 0px 10px;
        padding: 5px 5px 0px 0px;
        font-size: 50%;
        font-weight: 100;
        height: 20px;
        .ll-colpass {
          color: black;
          background-color: @brand-success;
        }
        .ll-colfail {
          color: black;
          background-color: @brand-danger;
        }
        .ll-colunfin {
          color: white;
          background-color: grey;
        }
        .progress {
          margin-top:0px;
          padding-top:0px;
          height:15px;
          .progress-bar {
            line-height:15px;
          }
        }
      }
    }
  }
}
