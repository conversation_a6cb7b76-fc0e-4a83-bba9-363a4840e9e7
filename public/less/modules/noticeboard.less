.ll-noticeboard {
  .ll-nbbuttons {
    padding: 10px 0px 0px 10px;
    .ll-nbaction {
      font-size: 100%;
      font-weight: 500;
      color: @theme-color;
      padding-right: 10px;
    }
  }
  .ll-nbfilter {
    margin: 10px 0px 0px 0px;
    background-color: lighten(@theme-color, 40%);
    border-bottom: 2px solid @theme-color;
    border-top: 2px solid @theme-color;
    padding: 4px 5px 4px 0px;
    .ll-nbfelem {
      background-color: lighten(@theme-color, 60%);
      padding: 0px 2px 0px 2px;
      margin: 0px 0px 0px 5px;
      font-size: 100%;
      .ll-nbftitle {
        padding: 0px 0px 0px 3px;
      }
      .ll-nbficons {
      }
    }
  }
  .ll-nblist {
    margin: 0px;
    b {
      font-weight: 500;
    }
    .ll-nbodditem {
      background-color: lighten(@theme-color, 60%);
    }
    .ll-nbitem {
      font-size: small;
      font-weight: 300;
      .ll-nbcell {
        padding: 2px 5px 2px 5px;
      }
      .ll-nbelhdr {
        font-weight: 100;
        font-style: italic;
      }
      .ll-nbid {
        font-size: 120%;
        font-weight: 400;
      }
      .ll-tabletext {
        white-space: normal;
      }
      .ll-nbpath {
      }
      .ll-nbtext {
        font-size: 90%;
      }
      .ll-nbcat, .ll-nbtloss, .ll-nbart {
      }
      .ll-nbstatus {
        font-style: italic;
      }
      .ll-disabled {
        font-style: italic;
        text-decoration: line-through;
      }
    }
  }
}
