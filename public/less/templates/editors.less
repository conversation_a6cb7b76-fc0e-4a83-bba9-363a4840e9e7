.ll-editpanel {
  padding: 5px 15px 15px 5px;
  height: 100%;
  .ll-header {
    padding: 0px;
    margin: 0px 0px 10px 0px;
    border-bottom: 2px solid @theme-color;
    color: @theme-color;
    font-size:140%;
    font-style:normal;
    font-weight:400;
    .ll-subheader {
      font-size:70%;
      font-style:oblique;
      font-weight:300;
      text-align:right;
    }
  }
  .ll-bodylimit {
    max-height: 550px;
  }
  .ll-body {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    .ll-attribute {
      font-size: 110%;
      color: @theme-color;
      font-weight: 320;
      padding: 1px 5px 1px 5px;
      .ll-subattribute {
        font-size: 90%;
        font-weight: 200;
      }
    }
    .ll-snippet {
      font-size: 90%;
      color: @theme-color;
      font-weight: 200;
      padding: 1px 5px 1px 5px;
      .ll-icon {
        font-size: 120%;
        font-weight: 400;
        .flexvcnt-c;
      }
      .ll-locked {
        text-decoration: line-through;
      }
    }
    .ll-draggeditem {
      padding: 2px;
      margin: 5px 10px 5px 10px;
      background-color: lighten(@theme-color, 30%);
    }
    .ll-snippetodd {
      background-color: lighten(@theme-color, 60%);
    }
    .ll-snippetadd {
      font-size: 100%;
      font-weight: 300;
      color: forestgreen;
      padding: 1px 5px 1px 5px;
    }
    .ll-blockhdr {
      font-size:100%;
      font-style:italic;
      font-weight:200;
      color: @theme-color;
      .flexitem-0();
      .flexwrap();
      margin: 5px 0px 0px 0px;
    }
    .ll-afterhdr {
      margin: 5px 0px 5px 0px !important;
    }
    .ll-block {
      font-size: 120%;
      font-style: normal;
      font-weight: 200;
      margin: 20px 0px 5px 0px;
      .flexcnt();
      .ll-key {
        margin-top: 5px;
        font-size:100%;
        font-style:normal;
        font-weight:300;
        color: @theme-color;
        .flexitem-fix150();
        .ll-editicon {
          font-size: 70%;
          vertical-align: top;
          color: lighten(@theme-color,30%);
        }
      }
      .ll-edit {
        font-size:100%;
        font-style:normal;
        font-weight:300;
        .flexitem-1();
      }
      .ll-time-edit-text {
        font-size:100%;
        font-style:normal;
        font-weight:200;
        line-height: 200%;
        margin-left: 0.5em;
        margin-right: 1em;
      }
      .ll-processing-time-input {
        width: 4em;
      }
      .ll-pltext {
        margin-top: 7px;
        font-size:90%;
        font-style:normal;
        font-weight:300;
        .flexitem-1();
        .flexwrap();
        b {
          font-weight: 500;
        }
        .ll-plcomment {
          font-size: 80%;
          padding-left: 30px;
          font-weight: 200;
          font-style: italic;
          .flexwrap();
        }
      }
      .ll-inputsm {
        font-size:90%;
        padding: 0px 5px 0px 5px;
        font-weight:200;
      }
      .ll-input {
        font-size:110%;
        padding: 0px 7px 0px 7px;
        font-weight:300;
      }
      .ll-inputlg {
        font-size:140%;
        padding: 0px 5px 0px 5px;
        font-weight:200;
      }
      .ll-justright {
        text-align:right;
      }
    }
    .ll-matrix {
      font-size:medium;
      font-weight:200;
      .ll-rotate {
        .rotate(90deg);
      }
      .ll-ytitle9 {
        width:440px;
        text-align: center;
      }
      .ll-ytitle8 {
        width:400px;
        text-align: center;
      }
      .ll-ytitle7 {
        width:360px;
        text-align: center;
      }
      .ll-ytitle6 {
        width:320px;
        text-align: center;
      }
      .ll-ytitle5 {
        width:280px;
        text-align: center;
      }
      .ll-ytitle4 {
        width:240px;
        text-align: center;
      }
      .ll-ytitle3 {
        width:200px;
        text-align: center;
      }
      .ll-ytitle2 {
        width:160px;
        text-align: center;
      }
      .ll-ytitle1 {
        width:120px;
        text-align: center;
      }
      .ll-ystitle {
        width:40px;
        text-align: center;
      }
      .ll-xtitle {
        text-align: center;
      }
      .ll-xstitle {
        text-align: center;
      }
      .ll-xstitlesml {
        font-size:70%;
        padding-left:10px;
        text-align: left;
        height:14px;
      }
      .ll-inputbox {
        margin: -14px 3px 3px 3px;
        overflow: visible;
      }
      .ll-inputboxsml {
        margin: 0px 3px 3px 3px;
        overflow: visible;
      }
      .ll-input {
        font-size:medium;
        padding: 2px;
      }
      .ll-inputsml {
        font-size:medium;
        height:25px;
        max-height:25px;
        padding: 0px 2px 0px 2px;
      }
    }
    .ll-tooltip-overflow {
      overflow: visible;
    }
  }
  .ll-buttonbar {
  }
  .ll-numkeyboard {
    background-color: lighten(@theme-color, 68%);
    border: 1px solid lighten(@theme-color, 60%);
    margin-left: 10px;
    padding: 5px;
    border-radius: 3px;
    .ll-nkbdisp {
      margin: 5px;
      padding: 5px;
      color: @theme-color;
      border-radius: 3px;
      background-color: lighten(@theme-color, 65%);
      text-align: right;
      font-size: 16px;
      height: 30px;
      .flexbottom;
    }
    .ll-nkbbut {
      margin: 2px;
      padding: 5px;
      border: 1px solid #909090;
      box-shadow: 2px 2px 1px #707070;
      color: #909090;
      border-radius: 3px;
      background-color: #ebebeb;
      width: 70px;
      height: 50px;
      text-align: center;
      font-size: 30px;
    }
    .ll-nkbbutpressed {
      background-color: lighten(@theme-color, 60%);
      box-shadow: none;
      margin: 4px 0px 0px 4px;
    }
  }
}
.ll-editpanel-inmodal {
  border:5px solid lighten(@theme-color, 60%);
  padding: 10px;
}
// Overwrite the textAngular defaults
.ta-scroll-window > .ta-bind {
  min-height: 100px !important;
}
.ta-editor.ta-html, .ta-scroll-window.form-control {
  min-height: 100px !important;
}

