.ll-viewpanel {
  padding: 5px;
  height: 100%;
  .ll-info {
    padding: 0px;
    height: 100%;
    .ll-header {
      padding: 0px;
      margin: 0px 0px 10px 0px;
      border-bottom: 2px solid @theme-color;
      color: @theme-color;
      font-size:140%;
      font-style:normal;
      font-weight:400;
      .ll-code {
        font-size:70%;
        font-style:oblique;
        font-weight:200;
      }
    }
    .ll-version {
      font-size:110%;
      font-style:oblique;
      font-weight:400;
      text-align:right;
    }
    .ll-body {
      overflow-y: auto !important;
      overflow-x: hidden !important;
      .ll-message {
        font-size: 110%;
        font-style: italic;
        font-weight: 300;
        margin: 20px 0px 5px 0px;
      }
      .ll-title {
        font-size: 90%;
        font-style: normal;
        font-weight: 200;
        color: @theme-color;
        border-bottom: 1px solid lighten(@theme-color, 30%);
        margin: 20px 0px 5px 0px;
      }
      .ll-value {
        font-size:100%;
        font-style:normal;
        font-weight:300;
        .ll-newcoll {
          font-weight:100;
          font-style:italic;
        }
        .ll-emptycoll {
          font-weight:300;
          font-style:italic;
          color:red;
        }
      }
      .ll-modifyicon {
        .ll-ptrhand;
        .text-primary;
        margin-right:10px;
      }
    }
    .ll-buttonbar {
    }
  }
  .ll-list {
    .ll-header {
      padding: 0px 0px 0px 0px;
      margin: 0px 5px 10px 5px;
      border-bottom: 2px solid @theme-color;
      color: @theme-color;
      font-size:140%;
      font-style:normal;
      font-weight:400;
    }
    .ll-dropbox {
      font-size: medium;
      font-weight: 200;
      margin: 0px 5px 0px 5px;
      padding:5px;
      height:100px;
      border-bottom: 2px solid @theme-color;
      .ll-drophint {
        font-weight: 400;
      }
      .ll-dropinfo {
        font-size: 80%;
        font-weight: 200;
      }
      .ll-dropicon {
        font-size: 400%;
        color: lighten(@theme-color, 30%);
      }
    }
    .ll-body {
      overflow-y: auto !important;
      overflow-x: hidden !important;
      .ll-item {
        margin: 0px 5px 0px 5px;
        padding: 2px;
        font-size: small;
        border-bottom: 1px solid lighten(@theme-color, 30%);
        .ll-title {
          font-size:120%;
          font-style:normal;
          font-weight:400;
        }
        .ll-counter {
          font-size:90%;
          font-style:normal;
          font-weight:100;
          .ll-code {
            font-weight:400;
          }
        }
        .ll-actions {
          .flexitem-fix20();
          .flexcenter();
          .text-right();
          font-size:140%;
          font-style:normal;
          font-weight:100;
          cursor: pointer;
          .ll-subscript {
            font-size:70%;
          }
        }
        .ll-prealloc0 {
          padding-right:2px;
          .text-primary();
        }
        .ll-prealloc1 {
          padding-right:2px;
          .text-success();
        }
        .ll-prealloc2 {
          padding-right:2px;
          color: lighten(@alert-success-text, 60%);
          text-shadow: -1px -1px 0 @alert-success-text, 1px -1px 0 @alert-success-text, -1px 1px 0 @alert-success-text, 1px 1px 0 @alert-success-text;
        }
        .ll-prealloc3 {
          padding-right:2px;
          .text-danger();
        }
        .ll-dragaction {
          .ll-actions();
          cursor: move;
          margin-right:10px;
        }
      }
      .ll-draggeditem {
        padding: 2px;
        margin: 5px 10px 5px 10px;
        background-color: lighten(@theme-color, 30%);
      }
    }
  }
}
