.ll-modalmsg-primary {
  border:5px solid #428bca;
  padding:10px;
  .ll-headcol {
  }
  .ll-header {
    padding: 0px 0px 0px 0px;
    margin: 0px 5px 10px 5px;
    border-bottom: 2px solid @theme-color;
    color: @theme-color;
    font-size:140%;
    font-weight:400;
  }
  .ll-body {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: 400px;
    .ll-blockheader {
      font-size: 110%;
      font-weight:500;
    }
  }
}
.ll-modalmsg-success {
  border:5px solid #3B7E36;
  padding:10px;
  .ll-headcol {
    color: #3B7E36;
    border-bottom: 2px solid #3B7E36;
  }
}
.ll-modalmsg-warning {
  border:5px solid #C3BD2E;
  padding:10px;
  .ll-headcol {
    color: #C3BD2E;
    border-bottom: 2px solid #C3BD2E;
  }
}
.ll-modalmsg-danger {
  border:5px solid #C3382E;
  padding:10px;
  .ll-headcol {
    color: #C3382E;
    border-bottom: 2px solid #C3382E;
  }
}

.ll-videopane {
  height:510px;
  background-color: black;
}

.ll-imagepane {
  height:510px;
  background-color: black;
  .ll-image {
    border:2px solid lighten(@theme-color,60%);
    background-color:lighten(@theme-color,60%);
  }
  .ll-imagemob {
    border:2px solid lighten(@theme-color,60%);
    background-color:black;
    overflow-x: auto;
    overflow-y: auto;
  }
}
.ll-imagezoombar {

}


.ll-editpanel {
  .ll-body {
    .ll-meditortext {
      font-size:120%;
      font-weight:300;
      font-style:normal;
    }
    .ll-meditorinput {
      font-size:130%;
      font-weight:200;
      font-style:normal;
    }
    .ll-meditorarea {
      font-size:110%;
      font-weight:200;
      font-style:normal;
    }
    .ll-meditorselect {
      font-size:120%;
      font-style:italic;
      font-weight:300;
    }
  }
}

  .ll-pdfpane {
    height:96%;
    .ll-pdf {
      margin:3px 0px 3px 6px;
      width: 99%;
      height: calc(99% - 3.2em);
      border:2px solid lighten(@theme-color, 40%);
    }
    .ll-download-button {
      float: right;
      margin-right: 1em;
    }
  }

.ll-modeladdmodal {
  padding: 5px 15px 15px 5px;
  height: 100%;
  font-size:medium;
  font-style:normal;
  .ll-header {
    padding: 0px 0px 0px 0px;
    margin: 0px 5px 10px 5px;
    border-bottom: 2px solid @theme-color;
    color: @theme-color;
    font-size:140%;
    font-weight:400;
  }
  .ll-subheader {
    margin: 0px 5px 0px 5px;
    font-size:100%;
    font-weight:200;
  }
  .ll-allsel {
    font-size: 110%;
    color: lighten(@theme-color, 10%);
    margin-right: 10px;
  }
  .ll-body {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: 400px;
    .ll-blockheader {
      background-color: lighten(@theme-color, 60%);
      font-weight:500;
    }
    .ll-item {
      margin: 0px 5px 0px 5px;
      padding: 2px;
      font-size: small;
      border-bottom: 1px solid lighten(@theme-color, 30%);
      .ll-title {
        font-size: 120%;
        font-style: normal;
        font-weight: 400;
      }
      .ll-counter {
        font-size: 90%;
        font-style: normal;
        font-weight: 100;
        .ll-code {
          font-weight: 300;
        }
        .ll-emph {
          font-weight: 600;
        }
      }
      .ll-actions {
        font-size: 140%;
        font-style: normal;
        font-weight: 100;
        cursor: pointer;
      }
    }
  }
}

.ll-wfmodeditor {
  .ll-blocktitle {
    font-size:120%;
    font-weight:400;
    background-color:lighten(@theme-color, 60%);
    border-bottom:1px solid @theme-color;
    margin:10px 0px 0px 0px;
    padding-left: 2px;
  }
  .ll-blockbody {
    margin:5px 0px 0px 0px;
    font-size:large;
  }
  .ll-listitem {
    font-size:100%;
    font-weight:200;
    .ll-actdesc {
      border-top:1px solid lighten(@theme-color,40%);
    }
  }
  .ll-seltext {
    padding-top:3px;
    font-size:100%;
    font-weight:200;
  }
  .ll-selbox {
    font-size:100%;
    font-weight:300;
    padding:2px 5px 2px 5px;
  }
  .ll-intro {
    font-style:italic;
  }
}

.ll-msrstat {
  .ll-blocktitle {
    font-size:120%;
    font-weight:400;
    background-color:lighten(@theme-color, 60%);
    border-bottom:1px solid @theme-color;
    margin:10px 0px 0px 0px;
    padding-left: 2px;
  }
  .ll-blockbody {
    margin:5px 0px 0px 0px;
    font-size:large;
  }
  .ll-listitem {
    font-size:100%;
    font-weight:200;
    b {
      font-weight:400;
    }
  }
  .ll-intro {
    font-weight:200;
    font-size:90%;
    font-style:italic;
  }
  .ll-subbuttonbar {
    margin-top:15px;
  }
  .ll-buttonbar {
    border-top:1px solid @theme-color;
    padding-top:10px;
  }
}

.ll-typelisteditor {
    height:500px;
  .ll-intro {
    font-size:large;
    font-weight:300;
    font-style:italic;
  }
  .ll-listblock {
    border-top:1px solid @theme-color;
    margin-bottom:20px;
    .ll-listitem {
      border-bottom:1px solid @theme-color;
      .ll-itemtitle {
        font-size:medium;
        font-weight:300;
      }
      .ll-itemcode {
        font-weight:200;
      }
      .ll-iteminfo {
        font-size:small;
        font-weight:200;
      }
    }
  }
}

.ll-changelog {
  .ll-intro {

  }
  .ll-logline {
    font-size: 90%;
    font-weight: 200;
    .ll-emph {
      font-weight: 400;
    }
  }
  .ll-odd {
    background-color:lighten(@theme-color, 60%);
  }
  .ll-styledirty {
    color: blue;
  }
  .ll-stylepostfin {
    color: red;
  }
}

.ll-sresultview {
  .ll-intro {
    font-weight: 200;
  }
  .ll-hint {
    margin-top: 20px;
    font-weight: 500;
  }
  .ll-resbody {
    margin-top: 20px;
    max-height: 400px;
    .ll-result {
      border-bottom: 1px solid @theme-color;
      font-size: 100%;
      .ll-object {
        font-weight: 400;
        white-space: normal;
        text-indent: -20px;
        padding-left: 20px;
      }
      .ll-string {
        font-size: 90%;
        font-weight: 200;
        white-space: normal;
        text-indent: -20px;
        padding-left: 20px;
        .ll-field {
          font-style: italic;
          font-weight: 300;
        }
        .ll-prepost {
        }
        .ll-match {
          font-weight: 500;
          color: @theme-color;
        }
      }
    }
  }
}