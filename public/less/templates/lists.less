.ll-listpanel {
  /*
  ::-webkit-scrollbar{width:14px;height:14px;}
  ::-webkit-scrollbar-track-piece{background-color:rgba(255,255,255,0.2);border-radius:8px;border:3px solid white;}
  ::-webkit-scrollbar-thumb:vertical{height:50px;background-color:rgba(0,0,0,0.3);-webkit-border-radius:8px;border:3px solid white;}
  */
  border-right: 8px solid @theme-color;
  .ll-header {
    padding: 5px 5px 5px 5px;
    margin: 0px 0px 0px 0px;
    border-bottom: 2px solid @theme-color;
    color: white;
    background-color: lighten(@theme-color, 30%);
    font-size:140%;
    font-style:normal;
    font-weight:400;
    .ll-subheader {
      font-size: 80%;
    }
  }
  .ll-dropbox {
    font-size: normal;
    font-weight: 200;
    padding:5px;
    height:100px;
    border-bottom: 2px solid @theme-color;
    .ll-drophint {
      font-weight: 400;
    }
    .ll-dropinfo {
      font-size: 80%;
      font-weight: 200;
    }
    .ll-dropicon {
      font-size: 400%;
      color: lighten(@theme-color, 30%);
    }
  }
  .ll-controls {
    background-color: lighten(@theme-color, 60%);
    padding: 4px 8px 4px 8px;
    font-size: normal;
    //border-top: 1px solid lighten(@theme-color, 30%);
    border-bottom: 2px solid lighten(@theme-color, 30%);
  }
  .ll-body {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    .ll-item {
      margin: 0px 0px 0px 0px;
      padding: 2px 5px 2px 5px;
      font-size: small;
      border-bottom: 1px solid lighten(@theme-color, 30%);
      .ll-title {
        font-size: 120%;
        font-style: normal;
        font-weight: 400;
      }
      .ll-counter {
        font-size: 100%;
        font-style: normal;
        font-weight: 200;
        .ll-code {
          font-weight: 400;
        }
      }
      .ll-version {
        font-size: 90%;
        font-style: normal;
        font-weight: 200;
        text-align: right;
      }
      .ll-disabled {
        text-decoration: line-through;
      }
      .ll-dirty {
        color: lighten(@theme-color, 10%);
        font-style: italic;
      }
    }
    .ll-selected {
      background-color: lighten(@theme-color, 60%);
    }
    .ll-subselected {
      background-color: lighten(@theme-color, 66%);
    }
  }
}