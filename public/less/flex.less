// Flexcontainer horizontal, linksbündig
.flexcnt {
  display:flex;
  display:-webkit-flex;
}
// Flexcontainer horizontal, rechtsbündig
.flexcnt-r {
  display:flex;
  display:-webkit-flex;
  justify-content:flex-end;
  -webkit-justify-content:flex-end;
}
// Flexcontainer horizontal, zentriert
.flexcnt-c {
  display:flex;
  display:-webkit-flex;
  justify-content:center;
  -webkit-justify-content:center;
}
// Flexcontainer vertikal, startbündig
.flexvcnt {
  display:flex;
  display:-webkit-flex;
  flex-direction:column;
  -webkit-flex-direction:column;
}
// Flexcontainer vertikal, endbündig
.flexvcnt-b {
  display:flex;
  display:-webkit-flex;
  justify-content:flex-end;
  -webkit-justify-content:flex-end;
  flex-direction:column;
  -webkit-flex-direction:column;
}
// Flexcontainer vertikal, zentriert
.flexvcnt-c {
  display:flex;
  display:-webkit-flex;
  justify-content:center;
  -webkit-justify-content:center;
  flex-direction:column;
  -webkit-flex-direction:column;
}

// Flexitem, minimaler Platz
.flexitem-0 {
  -webkit-flex:0 0 auto;
  flex:0 0 auto;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 30 Pixel
.flexitem-fix16 {
  -webkit-flex:0 0 16px;
  flex:0 0 16px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 30 Pixel
.flexitem-fix20 {
  -webkit-flex:0 0 20px;
  flex:0 0 20px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 30 Pixel
.flexitem-fix30 {
  -webkit-flex:0 0 30px;
  flex:0 0 30px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 40 Pixel
.flexitem-fix40 {
  -webkit-flex:0 0 40px;
  flex:0 0 40px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 50 Pixel
.flexitem-fix50 {
  -webkit-flex:0 0 50px;
  flex:0 0 50px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 60 Pixel
.flexitem-fix60 {
  -webkit-flex:0 0 60px;
  flex:0 0 60px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 100 Pixel
.flexitem-fix100 {
  -webkit-flex:0 0 100px;
  flex:0 0 100px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 120 Pixel
.flexitem-fix120 {
  -webkit-flex:0 0 120px;
  flex:0 0 120px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 150 Pixel
.flexitem-fix150 {
  -webkit-flex:0 0 150px;
  flex:0 0 150px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 200 Pixel
.flexitem-fix200 {
  -webkit-flex:0 0 200px;
  flex:0 0 200px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 300 Pixel
.flexitem-fix300 {
  -webkit-flex:0 0 300px;
  flex:0 0 300px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 400 Pixel
.flexitem-fix400 {
  -webkit-flex:0 0 400px;
  flex:0 0 400px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Feste Höhe/Breite 500 Pixel
.flexitem-fix500 {
  -webkit-flex:0 0 500px;
  flex:0 0 500px;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Greedy 1x
.flexitem-1 {
  -webkit-flex:1;
  flex:1;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Greedy 2x
.flexitem-2 {
  -webkit-flex:2;
  flex:2;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Greedy 3x
.flexitem-3 {
  -webkit-flex:3;
  flex:3;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Greedy 4x
.flexitem-4 {
  -webkit-flex:4;
  flex:4;
  white-space:nowrap;
  overflow:hidden;
}
// Flexitem, Greedy 5x
.flexitem-5 {
  -webkit-flex:5;
  flex:5;
  white-space:nowrap;
  overflow:hidden;
}

// Flexitem, Abstandshalter klein
.flexspace-s {
  -webkit-flex:0 0 5px;
  flex:0 0 5px;
}
// Flexitem, Abstandshalter mittel
.flexspace-m {
  -webkit-flex:0 0 10px;
  flex:0 0 10px;
}
// Flexitem, Abstandshalter gross
.flexspace-l {
  -webkit-flex:0 0 20px;
  flex:0 0 20px;
}
// Flexitem, Abstandshalter klein
.flexspace-divv {
  -webkit-flex:0 0 20px;
  flex:0 0 5px;
  border-right: 2px solid @theme-color;
  padding-left: 10px;
}

// Modifikator für Flexitem (in horizonalen Containern): obenbündig
.flextop {
  -webkit-align-self:flex-start;
  align-self:flex-start;
}
// Modifikator für Flexitem (in horizonalen Containern): untenbündig
.flexbottom {
  -webkit-align-self:flex-end;
  align-self:flex-end;
}
// Modifikator für Flexitem (in horizonalen Containern): vertikal zentriert
.flexcenter {
  -webkit-align-self:center;
  align-self:center;
}
// Modifikator für Flexitem (in horizonalen Containern): Schriftbasiszentriert
.flexbase {
  -webkit-align-self:baseline;
  align-self:baseline;
}

.flexwrap {
  white-space:normal;
}
