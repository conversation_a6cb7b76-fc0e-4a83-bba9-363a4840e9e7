@import "bootstrap";

@import "fonts";

@theme-color: #005499;
//@theme-color: #549900;

@icon-font-path: '/bootstrap/fonts/';

@navbar-inverse-bg: @theme-color;
@component-active-bg: lighten(@theme-color, 14%);
@navbar-inverse-link-color: white;
@navbar-inverse-link-hover-color: @theme-color;
@navbar-inverse-link-hover-bg: #fff;
@navbar-inverse-link-active-color: #fff;
@navbar-inverse-link-active-bg: lighten(@navbar-inverse-bg, 10%);
@navbar-inverse-link-disabled-color: darken(@theme-color, 10%);

html, body {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

pre {
  word-wrap: normal;
}

.nav, .pagination, .carousel, .panel-title a { cursor: pointer; }

.navbar-brand {
  position: relative;
  .version {
    font-size: 70%;
    font-style: italic;
    font-weight: 300;
    position: relative;
    margin-left: 5px;
    top:-7px;
  }
}

.tooltip {
  z-index: 2000;
}

.tooltip-inner {
  max-width: 800px;
  padding: 3px 8px;
  color: #000;
  border:1px solid @theme-color;
  text-align: center;
  text-decoration: none;
  background-color: lighten(@theme-color,63%);
  font-weight:300;
  border-radius: 4px;
}

.typeahead {
  max-height: 200px;
  overflow-y: scroll;
}

@import "flex";
@import "global";

@import "templates/editors";
@import "templates/lists";
@import "templates/modals";
@import "templates/viewers";

@import "modules/login";
@import "modules/dashboard";
@import "modules/management";
@import "modules/mediamgr";
@import "modules/misc";
@import "modules/workflow";
@import "modules/noticeboard";

.modal-dialog {
  width:800px;
}