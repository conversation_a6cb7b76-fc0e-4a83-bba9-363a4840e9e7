@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 100;
  src: url('../css/fonts/SourceSansPro-ExtraLight.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 100;
  src: url('../css/fonts/SourceSansPro-ExtraLightIt.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 200;
  src: url('../css/fonts/SourceSansPro-Light.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 200;
  src: url('../css/fonts/SourceSansPro-LightIt.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 300;
  src: url('../css/fonts/SourceSansPro-Regular.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 300;
  src: url('../css/fonts/SourceSansPro-It.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url('../css/fonts/SourceSansPro-Semibold.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: url('../css/fonts/SourceSansPro-SemiboldIt.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 500;
  src: url('../css/fonts/SourceSansPro-Bold.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 500;
  src: url('../css/fonts/SourceSansPro-BoldIt.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 600;
  src: url('../css/fonts/SourceSansPro-Black.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 600;
  src: url('../css/fonts/SourceSansPro-BlackIt.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 100;
  src: url('../css/fonts/SourceCodePro-ExtraLight.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 200;
  src: url('../css/fonts/SourceCodePro-Light.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 300;
  src: url('../css/fonts/SourceCodePro-Regular.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 400;
  src: url('../css/fonts/SourceCodePro-Semibold.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 500;
  src: url('../css/fonts/SourceCodePro-Bold.otf.woff') format('woff');
}
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 600;
  src: url('../css/fonts/SourceCodePro-Black.otf.woff') format('woff');
}
@font-family-sans-serif: "Source Sans Pro";
@font-family-monospace: "Source Code Pro";