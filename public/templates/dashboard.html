<div class="ll-mainview">
    <div class="ll-mainpane ll-fullwidth ll-dashboard">
        <div class="ll-logo"><img src="/img/logo.png"></div>
        <div class="flexvcnt ll-fullheight " ng-show="dashLoaded">
            <div ng-class="{'ll-dbtitle': !isMobileApp, 'll-dbtitlemob': isMobileApp }" class="ll-dbtitle flexitem-0 flexcnt">
                <div class="ll-ptrhand ll-icon"><i class="fa fa-chevron-left" ng-click="rotateDashboard(-1,$event)" ng-show="hasMoreDashboards" tooltip-placement="right" tooltip-popup-delay='1000' tooltip="{{ 'DASHBOARD.TOOLTIP.PREVDB' | translate }}"></i></div>
                <div class="flexitem-1"><i class="fa fa-dashboard"></i> {{ dashboardnum+1 }}: <b ng-click="editDashboardName()">{{ dashboardinfo.name }}</b> <span ng-click="editDashboardName()" class="ll-editpen ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'DASHBOARD.TOOLTIP.EDITDB' | translate }}"><i class="fa fa-pencil"></i></span></div>
                <div class="ll-ptrhand ll-icon"><i class="fa fa-minus" ng-click="removeDashboard()" ng-show="hasMoreDashboards" tooltip-placement="left" tooltip-popup-delay='1000' tooltip="{{ 'DASHBOARD.TOOLTIP.DELDB' | translate }}"></i></div>
                <div class="ll-ptrhand ll-icon"><i class="fa fa-plus" ng-click="addDashboard()" tooltip-placement="left" tooltip-popup-delay='1000' tooltip="{{ 'DASHBOARD.TOOLTIP.ADDDB' | translate }}"></i></div>
                <div class="flexspace-m"></div>
                <div class="ll-ptrhand ll-icon"><i class="fa fa-chevron-right" ng-click="rotateDashboard(1,$event)" ng-show="hasMoreDashboards" tooltip-placement="left" tooltip-popup-delay='1000' tooltip="{{ 'DASHBOARD.TOOLTIP.NEXTDB' | translate }}"></i></div>
            </div>
            <div class="flexitem-0 ll-welcome flexcnt" ng-show="numblocks<1">
                <div class="flexitem-1" translate translate-values="{ name: realname }">DASHBOARD.WELCOME</div>
                <div class="flexitem-0 ll-version flextop"><!--{{ 'DASHBOARD.VERSION' | translate : {version: appversion} }}-->&nbsp;<span class="ll-adddash ll-ptrhand" ng-click="addBlock()"><i class="fa fa-plus-circle"></i> {{ 'DASHBOARD.ADDDASH.BUTTON' | translate }}</span></div>
            </div>
            <div class="flexitem-1 flexcnt" ng-show="numblocks > 0">
                <div class="flexitem-1 flexvcnt">
                    <dash-block class="flexitem-1" data="dashblocks[0]"></dash-block>
                    <dash-block class="flexitem-1" ng-show="numblocks > 2" data="dashblocks[2]"></dash-block>
                </div>
                <div class="flexitem-1 flexvcnt" ng-show="numblocks > 1">
                    <dash-block class="flexitem-1" data="dashblocks[1]"></dash-block>
                    <dash-block class="flexitem-1" ng-show="numblocks > 3" data="dashblocks[3]"></dash-block>
                </div>
            </div>
        </div>
    </div>
</div>

