<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-1 ll-listpanel flexvcnt ll-fullheight">
            <div class="flexitem-1 flexvcnt" ng-show="configtableListLoaded">
                <div class="flexitem-0 ll-header flexcnt">
                    <div class="flexitem-1" translate>CONFIGTABLE.CONFIGTABLES</div>
                    <div class="flexitem-0"><i tooltip-popup-delay='1000' tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.IMPORTTABLE' | translate }}" class="fa fa-upload ll-ptrhand" ng-click="toggleUploader()" ng-show="hasGrant('MNGCFG')"></i></div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i tooltip-popup-delay='1000' tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.NEWTABLE' | translate }}" class="fa fa-lg fa-plus-square-o ll-ptrhand" ng-click="newTable()" ng-show="hasGrant('MNGCFG')"></i></div>
                </div>
                <dropbox data="uploaderinfo"></dropbox>
                <div class="flexitem-0 ll-controls flexcnt">
                    <div class="flexitem-0"><i class="fa fa-search ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.CLTF' | translate }}" ng-class="{ 'text-success': filters.textfilter.length > 0 }" ng-click="clearTextfilter()"></i></div>
                    <div class="flexspace-s"></div>
                    <input type="text" id="listfilter" ng-model="filters.textfilter" class="flexitem-1 form-control ll-input input-xxs" ng-blur="saveFilterSettings()">
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-eye ll-ptrhand" ng-class="{'text-success': showDeleted}" tooltip-popup-delay='1000' tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.SHOWDEL' | translate }}" ng-click="toggleShowDeleted()"></i></div>
                </div>
                <div class="flexitem-1 ll-body">
                    <div class="ll-item flexvcnt ll-ptrhand" ng-repeat="table in items | filter:filters.textfilter" ng-class="itemSelected(table)" ng-click="goto(table.id)" ng-show="showDeleted || !table.deleted">
                        <div class="flexitem-0 flexcnt">
                            <div class="flexitem-1 ll-title" ng-class="{ 'll-disabled': table.deleted, 'll-dirty': table.dirty }">{{ table.title }}</div>
                            <div class="flexitem-0 flexbottom ll-version"> <!-- {{ table.id }}--> </div>
                        </div>
                        <div class="flexitem-0 ll-counter flexcnt">
                            <div class="flexitem-1">
                                <span class="ll-code"> # {{ table.id }} </span> -
                                <span ng-hide="table.parent">{{ 'CONFIGTABLE.LIST.RELATION.NONE' | translate }} </span>
                                <span ng-show="table.parent">{{ 'CONFIGTABLE.LIST.RELATION.MODEL' | translate }} {{ table.parentcode }}</span>
                            </div>
                            <div class="flexspace-s"></div>
                            <div class="flexitem-0"><i tooltip-popup-delay='1000' tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.GOTOUNITS' | translate }}" class="fa fa-external-link-square ll-ptrhand text-primary" ng-show="table.parent" ng-click="gotoModel(table)"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-3 ll-scroll"><ui-view/></div>
    </div>
</div>