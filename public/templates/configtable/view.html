<div ng-show="configtableListLoaded">
    <div class="flexcnt ll-viewpanel ll-noticeboard">
        <div class="flexitem-1 ll-info flexvcnt">
            <div class="flexitem-0 ll-header flexcnt">
                <div class="flexitem-1"><span class="ll-code">#{{ table.id }} - </span>{{ table.title }} <span class="text-danger" ng-show="table.deleted"><i class="fa fa-exclamation-triangle"></i> {{ 'CONFIGTABLE.VIEW.DISABLED' | translate }}</span> </div>
                <div ng-hide="table.deleted" class="ll-nbaction flexitem-0 ll-ptrhand" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.EDIT' | translate }}" ng-click="editTable()"><i class="fa fa-edit ll-ptrhand"></i></div>
                <div class="flexspace-s"></div>
                <div class="flexitem-0"><i class="fa fa-copy ll-ptrhand" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.CLONE' | translate }}" ng-click="cloneTable(table)" ng-show="hasGrant('MNGCFG')"></i></div>
                <div class="flexspace-s"></div>
                <div ng-hide="table.deleted || table.parent" class="flexitem-0"><i class="fa fa-ban ll-ptrhand text-danger" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.DELETE' | translate }}" ng-click="deleteTable(table)" ng-show="hasGrant('MNGCFG')"></i></div>
                <div class="flexspace-s"></div>
                <div ng-show="table.deleted" class="flexitem-0"><i class="fa fa-cog ll-ptrhand" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.REACTIVATE' | translate }}" ng-click="reactivateTable(table)" ng-show="hasGrant('MNGCFG')"></i></div>
            </div>

            <div class="ll-nbbuttons flexitem-0 flexcnt">
                <div ng-hide="table.deleted" class="ll-nbaction flexitem-0 ll-ptrhand" ng-click="newEntry()">
                    <i class="fa fa-plus-square-o"></i>
                    <span translate>CONFIGTABLE.EDITOR.TITLE.NEW</span></div>
                <div class="flexitem-1"></div>

                <!-- show deleted entries -->
                <div class="ll-nbaction flexitem-0 ll-ptrhand">
                    <i ng-if="!table.deleted" class="fa fa-eye" ng-class="{'text-success': showDeleted}" tooltip-popup-delay='1000' tooltip="{{ 'CONFIGTABLE.LIST.TOOLTIP.SHOWDEL' | translate }}" ng-click="toggleShowDeleted(); filterEntrylist()"> </i>
                </div>

                <div class="flexvcnt"></div>
                <div class="ll-nbaction flexitem-0 ll-ptrhand" ng-click="export(table)">
                    <i class="fa fa-download"></i>
                    <span translate>NOTICES.BUTTON.EXPORT</span>
                </div>
            </div>
        </div>
    </div>
    <div>
        <div class="ll-mainpane ll-fullwidth ll-noticeboard flexvcnt">
            <div class="ll-nblist flexitem-1">
                <div class="flexvcnt">
                    <div class="ll-nbitem flexitem-0 flexcnt ll-header">
                        <!-- <div class="flexitem-fix30 ll-nbcell flexvcnt">id</div> -->
                        <div class="flexitem-fix50 ll-nbcell ll-ptrhand" ng-click="sortBy('code_id')">
                            {{ 'CONFIGTABLE.VIEW.CODE' | translate }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'code_id'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'code_id' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'code_id' && sortingReverse"></i>
                        </div>

                        <div class="flexitem-fix50 ll-nbcell ll-ptrhand" ng-click="sortBy('active')">
                            {{ 'CONFIGTABLE.VIEW.ACTIVE' | translate }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'active'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'active' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'active' && sortingReverse"></i>
                        </div>
                        <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-click="sortBy('col1')">
                            {{ table.colheader1 }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'col1'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col1' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col1' && sortingReverse"></i>
                        </div>
                        <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader2" ng-click="sortBy('col2')">
                            {{ table.colheader2 }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'col2'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col2' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col2' && sortingReverse"></i>
                        </div>
                        <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader3" ng-click="sortBy('col3')">
                            {{ table.colheader3 }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'col3'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col3' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col3' && sortingReverse"></i>
                        </div>
                        <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader4" ng-click="sortBy('col4')">
                            {{ table.colheader4 }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'col4'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col4' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col4' && sortingReverse"></i>
                        </div>
                        <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader5" ng-click="sortBy('col5')">
                            {{ table.colheader5 }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'col5'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col5' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col5' && sortingReverse"></i>
                        </div>
                        <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader6" ng-click="sortBy('col6')">
                            {{ table.colheader6 }}
                            <i class="fa fa-sort" ng-hide="sortingColumn === 'col6'"></i>
                            <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col6' && !sortingReverse"></i>
                            <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col6' && sortingReverse"></i>
                        </div>
                        <div class="flexitem-fix60 ll-nbcell"> </div>
                    </div>


                    <tr ng-repeat="item in items" ng-class="{class1 : $index%2==0, class2 : !($index%2==0)}">

                    <div class="ll-nbitem flexitem-0 flexcnt" ng-repeat="entry in entries | orderBy:sortingColumn:sortingReverse" ng-class="{'ll-nbodditem': $index%2==0, 'll-disabled': entry.deleted}">
                        <!-- <div class="flexitem-fix30 ll-nbcell ll-nbid">{{ entry.id }}</div> -->
                        <div class="flexitem-fix50 ll-nbcell ll-nbid">{{ entry.code_id }}</div>
                        <div class="flexitem-fix50 ll-nbcell ll-nbid">
                            <button type="button" class="btn" ng-class="{ 'btn-success': entry.active == true, 'btn-default': entry.active != true }"
                                    ng-model="entry.active" btn-checkbox btn-checkbox-true="true" btn-checkbox-false="false" ng-change="setActive(entry)" ng-disabled="!showEditButtons('MNGCFG') || table.deleted || entry.deleted">
                                <i ng-class="{ 'fa-check': entry.active == true, 'fa-remove': entry.active != true}"
                                   class="fa fa-fw"></i></button>
                        </div>
                        <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext">{{ entry.col1 }}</div>
                        <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader2">{{ entry.col2 }}</div>
                        <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader3">{{ entry.col3 }}</div>
                        <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader4">{{ entry.col4 }}</div>
                        <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader5">{{ entry.col5 }}</div>
                        <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader6">{{ entry.col6 }}</div>
                        <div class="flexitem-fix60 ll-nbcell ll-nbid">
                            <span ng-show="showEditButtons('MNGCFG') && !(table.deleted || entry.deleted)" tooltip-popup-delay="1000" tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.ENTRY.EDIT' | translate }}" ng-click="editEntry(entry)">
                                <i class="fa fa-edit ll-ptrhand"></i>
                            </span>
                            <span ng-show="showEditButtons('MNGCFG') && !(table.deleted || entry.deleted)" tooltip-popup-delay="1000" tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.ENTRY.DELETE' | translate }}" ng-click="disableEntry(entry)">
                                <i class="fa fa-trash-o text-danger ll-ptrhand"></i>
                            </span>
                            <span ng-show="showEditButtons('MNGCFG') && (entry.deleted && !table.deleted)" tooltip-popup-delay="1000" tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.ENTRY.UNDELETE' | translate }}" ng-click="enableEntry(entry)">
                                <i class="fa fa-cog ll-ptrhand"></i>
                            </span>
                            <span ng-show="entry.blocked" tooltip-popup-delay="1000" tooltip-placement="left" tooltip="{{ 'CONFIGTABLE.LIST.ENTRY.BLOCKED' | translate }}">
                                <i class="fa fa-lock text-danger ll-ptrhand"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
