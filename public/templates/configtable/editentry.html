<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{editor.title}}" translate-values="editor.titlevals"></div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="entryEditLoaded">
        <!--
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.VIEW.CODE' | translate }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="m_code" ng-model="item.code" class="form-control ll-input flexitem-2" autofocus>
                </div>
                <div class="ll-measuresubkey" ng-show="mtinfo.link == 'bycode'">{{ 'MEASURE.EDIT.COMPLCODE' | translate }}</div>
                <div class="flexitem-2" ng-show="mtinfo.link == 'bycode'">
                    <input type="text" id="m_complcode" ng-model="item.calculation.complcode" class="form-control ll-input">
                </div>
            </div>
        </div>
        -->

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ table.colheader1 }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" ng-model="item.col1" class="form-control ll-input flexitem-2" autofocus>
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block" ng-if="table.colheader2">
            <div class="ll-key">{{ table.colheader2 }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" ng-model="item.col2" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block" ng-if="table.colheader3">
            <div class="ll-key">{{ table.colheader3 }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" ng-model="item.col3" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block" ng-if="table.colheader4">
            <div class="ll-key">{{ table.colheader4 }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" ng-model="item.col4" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block" ng-if="table.colheader5">
            <div class="ll-key">{{ table.colheader5 }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" ng-model="item.col5" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block" ng-if="table.colheader6">
            <div class="ll-key">{{ table.colheader6 }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" ng-model="item.col6" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="entryEditLoaded">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancel()"><i class="fa fa-ban"></i> {{ 'MEASURE.EDIT.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="continue()"><i class="fa fa-save"></i> {{ 'MEASURE.EDIT.BUTTON.CLOSE' | translate }}</button></div>
    </div>
</div>
