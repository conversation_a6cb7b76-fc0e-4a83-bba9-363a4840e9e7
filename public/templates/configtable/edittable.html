<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{editor.title}}" translate-values="editor.titlevals"></div>
        <div class="flexitem-0 ll-subheader flexbottom">{{ editor.subtitle }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="tableEditLoaded">

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.EDIT.TITLE' | translate }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="t_title" ng-model="item.title" class="form-control ll-input flexitem-2" autofocus>
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.EDIT.HEADER' | translate }} 1</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="t_colheader1" ng-model="item.colheader1" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.EDIT.HEADER' | translate }} 2</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="t_colheader2" ng-model="item.colheader2" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.EDIT.HEADER' | translate }} 3</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="t_colheader3" ng-model="item.colheader3" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.EDIT.HEADER' | translate }} 4</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="t_colheader4" ng-model="item.colheader4" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.EDIT.HEADER' | translate }} 5</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="t_colheader5" ng-model="item.colheader5" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'CONFIGTABLE.EDIT.HEADER' | translate }} 6</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="t_colheader6" ng-model="item.colheader6" class="form-control ll-input flexitem-2">
                </div>
            </div>
        </div>

    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="tableEditLoaded">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancel()"><i class="fa fa-ban"></i> {{ 'CONFIGTABLE.EDIT.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="continue()"><i class="fa fa-save"></i> {{ 'CONFIGTABLE.EDIT.BUTTON.CLOSE' | translate }}</button></div>
    </div>
</div>
