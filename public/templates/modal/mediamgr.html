<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-0">{{ 'MEDIAMGR.TITLE' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-subheader flexbottom" translate="{{subtitle.msg}}" translate-values="subtitle.vals"></div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div class="flexitem-1 ll-body ll-mediamgr">
        <tabset>
            <tab select="refreshImageData()" deselect="killZoomImage()">
                <tab-heading><i class="fa fa-image"></i> {{ 'MEDIAMGR.TAB.IMAGES' | translate }}</tab-heading>
                <div class="ll-mediapane flexvcnt">
                    <div class="flexitem-1 flexcnt"><!--ng-show="mmImageLoaded"-->
                        <div class="ll-imagepreview flexitem-1 flexvcnt-c">
                            <div class="flexitem flexcenter" ng-if="selectedImage">
                                <div class="ll-image"><img id="zoomimg" zoomable-image="zoomableImageOptions" ng-src="/media/{{selectedImage.preview.filename}}" zoom-image-src="selectedZoomImage"/></div>
                            </div>
                        </div>
                        <div class="ll-imagelist flexitem-fix400" ui-sortable="imageSortOptions" ng-model="imagelist">
                            <div ng-repeat="image in imagelist" class="ll-imageitem flexcnt" ng-class="{'ll-listbgodd': $odd, 'll-listbgeven': $even}">
                                <div id="IMGROW{{image.id}}" class="flexitem-fix20 text-primary flexcenter"><i ng-show="image==selectedImage" class="fa fa-chevron-left fa-lg"></i></div>
                                <div class="ll-imagename flexcenter flexitem-1 ll-ptrhand" ng-click="selectImage(image,0)">{{ image.title }}</div>
                                <div class="flexspace-s"></div>
                                <div class="ll-imagedata flexcenter flexitem-0" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.IMGSIZE' | translate }}" ng-show="imageInfo==0" ng-click="toggleImageInfo()">{{ image.fullimage.metadata.width }} x {{ image.fullimage.metadata.height }}</div>
                                <div class="ll-imagedata flexcenter flexitem-0" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.IMGMETA' | translate }}" ng-show="imageInfo==1" ng-click="toggleImageInfo()">{{ image.fullimage.size/(1024*1024) | sprintf : '%0.3f MB' }}</div>
                                <div class="flexspace-m"></div>
                                <div class="flexitem-0 text-primary flexcenter ll-dragaction"><i class="fa fa-sort"></i></div>
                                <div class="flexspace-m"></div>
                                <div class="flexitem-0 text-primary flexcenter ll-action" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.DOWNLOAD' | translate }}" ng-click="downloadImage(image)"><i class="fa fa-download"></i></div>
                                <div class="flexspace-s"></div>
                                <div class="flexitem-0 text-primary flexcenter ll-action" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.EDITCAP' | translate }}" ng-click="editCaption(image)"><i class="fa fa-edit"></i></div>
                                <div class="flexspace-s"></div>
                                <div class="flexitem-0 text-danger flexcenter ll-action" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.REMIMG' | translate }}" ng-click="removeImage(image)"><i class="fa fa-trash-o"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="ll-imagethumbnailgallery flexitem-fix200 flexcnt"><!--ng-show="mmImageLoaded"-->
                        <div ng-repeat="image in imagelist" class="ll-thumbnailblock flexitem-fix150 flexvcnt">
                            <div class="flexitem-fix20 text-primary flexcenter"><i ng-show="image==selectedImage" class="fa fa-chevron-up fa-lg"></i></div>
                            <div id="IMGTN{{image.id}}" class="flexitem-1 flexcenter flexcnt"><img class="flexitem-0 flexcenter ll-thumbnail ll-ptrhand" ng-src="/media/{{image.thumbnail.filename}}" ng-click="selectImage(image,1)"/></div>
                        </div>
                    </div>
                </div>
            </tab>
            <tab select="refreshDocData()">
                <tab-heading><i class="fa fa-video-camera"></i> {{ 'MEDIAMGR.TAB.VIDEOS' | translate }} / <i class="fa fa-file-text-o"></i> {{ 'MEDIAMGR.TAB.DOCS' | translate }}</tab-heading>
                <div class="ll-mediapane fullwidth fullheight">
                    <div class="ll-doclist" ui-sortable="docSortOptions" ng-model="doclist"><!--ng-show="mmDocLoaded"-->
                        <div ng-repeat="doc in doclist" class="ll-docitem flexcnt" ng-class="{'ll-listbgodd': $odd, 'll-listbgeven': $even}">
                            <div class="flexitem-0 text-success flexcenter ll-action" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.VIEWDOC' | translate }}" ng-hide="doc.doctype == 3" ng-click="viewDoc(doc)"><i class="fa fa-play"></i></div>
                            <div class="flexspace-m"></div>
                            <div class="flexitem-fix20 text-primary flexcenter" ng-show="doc.doctype == 1"><i class="fa fa-file-pdf-o"></i></div>
                            <div class="flexitem-fix20 text-primary flexcenter" ng-show="doc.doctype == 2"><i class="fa fa-video-camera"></i></div>
                            <div class="flexitem-fix20 text-primary flexcenter" ng-show="doc.doctype == 3"><i class="fa fa-file-archive-o"></i></div>
                            <div class="ll-docname flexcenter flexitem-1" ng-class="{'ll-ptrhand': doc.doctype == 1 || doc.doctype == 2}" ng-click="viewDoc(doc)">{{ doc.title }}</div>
                            <div class="flexspace-s"></div>
                            <div class="ll-docdata flexcenter flexitem-0" ng-show="docInfo==0 && doc.doctype == 1" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.DOCSIZE' | translate }}" ng-click="toggleDocInfo()">
                                {{ 'MEDIAMGR.PDFPAGES' | translate : { nump: doc.binaryfile.metadata.pages } }}
                            </div>
                            <div class="ll-docdata flexcenter flexitem-0" ng-show="docInfo==0 && doc.doctype == 2" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.DOCSIZE' | translate }}" ng-click="toggleDocInfo()">
                                {{ 'MEDIAMGR.VIDINFO' | translate : { width: doc.binaryfile.metadata.width, height: doc.binaryfile.metadata.height, fps: sprintf('%0.1f',doc.binaryfile.metadata.fps || 0.0), rtime: sprintf('%0.1f',doc.binaryfile.metadata.duration || 0.0) } }}
                            </div>
                            <div class="ll-docdata flexcenter flexitem-0" ng-show="docInfo==1" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.DOCMETA' | translate }}" ng-click="toggleDocInfo()">{{ doc.binaryfile.size/(1024*1024) | sprintf : '%0.3f MB' }}</div>
                            <div class="flexspace-m"></div>
                            <div class="flexitem-0 text-primary flexcenter ll-dragaction"><i class="fa fa-sort"></i></div>
                            <div class="flexspace-m"></div>
                            <div class="flexitem-0 text-primary flexcenter ll-action" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.DOWNLOAD' | translate }}" ng-click="downloadDoc(doc)"><i class="fa fa-download"></i></div>
                            <div class="flexspace-s"></div>
                            <div class="flexitem-0 text-primary flexcenter ll-action" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.EDITCAP' | translate }}" ng-click="editDocCaption(doc)"><i class="fa fa-edit"></i></div>
                            <div class="flexspace-s"></div>
                            <div class="flexitem-0 text-danger flexcenter ll-action" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.REMDOC' | translate }}" ng-click="removeDoc(doc)"><i class="fa fa-trash-o"></i></div>
                        </div>
                    </div>
                </div>
            </tab>
            <tab>
                <tab-heading><i class="fa fa-upload"></i> {{ 'MEDIAMGR.TAB.UPLOAD' | translate }}</tab-heading>
                <div class="ll-mediapane flexvcnt" nv-file-drop nv-file-over uploader="uploader">
                    <div class="flexitem-0 ll-uploadheader">{{ 'MEDIAMGR.UPLOAD.HINT' | translate }}</div>
                    <div class="flexitem-0 ll-uploaddescription">{{ 'MEDIAMGR.UPLOAD.RESTR' | translate }}</div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-uploadqueue"><div ng-repeat="item in uploader.queue"><div class="ll-uploaditem flexcnt" ng-class="{'ll-listbgodd': $odd}">
                        <div class="flexitem-fix20"><i ng-class="filetypeIcon(item.file.type)"></i></div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-fix400 ll-itemname">{{item.file.name}}</div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-fix100 text-right ll-itemsize">{{item.file.size/(1024*1024) | sprintf : '%0.3f MB' }}</div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-fix20 text-right"><i ng-class="actionIcon(item)"></i></div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-1 ll-itemstatus" ng-show="itemStatus(item) == 0">{{ 'MEDIAMGR.UPLOAD.STATUS.READY' | translate }}</div>
                        <div class="flexitem-1 ll-itemstatus" ng-show="itemStatus(item) == 1">{{ 'MEDIAMGR.UPLOAD.STATUS.WAITING' | translate }}</div>
                        <div class="flexitem-1 ll-itemstatus" ng-show="itemStatus(item) == 2">{{ 'MEDIAMGR.UPLOAD.STATUS.UPLOADING' | translate : { prog: item.progress } }}</div>
                        <div class="flexitem-1 ll-itemstatus" ng-show="itemStatus(item) == 3">{{ 'MEDIAMGR.UPLOAD.STATUS.CANCELLED' | translate }}</div>
                        <div class="flexitem-1 ll-itemstatus" ng-show="itemStatus(item) == 4">{{ 'MEDIAMGR.UPLOAD.STATUS.FINISHED' | translate }}</div>
                        <div class="flexitem-1 ll-itemstatus" ng-show="itemStatus(item) == 5">{{ 'MEDIAMGR.UPLOAD.STATUS.FAILED' | translate }}: {{ (item.resterror || "") | translate }}</div>
                        <div class="flexitem-1 ll-itemstatus" ng-show="itemStatus(item) == 6">{{ 'MEDIAMGR.UPLOAD.STATUS.ERROR' | translate }}</div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-fix20 text-success ll-ptrhand" ng-show="itemStatus(item) == 0" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.UPLOAD' | translate }}" ng-click="item.upload()"><i class="fa fa-upload fa-lg"></i></div>
                        <div class="flexitem-fix20 text-primary ll-ptrhand" ng-show="itemStatus(item) != 2" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.REMUPL' | translate }}" ng-click="item.remove()"><i class="fa fa-trash-o fa-lg"></i></div>
                        <div class="flexitem-fix20 text-danger ll-ptrhand" ng-show="itemStatus(item) == 2" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.CNCLUPL' | translate }}" ng-click="item.cancel()"><i class="fa fa-ban fa-lg"></i></div>
                    </div></div></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-0 ll-uploaddescription flexcnt">
                        <div class="flexitem-1"></div>
                        <div class="flexitem-0 ll-ptrhand" ng-show="uploadsReady()" ng-click="startAllUploads()"><i class="fa fa-upload fa-lg"></i> {{ 'MEDIAMGR.UPLOAD.STARTALL' | translate }}</div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-0 ll-ptrhand" ng-show="uploadsFinished()" ng-click="removeAllFinishedUploads()"><i class="fa fa-trash-o fa-lg"></i> {{ 'MEDIAMGR.UPLOAD.DELALL' | translate }}</div>
                    </div>
                </div>
            </tab>
        </tabset>
    </div>
</div>
