 <div class="modal-body ll-modeladdmodal flexvcnt ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header" translate>{{ title }}</div>
    <div class="flexspace-s"></div>
     <div class="flexitem-0 ll-subheader flexwrap" ng-hide="!value && title == 'CHECK.SELTOOL.TITLE'" translate>{{ text }}</div>
     <div class="flexitem-0 ll-subheader flexwrap" ng-show="title == 'CHECK.SELTOOL.TITLE' && !value" translate>{{ 'CHECK.SELTOOL.NOTOOLAVAILABLE' }}</div>
     <div class="flexspace-s"></div>
    <div class="flexitem-1 ll-body" ng-if="value || title != 'CHECK.SELTOOL.TITLE'">
        <div ng-repeat="block in elems">
            <div class="ll-item ll-blockheader flexcnt ll-ptrhand" ng-show="block.name || block.title" ng-click="collapse(block)" tooltip-append-to-body=true tooltip-placement="right" tooltip="{{ block.ttip | translate }}">
                <div class="flexitem-fix20 flexcenter ll-actions text-primary" ng-if="!params.maxsel" ng-click="toggleUse(block, $event)"><i ng-class="getUseIcon(block)"></i></div>
                <div class="flexspace-m"></div>
                <div class="flexitem-fix50 flexcenter ll-counter" ng-if="params.style == 1"><span class="ll-code">{{ block.code }}</span></div>
                <div class="flexspace-s" ng-if="params.style == 1"></div>
                <div class="flexitem-0 ll-title" ng-if="block.title">{{ block.title | translate_model }}</div>
                <div class="flexitem-0 ll-title" ng-if="block.name" translate>{{ block.name }}</div>
                <div class="flexitem-1" ng-if="params.style == 2 || params.style == 3"></div>
                <div class="flexitem-0 flexcenter ll-counter" ng-if="params.style == 2 || params.style == 3"><span class="ll-code" ng-class="{ 'll-emph' : item.emph }">{{ block.code }}</span></div>
                <div class="flexspace-s" ng-if="params.style == 2 || params.style == 3"></div>
                <div class="flexitem-1" ng-if="params.style < 2"></div>
                <div class="flexitem-fix20 flexcenter ll-actions text-primary"><i ng-class="block.collapsed ? 'fa fa-chevron-right' : 'fa fa-chevron-down'"></i></div>
                <div class="flexspace-m"></div>
            </div>
            <div collapse="block.collapsed">
                <div class="ll-item flexcnt" ng-repeat="item in block.items" ng-click="toggleUse(item, $event)" tooltip-popup-delay="300" tooltip-append-to-body=true tooltip-placement="top" tooltip="{{ item.ttip | translate }}">
                    <div class="flexitem-fix20 flexcenter ll-actions text-primary"><i ng-class="getUseIcon(item)"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-fix50 flexcenter ll-counter" ng-if="params.style == 1"><span class="ll-code" ng-class="{ 'll-emph' : item.emph }">{{ item.code }}</span></div>
                    <div class="flexspace-s" ng-if="params.style == 1"></div>
                    <div class="flexitem-0 ll-title" ng-if="item.title" tooltip-popup-delay="300" tooltip-append-to-body=true tooltip-placement="top" tooltip="{{ item.ttip | translate }}">{{ item.title | translate_model }}</div>
                    <div class="flexitem-0 ll-title" ng-if="item.name" translate>{{ item.name }}</div>
                    <div class="flexitem-1" ng-if="params.style == 2 || params.style == 3"></div>
                    <div class="flexitem-0 flexcenter ll-counter" ng-if="params.style == 2"><span class="ll-code" ng-class="{ 'll-emph' : item.emph }">{{ item.code }}</span></div>
                    <div class="flexitem-0 flexcenter ll-counter" ng-if="params.style == 3"><span class="ll-code" ng-class="{ 'll-emph' : item.emph }">{{ item.code.text | translate : item.code }}</span></div>
                    <div class="flexitem-1" ng-if="params.style != 2 && params.style != 3"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="flexitem-0 ll-allsel flexcnt" ng-show="!params.single && !params.maxsel">
        <div class="flexitem-1"></div>
        <div class="flexitem-0 ll-ptrhand" ng-click="procAll(true)"><i class="fa fa-square"></i> {{ 'UI.BUTTONS.MEDITOR.SELALL' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-ptrhand" ng-click="procAll(false)"><i class="fa fa-square-o"></i> {{ 'UI.BUTTONS.MEDITOR.DESELALL' | translate }}</div>
    </div>
    <div class="flexitem-0 ll-allsel flexcnt" ng-show="!params.single && params.maxsel">
        <div class="flexitem-1"></div>
        <div class="flexitem-0" ng-show="countpicks < params.maxsel">{{ 'UI.MEDITOR.MAXSEL' | translate : { num: (params.maxsel-countpicks) } }}</div>
        <div class="flexitem-0" ng-hide="countpicks < params.maxsel">{{ 'UI.MEDITOR.NOMORESEL' | translate }}</div>
    </div>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button class="btn btn-danger" ng-click="cancel()"><i class="fa fa-save"></i> <span translate>UI.BUTTONS.MEDITOR.CANCEL</span></button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button class="btn btn-success" ng-hide="params.single || (!value && title=='CHECK.SELTOOL.TITLE')" ng-click="ok()"><i class="fa fa-play-circle"></i> <span translate translate-values="{ num: countpicks }">{{ params.buttext ? params.buttext : 'UI.BUTTONS.MEDITOR.ADDMSEL'}}</span></button></div>
        <div class="flexitem-0"><button class="btn btn-success" ng-show="params.single || (!value && title=='CHECK.SELTOOL.TITLE')" ng-click="ok()"><i class="fa fa-check"></i> <span translate>UI.BUTTONS.MEDITOR.SELECT</span></button></div>
    </div>
</div>

