<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1, initX: 0 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1"> {{ 'MODEL.VIEW.CONFIGTABLE.TTL' | translate }} <b>{{ table.title }}</b></div>
    </div>

    <div class="ll-scroll ll-mh500"> <!-- <div ng-show="configtableListLoaded"> -->
         <div>
            <div class="ll-mainpane ll-fullwidth ll-noticeboard flexvcnt">
                <div class="ll-nblist flexitem-1">
                    <div class="flexvcnt">
                        <div class="ll-nbitem flexitem-0 flexcnt ll-header">
                            <div class="flexitem-fix50 ll-nbcell ll-ptrhand" ng-click="sortBy('code_id')">
                                {{ 'CONFIGTABLE.VIEW.CODE' | translate }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'code_id'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'code_id' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'code_id' && sortingReverse"></i>
                            </div>

                            <div class="flexitem-fix50 ll-nbcell ll-ptrhand" ng-click="sortBy('active')">
                                {{ 'CONFIGTABLE.VIEW.ACTIVE' | translate }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'active'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'active' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'active' && sortingReverse"></i>
                            </div>
                            <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-click="sortBy('col1')">
                                {{ table.colheader1 }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'col1'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col1' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col1' && sortingReverse"></i>
                            </div>
                            <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader2" ng-click="sortBy('col2')">
                                {{ table.colheader2 }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'col2'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col2' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col2' && sortingReverse"></i>
                            </div>
                            <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader3" ng-click="sortBy('col3')">
                                {{ table.colheader3 }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'col3'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col3' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col3' && sortingReverse"></i>
                            </div>
                            <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader4" ng-click="sortBy('col4')">
                                {{ table.colheader4 }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'col4'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col4' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col4' && sortingReverse"></i>
                            </div>
                            <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader5" ng-click="sortBy('col5')">
                                {{ table.colheader5 }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'col5'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col5' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col5' && sortingReverse"></i>
                            </div>
                            <div class="flexitem-1 ll-nbcell ll-ptrhand" ng-if="table.colheader6" ng-click="sortBy('col6')">
                                {{ table.colheader6 }}
                                <i class="fa fa-sort" ng-hide="sortingColumn === 'col6'"></i>
                                <i class="fa fa-sort-asc" ng-show="sortingColumn === 'col6' && !sortingReverse"></i>
                                <i class="fa fa-sort-desc" ng-show="sortingColumn === 'col6' && sortingReverse"></i>
                            </div>
                        </div>
                        <div class="ll-nbitem flexitem-0 flexcnt" ng-repeat="entry in entries | orderBy : '1*'+sortingColumn+' ? 1*'+sortingColumn+' : '+sortingColumn : sortingReverse" ng-class="{'ll-nbodditem': $odd, 'll-disabled': !hasGrant('MNGCFE')}" ng-show="!entry.deleted">
                            <div class="flexitem-fix50 ll-nbcell ll-nbid">{{ entry.code_id }}</div>
                            <div class="flexitem-fix50 ll-nbcell ll-nbid">
                                <button type="button" class="btn" ng-class="{ 'btn-success': entry.active == true, 'btn-default': entry.active != true }"
                                        ng-model="entry.active" btn-checkbox btn-checkbox-true="true" btn-checkbox-false="false" ng-change="setActive(entry)" ng-disabled="!hasGrant('MNGCFE')">
                                    <i ng-class="{ 'fa-check': entry.active == true, 'fa-remove': entry.active != true}"
                                       class="fa fa-fw"></i></button>
                            </div>
                            <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext">{{ entry.col1 }}</div>
                            <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader2">{{ entry.col2 }}</div>
                            <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader3">{{ entry.col3 }}</div>
                            <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader4">{{ entry.col4 }}</div>
                            <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader5">{{ entry.col5 }}</div>
                            <div class="flexitem-1 ll-nbcell ll-nbid ll-tabletext" ng-if="table.colheader6">{{ entry.col6 }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexspace-s"></div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button class="btn btn-success" ng-click="revalidate()" ng-show="hasGrant('MNGCFE')"> {{ 'NOTICES.BUTTON.USE' | translate }} </button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="close()"><i class="fa fa-ban"></i> {{ 'NOTICES.BUTTON.CLOSE' | translate }}</button></div>
        <div class="flexspace-s"></div>
    </div>
</div>
