<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt" ng-hide="typeListLoaded">
        <div class="flexitem-1">{{ 'TLISTEDIT.LOADING' | translate }}</div>
    </div>
    <div class="flexitem-0 ll-header flexcnt" ng-show="typeListLoaded">
        <div class="flexitem-1">{{ title | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div class="flexitem-1 ll-body ll-typelisteditor flexvcnt" ng-show="typeListLoaded">
        <div class="flexitem-0 ll-intro">{{ text | translate }}</div>
        <div class="flexitem-1 ll-listblock flexvcnt ll-scroll" ui-sortable="sublistSortOptions" ng-model="items">
            <div class="flexitem-0 flexcnt ll-listitem" ng-repeat="item in items">
                <div class="ll-dragaction flexitem-fix30 text-primary text-center flexcenter ll-ptrhand" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.REORDER' | translate }}" ng-show="showReorderIcon('EDTMOD')"><i class="fa fa-sort"></i></div>
                <div class="flexitem-fix30 text-center flexcenter ll-ptrhand"><i class="fa fa-minus-circle text-danger fa-lg" ng-show="!editmode && mayDelete(item)" tooltip-popup-delay='1000' tooltip="{{ 'TLISTEDIT.TOOLTIP.DEL' | translate }}" ng-click="actionDelete(item)"></i></div>
                <div class="flexitem-1">
                    <div class="ll-itemtitle">
                        <span class="ll-itemcode">{{ item.code }}: </span>
                        {{ item.title | translate_model }}
                        <span ng-show="item.deleted">&nbsp;&nbsp;&nbsp;<i class="fa fa-exclamation-triangle text-danger"></i> {{ 'TLISTEDIT.DISABLED' | translate }}</span>
                    </div>
                    <div>
                        <span class="ll-iteminfo" ng-show="item.checkcnt">{{ 'TLISTEDIT.CNT.CHECK' | translate : { cnt: item.checkcnt } }}</span>
                        <span class="ll-iteminfo" ng-show="item.modelcnt">{{ 'TLISTEDIT.CNT.MODEL' | translate : { cnt: item.modelcnt } }}</span>
                        <span class="ll-iteminfo" ng-show="item.images">{{ 'TLISTEDIT.CNT.IMAGES' | translate : { cnt: item.images.length } }}</span>
                        <span class="ll-iteminfo" ng-show="item.documents">{{ 'TLISTEDIT.CNT.DOCS' | translate : { cnt: item.documents.length } }}</span>&nbsp;
                    </div>
                </div>
                <div class="flexitem-fix30 text-center flexcenter ll-ptrhand"><i class="fa fa-image fa-lg text-primary" ng-show="!editmode && mmEnabled" tooltip-popup-delay='1000' tooltip="{{ 'TLISTEDIT.TOOLTIP.MEDMGR' | translate }}" ng-click="actionMediaManager(item)"></i></div>
                <div class="flexitem-fix30 text-center flexcenter ll-ptrhand"><i class="fa fa-edit fa-lg text-primary" ng-show="!editmode" tooltip-popup-delay='1000' tooltip="{{ 'TLISTEDIT.TOOLTIP.EDIT' | translate }}" ng-click="actionEdit(item)"></i></div>
            </div>
        </div>
        <div class="flexitem-0 ll-listitem" collapse="editmode">
            <div class="flexcnt">
                <div class="flexitem-fix30 text-center flexcenter ll-ptrhand"><i class="fa fa-plus-circle fa-lg text-success" tooltip-popup-delay='1000' tooltip="{{ 'TLISTEDIT.TOOLTIP.NEW' | translate }}" ng-click="actionNewItem()"></i></div>
                <div class="flexitem-1 ll-itemtitle" style="padding:5px 0px 5px 0px"><i>{{ 'TLISTEDIT.NEW' | translate }}</i></div>
            </div>
        </div>
        <div class="flexitem-0" collapse="!editmode">
            <div class="ll-block">
                <div class="ll-key">{{ 'TLISTEDIT.CODE' | translate }}</div>
                <div class="ll-edit">
                    <input type="text" id="m_code" ng-model="edititem.code" class="form-control ll-input" ng-disabled="!hasGrant('CHGCOD') && !editnew">
                </div>
            </div>
            <div class="ll-block">
                <div class="ll-key">{{ 'TLISTEDIT.TITLE' | translate }}</div>
                <div class="ll-edit">
                    <input type="text" id="m_title" ng-model="edititem.title[languageinfo.selected]" class="form-control ll-input" ui-keyup="langToggleKeymap">
                    <lang-completion-info langinfo="languageinfo" model="edititem.title"></lang-completion-info>
                </div>
            </div>
            <div class="ll-block">
                <div class="ll-key">{{ 'TLISTEDIT.DESC' | translate }}</div>
                <div class="ll-edit">
                    <textarea rows="3" id="m_desc" ng-model="edititem.description[languageinfo.selected]" class="form-control ll-inputsm" ui-keyup="langToggleKeymap"></textarea>
                    <lang-completion-info langinfo="languageinfo" model="edititem.description"></lang-completion-info>
                </div>
            </div>
            <div class="ll-block">
                <div class="ll-key">{{ 'TLISTEDIT.DISSET.TITLE' | translate }}</div>
                <div class="ll-edit">
                    <input type="checkbox" ng-model="edititem.deleted"> <span ng-show="edititem.deleted">{{ 'TLISTEDIT.DISSET.TRUE' | translate }}</span><span ng-hide="edititem.deleted">{{ 'TLISTEDIT.DISSET.FALSE' | translate }}</span>
                </div>
            </div>
            <div class="flexitem-0 ll-buttonbar flexcnt">
                <div class="btn-group flexitem-0">
                    <label class="btn btn-primary" ng-repeat="lang in languageinfo.languages" ng-model="languageinfo.selected" btn-radio="lang.code">{{ lang.name }}</label>
                </div>
                <div class="flexitem-1"></div>
                <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancelEdit()"><i class="fa fa-ban"></i> {{ 'TLISTEDIT.BUTTON.CANCEL' | translate }}</button></div>
                <div class="flexspace-s"></div>
                <div class="flexitem-0"><button type="button" class="btn btn-success" ng-show="editnew" ng-click="saveEdit()"><i class="fa fa-save"></i> {{ 'TLISTEDIT.BUTTON.ADD' | translate }}</button></div>
                <div class="flexitem-0"><button type="button" class="btn btn-success" ng-hide="editnew" ng-click="saveEdit()"><i class="fa fa-save"></i> {{ 'TLISTEDIT.BUTTON.SAVE' | translate }}</button></div>
            </div>
        </div>
    </div>
</div>
