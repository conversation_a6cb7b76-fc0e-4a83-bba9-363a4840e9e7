<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-0">{{ 'CHANGELOG.VIEWER.TITLE' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-subheader flexbottom">{{ title }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div id="fullpane" class="flexitem-1 ll-body ll-changelog ll-scroll">
        <div class="flexvcnt">
            <div class="flexitem-0 flexcnt ll-logline" ng-repeat="line in lines" ng-class="['ll-style'+line.marked, 'll-'+($odd ? 'odd' : 'even')]">
                <div class="flexitem-2 ll-cell">{{ line.timestamp | date:i18n.selectedLanguage.tformat:'utc' }} <span class="ll-emph">{{ line.user.realname }} ({{ line.user.username }})</span></div>
                <div class="flexitem-3 ll-cell">{{ line.itemtype | translate }} <span class="ll-emph">{{ line.item }}</span>: {{ line.description | translate : line.details }}</div>
            </div>
        </div>
    </div>
    <div class="flexspace-l" ng-if="hasDirty || hasPostFinalize"></div>
    <div class="flexitem-0 ll-info flexcnt" ng-if="hasDirty" translate>CHANGELOG.VIEWER.EXTRODIRTY</div>
    <div class="flexitem-0 ll-info flexcnt" ng-if="hasPostFinalize" translate>CHANGELOG.VIEWER.EXTROPOSTFIN</div>
    <div class="flexitem-0 ll-info flexcnt" ng-if="lines.length < 1" translate>CHANGELOG.VIEWER.NOENTRIES</div>
</div>
