<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1">{{ 'MEASUREMENTERRORCATEGORIES.TITLE' | translate }}</div>
    </div>

    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="ctrl.items">

        <div class="flexitem-0 ll-snippetadd ll-ptrhand" ng-click="ctrl.addCategory()"><i class="fa fa-plus-square"></i> {{ 'MEASUREMENTERRORCATEGORIES.ADD' | translate }}</div>
        <div class="flexspace-m"></div>

        <div class="flexitem-1 ll-scroll ll-mh500 flexvcnt" ui-sortable="sublistSortOptions" ng-model="ctrl.items">
            <div class="flexitem-0 flexcnt ll-attribute" ng-repeat="item in ctrl.items" ng-class="{ 'll-snippetodd': $odd }">
                <div class="flexspace-m"></div>
                <div class="flexitem-0 ll-icon ll-ptrhand ll-dragaction"><i class="fa fa-fw fa-sort" tooltip-popup-delay='1000' tooltip="{{ 'MEASUREMENTERRORCATEGORIES.TOOLTIP.REORDER' | translate }}"></i></div>
                <div class="flexspace-m"></div>
                <div class="flexitem-1 flexwrap">{{ item.name | translate_model }}
                </div>
                <div class="flexspace-l"></div>
                <div class="flexitem-0 text-danger ll-icon ll-ptrhand"><i class="fa fa-fw fa-trash-o" ng-click="ctrl.removeCategory(item)" tooltip-popup-delay='1000' tooltip="{{ 'MEASUREMENTERRORCATEGORIES.DELETE' | translate }}"></i></div>
                <div class="flexitem-0 ll-icon ll-ptrhand"><i class="fa fa-fw fa-edit" ng-click="ctrl.editCategory(item)" tooltip-popup-delay='1000' tooltip="{{ 'MEASUREMENTERRORCATEGORIES.EDIT' | translate }}"></i></div>
            </div>
        </div>
    </form>

    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="ctrl.items">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-primary" ng-click="ctrl.close()"><i class="fa fa-ban"></i> {{ 'SNIPPET.BUTTON.CLOSE' | translate }}</button></div>
    </div>
</div>
