<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-0">{{ 'VIEWER.VIDEO.TITLE' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-subheader flexbottom">{{ title }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div class="flexitem-1 ll-body ll-videopane flexcnt-c">
        <div class="flexvcnt-c flexitem-0">
            <videogular class="flexitem-0" vg-width="config.width" vg-height="config.height" vg-theme="config.theme" vg-autoplay="config.autoplay" vg-stretch="config.stretch" vg-responsive="config.responsive">
                <video class='videoPlayer' preload='none'>
                    <source ng-src='{{ medium.url }}' type='{{ medium.type }}'>
                </video>

                <vg-controls vg-autohide="config.autoHide" vg-autohide-time="config.autoHideTime" style="height: 50px;">
                    <vg-play-pause-button></vg-play-pause-button>
                    <vg-timeDisplay>{{ currentTime }}</vg-timeDisplay>
                    <vg-scrubBar>
                        <vg-scrubbarcurrenttime></vg-scrubbarcurrenttime>
                    </vg-scrubBar>
                    <vg-timeDisplay>{{ totalTime }}</vg-timeDisplay>
                    <vg-volume>
                        <vg-mutebutton></vg-mutebutton>
                        <vg-volumebar></vg-volumebar>
                    </vg-volume>
                </vg-controls>

                <vg-buffering></vg-buffering>
            </videogular>
        </div>
    </div>
</div>
