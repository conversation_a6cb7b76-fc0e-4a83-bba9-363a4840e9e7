<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
  <div class="flexitem-0 ll-header flexcnt">
    <div class="flexitem-1" translate>WFLOW.INTRO.FILTER.TITLE</div>
  </div>

  <form role="form" class="flexitem-1 ll-body flexvcnt">
    <div class="flexitem-0 ll-meditortext flexwrap">{{'WFLOW.INTRO.FILTER.FILTTITLE' | translate}}</div>
    <div class="flexspace-m"></div>

    <div class="flexitem-0 flexcnt">
      <div class="flexitem-0 btn-group">
        <label class="btn btn-primary" ng-click="ctrl.toggleCheckbox('useOnCodes')" ng-class="{'btn-success': ctrl.listFilter.useOnCodes}" ng-disabled="ctrl.focusUser" ng-model="ctrl.listFilter.useOnCodes">{{ 'WFLOW.INTRO.FILTER.FILTCODE' | translate }}</label>
        <label class="btn btn-primary" ng-click="ctrl.toggleCheckbox('useOnTitle')" ng-class="{'btn-success': ctrl.listFilter.useOnTitle}" ng-disabled="ctrl.focusUser" ng-model="ctrl.listFilter.useOnTitle">{{ 'WFLOW.INTRO.FILTER.FILTTEXT' | translate }}</label>
      </div>
      <div class="flexspace-l"></div>
      <div class="flexitem-0 btn-group">
        <label class="btn btn-primary" ng-click="ctrl.toggleCheckbox('applyProcedures')" ng-class="{'btn-success': ctrl.listFilter.applyProcedures}" ng-disabled="ctrl.focusUser" ng-model="listFilter.applyProcedures">{{ 'WFLOW.INTRO.FILTER.FILTPROC' | translate }}</label>
        <label class="btn btn-primary" ng-click="ctrl.toggleCheckbox('applySteps')" ng-class="{'btn-success': ctrl.listFilter.applySteps}" ng-disabled="ctrl.focusUser" ng-model="listFilter.applySteps">{{ 'WFLOW.INTRO.FILTER.FILTSTEP' | translate }}</label>
        <label class="btn btn-primary" ng-click="ctrl.toggleCheckbox('applyMeasures')" ng-class="{'btn-success': ctrl.listFilter.applyMeasures}" ng-disabled="ctrl.focusUser" ng-model="listFilter.applyMeasures">{{ 'WFLOW.INTRO.FILTER.FILTMEAS' | translate }}</label>
      </div>
    </div>

    <div class="flexspace-l"></div>
    <div class="flexitem-0"><input class="form-control ll-meditorinput" type="text" ng-disabled="ctrl.focusUser" ng-model="ctrl.listFilter.filterText"></div>

    <div class="flexspace-l"></div>

    <div class="flexitem-0 ll-meditortext flexwrap">{{'WFLOW.INTRO.FILTER.FILTMARKELEMS' | translate}}</div>
    <div class="flexspace-m"></div>
    <div class="btn-group flexitem-0 ll-tooltip-overflow">
      <label class="btn btn-primary" ng-click="ctrl.toggleRadioBtn('unfin')" ng-class="{'btn-success active': ctrl.filterStatus.unfin}"  ng-disabled="ctrl.checkStatus < 20 || ctrl.focusUser" tooltip-popup-delay='1000' tooltip-placement="right" tooltip="{{ 'WFLOW.INTRO.FILTER.FILTUNFIN' | translate }}"><i class="fa fa-circle-o"></i></label>
      <label class="btn btn-primary" ng-click="ctrl.toggleRadioBtn('success')" ng-class="{'btn-success active': ctrl.filterStatus.success}" ng-disabled="ctrl.checkStatus < 20 || ctrl.focusUser" tooltip-popup-delay='1000' tooltip-placement="right" tooltip="{{ 'WFLOW.INTRO.FILTER.FILTSUCC' | translate }}"><i class="fa fa-thumbs-up"></i></label>
      <label class="btn btn-primary" ng-click="ctrl.toggleRadioBtn('fail')" ng-class="{'btn-success active': ctrl.filterStatus.fail}" ng-disabled="ctrl.checkStatus < 20 || ctrl.focusUser" tooltip-popup-delay='1000' tooltip-placement="right" tooltip="{{ 'WFLOW.INTRO.FILTER.FILTFAIL' | translate }}"><i class="fa fa-thumbs-down"></i></label>
    </div>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-meditortext flexwrap">{{'WFLOW.INTRO.FILTER.FILTUSER' | translate}}</div>
    <div class="flexspace-m"></div>
    <div class="flexitem-0 btn-group">
      <label class="btn btn-primary" ng-class="{'btn-success': ctrl.focusUser}" ng-model="ctrl.focusUser" btn-checkbox><i class="fa fa-user"></i></label>
    </div>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-meditortext flexwrap">{{'WFLOW.INTRO.FILTER.HIDEINSTR' | translate}}</div>
    <div class="flexspace-m"></div>
    <div class="flexitem-0 btn-group">
      <label class="btn btn-primary" ng-class="{'btn-success': ctrl.listFilter.hideInstructionSteps}" ng-model="ctrl.listFilter.hideInstructionSteps" btn-checkbox><i class="fa fa-book"></i></label>
    </div>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-meditortext flexwrap">{{'WFLOW.INTRO.FILTER.HIDEOMITTEDORSKIPPED' | translate}}</div>
    <div class="flexspace-m"></div>
    <div class="flexitem-0 btn-group">
      <label class="btn btn-primary" ng-class="{'btn-success': ctrl.listFilter.hideOmittedOrSkipped}" ng-model="ctrl.listFilter.hideOmittedOrSkipped" btn-checkbox><i class="fa fa-times-circle-o"></i></label>
    </div>
    <div class="flexspace-l"></div>
  </form>

  <div class="flexspace-l"></div>
  <div class="flexitem-0 ll-buttonbar flexcnt">
    <div class="flexitem-1"></div>
    <div class="flexitem-0"><button class="btn btn-danger" ng-click="ctrl.close(true)"><i class="fa fa-ban"></i> <span translate>UI.BUTTONS.MEDITOR.CANCEL</span></button></div>
    <div class="flexspace-s"></div>
    <div class="flexitem-0"><button class="btn btn-primary" ng-click="ctrl.disableFilter()"><i class="fa fa-times"></i> <span translate>WFLOW.INTRO.FILTER.DISABLE</span></button></div>
    <div class="flexspace-s"></div>
    <div class="flexitem-0"><button class="btn btn-success" ng-click="ctrl.enableFilter()"><i class="fa fa-filter"></i> <span translate>UI.BUTTONS.MEDITOR.SAVE</span></button></div>
  </div>
</div>