<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1">{{ 'SETTINGS.PROC.TAGS.TITLE' | translate }}</div>
    </div>

    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="ctrl.items">

        <div class="flexitem-0 ll-snippetadd ll-ptrhand" ng-click="ctrl.addTag()" ng-show="ctrl.isAddButtonEnabled()"><i class="fa fa-plus-square"></i> {{ 'SETTINGS.PROC.TAGS.ADD' | translate }}</div>
        <div class="flexspace-m"></div>

        <div class="flexitem-1 ll-scroll ll-mh500 flexvcnt"ng-model="ctrl.items">
            <div class="flexitem-0 flexcnt ll-attribute" ng-repeat="item in ctrl.items" ng-class="{ 'll-snippetodd': $odd }">
                <div class="flexspace-m"></div>
                <div class="flexitem-1 flexwrap">{{ item.value | translate_model }}

                    <div class="flexitem-0 flexvcnt" ng-show="item.collapsed">
                        <div class="flexitem-0 flexcnt ll-subattribute" ng-repeat="sub_value in item.sub_values track by $index">
                            <div class="flexspace-l"></div>
                            <div class="flexitem-1 flexwrap">{{sub_value}}</div>
                            <!--<div class="flexitem-0 text-danger ll-icon ll-ptrhand"><i class="fa fa-fw fa-trash-o" ng-click="ctrl.removeTag(item)" tooltip-popup-delay='1000' tooltip="{{ 'SETTINGS.PROC.TAGS.DELETE' | translate }}"></i></div>-->
                            <!--<div class="flexitem-0 ll-icon ll-ptrhand"><i class="fa fa-fw fa-edit" ng-click="ctrl.editTag(item)" tooltip-popup-delay='1000' tooltip="{{ 'SETTINGS.PROC.TAGS.EDIT' | translate }}"></i></div>-->
                        </div>
                    </div>

                </div>
                <div class="flexitem-0 ll-icon ll-ptrhand" ng-click="ctrl.toggleCollapse(item)" ><i class="fa" ng-class="!item.collapsed ? 'fa-chevron-right' : 'fa-chevron-down'" tooltip-popup-delay='1000' tooltip="{{ 'SETTINGS.PROC.TAGS.COLLAPSE' | translate }}"></i></div>
                <div class="flexspace-l"></div>
                <div class="flexitem-0 text-danger ll-icon ll-ptrhand"><i class="fa fa-fw fa-trash-o" ng-click="ctrl.removeTag(item)" tooltip-popup-delay='1000' tooltip="{{ 'SETTINGS.PROC.TAGS.DELETE' | translate }}"></i></div>
                <div class="flexitem-0 ll-icon ll-ptrhand"><i class="fa fa-fw fa-edit" ng-click="ctrl.editTag(item)" tooltip-popup-delay='1000' tooltip="{{ 'SETTINGS.PROC.TAGS.EDIT' | translate }}"></i></div>
            </div>
        </div>
    </form>

    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="ctrl.items">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-primary" ng-click="ctrl.close()"><i class="fa fa-ban"></i> {{ 'SNIPPET.BUTTON.CLOSE' | translate }}</button></div>
    </div>
</div>
