<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1">{{ 'PROCEDURE.FILTER.TITLE' | translate }}</div>
    </div>

    <form role="form" class="ll-body flexvcnt" style="overflow-y: visible !important; overflow-x: visible !important;">
        <div ng-repeat="tag in ctrl.tags track by $index">
            <div class="ll-block">
                <div class="ll-key">{{tag.description[ctrl.getSelectedLanguage().code]}}</div>
                <ui-select multiple ng-model="tag.selectedTagValues" theme="bootstrap" sortable="true" close-on-select="false" style="width: 400px;">
                    <ui-select-match placeholder="{{ 'PROCEDURE.FILTER.PLEASECHOOSE' | translate}}">{{$item.value}}</ui-select-match>
                    <ui-select-choices repeat="tagValue in tag.availableTagValues | filter: {value: $select.search}" ng-hide="!$select.open" refresh-delay="0"
                        position="auto">
                        <div ng-bind-html="tagValue.value | highlight: $select.search"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
        </div>
    </form>

    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="ctrl.cancel()"><i class="fa fa-ban"></i> {{ 'PROCEDURE.FILTER.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="ctrl.applyFilter()"><i class="fa fa-save"></i> {{ 'PROCEDURE.FILTER.APPLY' | translate }}</button></div>
    </div>
</div>
