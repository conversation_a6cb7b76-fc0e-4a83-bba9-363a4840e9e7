<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate>{{ title }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">
        <div class="flexitem-0 ll-meditortext flexwrap" translate>{{ text }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0">
            <input type="text" id="first" ng-model="value[languageinfo.selected]" class="form-control ll-meditorinput" autofocus ui-keypress="quickcloseKeymap">
            <lang-completion-info langinfo="languageinfo" model="value"></lang-completion-info>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button class="btn btn-danger" ng-click="cancel()"><i class="fa fa-save"></i> <span translate>UI.BUTTONS.MEDITOR.CANCEL</span></button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button class="btn btn-primary" ng-click="ok()"><i class="fa fa-save"></i> <span translate>UI.BUTTONS.MEDITOR.SAVE</span></button></div>
    </div>
</div>
