
<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" ng-class="'ll-modalmsg-'+gettypestr()" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header ll-headcol flexcnt">
        <div class="flexitem-1" translate>{{ head }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">
        <div class="flexitem-0 ll-meditortext flexwrap" translate translate-values="intro.vals">{{ intro.msg }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-meditortext flexwrap">
            <ul ng-show="reasons && reasons.length > 1">
                <li ng-repeat="reason in reasons" translate translate-values="reason.vals">{{ reason.msg }}</li>
            </ul>
            <div ng-show="reasons && reasons.length == 1" translate translate-values="reasons[0].vals">{{ reasons[0].msg }}</div>
        </div>
        <div ng-show="type == 5" class="flexspace-m"></div>
        <div class="flexitem-0" ng-show="type == 5">
            <input class="form-control ll-meditorinput" type="text" ng-model="confirmationInput.userInput">
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button class="btn btn-success" ng-click="ok()" ng-show="type == 1 || type == 5" translate>UI.BUTTONS.ALERT.YES</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button class="btn btn-danger" ng-click="cancel()" ng-show="type == 1 || type == 5" translate>UI.BUTTONS.ALERT.NO</button></div>
        <div class="flexitem-0"><button class="btn btn-default" ng-click="ok()" ng-show="type > 1 && type !== 5" translate>UI.BUTTONS.ALERT.OK</button></div>
    </div>
</div>