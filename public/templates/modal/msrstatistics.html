<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-0">{{ 'MSRSTAT.TITLE' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-subheader flexbottom">{{ 'MSRSTAT.SUBT' | translate : { pcode: proc.code } }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="cancel()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div class="flexitem-1 ll-body ll-msrstat flexvcnt">

        <div class="flexitem-0 ll-blocktitle">{{ 'MSRSTAT.SELM.TTL' | translate }}</div>
        <div class="flexitem-0 ll-blockbody flexvcnt">
            <div class="flexitem-0 ll-intro flexwrap">{{ 'MSRSTAT.SELM.TXT' | translate }}</div>
            <div class="flexitem-0 ll-subbuttonbar flexcnt">
                <div class="flexitem-1">{{ 'MSRSTAT.SELM.STATUS' | translate : { msel: selectedMeasures.length } }}</div>
                <div class="flexspace-l"></div>
                <div class="flexitem-0"><button class="btn btn-primary btn-sm" ng-click="reuseMeasures()"><i class="fa fa-recycle"></i> {{ 'MSRSTAT.BUTTON.REUSE' | translate }}</button></div>
                <div class="flexspace-s"></div>
                <div class="flexitem-0"><button class="btn btn-success btn-sm" ng-click="selectMeasures()"><i class="fa fa-list"></i> {{ 'MSRSTAT.BUTTON.SEL' | translate }}</button></div>
            </div>
        </div>

        <div class="flexitem-0 ll-blocktitle">{{ 'MSRSTAT.SELC.TTL' | translate }}</div>
        <div class="flexitem-0 ll-blockbody flexvcnt">
            <div class="flexitem-0 ll-intro flexwrap">{{ 'MSRSTAT.SELC.TXT' | translate }}</div>
            <div class="flexitem-fix150 ll-list ll-scroll flexvcnt">
                <div class="flexitem-0 ll-listitem flexcnt" ng-repeat="check in selectedChecks">
                    <div class="flexitem-fix30 flexcenter text-center"><i ng-click="removeCheck($index)" tooltip-popup-delay='1000' tooltip="{{ 'MSRSTAT.TOOLTIP.REMCHK' | translate }}" class="fa fa-minus-circle text-danger"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1" translate-compile translate="MSRSTAT.CLIST" translate-values="check"></div>
                </div>
            </div>
            <div class="flexitem-0 ll-subbuttonbar flexcnt">
                <div class="flexitem-1">{{ 'MSRSTAT.SELC.STATUS' | translate : { csel: selectedChecks.length } }}</div>
                <div class="flexspace-l"></div>
                <div class="flexitem-0"><button class="btn btn-primary btn-sm" ng-click="reuseChecks()"><i class="fa fa-recycle"></i> {{ 'MSRSTAT.BUTTON.REUSE' | translate }}</button></div>
                <div class="flexspace-s"></div>
                <div class="flexitem-0"><button class="btn btn-success btn-sm" ng-click="addChecks()"><i class="fa fa-plus-square-o"></i> {{ 'MSRSTAT.BUTTON.ADD' | translate }}</button></div>
            </div>
        </div>

        <div class="flexspace-l"></div>
        <div class="flexitem-0 ll-buttonbar flexcnt">
            <div class="flexitem-0"><button class="btn btn-danger" ng-click="cancel()"><i class="fa fa-ban"></i> {{ 'MSRSTAT.BUTTON.CANCEL' | translate }}</button></div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0"><button class="btn btn-primary" ng-click="okfile()" ng-show="selectedChecks.length > 0 && selectedMeasures.length > 0"><i class="fa fa-file-text-o"></i> {{ 'MSRSTAT.BUTTON.EXPORT' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button class="btn btn-primary" ng-click="okpdf()" ng-show="selectedChecks.length > 0 && selectedMeasures.length > 0"><i class="fa fa-file-pdf-o"></i> {{ 'MSRSTAT.BUTTON.GENERATE' | translate }}</button></div>
        </div>

    </div>
</div>
