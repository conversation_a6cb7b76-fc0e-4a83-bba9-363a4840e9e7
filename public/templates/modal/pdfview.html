<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" style="height: 100%"
     draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
  <div class="flexitem-0 ll-header flexcnt">
    <div class="flexitem-0">{{ 'VIEWER.PDF.TITLE' | translate }}</div>
    <div class="flexspace-m"></div>
    <div class="flexitem-1 ll-subheader flexbottom">{{ title }}</div>
    <div class="flexspace-m"></div>
    <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i>
    </div>
  </div>
  <div class="flexitem-0 ll-body ll-pdfpane">
    <object data="{{pdfurl}}"  type="application/pdf" class="ll-pdf">
      <param name="toolbar" value="0">
    </object>
    <div class="flexitem-0 ll-button flexbottom">
      <button type="button" class="btn btn-primary ll-download-button" tooltip-popup-delay='1000' tooltip="{{ 'MEDIAMGR.TOOLTIP.DOWNLOAD' | translate }}" ng-click="downloadPdf()"><i class="fa fa-download"></i></button>
    </div>
  </div>
</div>
