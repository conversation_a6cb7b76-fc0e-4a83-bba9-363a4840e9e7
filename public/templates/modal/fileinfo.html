<div class="flexvcnt ll-modalmsg-primary" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 flexcnt">
        <div class="flexitem-1 ll-header">{{ fileInfo.filename }}</div>
    </div>
    <div class="flexitem-1 flexvcnt ll-body">
        <div class="flexspace-m"></div>
        <div class="flexcnt ll-blockheader">
            <div class="flexitem-0">{{ 'WFLOW.INTRO.FILEINFO.TITLE' | translate }}</div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexcnt">
            <div class="flexvcnt">
                <div class="flexitem-0">{{ 'WFLOW.INTRO.FILEINFO.UPLOADDATE' | translate }}</div>
                <div class="flexitem-0">{{ 'WFLOW.INTRO.FILEINFO.UPLOADTIME' | translate }}</div>
                <div class="flexitem-0">{{ 'WFLOW.INTRO.FILEINFO.UPLOADEDBY' | translate }}</div>
            </div>
            <div class="flexspace-l"></div>
            <div class="flexvcnt">
                <div class="flexitem-0">{{ fileInfo.date | date:i18n.selectedLanguage.dformat }}</div>
                <div class="flexitem-0">{{ fileInfo.time }}</div>
                <div class="flexitem-0">{{ fileInfo.user }}</div>
            </div>
        </div>
    </div>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt-r">
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button class="btn btn-default" ng-click="ok()" translate>UI.BUTTONS.ALERT.OK</button></div>
    </div>
</div>