<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate>{{ title }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="userPickLoaded">
        <div class="flexitem-0 ll-meditortext flexwrap" translate>{{ text }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 flexcnt">
            <div class="flexitem-1 ll-meditor-input flexcenter">{{ 'MEDITOR.GROUP' | translate }}:</div>
            <div class="flexitem-5"><select id="selgrp" class="form-control ll-meditorselect" ng-model="selgrp" ng-show="ressource" ng-options="group.id as group.name for group in groups" ng-selected="setupPicker(true)"></select></div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 flexcnt">
            <div class="flexitem-1 ll-meditor-input flexcenter">{{ 'MEDITOR.USER' | translate }}:</div>
            <div class="flexitem-5"><select id="selusr" class="form-control ll-meditorselect" ng-model="selusr" ng-show="ressource" ng-options="user.id as user.name for user in users"></select></div>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button class="btn btn-danger" ng-click="cancel()"><i class="fa fa-save"></i> <span translate>UI.BUTTONS.MEDITOR.CANCEL</span></button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button class="btn btn-primary" ng-click="ok()" ng-show="ressource"><i class="fa fa-check"></i> <span translate>UI.BUTTONS.MEDITOR.SELECT</span></button></div>
    </div>
</div>
