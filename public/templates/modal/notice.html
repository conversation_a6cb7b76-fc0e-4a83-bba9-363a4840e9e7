<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1">{{ 'NOTICES.TITLE.PROBLREPNO' | translate }} {{ notice.id }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt ll-scroll ll-bodylimit">

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'NOTICES.VIEW.CATEGORY' | translate }} <i class="fa fa-edit ll-editicon ll-ptrhand" ng-click="correctCategory()"></i></div>
            <div class="ll-pltext">
                {{ notice.category.text }}
            </div>
        </div>
        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.VIEW.LOCATION' | translate }}</div>
            <div class="ll-pltext">
                <span ng-repeat="segment in notice.pathstrings"><span translate-compile translate="{{segment.text}}" translate-values="segment"></span><span ng-if="!$last">, </span></span>
            </div>
        </div>
        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.VIEW.DESC' | translate }} <i class="fa fa-edit ll-editicon ll-ptrhand" ng-click="correctDescription()"></i></div>
            <div class="ll-pltext">
                {{ notice.text }}
            </div>
        </div>
        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.VIEW.ARTICLE' | translate }} <i class="fa fa-edit ll-editicon ll-ptrhand" ng-click="correctArticle()"></i></div>
            <div class="ll-pltext">
                <span ng-if="notice.articlestring !== ''" ng-bind-html="notice.articlestring"></span>
                <b ng-if="notice.articlestring === ''" translate="NOTICES.VIEW.NOTEXT"></b>
            </div>
        </div>
        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.VIEW.TIMELOSS' | translate }} <i class="fa fa-edit ll-editicon ll-ptrhand" ng-click="correctTimeloss()"></i></div>
            <div class="ll-pltext">
                <b>{{ notice.timelossstring | translate }}</b>
            </div>
        </div>
        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.VIEW.STATUS' | translate }}</div>
            <div class="ll-pltext flexvcnt">
                <div class="flexitem-0" ng-repeat="sstring in notice.statusstrings">
                    <div translate-compile translate="{{sstring.text}}" translate-values="sstring"></div>
                    <div class="ll-plcomment" ng-if="sstring.comment">{{sstring.comment}}</div>
                </div>
            </div>
        </div>

    </form>

    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-0" ng-if="status == 1"><button type="button" class="btn btn-success" ng-click="switch(1,2)"><i class="fa fa-mail-forward"></i> {{ 'NOTICES.BUTTON.STT_12' | translate }}</button></div>
        <div class="flexitem-0" ng-if="status == 2"><button type="button" class="btn btn-success" ng-click="switch(2,5)"><i class="fa fa-mail-forward"></i> {{ 'NOTICES.BUTTON.STT_25' | translate }}</button></div>
        <div class="flexitem-0" ng-if="status == 5"><button type="button" class="btn btn-success" ng-click="switch(5,9)"><i class="fa fa-mail-forward"></i> {{ 'NOTICES.BUTTON.STT_59' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0" ng-if="status == 2"><button type="button" class="btn btn-success" ng-click="switch(2,1)"><i class="fa fa-mail-reply"></i> {{ 'NOTICES.BUTTON.STT_21' | translate }}</button></div>
        <div class="flexitem-0" ng-if="status == 5"><button type="button" class="btn btn-success" ng-click="switch(5,2)"><i class="fa fa-mail-reply"></i> {{ 'NOTICES.BUTTON.STT_52' | translate }}</button></div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="close()"><i class="fa fa-ban"></i> {{ 'NOTICES.BUTTON.CLOSE' | translate }}</button></div>
        <div class="flexspace-s"></div>
    </div>
</div>
