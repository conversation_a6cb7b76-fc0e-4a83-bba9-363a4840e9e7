<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-0">{{ 'VIEWER.IMAGE.TITLE' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-subheader flexbottom">{{ title }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div id="fullpane" class="flexitem-1 ll-body ll-imagepane flexcnt">
        <div class="flexitem-1 ll-imagemob">
            <img style="margin: auto" id="bigimg" width="{{image.width}}" height="{{image.height}}" ng-src="{{ image.path }}"/>
        </div>
    </div>
    <div class="flexspace-m" ng-show="scalesteps.length > 0 || medium.imageList.length > 1"></div>
    <div class="flexcnt" style="justify-content: space-between" ng-if="scalesteps.length > 0 || medium.imageList.length > 1">
        <div class="flexcnt ll-imagezoombar" ng-if="scalesteps.length > 0">
            <div class="flexitem-0" ng-repeat="step in scalesteps"><button type="button" class="btn" ng-class="{ 'btn-success': step.scale === image.scale, 'btn-default': step.scale !== image.scale }" ng-click="zoom(step.scale)">{{ 'VIEWER.IMAGE.ZOOM.'+step.name | translate : { factor: step.factor } }}</button>&nbsp;&nbsp;</div>
        </div>
        <div class="flexcnt" ng-if="medium.imageList.length > 1">
            <div class="flexitem-0 ll-button"><button type="button" class="btn btn-default" ng-click="toggleImage($event, -1)"><i class="fa fa-chevron-left"></i> {{ 'WFLOW.STEP.IMAGE.PREVIOUS' | translate }}</button>&nbsp;&nbsp;</div>
            <div class="flexitem-0 ll-button"><button type="button" class="btn btn-default" ng-click="toggleImage($event, 1)"><i class="fa fa-chevron-right"></i> {{ 'WFLOW.STEP.IMAGE.NEXT' | translate }}</button>&nbsp;&nbsp;</div>
        </div>
    </div>
</div>
