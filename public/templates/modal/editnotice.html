<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1">{{ 'NOTICES.TITLE.PROBLREP' | translate }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="stepNEditLoaded">

        <div class="flexitem-0 ll-blockhdr">{{ 'NOTICES.TEXT.DESC' | translate }}</div>

        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.TITLE.DESC' | translate }}</div>
            <div class="ll-edit flexvcnt">
                <div class="flexitem-0">
                    <textarea rows="5" id="n_text" ng-model="notice.text" class="form-control ll-inputsm" autofocus></textarea>
                </div>
                <div class="flexspace-s"></div>
                <div class="flexitem-0 flexcnt">
                    <div class="flexitem-0 ll-noticesmallsubkey">{{ 'NOTICES.TITLE.PROPOSALS' | translate }} </div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-1"><select id="n_textprop" class="form-control ll-input ll-noticesmallcombo" ng-model="select.textprop_sel" ng-options="item.id as item.text for item in select.textprop"></select></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-0"><button type="button" class="btn btn-xs btn-success" ng-click="useTextProposal()"><i class="fa fa-redo"></i> {{ 'NOTICES.BUTTON.USE' | translate }}</button></div>
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-blockhdr">{{ 'NOTICES.TEXT.CHOOSECAT' | translate }}</div>

        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.TITLE.CAT' | translate }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <select id="n_cat" class="form-control ll-input" ng-model="notice.category_id" ng-options="item.id as item.text for item in select.categories"></select>
                </div>
                <div class="ll-noticesubkey">{{ 'NOTICES.TITLE.TIMELOSS' | translate }}</div>
                <div class="flexitem-1">
                    <select id="n_timel" class="form-control ll-input" ng-model="notice.timeloss" ng-options="item.min as item.text | translate for item in select.timeloss"></select>
                </div>
            </div>
        </div>

        <div class="flexitem-0 ll-blockhdr">{{ 'NOTICES.TEXT.ARTICLE' | translate }}</div>

        <div class="flexitem-0 ll-block ll-afterhdr">
            <div class="ll-key">{{ 'NOTICES.TITLE.ARTNO' | translate }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-1">
                    <input type="text" id="n_artno" ng-model="notice.artno" class="form-control ll-input" typeahead="preval for preval in select.artnoprop | filter:$viewValue:emptyOrMatch" >
                </div>
                <div class="ll-noticesubkey">{{ 'NOTICES.TITLE.ARTDESC' | translate }}</div>
                <div class="flexitem-2">
                    <input type="text" id="n_artbez" ng-model="notice.artdesc" class="form-control ll-input" typeahead="preval for preval in select.artdescprop | filter:$viewValue:emptyOrMatch" >
                </div>
            </div>
        </div>

        <!-- Zeitverlust -->

    </form>

    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="stepNEditLoaded">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancel()"><i class="fa fa-ban"></i> {{ 'NOTICES.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="save()"><i class="fa fa-save"></i> {{ 'NOTICES.BUTTON.SEND' | translate }}</button></div>
    </div>
</div>
