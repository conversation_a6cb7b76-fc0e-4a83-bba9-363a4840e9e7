<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal ll-sresultview" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-0">{{ 'GSEARCH.RESULT.TITLE' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-subheader flexbottom">{{ searchstring }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div class="flexitem-0 ll-intro flexcnt" translate>GSEARCH.RESULT.TEXT</div>
    <div class="flexitem-0 ll-hint flexcnt" ng-if="result.length < 1" translate>GSEARCH.RESULT.NOMATCH</div>
    <div id="fullpane" class="flexitem-1 ll-body ll-resbody ll-scroll">
        <div class="flexvcnt">
            <div class="flexitem-0 flexvcnt ll-result ll-ptrhand" ng-repeat="item in result" ng-click="goto(item)">
                <div class="flexitem-0 ll-object" translate-compile translate="{{ 'GSEARCH.TYPE.'+item.type.toUpperCase()+'.DISPLAY' }}" translate-values="item"></div>
                <div class="flexitem-0 ll-string ptr-hand">
                    <span class="ll-field">{{ 'GSEARCH.FIELD.'+item.field.toUpperCase() | translate }}: </span>
                    <span class="ll-prepost" ng-repeat-start="match in item.matches">{{ match.prematch }}</span>
                    <span class="ll-match">{{ match.match }}</span>
                    <span class="ll-prepost">{{ match.postmatch }}</span>
                    <span class="ll-prepost" ng-repeat-end ng-if="!$last"> / </span>
                </div>
            </div>
        </div>
    </div>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-0 ll-intro flexcnt" ng-if="result.length >= 50" translate>GSEARCH.RESULT.TOOMUCH</div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button class="btn btn-success" ng-click="newSearch()"><i class="fa fa-search"></i> <span translate>GSEARCH.BUTTONS.NEWSEARCH</span></button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button class="btn btn-primary" ng-click="close()"><i class="fa fa-times-circle-o"></i> <span translate>GSEARCH.BUTTONS.CLOSE</span></button></div>
    </div>
</div>
