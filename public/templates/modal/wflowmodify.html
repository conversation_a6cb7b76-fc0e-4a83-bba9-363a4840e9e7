<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1, initY: 100 }">
    <div class="flexitem-0 ll-header flexcnt" ng-hide="wfModEditorLoaded">
        <div class="flexitem-1">{{ 'WFLOWEDIT.LOADING' | translate }}</div>
    </div>
    <div class="flexitem-0 ll-header flexcnt" ng-show="wfModEditorLoaded">
        <div class="flexitem-0">{{ 'WFLOWEDIT.TITLE' | translate }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-subheader flexbottom" translate="{{subtitle.msg}}" translate-values="subtitle.vals"></div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 text-danger flexcenter ll-ptrhand" ng-click="close()"><i class="fa fa-times-circle"></i></div>
    </div>
    <div class="flexitem-1 ll-body ll-wfmodeditor flexvcnt" ng-show="wfModEditorLoaded">
        <div class="flexitem-0 ll-blocktitle">{{ 'WFLOWEDIT.ACTIVER' | translate }}</div>
        <div class="flexitem-0 ll-blockbody flexvcnt">
            <div class="flexitem-0 ll-listitem flexcnt" ng-repeat="wfmod in wfmods">
                <div class="flexitem-fix30 flexcenter text-center ll-ptrhand"><i ng-show="$index > 0" tooltip-popup-delay='1000' tooltip="{{ 'WFLOWEDIT.TOOLTIP.REMMOD' | translate }}" ng-click="removeWorkflowModifier($index)" class="fa fa-minus-circle text-danger"></i></div>
                <div class="flexspace-m"></div>
                <div class="flexitem-1 flexwrap" ng-class="{ 'll-intro': $index == 0, 'll-actdesc': $index > 0 } " translate translate-values="wfmod[1]">{{ wfmod[0] }}</div>
            </div>
        </div>
        <div class="flexitem-0 ll-blocktitle">{{ 'WFLOWEDIT.ADDRULE' | translate }}</div>
        <div class="flexitem-0 ll-blockbody flexcnt">
            <div class="flexitem-fix30 flexcenter text-center ll-ptrhand"><i class="fa fa-plus-circle text-success" tooltip-popup-delay='1000' tooltip="{{ 'WFLOWEDIT.TOOLTIP.ADDMOD' | translate }}" ng-click="addWorkflowModifier()"></i></div>
            <div class="flexspace-m"></div>
            <div class="flexitem-1 flexvcnt">
                <div class="flexitem-0 flexcnt">
                    <div class="flexitem-1 ll-seltext">{{ 'WFLOWEDIT.RULE1' | translate }}</div>
                    <div class="flexitem-2">
                        <ui-select class="ll-selbox" ng-model="newitem.type" theme="bootstrap" on-select="updateComboboxes(0)" search-enabled="false" reset-search-input >
                            <ui-select-match>{{$select.selected.title}}</ui-select-match>
                            <ui-select-choices location="wflowmodify-top" repeat="item.id as item in types | filter: $select.search" refresh-delay="10" position="down">
                                <span ng-bind-html="item.title | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-seltext">{{ 'WFLOWEDIT.RULE2' | translate }}</div>
                    <div class="flexitem-5">
                        <ui-select class="ll-selbox" ng-model="newitem.code" theme="bootstrap" reset-search-input >
                            <ui-select-match>{{$select.selected.title}}</ui-select-match>
                            <ui-select-choices location="wflowmodify-top" repeat="item.id as item in codes | filter: $select.search" refresh-delay="10" position="down">
                                <span ng-bind-html="item.title | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
                <div class="flexspace-s"></div>
                <div class="flexitem-0 flexcnt">
                    <div class="flexitem-1 ll-seltext">{{ 'WFLOWEDIT.RULE3' | translate }}</div>
                    <div class="flexitem-2">
                        <ui-select class="ll-selbox" ng-model="newitem.trigger" theme="bootstrap" on-select="updateComboboxes(0)" search-enabled="false" reset-search-input >
                            <ui-select-match>{{$select.selected.title}}</ui-select-match>
                            <ui-select-choices location="wflowmodify-bottom" repeat="item.id as item in triggers | filter: $select.search" refresh-delay="10" position="down">
                                <span ng-bind-html="item.title | translate | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-seltext">{{ 'WFLOWEDIT.RULE4' | translate }}</div>
                    <div class="flexitem-5">
                        <ui-select class="ll-selbox" ng-model="newitem.res" theme="bootstrap" search-enabled="false" reset-search-input >
                            <ui-select-match>{{$select.selected.title}}</ui-select-match>
                            <ui-select-choices location="wflowmodify-bottom" repeat="item.id as item in resolutions | filter: $select.search" refresh-delay="10" position="down">
                                <span ng-bind-html="item.title | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
