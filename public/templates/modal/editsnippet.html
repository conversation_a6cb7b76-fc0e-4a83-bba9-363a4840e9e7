<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1">{{ catname.full | translate }}</div>
    </div>

    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="stepSnippetEditLoaded">

        <div class="flexitem-0 ll-snippetadd ll-ptrhand" ng-click="addSnippet()"><i class="fa fa-plus-square"></i> {{ catname.singular | translate }} {{ 'SNIPPET.TITLE.ADD' | translate }}</div>
        <div class="flexspace-m"></div>

        <div class="flexitem-1 ll-scroll ll-mh500 flexvcnt" ui-sortable="sublistSortOptions" ng-model="snippets">
            <div class="flexitem-0 flexcnt ll-snippet" ng-repeat="snippet in snippets" ng-class="{ 'll-snippetodd': $odd }">
                <div class="flexitem-0 ll-icon ll-ptrhand ll-dragaction"><i class="fa fa-fw fa-sort" tooltip-popup-delay='1000' tooltip="{{ 'SNIPPET.TOOLTIP.REORD' | translate }}"></i></div>
                <div class="flexitem-0 ll-icon ll-ptrhand" ng-show="snippet.status == 1"><i class="fa fa-fw fa-unlock" ng-click="lockSnippet(snippet)" tooltip-popup-delay='1000' tooltip="{{ 'SNIPPET.TOOLTIP.LOCK' | translate }}"></i></div>
                <div class="flexitem-0 ll-icon ll-ptrhand" ng-show="snippet.status == 2"><i class="fa fa-fw fa-lock" ng-click="unlockSnippet(snippet)" tooltip-popup-delay='1000' tooltip="{{ 'SNIPPET.TOOLTIP.UNLOCK' | translate }}"></i></div>
                <div class="flexspace-m"></div>
                <div class="flexitem-1 flexwrap" ng-class="{ 'll-locked': snippet.status == 2 }">{{ snippet.text }}</div>
                <div class="flexspace-m" ng-show="mutable"></div>
                <div class="flexitem-0 text-danger ll-icon ll-ptrhand"><i class="fa fa-fw fa-trash-o" ng-click="deleteSnippet(snippet)" tooltip-popup-delay='1000' tooltip="{{ 'SNIPPET.TOOLTIP.DELETE' | translate }}"></i></div>
                <div class="flexitem-0 ll-icon ll-ptrhand"><i class="fa fa-fw fa-edit" ng-click="editSnippet(snippet)" tooltip-popup-delay='1000' tooltip="{{ 'SNIPPET.TOOLTIP.EDIT' | translate }}"></i></div>
            </div>
        </div>
    </form>

    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="stepSnippetEditLoaded">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-primary" ng-click="close()"><i class="fa fa-ban"></i> {{ 'SNIPPET.BUTTON.CLOSE' | translate }}</button></div>
    </div>
</div>
