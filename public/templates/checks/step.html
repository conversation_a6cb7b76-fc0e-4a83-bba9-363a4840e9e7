<div class="ll-mainview" ng-swipe-left="forward()" ng-swipe-right="rewind()" tabindex="1" ng-keyup="onKeyPress($event)" autofocus>
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-1 ll-fullheight flexvcnt" ng-show="wfStepLoaded">
            <div class="flexitem-1 ll-wflowinfo flexvcnt">
                <div class="flexitem-0 ll-toptitlesml flexwrap">
                    {{ 'WFLOW.STEP.MODEL' | translate }}
                    {{ step.check.model.code }}:
                    <span class="ll-emph ll-ptrhand" ng-show="showGotoModel()" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.STEP.TOOLTIP.GOTOMODEL' | translate }}" ng-click="gotoModel()">{{ step.check.model.title | translate_model }},</span>
                    <span class="ll-emph" ng-hide="showGotoModel()">{{ step.check.model.title | translate_model }},</span>
                    {{ 'WFLOW.STEP.UNIT' | translate }}
                    <span class="ll-emph ll-ptrhand" ng-show="showGotoUnit()" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.STEP.TOOLTIP.GOTOUNIT' | translate }}" ng-click="gotoUnit()">{{ step.check.unit.code }}</span>
                    <span class="ll-emph" ng-hide="showGotoUnit()">{{ step.check.unit.code }}</span>
                    ({{ step.check.unit.customer }}),
                    <span class="ll-emph">{{ step.check.checktype.title | translate_model }}</span>
                    ({{ step.check.checktype.code}})<br>
                    {{ 'WFLOW.STEP.PROCEDURE' | translate }} {{ step.procedure.code }}:
                    <span class="ll-emph ll-ptrhand" ng-show="hasGrant('MNGPSM') && !isMobileApp" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.STEP.TOOLTIP.GOTOPROC' | translate }}" ng-click="gotoProcedure()">{{ step.procedure.title | translate_model }}</span>
                    <span class="ll-emph" ng-hide="hasGrant('MNGPSM') && !isMobileApp">{{ step.procedure.title | translate_model }}</span>
                </div>
                <div class="flexitem-0 ll-title flexcnt">
                    <div class="flexitem-1 flexbottom">{{ 'WFLOW.STEP.STEP' | translate }} {{ step.code }}: <span class="ll-emph">{{ step.title | translate_model }}</span></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-0 ll-statusicon" ng-show="step.steptype == 1"><i class="fa fa-book"></i></div>
                    <div class="flexitem-0 ll-statusicon text-danger" ng-show="step.readonly && !step.cdentry.locked && step.steptype != 1"><i class="fa fa-eye"></i></div>
                    <div class="flexitem-0 ll-statusicon text-danger" ng-show="step.cdentry.locked && step.steptype != 1"><i class="fa fa-lock"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-0 flexbottom ll-status text-right" ng-show="step.steptype != 1">{{ checkStatusName() | translate }}</div>
                </div>
                <div class="flexitem-0 ll-testrunbar" ng-if="testrun">{{ ('WFLOW.INTRO.TESTRUN.'+testrun.toUpperCase()) | translate }} {{ step.check.metadata.testrun.item }}</div>
                <div class="flexitem-0 ll-viewbar flexcnt" ng-if="isMobileApp && step.steptype !== 1">
                    <div class="flexitem-1 ll-viewitem" ng-click="changeViewport(0,true)" ng-class="{'ll-selected': viewport === 0}">{{ 'WFLOW.STEP.VIEW.INFO' | translate }}</div>
                    <div class="flexitem-1 ll-viewitem" ng-click="changeViewport(1,step.images.length > 0)" ng-class="{'ll-disabled': step.images.length === 0, 'll-selected': viewport === 1}">{{ 'WFLOW.STEP.VIEW.IMAGES' | translate : { numi: step.images.length } }}</div>
                    <div class="flexitem-1 ll-viewitem" ng-click="changeViewport(2,step.documents.length > 0)" ng-class="{'ll-disabled': step.documents.length === 0, 'll-selected': viewport === 2}">{{ 'WFLOW.STEP.VIEW.DOCS' | translate : { numd: step.documents.length } }}</div>
                </div>
                <div class="flexitem-1 ll-scroll" ng-if="!isMobileApp || viewport === 0" ng-include="'/templates/checks/step_info.html'"></div>
                <div class="flexitem-1 ll-scroll" ng-if="viewport === 1">
                    <div class="flexitem-1 ll-tngallerymob ll-scroll">
                        <div class="ll-thumbnail flexcnt-c" ng-repeat="image in step.images">
                            <div class="flexitem-0 flexvcnt-c">
                                <img class="flexitem-0 ll-ptrhand" ng-src="/media/{{image.thumbnail.filename}}" ng-click="showImage($index,0)" tooltip-popup-delay='1000' tooltip="{{ image.caption ? image.caption : image.fullimage.original_filename }}"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flexitem-1 ll-scroll ll-docgalbodymob" ng-if="viewport === 2" ng-include="'/templates/checks/step_docs.html'"></div>
                <div class="flexspace-m"></div>
                <div class="flexitem-0 ll-buttonbar flexcnt-r">
                    <div class="flexitem-0 ll-button"><button type="button" class="btn btn-primary" ng-click="goBack()"><i class="fa fa-chevron-left"></i> {{ 'WFLOW.STEP.BUTTON.BACK' | translate }}</button></div>
                    <div class="flexitem-0 ll-button" ng-show="showRewindButton()"><button type="button" class="btn btn-primary" ng-click="rewind()"><i class="fa fa-chevron-left"></i> {{ 'WFLOW.STEP.BUTTON.REWIND' | translate }}</button></div>
                    <div class="flexitem-0 ll-button" ng-show="showForwardButton()"><button type="button" class="btn btn-primary" ng-click="forward()"><i class="fa fa-chevron-right"></i> {{ 'WFLOW.STEP.BUTTON.FORWARD' | translate }}</button></div>

                    <div class="flexitem-1"></div>
                    <div class="flexitem-0 ll-button" ng-show="!step.readonly && step.nextstepid && step.cdentry.completem && step.cdentry.completet" ng-click="goForward()"><button type="button" class="btn btn-success"><i class="fa fa-chevron-right"></i> {{ 'WFLOW.STEP.BUTTON.CONTINUE' | translate }}</button></div>
                    <div class="flexitem-0 ll-button" ng-show="!step.readonly && !step.nextstepid && step.cdentry.completem && step.cdentry.completet" ng-click="goBack()"><button type="button" class="btn btn-success"><i class="fa fa-chevron-right"></i> {{ 'WFLOW.STEP.BUTTON.FIN' | translate }}</button></div>
                </div>

                <!-- Progress Bar -->
                <div ng-if="step.procedure.processing_time.hours > 0 || step.procedure.processing_time.minutes > 0" class="ll-procedure-timer" ng-click="toggleProccessingTimeTimer()">
                    <progress animate="false" class="flexcnt">
                        <timer countdown="processingTime.remaining" finish-callback="processingTime.finishedCallback()" max-time-unit="'hour'" auto-start="false" interval="1000"><span>{{hhours}}:{{mminutes}}:{{sseconds}}</span>
                            <div class="ll-timer-bar" ng-class="{'ll-timer-bar-stopped': !processingTime.running, 'll-timer-bar-running': processingTime.running}" ng-style="{'width': ((processingTime.remaining / processingTime.full) * 100)+'%'}"></div>
                        </timer>
                    </progress>
                </div>
            </div>
            <div class="flexitem-0 ll-wflowsublist" ng-if="!isMobileApp && step.steptype !== 1" ng-include="'/templates/checks/step_measures.html'"></div>
            <div class="flexitem-0 ll-wflowsublist" ng-if="!isMobileApp && step.tools.length > 0" ng-include="'/templates/checks/step_tools.html'"></div>
        </div>

        <div class="ll-fullheight ll-measurebar flexvcnt" ng-if="isMobileApp && step.steptype !== 1">
            <div class="flexitem-0 ll-wflowsublist" ng-include="'/templates/checks/step_measures.html'"></div>
            <div class="flexitem-0 ll-wflowsublist" ng-if="step.tools.length > 0" ng-include="'/templates/checks/step_tools.html'"></div>
        </div>

        <div class="flexitem-fix500 ll-fullheight ll-mediabar flexvcnt" ng-if="!isMobileApp || step.steptype === 1" ng-show="wfStepLoaded">
            <div class="flexitem-0 ll-caption">{{ selImage.caption }}</div>
            <div class="flexitem-fix400 flexcnt">
                <div class="flexitem-0 ll-imagebox flexcenter" ng-if="selImage != null">
                    <div ng-click="viewCurrentImage()" class="ll-image" ng-style="{ 'margin-right': selImage.padleft, 'margin-bottom': selImage.padtop, 'margin-left': selImage.padleft, 'margin-top': selImage.padtop }"><img id="zoomimg" width="{{selImage.width}}" height="{{selImage.height}}" zoomable-image="zoomableImageOptions" ng-src="{{selImage.path}}" zoom-image-src="selectedZoomImage"/></div>
                </div>
            </div>
            <div class="flexitem-1 ll-tngallery ll-scroll">
                <div class="ll-thumbnail flexcnt-c" ng-repeat="image in step.images">
                    <div class="flexitem-0 flexvcnt-c">
                        <img class="flexitem-0 ll-ptrhand" ng-src="/media/{{image.thumbnail.filename}}" ng-click="selectImage($index,0)" tooltip-popup-delay='1000' tooltip="{{ image.caption ? image.caption : image.fullimage.original_filename }}"/>
                    </div>
                </div>
            </div>
            <div class="flexitem-0 ll-docgallery" ng-if="step.documents.length > 0">
                <div class="ll-docgaltitle flexcnt ll-ptrhand" ng-click="toggleShowDocs()">
                    <div class="flexitem-1">{{ 'WFLOW.STEP.DOCS' | translate }}</div>
                    <div class="flexitem-fix20 text-center"><i class="fa" ng-class="collapseDocs ? 'fa-chevron-right' : 'fa-chevron-down'"></i></div>
                </div>
                <div class="ll-docgalbody" collapse="collapseDocs" ng-include="'/templates/checks/step_docs.html'"></div>
            </div>
        </div>
    </div>
</div>