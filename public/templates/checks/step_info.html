<div class="flexcnt ll-infogroup">
    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-cubes"></i></div>
    <div class="flexspace-m"></div>
    <div class="flexitem-1 ll-text">
        <!--div>
            {{ 'WFLOW.STEP.PROCEDURE' | translate }} {{ step.procedure.code }}:
            <span class="ll-emph ll-ptrhand" ng-show="hasGrant('MNGPSM')" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.STEP.TOOLTIP.GOTOPROC' | translate }}" ng-click="gotoProcedure()">{{ step.procedure.title | translate_model }}</span>
            <span class="ll-emph" ng-hide="hasGrant('MNGPSM')">{{ step.procedure.title | translate_model }}</span>
        </div-->
        <div class="flexwrap" ng-bind-html="step.procedure.description | translate_model"></div>
    </div>
</div>
<div class="flexcnt ll-infogroup">
    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-cube"></i></div>
    <div class="flexspace-m"></div>
    <div class="flexitem-1 ll-text ll-highlight">
        <div class="flexwrap" ng-bind-html="step.description | translate_model"></div>
    </div>
</div>
<div class="flexcnt ll-infogroup">
    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-user-md"></i></div>
    <div class="flexspace-m"></div>
    <div class="flexitem-1 ll-text flexvcnt">
        <div class="flexitem-0 flexcnt">
            <div class="flexitem-fix200">{{ 'WFLOW.STEP.COMMITTER' | translate }}</div>
            <div class="flexitem-0" ng-show="step.admindata.committer.username">{{ step.admindata.committer.username }} ({{ step.admindata.committer.userid }}, {{ step.admindata.committer.realname }})</div>
            <div class="flexitem-0" ng-hide="step.admindata.committer.username">{{ 'WFLOW.STEP.NOCOMMIT' | translate }}</div>
        </div>
        <div class="flexitem-0 flexcnt" ng-repeat="msr in step.admindata.measurers">
            <div class="flexitem-fix200">{{ 'WFLOW.STEP.MEASURER' | translate : { code: msr.mcode } }}</div>
            <div class="flexitem-0" ng-show="msr.username">{{ msr.username }} ({{ msr.userid }}, {{ msr.realname }}), {{ msr.savedate*1000 | date:i18n.selectedLanguage.tformat:'utc' }}</div>
            <div class="flexitem-0" ng-hide="msr.username">{{ 'WFLOW.STEP.NOMEASURE' | translate }}</div>
        </div>
    </div>
</div>