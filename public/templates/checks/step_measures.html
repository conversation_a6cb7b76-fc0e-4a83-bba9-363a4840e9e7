<div class="ll-listheader flexcnt">
    <div class="flexitem-1 ll-ptrhand" ng-click="toggleShowMeasures()">{{ 'WFLOW.STEP.MEASURES' | translate }}</div>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-ptrhand ll-button" ng-show="!step.readonly && !step.cdentry.locked && firstProcItem && hasGrant('TRUSER')" ng-click="processAll($event)"><i class="fa fa-list-ol"></i> {{ 'WFLOW.STEP.BUTTON.PROCALL' | translate }}</div>
    <div class="flexspace-l"></div>
    <div class="flexitem-fix20 text-center"><i class="fa fa-circle" ng-class="step.cdentry.completem ? 'text-success' : 'text-danger'"></i></div>
    <div class="flexitem-fix20 text-center ll-ptrhand" ng-click="toggleShowMeasures()"><i class="fa" ng-class="collapseMeasures ? 'fa-chevron-right' : 'fa-chevron-down'"></i></div>
</div>
<div collapse="collapseMeasures">
    <div class="ll-listitem flexcnt" ng-class="{'ll-ptrhand': !step.readonly && !measure.cdentry.locked}" ng-repeat="measure in step.measures" ng-click="enterMeasurement(measure)">
        <div class="flexitem-fix40 ll-starticon text-center"><i class="fa" ng-class="measureStatusIcon(measure)"></i></div>
        <div class="flexitem-1 flexcnt">
            <span class="ll-deemph flexitem-fix60">{{ measure.code }}</span>
            {{ measure.title | translate_model }}
            <span class="ll-addicon" ng-show="measure.tooltype_id && !matchConst(measure.cdentry.status, CHK_CDSTATUS_ALL_LEFTOUT)" ng-click="chooseTool(step.toolmap[measure.tooltype_id], $event)"><i class="fa fa-wrench" ng-class="{ 'text-success': !measure.cdentry.needstool }"></i><span class="ll-tiny">{{ step.toolmap[measure.tooltype_id][0].num }}</span></span>
            <span class="ll-addicon" ng-show="measure.measurement && measure.measurement.comment && measure.measurement.comment.length > 0"><i class="fa fa-comments-o" tooltip-append-to-body=true tooltip="{{measure.measurement.comment}}"></i></span>
        </div>
        <!--div class="flexitem-0 text-success" ng-click="quickEnterMeasurement(measure,$event)"><i class="fa fa-clock-o"></i></div-->
        <div class="flexitem-0 ll-emph text-primary" ng-show="!measure.cdentry.locked && !measure.needinput">{{ (measure.measurement && !matchConst(measure.cdentry.status, CHK_CDSTATUS_ALL_LEFTOUT)) ? measure.measurement.value+measure.measurement.unit : "" | formatdate }}</div>
        <div class="flexitem-0 ll-emph text-danger" ng-show="!measure.cdentry.locked && measure.needinput">{{ 'WFLOW.STEP.MEASUREINP' | translate }}</div>
        <div class="flexitem-0 ll-emph text-muted" ng-show="measure.cdentry.locked"><i class="fa fa-lock"></i> {{ 'WFLOW.STEP.INPLOCKED' | translate }}</div>
    </div>
</div>
