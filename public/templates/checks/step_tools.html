<div class="ll-listheader flexcnt ll-ptrhand" ng-click="toggleShowTools()">
    <div class="flexitem-1">{{ 'WFLOW.STEP.TOOLS' | translate }}</div>
    <div class="flexitem-fix20 text-center"><i class="fa fa-circle" ng-class="step.cdentry.completet ? 'text-success' : 'text-danger'"></i></div>
    <div class="flexitem-fix20 text-center"><i class="fa" ng-class="collapseTools ? 'fa-chevron-right' : 'fa-chevron-down'"></i></div>
</div>
<div collapse="collapseTools">
    <div class="ll-listitem flexcnt" ng-class="{'ll-ptrhand': !step.readonly}" ng-repeat="tool in step.tools" ng-click="chooseTool(tool)">
        <div class="flexitem-fix40 ll-starticon text-center"><i class="fa fa-wrench"></i><span class="ll-tiny">{{ tool[0].num }}</span></div>
        <div class="flexitem-1">
            <span class="ll-deemph">{{ tool[0].code }}: </span>{{ tool[0].title | translate_model }}</i>
            <span class="ll-addicon" ng-show="tool[0].description[i18n.selectedLanguage.code] && tool[0].description[i18n.selectedLanguage.code].length > 0" tooltip-append-to-body=true tooltip="{{ tool[0].description[i18n.selectedLanguage.code] }}"><i class="fa fa-file-text-o"></i></span>
            <span class="ll-addicon ll-ptrhand" ng-show="tool[0].images.length > 0" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.STEP.TOOLTIP.VIEWIMG' | translate }}" ng-click="$event.stopPropagation(); viewImage(tool[0].images[0])"><i class="fa fa-file-photo-o"></i></span>
            <span class="ll-addicon ll-ptrhand" ng-show="tool[0].documents.length > 0" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.STEP.TOOLTIP.VIEWDOC' | translate }}" ng-click="$event.stopPropagation(); viewDoc(tool[0].documents[0])"><i class="fa fa-file-pdf-o"></i></span>
        </div>
        <!--div class="flexitem-0 text-success" ng-click="quickChooseTool(tool,$event)"><i class="fa fa-clock-o"></i></div-->
        <div ng-if="tool[1]" class="flexitem-0 ll-addicon" ng-class="tool[1] && tool[1].comment && tool[1].comment.length > 0 ? 'text-success' : ''" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.SETCOMMENT' | translate }}" ng-click="setToolunitComment(tool[1], $event)"><i class="fa fa-comments-o"></i>&nbsp;</div>
        <div class="flexitem-0 ll-emph text-primary" ng-show="tool[1] || step.readonly">
            {{ tool[1] ? tool[1].code : '' }} {{tool[1].comment}}
        </div>
        <div class="flexitem-0 ll-emph text-danger" ng-hide="tool[1] || step.readonly">{{ 'WFLOW.STEP.TOOLCHOOSE' | translate }}</div>
    </div>
</div>