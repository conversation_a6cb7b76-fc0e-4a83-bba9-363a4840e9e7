<div class="modal-body flexcnt ll-editpanel ll-editpanel-inmodal"
     draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1, initX: isMobileApp ? 0 : -150 }">
    <div class="flexitem-1 flexvcnt">
        <div class="flexitem-0 ll-header flexcnt" ng-show="wfInputLoaded">
            <div class="flexitem-1">{{ measure.title | translate_model }}</div>
            <div class="flexitem-0 ll-subheader flexbottom">{{ measure.code }}</div>
        </div>
        <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="wfInputLoaded">
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap" ng-show="mtinfo.type == 'thold'">
                <div class="flexitem-0">{{ 'WFLOW.INPUT.TARGET' | translate }}: <span
                        class="ll-measureparbold">{{ measure.calculation.t1_targetv | sprintf : floatformat }} {{ measure.calculation.t1_targetu }}</span>
                </div>
                <div class="flexitem-1"></div>
                <div class="flexitem-0">{{ 'WFLOW.INPUT.THRESH' | translate }}: <span
                        class="ll-measureparbold">{{ measure.calculation.t1_tholdv | sprintf : floatformat}} {{ measure.calculation.t1_tholdu }}</span>
                </div>
            </div>
            <div class="flexitem-0 ll-measureparameter flexwrap" ng-show="mtinfo.type == 'absrng'">
                {{ 'WFLOW.INPUT.VALUERNG1' | translate }}&nbsp;<span
                    class="ll-measureparbold">{{ measure.calculation.t2_minv | sprintf : floatformat }} {{ measure.calculation.t2_unit }}</span>
                &nbsp;{{ 'WFLOW.INPUT.VALUERNG2' | translate }}&nbsp;<span
                    class="ll-measureparbold">{{ measure.calculation.t2_maxv | sprintf : floatformat }} {{ measure.calculation.t2_unit }}</span>
            </div>
            <div class="flexitem-0 ll-measureparameter flexwrap" ng-show="mtinfo.type == 'abs'">
                {{ 'WFLOW.INPUT.VALUECMP'+measure.calculation.t8_comparator | translate : { val: !measure.calculation.t8_value ? '---' : sprintf(floatformat+' %s',measure.calculation.t8_value,measure.calculation.t8_unit) } }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexwrap" ng-show="mtinfo.type == 'statst'">
                {{ 'WFLOW.INPUT.STATISTIC' | translate }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexwrap" ng-show="mtinfo.type == 'reschk'">
                {{ 'WFLOW.INPUT.CHECKCMP'+measure.calculation.t10_comparator | translate : { val: !measure.calculation.t10_value ? '---' : sprintf(floatformat+' %s',measure.calculation.t10_value,measure.calculation.t10_unit) } }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap" ng-show="mtinfo.type == 'frtext'">
                {{ 'WFLOW.INPUT.TEXTLEN1' | translate }}&nbsp;<span
                    class="ll-measureparbold">{{ measure.calculation.t3_minlen }}</span>&nbsp;{{ 'WFLOW.INPUT.TEXTLEN2' | translate }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap" ng-show="mtinfo.type == 'regexp'">
                {{ 'WFLOW.INPUT.TEXTPAT' | translate }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap" ng-show="mtinfo.type == 'flag'">
                <span ng-show="measure.calculation.t5_expected == 0">{{ 'WFLOW.INPUT.EXPNO' | translate }}</span>
                <span ng-show="measure.calculation.t5_expected == 1">{{ 'WFLOW.INPUT.EXPYES' | translate }}</span>
                <span ng-show="measure.calculation.t5_expected == 2">{{ 'WFLOW.INPUT.EXPBOTH' | translate }}</span>
            </div>
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap" ng-show="mtinfo.type == 'timera'">
                {{ 'WFLOW.INPUT.TEXTTIMER.START' | translate : { ccode: measure.calculation.complcode } }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap" ng-show="mtinfo.type == 'timers'">
                {{ 'WFLOW.INPUT.TEXTTIMER.STOP' | translate : { ccode: measure.calculation.complcode } }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap"
                 ng-show="(mtinfo.type == 'timerq' || mtinfo.type == 'timerc') && measure.calculation.t15_comparator === COMP_LESSTHAN">
                {{ 'WFLOW.INPUT.TEXTTIMER.STOPLT' | translate : { ccode: measure.calculation.complcode, time: measure.calculation.t15_value } }}
            </div>
            <div class="flexitem-0 ll-measureparameter flexcnt flexwrap"
                 ng-show="(mtinfo.type == 'timerq' || mtinfo.type == 'timerc') && measure.calculation.t15_comparator === COMP_MORETHAN">
                {{ 'WFLOW.INPUT.TEXTTIMER.STOPGT' | translate : { ccode: measure.calculation.complcode, time: measure.calculation.t15_value } }}
            </div>
            <div class="flexitem-0 ll-measurehint flexwrap"
                 ng-show="hintGiven()" ng-bind-html="measure.description | translate_model"></div>

            <div class="flexitem-0 ll-measureskip flexcnt-c" ng-show="measure.calculation.optional">
                <div class="flexitem-1">{{ 'WFLOW.INPUT.OPTSKIP' | translate }}</div>
                <div class="flexitem-0 btn-group ll-measureskipbtns">
                    <label class="btn btn-default ll-btn-default btn-sm" ng-model="item.rawvalues.skip"
                           btn-radio="true">{{ 'WFLOW.INPUT.YES' | translate }} <i ng-show="item.rawvalues.skip == true"
                                                                                   class="fa fa-check"></i></label>
                    <label class="btn btn-default ll-btn-default btn-sm" ng-model="item.rawvalues.skip"
                           btn-radio="null">{{ 'WFLOW.INPUT.NO' | translate }} <i ng-show="item.rawvalues.skip == null"
                                                                                  class="fa fa-check"></i></label>
                </div>
            </div>

            <div class="flexitem-0 ll-measureinputintro"
                 ng-show="!item.rawvalues.skip && mtinfo.input == 'numeric'">{{ 'WFLOW.INPUT.INPUT.VALUE' | translate }}</div>
            <div class="flexitem-0 ll-measureinputintro"
                 ng-show="!item.rawvalues.skip && mtinfo.input == 'text'">{{ 'WFLOW.INPUT.INPUT.TEXT' | translate }}</div>
            <div class="flexitem-0 ll-measureinputintro"
                 ng-show="!item.rawvalues.skip && mtinfo.input == 'list'">{{ 'WFLOW.INPUT.INPUT.LIST' | translate }}</div>
            <div class="flexitem-0 ll-measureinputintro"
                 ng-show="!item.rawvalues.skip && mtinfo.input == 'bool'">{{ 'WFLOW.INPUT.INPUT.BOOL' | translate }}</div>
            <div class="flexitem-0 ll-measureinputintro"
                 ng-show="!item.rawvalues.skip && mtinfo.input == 'matrix'">{{ 'WFLOW.INPUT.INPUT.MATRIX' | translate }}</div>

            <div class="flexitem-0 flexcnt" ng-if="!item.rawvalues.skip && mtinfo.input == 'numeric'"
                 ng-click="activateKeyboardItem('val')">
                <input type="number" id="inputfld12" ng-if="!numkeyboard" ng-model="item.rawvalues.val"
                       class="form-control ll-measureinputfieldnum flexitem-1" autofocus>
                <input type="text" id="inputfld12kb" ng-if="numkeyboard" ng-model="item.rawvalues.val"
                       class="form-control ll-measureinputfieldnum flexitem-1"
                       ng-class="{ 'debugr': keyboard.modelkey === 'val' }" ng-init="activateKeyboardItem('val',true)"
                       disabled>
                <div class="flexspace-m"></div>
                <div class="flexitem-0 ll-measureinputfieldunit"
                     ng-show="mtinfo.type == 'thold'">{{ measure.calculation.t1_tholdu }}</div>
                <div class="flexitem-0 ll-measureinputfieldunit"
                     ng-show="mtinfo.type == 'absrng'">{{ measure.calculation.t2_unit }}</div>
                <div class="flexitem-0 ll-measureinputfieldunit"
                     ng-show="mtinfo.type == 'abs'">{{ measure.calculation.t8_unit }}</div>
                <div class="flexitem-0 ll-measureinputfieldunit"
                     ng-show="mtinfo.type == 'statst'">{{ measure.calculation.t11_unit }}</div>
            </div>
            <div class="flexitem-0 flexcnt"
                 ng-if="!item.rawvalues.skip && (mtinfo.input == 'timest' || mtinfo.input == 'timestxm')">
                <button type="button" class="btn btn-default flexitem-0" ng-click="valTimeConvAddDay(-1)">-</button>
                <div class="flexspace-m"></div>
                <div class="flexitem-0"><input type="text" id="inputfldcd0" ng-model="valtimeconv.date"
                                               class="form-control ll-measureinputfield" disabled></div>
                <div class="flexspace-m"></div>
                <button type="button" class="btn btn-default flexitem-0" ng-click="valTimeConvAddDay(1)">+</button>
                <div class="flexitem-1"></div>
                <div class="flexitem-0"><select id="inputselcd1" class="form-control ll-measureinputfield"
                                                ng-model="valtimeconv.h"
                                                ng-options="item.id as item.disp for item in sel.hours"></select></div>
                <div class="flexitem-0 ll-measureinputfieldunit">&nbsp;:&nbsp;</div>
                <div class="flexitem-0"><select id="inputselcd2" class="form-control ll-measureinputfield"
                                                ng-model="valtimeconv.m"
                                                ng-options="item.id as item.disp for item in sel.minutes"></select>
                </div>
                <div class="flexitem-0 ll-measureinputfieldunit">&nbsp;:&nbsp;</div>
                <div class="flexitem-0"><select id="inputselcd3" class="form-control ll-measureinputfield"
                                                ng-model="valtimeconv.s"
                                                ng-options="item.id as item.disp for item in sel.minutes"></select>
                </div>
            </div>
            <div class="flexitem-0 flexcnt" ng-if="!item.rawvalues.skip && mtinfo.input == 'timestxf'">
                <div class="flexitem-3"><input type="text" id="inputfldcd4" ng-model="valtimeconv.date"
                                               class="form-control ll-measureinputfield text-center" disabled></div>
                <div class="flexitem-1"></div>
                <div class="flexitem-1"><input type="text" class="form-control ll-measureinputfield text-center"
                                               ng-model="valtimeconv.h" disabled></div>
                <div class="flexitem-0 ll-measureinputfieldunit">&nbsp;:&nbsp;</div>
                <div class="flexitem-1"><input type="text" class="form-control ll-measureinputfield text-center"
                                               ng-model="valtimeconv.m" disabled></div>
                <div class="flexitem-0 ll-measureinputfieldunit">&nbsp;:&nbsp;</div>
                <div class="flexitem-1"><input type="text" class="form-control ll-measureinputfield text-center"
                                               ng-model="valtimeconv.s" disabled></div>
            </div>
            <div class="flexspace-m"
                 ng-if="!item.rawvalues.skip && (mtinfo.input == 'timestxf' || mtinfo.input == 'timestxm')"></div>
            <div class="flexitem-0 flexcnt"
                 ng-show="!item.rawvalues.skip && (mtinfo.input == 'timestxf' || mtinfo.input == 'timestxm')">
                <div class="flexitem-fix100"></div>
                <div class="flexitem-0 ll-measureinputfieldunit">{{ 'WFLOW.INPUT.TEXTTIMER.REDUCE' | translate }} </div>
                <div class="flexspace-m"></div>
                <button type="button" class="btn btn-default flexitem-0" ng-click="valTimeConvAddReduce(-15)">-15
                </button>
                <div class="flexspace-s"></div>
                <button type="button" class="btn btn-default flexitem-0" ng-click="valTimeConvAddReduce(-5)">-5</button>
                <div class="flexspace-m"></div>
                <div class="flexitem-1"><input type="text" id="inputfldcd5" ng-model="valtimeconv.reduce"
                                               class="form-control ll-measureinputfield" disabled></div>
                <div class="flexspace-m"></div>
                <button type="button" class="btn btn-default flexitem-0" ng-click="valTimeConvAddReduce(5)">+5</button>
                <div class="flexspace-s"></div>
                <button type="button" class="btn btn-default flexitem-0" ng-click="valTimeConvAddReduce(15)">+15
                </button>
                <div class="flexspace-m"></div>
                <div class="flexitem-0 ll-measureinputfieldunit"> {{ 'WFLOW.INPUT.TEXTTIMER.MIN' | translate }}</div>
            </div>

            <!-- text -->
            <div class="flexitem-0" ng-if="!item.rawvalues.skip && mtinfo.input == 'text'">
                <input type="text" id="inputfld34" ng-model="item.rawvalues.val"
                       class="form-control ll-measureinputfield" autofocus>
            </div>

            <!-- list -->
            <div class="ll-modeladdmodal">
                <div class="flexitem-0" ng-if="!item.rawvalues.skip && mtinfo.input == 'list'">
                    <div class="ll-item flexcnt ll-ptrhand" ng-repeat="value in choiceList()"
                         ng-click="item.rawvalues.val = value;">
                        <div class="flexitem-fix20 flexcenter ll-actions text-primary">
                            <i ng-class="{ 'fa-circle': item.rawvalues.val === value, 'fa-circle-o': item.rawvalues.val !== value }"
                               class="fa"></i>
                        </div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-0 ll-title">{{ value }}</div>
                        <div class="flexitem-1"></div>
                    </div>
                </div>
            </div>

            <!-- none -->
            <div class="flexitem-0" ng-if="!item.rawvalues.skip && mtinfo.input == 'none'">
                {{ 'WFLOW.INPUT.NOINPUT' | translate }}
            </div>

            <!-- bool -->
            <div class="flexitem-0" ng-if="!item.rawvalues.skip && mtinfo.input == 'bool'">
                <div class="flexitem-0 btn-group">
                    <label class="btn btn-default ll-btn-default ll-measureinputbutton" ng-model="item.rawvalues.val" btn-radio="1" uncheckable>
                        {{ 'WFLOW.INPUT.YES' | translate }}
                        <i ng-show="item.rawvalues.val == 1" class="fa fa-check"></i>
                    </label>
                    <label class="btn btn-default ll-btn-default ll-measureinputbutton" ng-model="item.rawvalues.val" btn-radio="0" uncheckable>
                        {{ 'WFLOW.INPUT.NO' | translate }}
                        <i ng-show="item.rawvalues.val == 0" class="fa fa-check"></i>
                    </label>
                </div>
            </div>
            <div class="flexitem-0 ll-matrix flexcnt" ng-if="!item.rawvalues.skip && mtinfo.input == 'matrix'">
                <div class="flexitem-fix20">
                    <div class="ll-rotate">
                        <div ng-class="yttlclass">{{ matrixTitle('yleft') }}</div>
                    </div>
                </div>
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-fix20 ll-xtitle">{{ matrixTitle('xtop') }}</div>
                    <div class="flexitem-1 flexcnt">
                        <div class="flexitem-fix20 flexvcnt">
                            <div class="flexitem-fix20"></div>
                            <div class="flexitem-fix40" ng-repeat="row in rows">
                                <div class="ll-rotate">
                                    <div class="ll-ystitle">{{ matrixTitle('rleft'+row) }}</div>
                                </div>
                            </div>
                            <div class="flexitem-fix20"></div>
                        </div>
                        <div class="flexitem-1 flexvcnt" ng-repeat="col in cols">
                            <div class="flexitem-fix20 ll-xstitle">{{ matrixTitle('ctop'+col) }}</div>
                            <div class="flexitem-fix40 flexcenter" ng-repeat="row in rows">
                                <div class="flexitem-fix20 ll-xstitlesml">{{ matrixTitle('ftop'+row+col) }}&nbsp;</div>
                                <div class="ll-inputbox" ng-click="activateKeyboardItem(matrixValModel(col,row))">
                                    <input type="number" ng-if="!numkeyboard && matrixUsedField(col,row)"
                                           placeholder="{{ matrixTitle('fplc'+row+col) }}"
                                           ng-model="item.rawvalues[matrixValModel(col,row)]"
                                           class="form-control ll-input ll-measureinputfieldnum"
                                           autofocus="{{ firstTextBox() }}">
                                    <input type="text" ng-if="numkeyboard && matrixUsedField(col,row)"
                                           placeholder="{{ matrixTitle('fplc'+row+col) }}"
                                           ng-model="item.rawvalues[matrixValModel(col,row)]"
                                           class="form-control ll-measureinputfieldnum flexitem-1"
                                           ng-class="{ 'debugr': keyboard.modelkey === matrixValModel(col,row) }"
                                           ng-init="activateKeyboardItem(matrixValModel(col,row),true)" disabled>
                                </div>
                            </div>
                            <div class="flexitem-fix20 ll-xstitle">{{ matrixTitle('cbottom'+col) }}</div>
                        </div>
                        <div class="flexitem-fix20 flexvcnt">
                            <div class="flexitem-fix20"></div>
                            <div class="flexitem-fix40" ng-repeat="row in rows">
                                <div class="ll-rotate">
                                    <div class="ll-ystitle">{{ matrixTitle('rright'+row) }}</div>
                                </div>
                            </div>
                            <div class="flexitem-fix20"></div>
                        </div>
                    </div>
                    <div class="flexitem-fix20 ll-xtitle">{{ matrixTitle('xbottom') }}</div>
                </div>
                <div class="flexitem-fix20">
                    <div class="ll-rotate">
                        <div ng-class="yttlclass">{{ matrixTitle('yright') }}</div>
                    </div>
                </div>
            </div>
            <div class="flexspace-l" ng-if="!item.rawvalues.skip"></div>

            <div class="flexitem-0 ll-measurestatus flexcnt">
                <div class="flexitem-0" ng-if="contains([1,2], item.status)">{{ 'WFLOW.INPUT.RESULT' | translate }}:
                    <span class="ll-measurestatusbold">{{ item.value | formatdate }}</span></div>
                <div class="flexitem-1"></div>
                <div class="flexitem-0">{{ 'WFLOW.INPUT.STATUS.TITLE' | translate }}:
                    <span class="ll-measurestatusbold" ng-show="item.status==0"><i
                            class="fa fa-edit"></i> {{ 'WFLOW.INPUT.STATUS.TODO' | translate }}</span>
                    <span class="ll-measurestatusbold text-success" ng-show="item.status==1"><i
                            class="fa fa-thumbs-o-up"></i> {{ 'WFLOW.INPUT.STATUS.PASS' | translate }}</span>
                    <span class="ll-measurestatusbold text-danger" ng-show="item.status==2"><i
                            class="fa fa-thumbs-o-down"></i> {{ 'WFLOW.INPUT.STATUS.FAIL' | translate }}</span>
                    <span class="ll-measurestatusbold text-primary" ng-show="item.status==3"><i
                            class="fa fa-share"></i> {{ 'WFLOW.INPUT.STATUS.SKIP' | translate }}</span>
                    <span class="ll-measurestatusbold text-danger" ng-show="item.status==4"><i
                            class="fa fa-exclamation-triangle"></i> {{ 'WFLOW.INPUT.STATUS.INV' | translate }}</span>
                    <span class="ll-measurestatusbold text-danger" ng-show="item.status==5"><i
                            class="fa fa-bolt"></i> {{ 'WFLOW.INPUT.STATUS.ERROR' | translate }}</span>
                </div>
            </div>
        </form>
        <div class="flexspace-l"></div>
        <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="wfInputLoaded">
            <div class="flexitem-0">
                <button type="button" ng-show="testmode" class="btn btn-primary" ng-click="close()"><i
                        class="fa fa-times"></i> {{ 'WFLOW.INPUT.BUTTON.CLOSE' | translate }}</button>
            </div>
            <div class="flexitem-0">
                <button type="button" ng-hide="testmode" class="btn btn-danger" ng-click="close()"><i
                        class="fa fa-ban"></i> {{ 'WFLOW.INPUT.BUTTON.CANCEL' | translate }}</button>
            </div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0">
                <button type="button" class="btn btn-success" ng-click="check()" ng-hide="mtinfo.input == 'none'"><i
                        class="fa fa-save"></i> {{ 'WFLOW.INPUT.BUTTON.CHECK' | translate }}</button>
            </div>
            <div class="flexspace-s" ng-hide="testmode"></div>
            <div class="flexitem-0">
                <button type="button" class="btn btn-success" ng-click="continue()"
                        ng-hide="testmode || mtinfo.input == 'none'"><i
                        class="fa fa-chevron-right"></i> {{ 'WFLOW.INPUT.BUTTON.CONTINUE' | translate }}</button>
            </div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0">
                <button type="button" class="btn btn-primary" ng-click="editComment()"
                        ng-show="_.isNull(item.comment) || item.comment == ''"><i
                        class="fa fa-comment-o"></i> {{ 'WFLOW.INPUT.BUTTON.ADDCOMM' | translate }}</button>
            </div>
            <div class="flexitem-0">
                <button type="button" class="btn btn-primary" ng-click="editComment()"
                        ng-hide="_.isNull(item.comment) || item.comment == ''"><i
                        class="fa fa-comment-o"></i> {{ 'WFLOW.INPUT.BUTTON.EDITCOMM' | translate }}</button>
            </div>
            <div class="flexspace-s" ng-if="mtinfo.input == 'matrix'"></div>
            <div class="flexitem-0" ng-if="mtinfo.input == 'matrix'">
                <button type="button" class="btn btn-default" ng-click="clearMatrix()"><i
                        class="fa fa-eraser"></i> {{ 'WFLOW.INPUT.BUTTON.CLEAR' | translate }}</button>
            </div>
        </div>
    </div>
    <div class="flexitem-0 flexvcnt" ng-if="keyboard.needed">
        <div class="flexitem-0 flexvcnt ll-numkeyboard">
            <div class="flexitem-1 flexcnt">
                <div class="flexitem-3 ll-nkbdisp">{{ keyboard.preview }}</div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === 'C' }"
                     ng-click="keyboardAction('C')">AC
                </div>
            </div>
            <div class="flexitem-1 flexcnt">
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '7' }"
                     ng-click="keyboardAction('7')">7
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '8' }"
                     ng-click="keyboardAction('8')">8
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '9' }"
                     ng-click="keyboardAction('9')">9
                </div>
            </div>
            <div class="flexitem-1 flexcnt">
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '4' }"
                     ng-click="keyboardAction('4')">4
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '5' }"
                     ng-click="keyboardAction('5')">5
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '6' }"
                     ng-click="keyboardAction('6')">6
                </div>
            </div>
            <div class="flexitem-1 flexcnt">
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '1' }"
                     ng-click="keyboardAction('1')">1
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '2' }"
                     ng-click="keyboardAction('2')">2
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '3' }"
                     ng-click="keyboardAction('3')">3
                </div>
            </div>
            <div class="flexitem-1 flexcnt">
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '-' }"
                     ng-click="keyboardAction('-')">±
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '0' }"
                     ng-click="keyboardAction('0')">0
                </div>
                <div class="flexitem-1 ll-nkbbut" ng-class="{ 'll-nkbbutpressed': keyboard.pressed === '.' }"
                     ng-click="keyboardAction('.')">.
                </div>
            </div>
        </div>
        <div class="flexitem-1"></div>
    </div>
</div>
