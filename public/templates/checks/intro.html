<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-2 ll-fullheight ll-wflowinfo flexvcnt" ng-show="wfIntroLoaded">
            <div class="flexitem-0 flexcnt ll-title">
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-0 ll-subtitle">
                        {{ 'WFLOW.INTRO.MODEL' | translate }} {{ check.model.code }}:
                        <span tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.GOTOMODEL' | translate }}" class="ll-emph ll-ptrhand" ng-show="showGotoModel()" ng-click="gotoModel()">{{ check.model.title | translate_model }},</span>
                        <span class="ll-emph" ng-hide="showGotoModel()">{{ check.model.title | translate_model }},</span>
                        {{ 'WFLOW.INTRO.UNIT' | translate }}
                        <span tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.GOTOUNIT' | translate }}" class="ll-emph ll-ptrhand" ng-show="showGotoUnit()" ng-click="gotoUnit()">{{ check.unit.code }}</span>
                        <span class="ll-emph" ng-hide="showGotoUnit()">{{ check.unit.code }}</span>
                        ({{ check.unit.customer }})
                    </div>
                    <div class="flexitem-0 flexcnt">
                        <div class="flexitem-1 flexbottom"><span class="ll-emph">{{ check.checktype.title | translate_model }}</span> ({{ check.checktype.code }}): <span class="ll-emph">{{ 'WFLOW.INTRO.CHECK' | translate }} #{{ check.id }}</span></div>
                        <div class="flexitem-0 flexbottom ll-status">
                            {{ checkStatusName() | translate }}
                        </div>
                        <div class="flexspace-m"></div>
                    </div>
                </div>
                <div class="flexitem-0 flexbottom ll-statusicon">
                    <i tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.EXPPDF' | translate }}" ng-show="hasGrant('WFLMNG')" class="fa fa-print ll-ptrhand" ng-click="exportPdf()"></i>
                    <i tooltip-popup-delay='1000' tooltip="{{ 'Vergleichstabelle' | translate }}" ng-show="hasGrant('WFLMNG')" class="fa fa-check ll-ptrhand" ng-click="exportCompareData()"></i>
                    <i tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.EXPFILE' | translate }}" ng-show="hasGrant('WFLMNG') && !isMobileApp" class="fa fa-download ll-ptrhand" ng-click="exportFile()"></i>
                    <i feature-flag="configtable" ng-show="check.configtable_id" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.CONFIGTABLE' | translate }}" ng-show="hasGrant('MNGCFG')" class="fa fa-list-alt ll-ptrhand" ng-click="openConfigtable()"></i>
                </div>
                <div class="flexitem-0 flexbottom ll-statusicon">
                    <comm-report class="flexitem-0" style="margin-left:5px" type="check" obj-id="{{ check.id }}" ng-show="hasGrant('WFLMNG')"></comm-report>
                </div>
            </div>
            <div class="flexitem-0 ll-testrunbar" ng-if="testrun">{{ ('WFLOW.INTRO.TESTRUN.'+testrun.toUpperCase()) | translate }} {{ check.metadata.testrun.item }}</div>
            <div class="flexitem-0 ll-subtitlesml text-right"><span ng-show="!check.model.version && (!testrun || testrun === 'model')" translate>WFLOW.INTRO.UNFINVERSION</span><span ng-show="check.newerversions.length > 0"><span translate>WFLOW.INTRO.VERSION</span> {{ check.model.version }}</span></div>
            <div class="flexitem-1 ll-scroll">
                <div class="flexcnt ll-infogroup" ng-if="imagelist.length > 0">
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-image"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1"></div>
                    <div tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.IMGLEFT' | translate }}" class="flexitem-0 ll-imgsel flexcenter ll-ptrhand" ng-show="imagelist.length > 1" ng-click="toggleImage(-1)"><i class="fa fa-chevron-left"></i></div>
                    <div class="flexitem-0 ll-imagebox flexcenter" ng-if="selImage != null">
                        <div ng-if="!isMobileApp" class="ll-image" ng-style="{ 'margin-right': selImage.padleft, 'margin-bottom': selImage.padtop, 'margin-left': selImage.padleft, 'margin-top': selImage.padtop }"><img id="zoomimg" width="{{selImage.width}}" height="{{selImage.height}}" zoomable-image="zoomableImageOptions" ng-src="{{selImage.path}}" zoom-image-src="selectedZoomImage"/></div>
                        <div ng-if="isMobileApp" class="ll-image" ng-style="{ 'margin-right': selImage.padleft, 'margin-bottom': selImage.padtop, 'margin-left': selImage.padleft, 'margin-top': selImage.padtop }" ng-click="openPlainImageViewer()"><img id="plainimg" width="{{selImage.width}}" height="{{selImage.height}}" ng-src="{{selImage.path}}"/></div>
                    </div>
                    <div tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.IMGRIGHT' | translate }}" class="flexitem-0 ll-imgsel flexcenter ll-ptrhand" ng-show="imagelist.length > 1" ng-click="toggleImage(1)">
                        <i class="fa fa-chevron-right"></i>
                    </div>
                    <div class="flexitem-1"></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw"></i></div>
                </div>
                <div class="flexcnt ll-infogroup">
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-car"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-text flexvcnt">
                        <div class="flexitem-0 flexwrap" ng-bind-html="check.model.devicetype.description | translate_model"></div>
                        <div class="flexitem-0 flexwrap" ng-bind-html="check.model.description | translate_model"></div>
                    </div>
                </div>
                <div class="flexcnt ll-infogroup">
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-calendar"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-text flexcnt">
                        <div class="flexitem-0 flexvcnt" ng-if="!skipsched">
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.DATE.SCHED' | translate }}:</div>
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.DATE.DUE' | translate }}:</div>
                        </div>
                        <div class="flexspace-m" ng-if="!skipsched"></div>
                        <div class="flexitem-1 flexvcnt" ng-if="!skipsched">
                            <div class="flexitem-0 ll-emph" ng-show="check.scheduled">{{ check.scheduled | date:i18n.selectedLanguage.dformat }}</div>
                            <div class="flexitem-0 ll-deemph" ng-hide="check.scheduled">{{ 'WFLOW.INTRO.DATE.NOSCHED' | translate }}</div>
                            <div class="flexitem-0 ll-emph" ng-show="check.dueby">{{ check.dueby | date:i18n.selectedLanguage.dformat }}</div>
                            <div class="flexitem-0 ll-deemph" ng-hide="check.dueby">{{ 'WFLOW.INTRO.DATE.NODUE' | translate }}</div>
                        </div>
                        <div class="flexspace-l" ng-if="!skipsched"></div>
                        <div class="flexitem-0 flexvcnt">
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.DATE.START' | translate }}:</div>
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.DATE.FIN' | translate }}:</div>
                        </div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-1 flexvcnt">
                            <div class="flexitem-0 ll-emph" ng-show="check.started">{{ check.started | date:i18n.selectedLanguage.dformat }}</div>
                            <div class="flexitem-0 ll-deemph" ng-hide="check.started">{{ 'WFLOW.INTRO.DATE.NOSTART' | translate }}</div>
                            <div class="flexitem-0 ll-emph" ng-show="check.finished">{{ check.finished | date:i18n.selectedLanguage.dformat }}</div>
                            <div class="flexitem-0 ll-deemph" ng-hide="check.finished">{{ 'WFLOW.INTRO.DATE.NOFIN' | translate }}</div>
                        </div>
                    </div>
                </div>
                <div class="flexcnt ll-infogroup">
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-group"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-text flexvcnt">
                        <div class="flexitem-0" ng-repeat="assignee in assignees">
                            <i class="fa fa-lg fa-minus-square-o text-danger ll-ptrhand" ng-show="hasGrant('WFLMNG') && check.status == CHK_CDSTATUS_PASSED" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.REMASS' | translate }}" ng-click="removeAssigneeAction(assignee)"></i>
                            <i class="fa fa-lg fa-sign-in text-primary ll-ptrhand" ng-show="hasGrant('WFLMNG') && check.status == CHK_CDSTATUS_PASSED" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.ASSIGN' | translate }}" ng-click="assignToAction(assignee)"></i>
                            <i ng-class="assignee.icon + (assignee.owned ? ' text-success' : '')" class="fa fa-fw"></i>
                            <span class="ll-emph ll-ptrhand" ng-class="{ 'll-highlight': listFilter.enableFilter && assignee.num === listFilter.focusUser }" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.HIGHLASS' | translate }}" ng-click="setFilter('focusUser', assignee.num, true)">{{ assignee.name }}</span>:
                            <span class="ll-emph">{{ assignee.numstep }}</span> {{ 'WFLOW.INTRO.STEPINPROC' | translate }}
                            <span class="ll-emph">{{ assignee.numproc }}</span> {{ 'WFLOW.INTRO.PROCS' | translate }}
                            <i class="fa fa-exchange text-primary ll-ptrhand" ng-show="showButtonReassign()" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.CHANGEASSIGN' | translate }}" ng-click="changeAssignmentAction(assignee)"></i>
                        </div>
                        <div class="flexitem-0 ll-deemph" ng-hide="assignees && assignees.length > 0">{{ 'WFLOW.INTRO.NOASS' | translate }}</div>
                        <div class="flexitem-0 ll-ptrhand" ng-show="hasGrant('WFLMNG') && check.status == CHK_CDSTATUS_PASSED" ng-click="addAssigneeAction()"><i tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.ADDASS' | translate }}" class="fa fa-lg fa-plus-square-o"></i> {{ 'WFLOW.INTRO.ADDASS' | translate }}</div>
                        <div class="flexitem-0" ng-hide="unassigned.steps == 0">
                            <span class="ll-emph">{{ unassigned.steps }}</span> {{ 'WFLOW.INTRO.STEPINPROC' | translate }}
                            <span class="ll-emph">{{ unassigned.procs }}</span> {{ 'WFLOW.INTRO.PROCSUNASS' | translate }}
                        </div>
                    </div>
                </div>

                <!-- Statistiken -->
                <div class="flexcnt ll-infogroup">
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-bar-chart-o"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-text flexcnt">
                        <div class="flexitem-0 flexvcnt">
                            <div class="flexitem-0">&nbsp;</div>
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.PROCEDURES' | translate }}:</div>
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.STEPS' | translate }}:</div>
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.INSTRUCTIONS' | translate }}:</div>
                            <div class="flexitem-0">{{ 'WFLOW.INTRO.MEASURES' | translate }}:</div>
                        </div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-1 flexvcnt">
                            <div class="flexitem-0 flexcenter">{{ 'WFLOW.INTRO.STAT.TOTAL' | translate }}</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.proc.todo + statistics.proc.pass + statistics.proc.fail + statistics.proc.skip }}</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.step.todo + statistics.step.pass + statistics.step.fail + statistics.step.skip }}</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.instruction }}</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.meas.todo + statistics.meas.pass + statistics.meas.fail + statistics.meas.skip }}</div>
                        </div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-2 flexvcnt">
                            <div class="flexitem-0 flexcenter">{{ 'WFLOW.INTRO.STAT.UNFIN' | translate }}</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.proc.todo }} ({{ statistics.proc.todoprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.step.todo }} ({{ statistics.step.todoprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph">-</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.meas.todo }} ({{ statistics.meas.todoprc | sprintf : "%4.1f%%"}})</div>
                        </div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-2 flexvcnt">
                            <div class="flexitem-0 flexcenter">{{ 'WFLOW.INTRO.STAT.PASSED' | translate }}</div>
                            <div class="flexitem-0 flexcenter ll-emph text-success">{{ statistics.proc.pass }} ({{ statistics.proc.passprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph text-success">{{ statistics.step.pass }} ({{ statistics.step.passprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph text-success">-</div>
                            <div class="flexitem-0 flexcenter ll-emph text-success">{{ statistics.meas.pass }} ({{ statistics.meas.passprc | sprintf : "%4.1f%%"}})</div>
                        </div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-2 flexvcnt">
                            <div class="flexitem-0 flexcenter">{{ 'WFLOW.INTRO.STAT.FAILED' | translate }}</div>
                            <div class="flexitem-0 flexcenter ll-emph text-danger">{{ statistics.proc.fail }} ({{ statistics.proc.failprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph text-danger">{{ statistics.step.fail }} ({{ statistics.step.failprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph text-danger">-</div>
                            <div class="flexitem-0 flexcenter ll-emph text-danger">{{ statistics.meas.fail }} ({{ statistics.meas.failprc | sprintf : "%4.1f%%"}})</div>
                        </div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-2 flexvcnt">
                            <div class="flexitem-0 flexcenter">{{ 'WFLOW.INTRO.STAT.SKIPPED' | translate }}</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.proc.skip }} ({{ statistics.proc.skipprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.step.skip }} ({{ statistics.step.skipprc | sprintf : "%4.1f%%"}})</div>
                            <div class="flexitem-0 flexcenter ll-emph">-</div>
                            <div class="flexitem-0 flexcenter ll-emph">{{ statistics.meas.skip }} ({{ statistics.meas.skipprc | sprintf : "%4.1f%%"}})</div>
                        </div>
                    </div>
                </div>
                <div class="flexcnt ll-infogroup" ng-if="documentlist.length > 0">
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-file"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-text">
                        <div ng-repeat="doc in documentlist" class="flexcnt">
                            <div class="flexitem-fix20 flexcenter" ng-show="doc.doctype == MEDIA_DOCTYPE_PDF"><i class="fa fa-file-pdf-o"></i></div>
                            <div class="flexitem-fix20 flexcenter" ng-show="doc.doctype == MEDIA_DOCTYPE_VIDEO"><i class="fa fa-video-camera"></i></div>
                            <div class="flexitem-fix20 flexcenter" ng-show="doc.doctype == MEDIA_DOCTYPE_ZIP"><i class="fa fa-file-archive-o"></i></div>
                            <div class="flexspace-s"></div>
                            <div tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.VIEWDOC' | translate }}" class="ll-docname flexcenter flexitem-0 ll-ptrhand" ng-click="viewDoc(doc)">{{ doc.caption ? doc.caption : doc.binaryfile.original_filename }}</div>
                            <div class="flexspace-m"></div>
                            <div class="ll-docdata flexcenter flexitem-0 ll-deemph" ng-show="doc.doctype == MEDIA_DOCTYPE_PDF">{{ 'WFLOW.INTRO.PDFINFO' | translate : { pnum: doc.binaryfile.metadata.pages } }}</div>
                            <div class="ll-docdata flexcenter flexitem-0 ll-deemph" ng-show="doc.doctype == MEDIA_DOCTYPE_VIDEO">{{ 'WFLOW.INTRO.VIDINFO' | translate : { width: doc.binaryfile.metadata.width, height: doc.binaryfile.metadata.height, fps: sprintf('%0.1f',doc.binaryfile.metadata.fps || 0.0), rtime: sprintf('%0.1f',doc.binaryfile.metadata.duration || 0.0) } }}</div>
                        </div>
                    </div>
                </div>
                <div class="flexcnt ll-infogroup">
                    <div class="flexitem-0 ll-icon"><i class="fa fa-fw fa-comment-o"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-1 ll-text">
                        <div class="flexwrap" ng-show="check.unit.comment && check.unit.comment != ''"><span class="ll-italemph">{{ 'WFLOW.INTRO.UNIT' | translate }}:</span> {{ check.unit.comment }}</div>
                        <div class="flexwrap ll-ptrhand" ng-show="hasGrant('WFLMNG,MNGMUC') && check.comment && check.comment != ''" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.EDITCOMM' | translate }}" ng-click="editComment()"><span class="ll-italemph">{{ 'WFLOW.INTRO.CHECK' | translate }}:</span> {{ check.comment }}</div>
                        <div class="flexwrap" ng-show="!hasGrant('WFLMNG,MNGMUC') && check.comment && check.comment != ''"><span class="ll-italemph">{{ 'WFLOW.INTRO.CHECK' | translate }}:</span> {{ check.comment }}</div>
                        <div class="ll-italemph ll-ptrhand" ng-hide="!hasGrant('WFLMNG,MNGMUC') || (check.comment && check.comment != '')" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.EDITCOMM' | translate }}" ng-click="editComment()">{{ 'WFLOW.INTRO.NOCOMMENT' | translate }}</div>
                        <div class="ll-italemph ll-ptrhand" ng-show="hasGrant('WFLMNG,MNGMUC')" ng-if="!isMobileApp" ng-click="mediamanager()">{{ 'WFLOW.INTRO.MEDIAMGR' | translate }}</div>
                        <div ng-repeat="img in check.images" class="flexcnt">
                            <div class="flexitem-fix20 flexcenter"><i class="fa fa-image"></i></div>
                            <div class="flexspace-s"></div>
                            <div class="ll-docname flexcenter flexitem-0 ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.VIEWIMG' | translate }}" ng-click="viewImage(img)">{{ img.caption ? img.caption : img.fullimage.original_filename }}</div>
                            <div class="flexspace-m"></div>
                            <div class="ll-docdata flexcenter flexitem-0 ll-deemph">{{ 'WFLOW.INTRO.IMGINFO' | translate : { width: img.fullimage.metadata.width, height: img.fullimage.metadata.height} }}</div>
                            <div class="flexspace-s"></div>
                            <div class="flexitem-fix20 flexcenter ll-ptrhand" ng-click="showFileInfoModal(img, true)"><i class="fa fa-info-circle" ></i></div>
                        </div>
                        <div ng-repeat="doc in check.documents" class="flexcnt">
                            <div class="flexitem-fix20 flexcenter" ng-show="doc.doctype == MEDIA_DOCTYPE_PDF"><i class="fa fa-file-pdf-o"></i></div>
                            <div class="flexitem-fix20 flexcenter" ng-show="doc.doctype == MEDIA_DOCTYPE_VIDEO"><i class="fa fa-video-camera"></i></div>
                            <div class="flexitem-fix20 flexcenter" ng-show="doc.doctype == MEDIA_DOCTYPE_ZIP"><i class="fa fa-file-archive-o"></i></div>
                            <div class="flexspace-s"></div>
                            <div class="ll-docname flexcenter flexitem-0 ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.VIEWDOC' | translate }}" ng-click="viewDoc(doc)">{{ doc.caption ? doc.caption : doc.binaryfile.original_filename }}</div>
                            <div class="flexspace-m"></div>
                            <div class="ll-docdata flexcenter flexitem-0 ll-deemph" ng-show="doc.doctype == MEDIA_DOCTYPE_PDF">{{ 'WFLOW.INTRO.PDFINFO' | translate : { pnum: doc.binaryfile.metadata.pages } }}</div>
                            <div class="ll-docdata flexcenter flexitem-0 ll-deemph" ng-show="doc.doctype == MEDIA_DOCTYPE_VIDEO">{{ 'WFLOW.INTRO.VIDINFO' | translate : { width: doc.binaryfile.metadata.width, height: doc.binaryfile.metadata.height, fps: sprintf('%0.1f',doc.binaryfile.metadata.fps || 0.0), rtime: sprintf('%0.1f',doc.binaryfile.metadata.duration || 0.0) } }}</div>
                            <div class="flexspace-s"></div>
                            <div class="flexitem-fix20 flexcenter ll-ptrhand" ng-click="showFileInfoModal(doc, false)"><i class="fa fa-info-circle" ></i></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flexspace-m"></div>
            <div class="flexitem-0 ll-buttonbar flexcnt-r">
                <div class="flexitem-1 flexcenter">
                    <div class="ll-warning flexwrap flexcenter" ng-show="check.timing.tooearly && check.status == CHK_STATUS_STARTED">{{ 'WFLOW.INTRO.TOOEARLY' | translate }}</div>
                    <div class="ll-warning flexwrap text-danger flexcenter" ng-show="check.timing.toolate && check.status == CHK_STATUS_STARTED">{{ 'WFLOW.INTRO.TOOLATE' | translate }}</div>
                </div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonCancel()"  ><button type="button" class="btn btn-danger"  ng-click="actionCancel()"  ><i class="fa fa-ban"></i> {{ 'WFLOW.INTRO.BUTTON.CANCEL' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonDelete()"  ><button type="button" class="btn btn-danger"  ng-click="actionDelete()"  ><i class="fa fa-trash"></i> {{ 'WFLOW.INTRO.BUTTON.DELETE' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonSchedule()"><button type="button" class="btn btn-primary" ng-click="actionSchedule()"><i class="fa fa-calendar"></i> {{ 'WFLOW.INTRO.BUTTON.SCHEDULE' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonResched()" ><button type="button" class="btn btn-primary" ng-click="actionResched()" ><i class="fa fa-calendar"></i> {{ 'WFLOW.INTRO.BUTTON.RESCHEDULE' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonStart()"   ><button type="button" class="btn btn-success" ng-click="actionStart()"   ><i class="fa fa-play"></i> {{ 'WFLOW.INTRO.BUTTON.START' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonReassign()"><button type="button" class="btn btn-danger"  ng-click="actionReassign()"><i class="fa fa-stop"></i> {{ 'WFLOW.INTRO.BUTTON.REASS' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonUnreg()"   ><button type="button" class="btn btn-primary" ng-click="actionUnreg()"   ><i class="fa fa-sign-out"></i> {{ 'WFLOW.INTRO.BUTTON.UNREG' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonRegister()"><button type="button" class="btn btn-primary" ng-click="actionRegister()"><i class="fa fa-sign-in"></i> {{ 'WFLOW.INTRO.BUTTON.REG' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonContinue()"><button type="button" class="btn btn-success" ng-click="actionContinue()"><i class="fa fa-play"></i> {{ 'WFLOW.INTRO.BUTTON.CONTINUE' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonCommit()"  ><button type="button" class="btn btn-primary" ng-click="actionCommit()"  ><i class="fa fa-check-square-o"></i> {{ 'WFLOW.INTRO.BUTTON.COMMIT' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonClose()"   ><button type="button" class="btn btn-primary" ng-click="actionClose()"   ><i class="fa fa-key"></i> {{ 'WFLOW.INTRO.BUTTON.CLOSE' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonCopy()"    ><button type="button" class="btn btn-danger"  ng-click="actionCopy()"    ><i class="fa fa-copy"></i> {{ 'WFLOW.INTRO.BUTTON.COPY' | translate }}</button></div>
                <div class="flexitem-0 ll-button flexbottom" ng-show="showButtonReopen()"  ><button type="button" class="btn btn-primary" ng-click="actionReopen()"  ><i class="fa fa-unlock"></i> {{ 'WFLOW.INTRO.BUTTON.REOPEN' | translate }}</button></div>
            </div>
        </div>

        <div class="flexitem-1 ll-fullheight ll-wflowlist flexvcnt" ng-show="wfIntroLoaded">
            <div class="flexitem-1 ll-scroll">
                <div class="ll-procedureblock" ng-repeat="procedure in check.checkdata.procedures" ng-if="!(((listFilter.hideInstructionSteps) && hasInstructionStepsOnly(procedure)) || (listFilter.hideOmittedOrSkipped && isEmittedOrSkipped(procedure)))">
                    <div class="ll-procedure flexcnt">
                        <div class="flexitem-fix20 text-center" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)"><i class="fa" ng-class="listStatusIcon(procedure)"></i></div>
                        <div class="flexitem-fix20 text-center ll-ptrhand" ng-show="check.status && check.status == CHK_STATUS_SCHEDULED && procedure.status != CHK_CDSTATUS_OMITTED_HARD" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.SELASS' | translate }}" ng-click="listUserAssignAction(procedure,null)"><i class="fa" ng-class="listUserAssignIcon(procedure)"></i></div>
                        <div class="flexitem-fix20 text-center ll-ptrhand" ng-show="check.status && check.status == CHK_STATUS_SCHEDULED && procedure.status == CHK_CDSTATUS_OMITTED_HARD"><i class="fa fa-times-circle-o"></i></div>
                        <div class="flexitem-fix20 text-center" ng-show="!check.status || check.status == 0"></div>
                        <div class="flexspace-m"></div>
                        <div class="flexitem-fix20 text-center text-primary ll-ptrhand" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.GOTOSTEP' | translate }}" ng-click="gotoStep(procedure)"><i class="fa" ng-class="listViewEditIcon(procedure)"></i></div>
                        <div class="flexspace-m" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)"></div>
                        <div class="flexitem-1 ll-ptrhand" ng-class="{ 'll-highlight': procedure.selected }" ng-click="listToggleCollapse(procedure)">{{ procedure.info.code }} <span class="ll-title">{{ procedure.info.title | translate_model }}</span></div>
                        <div class="flexitem-fix20 text-center text-success" ng-show="procedure.committed"><i class="fa fa-check"></i></div>
                        <div class="flexitem-fix20 text-center ll-ptrhand" ng-click="listToggleCollapse(procedure)">
                            <i class="fa" ng-class="procedure.collapsed ? 'fa-chevron-right' : 'fa-chevron-down'"></i>
                        </div>
                    </div>
                    <div ng-if="!procedure.collapsed">
                        <div class="ll-stepblock" ng-repeat="step in procedure.steps" ng-if="!((step.steptype == 1 && (listFilter.hideInstructionSteps)) || (listFilter.hideOmittedOrSkipped && isEmittedOrSkipped(step)))">
                            <div class="ll-step flexcnt">
                                <div class="flexitem-fix20 text-center" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)">
                                    <i class="fa" ng-class="listStatusIcon(step)"></i>
                                    <i class="fa fa-cogs" style="font-size:70%;position:relative;right:10px" ng-show="(!step.completet || !step.completem) && matchConst(step.status, CHK_CDSTATUS_ALL_FINISHED)"></i>
                                </div>
                                <div class="flexitem-fix20 text-center ll-ptrhand" ng-show="check.status && check.status == CHK_STATUS_SCHEDULED && step.status != CHK_CDSTATUS_OMITTED_HARD" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.SELASS' | translate }}" ng-click="listUserAssignAction(procedure,step)"><i class="fa" ng-class="listUserAssignIcon(step)"></i></div>
                                <div class="flexitem-fix20 text-center ll-ptrhand" ng-show="check.status && check.status == CHK_STATUS_SCHEDULED && step.status == CHK_CDSTATUS_OMITTED_HARD"><i class="fa fa-times-circle-o"></i></div>
                                <div class="flexitem-fix20 text-center" ng-show="!check.status || check.status == CHK_STATUS_INIT"></div>
                                <div class="flexspace-m"></div>
                                <div class="flexitem-fix20 text-center text-primary ll-ptrhand" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.INTRO.TOOLTIP.GOTOSTEP' | translate }}" ng-click="gotoStep(step)">
                                    <i ng-class="'fa fa-' + (step.steptype == 1 ? 'book' : ((canBeEdited(step)) ? (step.locked == 1 ? 'lock' : 'edit') : 'eye'))"></i>
                                </div>
                                <div class="flexspace-m" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)"></div>
                                <div class="flexitem-1 ll-ptrhand" ng-class="{ 'll-highlight': step.selected }" ng-click="listToggleCollapse(step)">{{ step.info.code }} <span class="ll-title">{{ step.info.title | translate_model }}</span></div>
                                <div class="flexitem-fix20 text-center text-success" ng-show="step.committed"><i class="fa fa-check"></i></div>
                                <div class="flexitem-fix20 text-center ll-ptrhand" ng-click="listToggleCollapse(step)">
                                    <i ng-show="step.steptype != 1" class="fa" ng-class="step.collapsed ? 'fa-chevron-right' : 'fa-chevron-down'"></i>
                                </div>
                            </div>
                            <div ng-if="!step.collapsed">
                                <div class="ll-measure flexcnt" ng-repeat="measure in step.measures" ng-if="!(listFilter.hideOmittedOrSkipped && isEmittedOrSkipped(measure))">
                                    <div class="flexitem-fix20 text-center" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)"><i class="fa" ng-class="listStatusIcon(measure)"></i></div>
                                    <div class="flexitem-fix20 text-center" ng-show="!check.status || matchConst(check.status, CHK_STATUS_ALL_PRESTART)"><i class="fa fa-times-circle-o" ng-if="measure.status == CHK_CDSTATUS_OMITTED_HARD"></i></div>
                                    <div class="flexspace-m"></div>
                                    <div class="flexitem-fix20 text-center text-primary" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)"><i ng-show="measure.locked == 1" class="fa fa-lock"></i></div>
                                    <div class="flexspace-m" ng-show="matchConst(check.status, CHK_STATUS_ALL_STARTED)"></div>
                                    <div class="flexitem-1 flexcnt" ng-class="{ 'll-highlight': measure.selected }">
                                        <span class="flexitem-fix50">{{ measure.info.code }}</span>
                                        <span class="ll-title">{{ measure.info.title | translate_model }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flexitem-0 flexcnt" ng-class="{'ll-filter': !isMobileApp, 'll-filtermob': isMobileApp }">
                <div class="flexitem-fix20 ll-tbutton" ng-click="openFilterDialog()" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'WFLOW.INTRO.FILTER.TOOLTIP.VIEWFILT' | translate }}">
                    <i class="fa fa-filter" ng-class="{ 'll-selected': listFilter.enableFilter }"></i>
                </div>
                <div class="flexitem-1" ng-show="!isMobileApp"></div>
                <div class="flexitem-0" ng-show="!isMobileApp">{{ 'WFLOW.INTRO.FILTER.BUTTONS.COLLAPSE' | translate }}</div>
                <div class="flexitem-0" ng-show="isMobileApp">|</div>
                <div class="flexspace-m"></div>
                <div class="flexitem-fix40 ll-tbutton" ng-click="setFilter('collapse',3)" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'WFLOW.INTRO.FILTER.TOOLTIP.PROC' | translate }}">
                    <span ng-class="{ 'll-selected': listFilter.collapse === 3 }">{{ 'WFLOW.INTRO.FILTER.BUTTONS.PROC' | translate }}</span>
                </div>
                <div class="flexitem-fix40 ll-tbutton" ng-click="setFilter('collapse',2)" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'WFLOW.INTRO.FILTER.TOOLTIP.MEAS' | translate }}">
                    <span ng-class="{ 'll-selected': listFilter.collapse === 2 }">{{ 'WFLOW.INTRO.FILTER.BUTTONS.STEP' | translate }}</span>
                </div>
                <div class="flexitem-fix40 ll-tbutton" ng-click="setFilter('collapse',1)" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'WFLOW.INTRO.FILTER.TOOLTIP.STEP' | translate }}">
                    <span ng-class="{ 'll-selected': listFilter.collapse === 1 }">{{ 'WFLOW.INTRO.FILTER.BUTTONS.MEAS' | translate }}</span>
                </div>
                <div class="flexitem-fix40 ll-tbutton" ng-show="!isMobileApp" ng-click="setFilter('collapse',4)" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'WFLOW.INTRO.FILTER.TOOLTIP.FILTER' | translate }}">
                    <span ng-class="{ 'll-selected': listFilter.collapse === 4 }">{{ 'WFLOW.INTRO.FILTER.BUTTONS.FILTER' | translate }}</span>
                </div>
                <div class="flexitem-fix40 ll-tbutton" ng-show="!isMobileApp" ng-click="setFilter('collapse',5)" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'WFLOW.INTRO.FILTER.TOOLTIP.FILTERPLUS' | translate }}">
                    <span ng-class="{ 'll-selected': listFilter.collapse === 5 }">{{ 'WFLOW.INTRO.FILTER.BUTTONS.FILTERPLUS' | translate }}</span>
                </div>
                <div class="flexspace-m"></div>
                <div class="flexitem-fix20 ll-tbutton" ng-show="!isMobileApp" ng-click="resetFilter()" tooltip-popup-delay='1000' tooltip-placement="left" tooltip="{{ 'WFLOW.INTRO.FILTER.TOOLTIP.RESET' | translate }}">
                    <i class="fa fa-times-circle-o"></i>
                </div>
            </div>
        </div>
    </div>
</div>
