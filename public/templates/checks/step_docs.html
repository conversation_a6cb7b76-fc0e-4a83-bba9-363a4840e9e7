<div ng-repeat="doc in step.documents" class="flexcnt">
    <div class="flexitem-fix20 flexcenter text-primary" ng-show="doc.doctype == MEDIA_DOCTYPE_PDF"><i class="fa fa-file-pdf-o"></i></div>
    <div class="flexitem-fix20 flexcenter text-primary" ng-show="doc.doctype == MEDIA_DOCTYPE_VIDEO"><i class="fa fa-video-camera"></i></div>
    <div class="flexspace-s"></div>
    <div class="flexcenter flexitem-0 ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'WFLOW.STEP.TOOLTIP.VIEWDOC' | translate }}" ng-click="viewDoc(doc)">{{ doc.caption ? doc.caption : doc.binaryfile.original_filename }}</div>
    <div class="flexspace-m"></div>
    <div class="flexcenter flexitem-0 ll-data" ng-show="doc.doctype == MEDIA_DOCTYPE_PDF">{{ 'WFLOW.STEP.PDFINFO' | translate : { pnum: doc.binaryfile.metadata.pages } }}</div>
    <div class="flexcenter flexitem-0 ll-data" ng-show="doc.doctype == MEDIA_DOCTYPE_VIDEO">{{ 'WFLOW.STEP.VIDINFO' | translate : { width: doc.binaryfile.metadata.width, height: doc.binaryfile.metadata.height, fps: sprintf('%0.1f',doc.binaryfile.metadata.fps || 0.0), rtime: sprintf('%0.1f',doc.binaryfile.metadata.duration || 0.0) } }}</div>
</div>