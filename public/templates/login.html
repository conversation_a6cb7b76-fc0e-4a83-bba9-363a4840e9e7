<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth ll-fullheight" ng-if="!isMobileApp">
        <form role="form" class="ll-loginbox flexvcnt" ng-submit="login()">
            <div class="flexitem-0 ll-title" translate>LOGIN.TITLE</div>
            <div class="flexitem-1"></div>
            <div class="flexcnt flexitem-0">
                <label for="username" class="flexitem-fix150 ll-label" translate>LOGIN.USERNAME</label>
                <div class="flexspace-m"></div>
                <div class="flexitem-1">
                    <input type="text" id="username" ng-model="logininfo.username" class="form-control ll-input" ui-keypress="quickloginKeymap" autofocus>
                </div>
            </div>
            <div class="flexspace-m"></div>
            <div class="flexcnt flexitem-0">
                <label for="pass" class="flexitem-fix150 ll-label" translate>LOGIN.PASSWORD</label>
                <div class="flexspace-m"></div>
                <div class="flexitem-1">
                    <input type="password" id="pass" ng-model="logininfo.password" class="form-control ll-input" ui-keypress="quickloginKeymap">
                </div>
            </div>
            <div class="flexitem-1"></div>
            <div class="flexcnt-r flexitem-0">
                <button type="button" class="btn btn-primary flexitem-fix150 ll-button" ng-click="login()"><i class="fa fa-key"></i> <span translate>LOGIN.BUTTON</span></button>
            </div>
        </form>
    </div>
    <div class="ll-mainpane ll-fullwidth ll-fullheight" ng-if="isMobileApp">
        <form role="form" class="ll-loginboxmob flexcnt" ng-submit="login()">
            <div class="flexitem-0 ll-title">{{ 'LOGIN.TITLE' | translate }}</div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0 ll-label">{{ 'LOGIN.USERNAME' | translate }}</div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><input type="text" id="usernamemob" ng-model="logininfo.username" class="form-control ll-input"></div>
            <div class="flexspace-l"></div>
            <div class="flexitem-0 ll-label">{{ 'LOGIN.PASSWORD' | translate }}</div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><input type="password" id="passmob" ng-model="logininfo.password" class="form-control ll-input"></div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0"><button type="button" class="btn btn-primary flexitem-fix150 ll-button" ng-click="login()"><i class="fa fa-key"></i> <span translate>LOGIN.BUTTON</span></button></div>
        </form>
    </div>
</div>
