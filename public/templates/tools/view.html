<div class="flexcnt ll-viewpanel">
    <div class="flexitem-1 flexvcnt ll-info" ng-show="toolViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ item.title | translate_model }}</div>
            <div class="flexitem-0 ll-code flexbottom">{{ item.code }}</div>
            <comm-report class="flexitem-0" style="margin-left:5px" type="tooltype" obj-id="{{ item.id }}"></comm-report>
        </div>
        <div class="flexitem-1 ll-body flexvcnt">
            <div class="flexitem-0 ll-value text-danger" ng-show="item.deleted"><i class="fa fa-exclamation-triangle"></i> {{ 'CONFIGTABLE.VIEW.DISABLED' | translate }}</div>
            <div class="flexitem-0 ll-title">{{ 'TOOL.VIEW.CODE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.code }}</div>
            <div class="flexitem-0 ll-title">{{ 'TOOL.VIEW.TITLE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.title | translate_model }}</div>
            <div class="flexitem-0 ll-title">{{ 'TOOL.VIEW.DESCRIPTION' | translate }}</div>
            <div class="flexitem-0 ll-value flexwrap">{{ item.description | translate_model }}</div>
            <div class="flexitem-0 ll-title">{{ 'TOOL.VIEW.MEDIA' | translate }}</div>
            <div class="flexitem-0 ll-value flexcnt">
                <div class="flexitem-1">{{ 'TOOL.VIEW.IMGREG' | translate : { imgcnt: item.images.length } }}<br>{{ 'TOOL.VIEW.DOCREG' | translate : { doccnt: item.documents.length } }}<br></div>
                <div class="flexitem-0"><button type="button" class="btn btn-default" ng-show="hasGrant('EDTTTY')" ng-click="mediamanager()"><i class="fa fa-image"></i> {{ 'TOOL.VIEW.BUTTON.MEDMGR' | translate }}</button></div>
            </div>
            <div class="flexitem-0 ll-value flexcnt"><i>{{ 'TOOL.VIEW.MEDINFO' | translate }}</i></div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-buttonbar flexcnt-r">
            <div class="flexitem-1"></div>
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('EDTTTY') && hasGrant('DELTTY')" class="btn btn-danger" ng-click="deleteItem()"><i class="fa fa-ban"></i> {{ 'TOOL.VIEW.BUTTON.DELETE' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('EDTTTY')" class="btn btn-primary" ng-click="editItem()"><i class="fa fa-edit"></i> {{ 'TOOL.VIEW.BUTTON.EDIT' | translate }}</button></div>
        </div>
    </div>
    <div class="flexspace-divv" ng-show="toolViewLoaded"></div>
    <div class="flexspace-s"></div>
    <div class="flexitem-1 flexvcnt ll-list" ng-show="toolViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ 'TOOL.VIEW.TUNITS' | translate }}</div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><i class="fa fa-eye ll-ptrhand" ng-class="{'text-success': showDeleted}" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.SHOWDEL' | translate }}" ng-click="toggleShowDeleted()"></i></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0 ll-ptrhand"><i class="fa fa-lg fa-plus-square-o" ng-show="hasGrant('CRTTOL')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.NEWTUNIT' | translate }}" ng-click="newToolunit()"></i></div>
        </div>

        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body">
            <div class="ll-item flexcnt" ng-repeat="tunit in item.toolunits" ng-show="showDeleted || !tunit.deleted">
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-0 ll-title">{{ tunit.code }}<span ng-show="tunit.deleted" class="text-danger">&nbsp;&nbsp;&nbsp;<i class="fa fa-exclamation-triangle"></i> {{ 'TOOL.VIEW.TOOLDEL' | translate }}</span></div>
                    <div class="flexitem-0 ll-counter">{{ 'TOOL.VIEW.TOOLUNITCNT' | translate : { ucnt: tunit.measurements.length } }}</div>
                </div>
                <div class="flexspace-m"></div>
                <div class="ll-actions" ng-class="tunit.hasComment ? 'text-success' : ''" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.SETCOMMENT' | translate }}" ng-click="setToolunitComment(tunit)"><i class="fa fa-comments-o"></i>&nbsp;</div>
                <div class="ll-actions text-primary" ng_show="hasGrant('CRTTOL')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.REPORT' | translate }}" ng-click="generateReport(tunit)"><i class="fa fa-file-text-o"></i></div>
                <div class="flexspace-s"></div>
                <div class="ll-actions text-primary" ng_show="hasGrant('CRTTOL')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.EDITTUNIT' | translate }}" ng-click="editToolunit(tunit)"><i class="fa fa-edit"></i></div>
                <div class="ll-actions text-success" ng_show="tunit.deleted && hasGrant('DELTOL')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.REENABLE' | translate }}" ng-click="toggleToolunitDeleted(tunit)"><i class="fa fa-cog"></i></div>
                <div class="ll-actions text-danger" ng_show="!tunit.deleted && hasGrant('DELTOL')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.DISABLE' | translate }}" ng-click="toggleToolunitDeleted(tunit)"><i class="fa fa-ban"></i></div>
                <div class="ll-actions text-danger" ng_show="hasGrant('DELTOL')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'TOOL.VIEW.TOOLTIP.REMTUNIT' | translate }}" ng-click="deleteToolunit(tunit)"><i class="fa fa-trash-o"></i></div>
            </div>
        </div>
    </div>
</div>


