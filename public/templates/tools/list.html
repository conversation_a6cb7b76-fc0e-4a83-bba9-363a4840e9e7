<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-1 ll-listpanel flexvcnt ll-fullheight">
            <div class="flexitem-1 flexvcnt" ng-show="toolListLoaded">
                <div class="flexitem-0 ll-header flexcnt">
                    <div class="flexitem-1">{{ 'TOOL.LIST.TTYPES' | translate }}</div>
                    <div class="flexitem-0"><i class="fa fa-lg fa-plus-square-o ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'TOOL.LIST.TOOLTIP.NEW' | translate }}" ng-click="newTooltype()" ng-show="hasGrant('EDTTTY') && hasGrant('CRTTTY')"></i></div>
                </div>
                <div class="flexitem-0 ll-controls flexcnt">
                    <div class="flexitem-0"><i class="fa fa-search ll-ptrhand" ng-class="{ 'text-success': filters.textfilter.length > 0 }" tooltip-popup-delay='1000' tooltip="{{ 'TOOL.LIST.TOOLTIP.CLTF' | translate }}" ng-click="clearTextfilter()"></i></div>
                    <div class="flexspace-s"></div>
                    <input type="text" id="listfilter" ng-model="filters.textfilter" class="flexitem-1 form-control ll-input input-xxs" ng-blur="saveFilterSettings()">
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-eye ll-ptrhand" ng-class="{'text-success': showDeleted}" tooltip-popup-delay='1000' tooltip="{{ 'TOOL.LIST.TOOLTIP.SHOWDEL' | translate }}" ng-click="toggleShowDeleted()"></i></div>
                </div>
                <div class="flexitem-1 ll-body">
                    <div class="ll-item flexvcnt ll-ptrhand" ng-repeat="tool in items | filter:filters.textfilter" ng-show="showDeleted || !tool.deleted" ng-class="itemSelected(tool)" ng-click="goto(tool.id)">
                        <div class="flexitem-0 flexcnt">
                            <div class="flexitem-1 ll-title" ng-class="{ 'll-disabled': tool.deleted }">{{ tool.title | translate_model }}</div>
                        </div>
                        <div class="flexitem-0 ll-counter"><span class="ll-code">{{ tool.code }}: </span> {{ 'TOOL.LIST.BTMROW' | translate : { mcnt: tool.measurecnt, tcnt: tool.unitcnt } }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-3 ll-fullheight"><ui-view/></div>
    </div>
</div>