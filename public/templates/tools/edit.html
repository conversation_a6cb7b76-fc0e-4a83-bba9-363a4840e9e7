<div class="flexvcnt ll-editpanel" ng-show="toolEditLoaded">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{editor.title}}" translate-values="editor.titlevals"></div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'TOOL.EDIT.CODE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="tt_code" ng-model="item.code" class="form-control ll-input" ng-disabled="!hasGrant('CHGCOD') && item.id">
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'TOOL.EDIT.TITLE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="tt_title" ng-model="item.title[languageinfo.selected]" class="form-control ll-input " ui-keyup="langToggleKeymap">
                <lang-completion-info langinfo="languageinfo" model="item.title"></lang-completion-info>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'TOOL.EDIT.HINTS' | translate }}</div>
            <div class="ll-edit">
                <textarea rows="3" id="tt_desc" ng-model="item.description[languageinfo.selected]" class="form-control ll-input" ui-keyup="langToggleKeymap"></textarea>
                <lang-completion-info langinfo="languageinfo" model="item.description"></lang-completion-info>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'TOOL.EDIT.DIS.TITLE' | translate }}</div>
            <div class="ll-edit">
                <input type="checkbox" ng-model="item.deleted"> <span ng-show="item.deleted">{{ 'TOOL.EDIT.DIS.TRUE' | translate }}</span><span ng-hide="item.deleted">{{ 'TOOL.EDIT.DIS.FALSE' | translate }}</span>
            </div>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancelEdit()"><i class="fa fa-ban"></i> {{ 'TOOL.EDIT.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="saveAndReturn()"><i class="fa fa-save"></i> {{ 'TOOL.EDIT.BUTTON.SAVE' | translate }}</button></div>
    </div>
</div>
