<div class="ll-dashblock flexvcnt">
    <div ng-class="{'ll-header': !$parent.isMobileApp, 'll-headermob': $parent.isMobileApp }" class="flexitem-0 flexcnt">
        <span class="flexitem-0 ll-nofilter ll-icon" ng-show="data.filtered == 1"><i class="ll-ptrhand fa fa-filter" tooltip-placement="right" tooltip-popup-delay='1000' tooltip="{{ 'DASHBLOCK.TOOLTIP.SETFILTER' | translate }}" ng-click="$parent.setFilter(data.num)"></i></span>
        <span class="flexitem-0 ll-filterused ll-icon" ng-show="data.filtered == 2"><i class="ll-ptrhand fa fa-filter" tooltip-placement="right" tooltip-popup-delay='1000' tooltip="{{ 'DASHBLOCK.TOOLTIP.SETFILTER' | translate }}" ng-click="$parent.setFilter(data.num)"></i></span>
        <span class="flexspace-s"></span>
        <span class="flexitem-1">{{ data.title | translate }} <span class="ll-editpen ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'DASHBLOCK.TOOLTIP.EDITTITLE' | translate }}" ng-click="$parent.editBlockName(data.num)"><i class="fa fa-pencil"></i></span></span>
        <span class="flexspace-l"></span>
        <small class="flexitem-0">
            <input type="text" class="ll-quickfilter" ng-model="quickfilter" style="color: black">
        </small>
        <span class="flexspace-l"></span>
        <small class="flexitem-0" style="min-width: 20px; vertical-align: middle; line-height: 1.8em;">{{filteredItems.length}}</small>
        <span class="flexspace-l"></span>
        <span class="flexitem-0 ll-icon" ng-show="$parent.numblocks<4"><i class="ll-ptrhand fa fa-plus-circle" tooltip-placement="left" tooltip-popup-delay='1000' tooltip="{{ 'DASHBOARD.ADDDASH.BUTTON' | translate }}" ng-click="$parent.addBlock()"></i></span>
        <span class="flexspace-s"></span>
        <span class="flexitem-0 ll-icon"><i class="ll-ptrhand fa fa-times-circle" tooltip-placement="left" tooltip-popup-delay='1000' tooltip="{{ 'DASHBLOCK.TOOLTIP.CLOSE' | translate }}" ng-click="$parent.closeBlock(data.num)"></i></span>
    </div>
    <div class="flexitem-1 ll-scroll">
        <div class="ll-list">
            <div class="ll-item" ng-repeat="item in filteredItems = (data.items | dashboarditemfilter:quickfilter.toLowerCase())" ng-click="$parent.$parent.gotoItem(item.url)">
                <div ng-repeat="line in item.showlines">
                    <div class="flexcnt" ng-if="$first && item.progress">
                        <div class="ll-topline flexitem-1" translate-compile translate="{{ data.line+line }}" translate-values="item"></div>
                        <div class="flexspace-s"></div>
                        <div class="ll-topline flexitem-fix40">V. {{item.model.version}}</div>
                        <div class="flexitem-fix200 ll-progress"><progress animate="false" style="display: flex">
                            <bar value="item.progress.passed" type="success" class="ll-colpass"><span ng-hide="item.progress.passed < 20">{{item.progress.passed | number:1}}%</span></bar>
                            <bar value="item.progress.failed" type="danger" class="ll-colfail"></bar>
                            <bar value="item.progress.unfin" type="primary" class="ll-colunfin"></bar>
                        </progress></div>
                    </div>
                    <div ng-class="$first ? 'll-topline' : 'll-bottomline'" ng-if="!$first || !item.progress" translate-compile translate="{{ data.line+line }}" translate-values="item"></div>
                </div>
            </div>
        </div>
    </div>
</div>