<div class="flexitem-0 ll-dropbox" collapse="data.collapsed" nv-file-drop nv-file-over uploader="$parent.uploader">
    <div class="flexcnt">
        <div class="flexitem-1">
            <div class="ll-drophint flexwrap" translate>DROPBOX.INTRO</div>
            <div class="ll-dropinfo" ng-show="data.uploading != null">{{ 'DROPBOX.UPLOADING' | translate : { num: data.uploading } }}</div>
            <div class="ll-dropinfo" ng-show="data.numremaining > 0">{{ 'DROPBOX.REMAINING' | translate : { num: data.numremaining } }}</div>
            <div class="ll-dropinfo text-success" ng-show="data.numsuccess > 0">{{ 'DROPBOX.SUCCESS' | translate : { num: data.numsuccess } }}</div>
            <div class="ll-dropinfo text-danger" ng-show="data.numerror > 0">{{ 'DROPBOX.ERRORS' | translate : { num: data.numerror } }}</div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 flexcenter ll-dropicon"><i class="fa fa-upload"></i></div>
    </div>
</div>