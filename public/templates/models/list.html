<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-1 ll-listpanel flexvcnt ll-fullheight">
            <div class="flexitem-1 flexvcnt" ng-show="modelListLoaded">
                <div class="flexitem-0 ll-header flexcnt">
                    <div class="flexitem-1" translate>MODEL.MODELS</div>
                    <div class="flexitem-0"><i tooltip-popup-delay='1000' tooltip="{{ 'MODEL.LIST.TOOLTIP.IMPORTMODEL' | translate }}" class="fa fa-upload ll-ptrhand" ng-click="toggleUploader()" ng-show="hasGrant('EDTMOD') && hasGrant('CRTMOD')"></i></div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i tooltip-popup-delay='1000' tooltip="{{ 'MODEL.LIST.TOOLTIP.NEWMODEL' | translate }}" class="fa fa-lg fa-plus-square-o ll-ptrhand" ng-click="newModel()" ng-show="hasGrant('EDTMOD') && hasGrant('CRTMOD')"></i></div>
                </div>
                <dropbox data="uploaderinfo"></dropbox>
                <div class="flexitem-0 ll-controls flexcnt">
                    <div class="flexitem-0"><i class="fa fa-search ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'MODEL.LIST.TOOLTIP.CLTF' | translate }}" ng-class="{ 'text-success': filters.textfilter.length > 0 }" ng-click="clearTextfilter()"></i></div>
                    <div class="flexspace-s"></div>
                    <input type="text" id="listfilter" ng-model="filters.textfilter" class="flexitem-1 form-control ll-input input-xxs" ng-blur="saveFilterSettings()">
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-eye ll-ptrhand" ng-class="{'text-success': showDeleted}" tooltip-popup-delay='1000' tooltip="{{ 'MODEL.LIST.TOOLTIP.SHOWDEL' | translate }}" ng-click="toggleShowDeleted()"></i></div>
                </div>
                <div class="flexitem-1 ll-body">
                    <div class="ll-item flexvcnt ll-ptrhand" ng-repeat="model in items | filter:filters.textfilter" ng-class="itemSelected(model)" ng-click="goto(model.id)" ng-show="showDeleted || !itemDeleted(model)">
                        <div class="flexitem-0 flexcnt">
                            <div class="flexitem-1 ll-title" ng-class="{ 'll-disabled': itemDeleted(model), 'll-dirty': model.dirty }">{{ model.title | translate_model }}</div>
                            <div class="flexitem-0 flexbottom ll-version">{{ model.versname.text | translate : model.versname }}</div>
                        </div>
                        <div class="flexitem-0 ll-counter flexcnt">
                            <div class="flexitem-1"><span class="ll-code">{{ model.code }} -</span> {{ 'MODEL.LIST.TOPROW' | translate : { dtypt: i18n.translate(model.devicetype.title), dtypc: model.devicetype.code, unitnum: model.usage.length } }}</div>
                            <div class="flexspace-s"></div>
                            <div class="flexitem-0"><i tooltip-popup-delay='1000' tooltip="{{ 'MODEL.LIST.TOOLTIP.GOTOUNITS' | translate }}" class="fa fa-external-link-square ll-ptrhand text-primary" ng-show="model.versname.vers > 0" ng-click="gotoUnits(model.id,$event)"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-3 ll-fullheight"><ui-view/></div>
    </div>
</div>