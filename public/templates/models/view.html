<div class="flexcnt ll-viewpanel">
    <div class="flexitem-1 flexvcnt ll-info" ng-show="modelViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ item.title | translate_model }}</div>
            <div class="flexitem-0 ll-code flexbottom">{{ item.code }}</div>
            <comm-report class="flexitem-0" style="margin-left:5px" type="model" obj-id="{{ item.id }}"></comm-report>
        </div>
        <div class="flexitem-0 ll-version">
            {{ 'MODEL.VIEW.VERSIONTAG' | translate }}
            {{ item.versname.text | translate : item.versname }}
            <i tooltip-popup-delay='1000' tooltip="{{ 'MODEL.VIEW.TOOLTIP.SWITCHVER' | translate }}" class="fa fa-fw fa-exchange text-primary ll-ptrhand" ng-show="item.versions.length > 1" ng-click="changeVersion()"></i>
            <i tooltip-popup-delay='1000' tooltip="{{ 'MODEL.VIEW.TOOLTIP.TESTVER' | translate }}" class="fa fa-fw fa-eye text-primary ll-ptrhand" ng-show="item.versions.length > 1" ng-click="testVersion()"></i>
            <i class="fa fa-fw fa-list text-primary ll-ptrhand" ng-show="hasGrant('EDTMUC') && hasGrant('FINALZ')" tooltip-popup-delay='1000' tooltip="{{ 'MODEL.VIEW.TOOLTIP.CHANGELOG' | translate }}" ng-click="showChangeLog()"></i>
            <i class="fa fa-fw fa-undo text-danger ll-ptrhand" ng-show="item.versions.length > 1 && !isVersion() && item.dirty && hasGrant('EDTMUC') && hasGrant('FINALZ')" tooltip-popup-delay='1000' tooltip="{{ 'MODEL.VIEW.TOOLTIP.RESET' | translate }}" ng-click="resetChanges()"></i>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body flexvcnt">
            <div class="flexitem-0 ll-title">{{ 'MODEL.VIEW.CODE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.code }}</div>
            <div class="flexitem-0 ll-title">{{ 'MODEL.VIEW.TITLE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.title | translate_model }}</div>
            <div class="flexitem-0 ll-title">{{ 'MODEL.VIEW.DTYPE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.devicetype.title | translate_model }} ({{ item.devicetype.code }})</div>
            <div class="flexitem-0 ll-title">{{ 'MODEL.VIEW.DESC' | translate }}</div>
            <div class="flexitem-0 ll-value flexwrap" ng-bind-html="item.description | translate_model"></div>
            <div class="flexitem-0 ll-title">{{ 'MODEL.VIEW.MEDIA' | translate }}</div>
            <div class="flexitem-0 ll-value flexcnt">
                <div class="flexitem-1">{{ 'MODEL.VIEW.IMREG' | translate : { imgnum: item.images.length } }}<br>{{ 'MODEL.VIEW.DOCREG' | translate : { docnum: item.documents.length } }}</div>
                <div class="flexitem-0" ng-show="showEditButtons('EDTMOD')"><button type="button" class="btn btn-default" ng-click="mediamanager()"><i class="fa fa-image"></i> {{ 'MODEL.VIEW.MEDMGR' | translate }}</button></div>
            </div>
            <div class="flexitem-0 ll-title">{{ 'MODEL.VIEW.UNITINFO.TTL' | translate }}</div>
            <div class="flexitem-0 ll-value flexcnt">
                <div class="flexitem-1" translate-compile translate="MODEL.VIEW.UNITINFO.TXT" translate-values="{ ucnt: item.counters.total, ocnt: item.counters.open, ccnt: item.counters.closed, dcnt: item.counters.discarded }"></div>
                <div class="flexitem-0" ng-show="item.versname.vers > 0"><button type="button" class="btn btn-default" ng-click="gotoUnits(item.id,null)"><i class="fa fa-external-link-square"></i> {{ 'MODEL.VIEW.BUTTON.UNIT' | translate }}</button></div>
            </div>

            <div feature-flag="configtable" class="flexitem-0">

                <div class="flexitem-0 ll-title flexcnt">
                    <div class="flexitem-1">
                        {{ 'MODEL.VIEW.CONFIGTABLE.TTL' | translate }}
                    </div>
                    <div ng-hide="item.configtable_id" tooltip-popup-delay='1000' tooltip="{{ 'MODEL.VIEW.TOOLTIP.ADDCONFIGTABLE' | translate }}" class="flexitem-0 ll-ptrhand flexbottom">
                        <i class="fa fa-lg fa-plus-square-o" ng-show="showEditButtons('MNGCFG')" ng-click="linkConfigtable()"></i>
                    </div>
                </div>

                <div ng-show="item.configtable_id" class="flexitem-0 ll-value flexcnt">
                    <div class="flexitem-1 flexcnt">
                        <div class="flexitem-0">{{ 'MODEL.VIEW.CONFIGTABLE.TTL' | translate }} {{ item.configtable_id }} </div>
                        <div class="flexspace-s"></div>
                        <div tooltip-popup-delay='1000' tooltip="{{ 'MODEL.VIEW.TOOLTIP.DELCONFIGTABLE' | translate }}" class="ll-actions text-danger ll-ptrhand" ng-show="showEditButtons('MNGCFG')" ng-click="unlinkConfigtable(item.configtable_id)"><i class="fa fa-trash-o"></i></div>
                    </div>
                    <div class="flexitem-0">
                        <button type="button" class="btn btn-default" ng-click="gotoConfigtable(item.configtable_id)">
                            <i class="fa fa-external-link-square"></i>
                            {{ 'MODEL.VIEW.BUTTON.CONFIGTABLE' | translate }}
                        </button>
                    </div>
                </div>
                <div ng-hide="item.configtable_id" class="flexitem-0 ll-value flexcnt">
                    {{  'MODEL.VIEW.CONFIGTABLE.NORELATION' | translate }}
                </div>
            </div>

        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-buttonbar flexcnt-r">

            <!-- LLQA-152 - Exportmöglichkeit für Modelle entfernt -->
            <!--<div class="flexitem-0"><button type="button" class="btn btn-primary" ng-click="exportItem($event)"><i class="fa fa-download"></i> {{ 'MODEL.VIEW.BUTTON.EXPORT' | translate }}</button></div>-->

            <div class="flexitem-1"></div>
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('DELMOD') && showEditButtons('EDTMOD')" class="btn btn-danger" ng-click="deleteItem()"><i class="fa fa-ban"></i> {{ 'MODEL.VIEW.BUTTON.DELETE' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button type="button" ng-show="!isVersion() && hasGrant('EDTMOD') && hasGrant('FINALZ')" ng-disabled="!item.dirty" class="btn btn-primary" ng-click="finalizeItem()"><i class="fa fa-check-circle"></i> {{ 'MODEL.VIEW.BUTTON.FIN' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button type="button" ng-show="showEditButtons('EDTMOD')" class="btn btn-primary" ng-click="editItem()"><i class="fa fa-edit"></i> {{ 'MODEL.VIEW.BUTTON.EDIT' | translate }}</button></div>
        </div>
    </div>
    <div class="flexspace-divv" ng-show="modelViewLoaded"></div>
    <div class="flexspace-s"></div>
    <div class="flexitem-1 flexvcnt ll-list" ng-show="modelViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ 'MODEL.VIEW.ACTPROC' | translate }}</div>
            <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.ACTREORD' | translate }}" class="flexitem-0 ll-ptrhand" ng_show="showActivateReorderIcon('EDTMOD')" ng-click="activateReordering(true)"><i class="fa fa-sort"></i></div>
            <div class="flexspace-s" ng-show="showEditButtons('EDTMOD')"></div>
            <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.ADDPROC' | translate }}" class="flexitem-0 ll-ptrhand" ng-show="showEditButtons('EDTMOD')"><i class="fa fa-lg fa-plus-square-o" ng-click="addProcedure()"></i></div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-2 ll-body" ui-sortable="sublistSortOptions" ng-model="item.activeprocedures">
            <div class="ll-item flexcnt" ng-repeat="aproc in item.activeprocedures">
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-0 ll-title">{{ aproc.procedure.title | translate_model }}</div>
                    <div class="flexitem-0 ll-counter">
                        <span class="ll-code">{{ aproc.procedure.code }}/{{ aproc.procedure.versname.text | translate : aproc.procedure.versname }} - </span>
                        {{ 'MODEL.VIEW.PROC.STEPNUM' | translate : { stepnum: aproc.procedure.scnt } }}
                        <span class="ll-code text-danger" ng-show="aproc.procedure.vinfo.disabled">- {{ 'MODEL.VIEW.PROC.DISCONTINUED' | translate }}</span>
                        <span class="ll-code" ng-show="aproc.procedure.vinfo.latest != aproc.procedure.version">- {{ 'MODEL.VIEW.PROC.OLDVERS' | translate : { pvers: aproc.procedure.version } }}</span>
                    </div>
                </div>
                <div class="flexspace-m"></div>
                <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.UPDPROC' | translate }}" class="ll-actions text-primary" ng_show="aproc.procedure.vinfo.latest != aproc.procedure.version && showEditButtons('EDTMOD') && hasGrant('FINALZ')" ng-click="updateProcedure(aproc)"><i class="fa fa-recycle"></i></div>
                <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.REORDER' | translate }}" class="ll-dragaction text-primary" ng-show="showReorderIcon('EDTMOD')"><i class="fa fa-sort"></i></div>
                <div class="ll-actions text-muted" ng_show="showEditButtons('EDTMOD') && $index == 0" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCETOP' | translate }}"><i class="fa fa-fw fa-circle-o"></i></div>
                <div class="ll-actions text-success" ng_show="showEditButtons('EDTMOD') && $index > 0 && aproc.enforce == PSM_ENFORCE_NONE" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE0' | translate }}" ng-click="setEnforceLevel(aproc,1)"><i class="fa fa-fw fa-circle-o"></i></div>
                <div class="ll-actions text-danger" ng_show="showEditButtons('EDTMOD') && $index > 0 && aproc.enforce == PSM_ENFORCE_LASTONE" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE1' | translate }}" ng-click="setEnforceLevel(aproc,2)"><i class="fa fa-fw fa-dot-circle-o"></i></div>
                <div class="ll-actions text-danger" ng_show="showEditButtons('EDTMOD') && $index > 0 && aproc.enforce == PSM_ENFORCE_ALLPRIOR" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE2' | translate }}" ng-click="setEnforceLevel(aproc,0)"><i class="fa fa-fw fa-circle"></i></div>
                <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.PREALLOC'+aproc.preallocation.status | translate }}" class="ll-actions" ng-class="'ll-prealloc'+(aproc.preallocation.status)" ng-show="showEditButtons('EDTMOD')" ng-click="preallocateProcedure(aproc)"><i class="fa fa-user"></i></div>
                <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.GOTOPROC' | translate }}" class="ll-actions text-primary" ng-show="hasGrant('MNGPSM')" ng-click="gotoProcedure(aproc)"><i class="fa fa-external-link-square"></i></div>
                <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.DELPROC' | translate }}" class="ll-actions text-danger" ng-show="showEditButtons('EDTMOD')" ng-click="removeProcedure(aproc)"><i class="fa fa-trash-o"></i></div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ 'MODEL.VIEW.ACTCTYPE' | translate }}</div>
            <div class="flexspace-s"></div>
            <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.ADDCTYPE' | translate }}" class="flexitem-0 ll-ptrhand"><i class="fa fa-lg fa-plus-square-o" ng-show="showEditButtons('EDTMOD')" ng-click="addChecktype()"></i></div>
            <div class="flexspace-m"></div>
            <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.ADDCTYPE' | translate }}" class="flexitem-0 ll-ptrhand"><i class="fa fa-lg fa-chevron-down" ng-show="showActiveChecktypes" ng-click="showActiveChecktypes = false"></i></div>
            <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.ADDCTYPE' | translate }}" class="flexitem-0 ll-ptrhand"><i class="fa fa-lg fa-chevron-up" ng-show="!showActiveChecktypes" ng-click="showActiveChecktypes = true"></i></div>
        </div>
        <div class="flexspace-m"></div>
        <div ng-show="showActiveChecktypes" class="flexitem-1 ll-body">
            <div class="ll-item flexcnt" ng-repeat="actype in item.activechecktypes">
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-0 ll-title">{{ actype.checktype.title | translate_model }}</div>
                    <div class="flexitem-0 ll-counter"><span class="ll-code">{{ actype.checktype.code }}</span></div>
                </div>
                <div class="flexspace-m"></div>
                <div tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'MODEL.VIEW.TOOLTIP.DELCTYPE' | translate }}" class="ll-actions text-danger" ng_show="showEditButtons('EDTMOD')" ng-click="removeChecktype(actype)"><i class="fa fa-trash-o"></i></div>
            </div>
        </div>
    </div>
</div>


