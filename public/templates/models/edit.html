<div class="flexvcnt ll-editpanel" ng-show="modelEditLoaded">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{editor.title}}" translate-values="editor.titlevals"></div>
        <div class="flexitem-0 ll-subheader flexbottom">{{editor.subtitle.text | translate}} {{ editor.subtitle.vers }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MODEL.EDIT.CODE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="md_code" ng-model="item.code" class="form-control ll-input" ng-disabled="!hasGrant('CHGCOD') && !mayChangeCode(item)">
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MODEL.EDIT.TITLE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="md_title" ng-model="item.title[languageinfo.selected]" class="form-control ll-input focustoggle" ui-keyup="langToggleKeymap">
                <lang-completion-info langinfo="languageinfo" model="item.title" focus-after-change="#md_title"></lang-completion-info>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MODEL.EDIT.DTYPE' | translate }}</div>
            <div class="ll-edit">
                <select id="md_devtype" class="form-control" ng-model="item.devicetype_id" ng-options="item.id as devicetypeCboxName(item) for item in devicetypes"></select>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MODEL.EDIT.DESC' | translate }}</div>
            <div class="ll-edit">
                <text-angular ng-model="item.description[languageinfo.selected]" id="md_desc" name="md_desc" disabled-ui-keyup="langToggleKeymap"></text-angular>
                <lang-completion-info langinfo="languageinfo" model="item.description" focus-ta-after-change="md_desc"></lang-completion-info>
            </div>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="btn-group flexitem-0">
            <label class="btn btn-primary" ng-repeat="lang in languageinfo.languages" ng-model="languageinfo.selected" btn-radio="lang.code">{{ lang.name }}</label>
        </div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancelEdit()"><i class="fa fa-ban"></i> {{ 'MODEL.EDIT.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="saveAndReturn()"><i class="fa fa-save"></i> {{ 'MODEL.EDIT.BUTTON.SAVE' | translate }}</button></div>
    </div>
</div>
