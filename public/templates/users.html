<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">

        <div class="flexitem-1 ll-listpanel flexvcnt ll-fullheight">
            <div class="flexitem-1 flexvcnt" ng-show="userMgrLoaded">
                <div class="flexitem-0 ll-header flexcnt">
                    <div class="flexitem-1">{{ 'USERS.USERS' | translate }}</div>
                    <span class="flexspace-l"></span>
                    <small class="flexitem-0 ll-subheader" style="line-height: 160%">{{ (users | filter:quickFilter).length }}</small>
                    <span class="flexspace-l"></span>
                    <comm-report class="flexitem-0" type="users" id="0"></comm-report>
                    <div class="flexitem-0"><i class="fa fa-lg fa-plus-square-o ll-ptrhand" style="padding-left:5px" ng-show="mayCreateNewUser()" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.NEWUSER' | translate }}" ng-click="newUser()"></i></div>
                </div>
                <div class="flexitem-0 ll-controls flexcnt">
                    <div class="flexitem-0"><i class="fa fa-search ll-ptrhand" ng-class="{ 'text-success': filters.textfilter.length > 0 }" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.CLTF' | translate }}" tooltip-placement="right" ng-click="clearTextfilter()"></i></div>
                    <div class="flexspace-s"></div>
                    <input type="text" id="listfilter" ng-model="filters.textfilter" class="flexitem-1 form-control ll-input input-xxs" ng-blur="saveFilterSettings()">
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0">
                        <i class="fa fa-eye ll-ptrhand" ng-class="{'text-success': showDeleted}" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.SHOWDEL' | translate }}" ng-click="toggleShowDeleted()"></i>
                    </div>
                </div>
                <div class="flexitem-2 ll-body">
                    <div class="ll-item flexvcnt ll-ptrhand" ng-repeat="user in users | filter:quickFilter" ng-class="{ 'll-selected': (selMode === 'user' && seluser.id === user.id) }" ng-click="selectUser(user)">
                        <div class="flexitem-0 ll-title" ng-class="{ 'll-disabled': user.status != 1 }">{{ user.username }} ({{ user.realname }}) <i ng-hide="mayBeEdited(user)" class="fa fa-lock"></i></div>
                    </div>
                </div>

                <div class="flexitem-0 ll-header flexcnt" ng-show="showGroups">
                    <div class="flexitem-1">{{ 'USERS.GROUPS' | translate }}</div>
                    <div class="flexitem-0"><i class="fa fa-chevron-down ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.HIDEGRP' | translate }}" ng-click="toggleGroups()"></i></div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0">
                        <i class="fa fa-eye ll-ptrhand" ng-class="{'text-danger': showDeletedGroups}" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.SHOWDELGROUPS' | translate }}" ng-click="toggleShowDeletedGroups()"></i>
                    </div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-lg fa-plus-square-o ll-ptrhand" ng-show="hasGrant('GRPCRT')" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.NEWGRP' | translate }}" ng-click="newGroup()"></i></div>
                </div>
                <div class="flexitem-0 ll-header flexcnt" ng-hide="showGroups">
                    <div class="flexitem-1">{{ 'USERS.GROUPS' | translate }}</div>
                    <div class="flexitem-0"><i class="fa fa-chevron-up ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.SHOWGRP' | translate }}" ng-click="toggleGroups()"></i></div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-lg fa-plus-square-o ll-ptrhand" ng-show="hasGrant('GRPCRT')" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.NEWGRP' | translate }}" ng-click="newGroup()"></i></div>
                </div>
                <div class="flexitem-fix200 ll-body" ng-show="showGroups">
                    <div class="ll-item flexvcnt ll-ptrhand" ng-repeat="group in groups | filter:quickFilter" ng-show="!group.deleted || showDeletedGroups" ng-class="{ 'll-selected': (selMode === 'group' && group.id == selgroup.id) }" ng-click="selectGroup(group)">
                        <div class="flexitem-0 ll-title" ng-class="{'ll-disabled': group.deleted}">{{ group.name }}
                            <i ng-hide="mayBeEdited(group)" class="fa fa-lock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-3 ll-fullheight flexcnt ll-viewpanel">
            <div class="flexitem-1 flexvcnt ll-info" ng-show="userMgrLoaded">
                <div class="flexitem-0 ll-header flexcnt" ng-show="seluser && selMode === 'user'">
                    <div class="flexitem-1"><span class="ll-desc">{{ 'USERS.USER' | translate }}:</span> {{ seluser.username }} ({{ 'USERS.RANK' | translate }}: {{ seluser.rank }})</div>
                    <div class="flexitem-0 ll-code flexbottom">{{ seluser.realname }}</div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0" ng-show="seluser.status == 1 && mayBeEdited(seluser)"><i class="ll-ptrhand fa fa-unlock-alt text-success" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.DISUSER' | translate }}" ng-click="toggleUserActive()"></i></div>
                    <div class="flexitem-0" ng-show="seluser.status == 0 && mayBeEdited(seluser)"><i class="ll-ptrhand fa fa-lock text-danger" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.ENUSER' | translate }}" ng-click="toggleUserActive()"></i></div>
                    <div class="flexitem-0" ng-show="seluser.status == 1 && !mayBeEdited(seluser)"><i class="fa fa-unlock-alt text-success"></i></div>
                    <div class="flexitem-0" ng-show="seluser.status == 0 && !mayBeEdited(seluser)"><i class="fa fa-lock text-danger"></i></div>
                </div>
                <div class="flexitem-1 ll-body flexvcnt" ng-show="seluser && selMode === 'user'">
                    <div class="flexitem-0 ll-title">{{ 'USERS.USERNAME' | translate }}</div>
                    <div class="flexitem-0 ll-value"><i ng-show="mayBeEdited(seluser)" class="fa fa-edit ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.EDITUNAM' | translate }}" ng-click="editUserUsername()"></i>{{ seluser.username }}</div>
                    <div class="flexitem-0 ll-title">{{ 'USERS.PASSWORD.TITLE' | translate }}</div>
                    <div class="flexcnt flexitem-0">
                        <div class="flexitem-1 ll-value" ng-show="seluser.passhash != '*'">{{ 'USERS.PASSWORD.SET' | translate }}</div>
                        <div class="flexitem-1 ll-value" ng-show="seluser.passhash == '*'">{{ 'USERS.PASSWORD.NOTSET' | translate }}</div>
                        <div class="flexitem-0"><button ng-show="mayBeEdited(seluser)" type="button" class="btn btn-primary btn-xs" ng-click="changePassword()"><i class="fa fa-credit-card"></i> {{ 'USERS.BUTTON.CHPASSW' | translate }}</button></div>
                    </div>
                    <div class="flexitem-0 ll-title">{{ 'USERS.REALNAME' | translate }}</div>
                    <div class="flexitem-0 ll-value"><i ng-show="mayBeEdited(seluser)" class="fa fa-edit ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.EDITURN' | translate }}" ng-click="editUserRealname()"></i>{{ seluser.realname }}</div>
                    <div class="flexitem-0 ll-title">{{ 'USERS.COMMENT' | translate }}</div>
                    <div class="flexitem-0 ll-value flexwrap"><i ng-show="mayBeEdited(seluser)" class="fa fa-edit ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.EDITUCOMM' | translate }}" ng-click="editUserComment()"></i>{{ seluser.comment }}</div>
                    <div class="flexitem-0 ll-title">{{ 'USERS.USERGROUPS' | translate }}</div>
                    <div class="flexitem-0 ll-value flexwrap" ng-show="seluser.groups.length > 0" ng-repeat="group in seluser.groups"><i ng-show="mayBeEdited(seluser)" class="fa fa-minus-square-o ll-modifyicon text-danger" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.REMOVEGROUP' | translate }}" ng-click="removeGroupFromUser(group)"></i><span class="ll-ptrhand" ng-click="selectGroup(group)">{{ group.name }}</span></div>
                    <div class="flexitem-0 ll-value flexwrap" ng-show="seluser.groups.length > 0 && mayBeEdited(seluser)"><i class="fa fa-plus-square-o ll-modifyicon text-success" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.ADDGROUP' | translate }}" ng-click="addGroupToUser()"></i><span class="ll-newcoll">{{ 'USERS.ADDGROUP' | translate }}</span></div>
                    <div class="flexitem-0 ll-value flexwrap" ng-hide="seluser.groups.length > 0"><i ng-show="mayBeEdited(seluser)" class="fa fa-plus-square-o ll-modifyicon text-success" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.ADDFIRSTGROUP' | translate }}" ng-click="addGroupToUser()"></i><span class="ll-emptycoll">{{ 'USERS.NOGROUPS' | translate }}</span></div>
                </div>

                <div class="flexitem-0 ll-header flexcnt" ng-show="selgroup && selMode === 'group'">
                    <div class="flexitem-1">
                        <span class="ll-desc">{{ 'USERS.GROUP' | translate }}:</span>
                        {{ selgroup.name }}
                        <span class="text-danger" ng-show="selgroup.deleted">
                            <i class="fa fa-exclamation-triangle"></i> {{ 'USERS.TOOLTIP.DISABLED' | translate }}
                        </span>
                    </div>
                    <div class="flexitem-0">
                        <i ng-show="mayBeEdited(selgroup) && hasGrant('GRPCRT') && !selgroup.deleted" class="ll-ptrhand fa fa-ban text-danger" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.REMGRP' | translate }}" ng-click="deleteGroup()"></i>
                    </div>
                    <div ng-show="selgroup.deleted" class="flexitem-0">
                        <i ng-show="mayBeEdited(selgroup) && hasGrant('GRPCRT')" class="ll-ptrhand fa fa-cog" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.REACTGRP' | translate }}" ng-click="reactivateGroup()"></i>
                    </div>
                </div>
                <div class="flexitem-1 ll-body flexvcnt" ng-show="selgroup && selMode === 'group'">
                    <div class="flexitem-0 ll-title">{{ 'USERS.GNAME' | translate }}</div>
                    <div class="flexitem-0 ll-value"><i ng-show="mayBeEdited(selgroup) && !selgroup.deleted" class="fa fa-edit ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.EDITGNAME' | translate }}" ng-click="editGroupName()"></i>{{ selgroup.name }}</div>
                    <div class="flexitem-0 ll-title">{{ 'USERS.LEVEL' | translate }}</div>
                    <div class="flexitem-0 ll-value"><i ng-show="mayBeEdited(selgroup) && hasGrant('GRPCRT') && !selgroup.deleted" class="fa fa-edit ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.EDITGLVL' | translate }}" ng-click="editGroupLevel()"></i>{{ selgroup.level }}</div>
                    <div class="flexitem-0 ll-title">{{ 'USERS.DESC' | translate }}</div>
                    <div class="flexitem-0 ll-value flexwrap"><i ng-show="mayBeEdited(selgroup) && !selgroup.deleted" class="fa fa-edit ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.EDITGDESC' | translate }}" ng-click="editGroupDescription()"></i>{{ selgroup.description }}</div>
                    <div class="flexitem-0 ll-title">{{ 'USERS.GROUPUSERS' | translate }}</div>
                    <div class="flexitem-0 ll-value flexwrap" ng-show="selgroup.users.length > 0" ng-repeat="user in selgroup.users"><i ng-show="mayBeEdited(seluser) && !selgroup.deleted" class="fa fa-minus-square-o ll-modifyicon text-danger" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.REMOVEUSER' | translate }}" ng-click="removeUserFromGroup(user)"></i><span class="ll-ptrhand" ng-click="selectUser(user)">{{ user.username }} ({{ user.realname }})</span></div>
                    <div class="flexitem-0 ll-value flexwrap" ng-show="selgroup.users.length > 0 && mayBeEdited(selgroup)"><i ng-show="!selgroup.deleted" class="fa fa-plus-square-o ll-modifyicon text-success" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.ADDUSER' | translate }}" ng-click="addUserToGroup()"></i><span class="ll-newcoll">{{ 'USERS.ADDUSER' | translate }}</span></div>
                    <div class="flexitem-0 ll-value flexwrap" ng-hide="selgroup.users.length > 0"><i ng-show="mayBeEdited(selgroup) && !selgroup.deleted" class="fa fa-plus-square-o ll-modifyicon text-success" tooltip-popup-delay='1000' tooltip="{{ 'USERS.TOOLTIP.ADDFIRSTUSER' | translate }}" ng-click="addUserToGroup()"></i><span class="ll-emptycoll">{{ 'USERS.NOUSERS' | translate }}</span></div>
                </div>
            </div>
            <div class="flexspace-divv" ng-show="userMgrLoaded"></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-1 flexvcnt ll-list" ng-show="userMgrLoaded">
                <div class="flexitem-0 ll-header flexcnt">
                    <div class="flexitem-1">{{ 'USERS.PRIVILEGES' | translate }}</div>
                </div>
                <div class="flexspace-m"></div>
                <div class="flexitem-1 ll-body">
                    <div class="ll-microheader flexcnt" ng-show="selMode === 'user'">
                        <div class="flexspace-s"></div>
                        <div class="flexitem-1">{{ 'USERS.PRIV.BYGROUP' | translate }}:</div>
                        <div class="flexspace-s"></div>
                    </div>
                    <div class="ll-item flexcnt" ng-show="selMode === 'user'" ng-repeat="priv in seluser.privileges.group">
                        <div class="flexitem-1 ll-grantcode" ng-class="{ 'll-multigrant' : priv.multi }">{{ priv.code }}</div>
                        <div class="flexitem-0 ll-grantname" tooltip-append-to-body=true tooltip-placement="left" tooltip="{{ priv.description | translate }}">{{ priv.name | translate }}</div>
                        <div class="flexspace-s"></div>
                    </div>
                    <div class="ll-microheader flexcnt" ng-show="selMode === 'user'">
                        <div class="flexspace-s"></div>
                        <div class="flexitem-1">{{ 'USERS.PRIV.BYUSER' | translate }}:</div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-fix20 flexcenter ll-actions text-right text-success" ng-show="mayAddGrants(seluser)" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'USERS.TOOLTIP.ADDUPRV' | translate }}" ng-click="addUserPrivilege()"><i class="fa fa-plus-square-o"></i></div>
                        <div class="flexspace-s"></div>
                    </div>
                    <div class="ll-item flexcnt" ng-repeat="priv in seluser.privileges.user" ng-show="selMode === 'user'">
                        <div class="flexitem-1 ll-grantcode" ng-class="{ 'll-multigrant' : priv.multi, 'text-muted': seluser.privileges.redundant[priv.id] }">{{ priv.code }}</div>
                        <div class="flexitem-0 ll-grantname" tooltip-append-to-body=true tooltip-placement="left" tooltip="{{ priv.description | translate }}">{{ priv.name | translate }}</div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-fix20 flexcenter ll-actions text-danger" ng-show="mayDelGrant(seluser,priv)" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'USERS.TOOLTIP.REMUPRV' | translate }}" ng-click="removeUserPrivilege(priv)"><i class="fa fa-minus-square-o"></i></div>
                        <div class="flexspace-s"></div>
                    </div>
                    <div class="ll-microheader flexcnt" ng-show="selMode === 'group'">
                        <div class="flexspace-s"></div>
                        <div class="flexitem-1">{{ 'USERS.PRIV.FORGROUP' | translate }}:</div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-fix20 flexcenter ll-actions text-right text-success" ng-show="mayAddGrants(selgroup) && !selgroup.deleted" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'USERS.TOOLTIP.ADDUPRV' | translate }}" ng-click="addGroupPrivilege()"><i class="fa fa-plus-square-o"></i></div>
                        <div class="flexspace-s"></div>
                    </div>
                    <div class="ll-item flexcnt" ng-repeat="priv in selgroup.privileges" ng-show="selMode === 'group'">
                        <div class="flexitem-1 ll-grantcode" ng-class="{ 'll-multigrant' : priv.multi }">{{ priv.code }}</div>
                        <div class="flexitem-0 ll-grantname" tooltip-append-to-body=true tooltip-placement="left" tooltip="{{ priv.description | translate }}">{{ priv.name | translate }}</div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-fix20 flexcenter ll-actions text-danger" ng-show="mayDelGrant(selgroup,priv) && !selgroup.deleted" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'USERS.TOOLTIP.REMUPRV' | translate }}" ng-click="removeGroupPrivilege(priv)"><i class="fa fa-minus-square-o"></i></div>
                        <div class="flexspace-s"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>