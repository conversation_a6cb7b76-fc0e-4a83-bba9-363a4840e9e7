<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal" draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1, initX: -50, initY: 50 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1">{{ 'STEP.MATRIX.TITLE' | translate }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">

        <div class="flexitem-1 ll-matrix flexcnt">
            <div class="flexitem-fix20"><div class="ll-rotate"><div ng-class="yttlclass + ' ' +editclass('yleft')" ng-click="selectTitle('yleft')">{{ matrixTitle('yleft') }}</div></div></div>
            <div class="flexitem-1 flexvcnt">
                <div class="flexitem-fix20 ll-xtitle" ng-class="editclass('xtop')" ng-click="selectTitle('xtop')">{{ matrixTitle('xtop') }}</div>
                    <div class="flexitem-1 flexcnt">
                        <div class="flexitem-fix20 flexvcnt">
                            <div class="flexitem-fix20"></div>
                            <div class="flexitem-fix40" ng-repeat="row in rows"><div class="ll-rotate"><div class="ll-ystitle" ng-class="editclass('rleft'+row)" ng-click="selectTitle('rleft'+row)">{{ matrixTitle('rleft'+row) }}</div></div></div>
                            <div class="flexitem-fix20"></div>
                        </div>
                        <div class="flexitem-1 flexvcnt" ng-repeat="col in cols">
                            <div class="flexitem-fix20 ll-xstitle" ng-class="editclass('ctop'+col)" ng-click="selectTitle('ctop'+col)">{{ matrixTitle('ctop'+col) }}</div>
                            <div class="flexitem-fix40 flexcenter" ng-repeat="row in rows">
                                <div class="flexitem-fix20 ll-xstitlesml" ng-class="editclass('ftop'+row+col)" ng-click="selectTitle('ftop'+row+col)">{{ matrixTitle('ftop'+row+col) }}</div>
                                <div class="ll-inputboxsml"><input type="text" ng-model="calculation['mx_fplc'+row+col]" class="form-control ll-inputsml"></div>
                            </div>
                            <div class="flexitem-fix20 ll-xstitle" ng-class="editclass('cbottom'+col)" ng-click="selectTitle('cbottom'+col)">{{ matrixTitle('cbottom'+col) }}</div>
                        </div>
                        <div class="flexitem-fix20 flexvcnt">
                            <div class="flexitem-fix20"></div>
                            <div class="flexitem-fix40" ng-repeat="row in rows"><div class="ll-rotate"><div class="ll-ystitle" ng-class="editclass('rright'+row)" ng-click="selectTitle('rright'+row)">{{ matrixTitle('rright'+row) }}</div></div></div>
                            <div class="flexitem-fix20"></div>
                        </div>
                    </div>
                <div class="flexitem-fix20 ll-xtitle" ng-class="editclass('xbottom')" ng-click="selectTitle('xbottom')">{{ matrixTitle('xbottom') }}</div>
            </div>
            <div class="flexitem-fix20"><div class="ll-rotate"><div ng-class="yttlclass + ' ' +editclass('yright')" ng-click="selectTitle('yright')">{{ matrixTitle('yright') }}</div></div></div>
        </div>

        <div class="flexitem-0 ll-mxeedithdr flexwrap">{{ 'STEP.MATRIX.HINT' | translate }}</div>
        <div><input type="text" id="mx_edit" ng-model="calculation['mx_'+editing]" class="form-control ll-mxeditfield"></div>

    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="continue()"><i class="fa fa-caret-left"></i> {{ 'STEP.MATRIX.BUTTON.BACK' | translate }}</button></div>
    </div>
</div>
