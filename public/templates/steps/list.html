<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-1 ll-listpanel flexvcnt ll-fullheight">
            <div class="flexitem-1 flexvcnt" ng-show="stepListLoaded">
                <div class="flexitem-0 ll-header">
                    <div class="flexcnt">
                        <div class="flexitem-1" translate>STEP.STEPS</div>
                        <div class="flexitem-0"><i class="fa fa-upload ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'STEP.LIST.TOOLTIP.UPLOAD' | translate }}" ng-click="toggleUploader()" ng-show="showEditButtons('EDTPSM')"></i></div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-0"><i class="fa fa-copy ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'STEP.LIST.TOOLTIP.CLONE' | translate }}" ng-click="cloneStep()" ng-show="showEditButtons('EDTPSM')"></i></div>
                        <div class="flexspace-s"></div>
                        <div class="flexitem-0"><i class="fa fa-lg fa-plus-square-o ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'STEP.LIST.TOOLTIP.ADDSTEP' | translate }}" ng-click="newStep()" ng-show="showEditButtons('EDTPSM')"></i></div>
                    </div>
                    <div class="ll-stepsubheader flexcnt">
                        <div class="flexitem-1 ll-ptrhand" translate translate-values="{ proc: procedure.code }" tooltip-popup-delay='1000' tooltip="{{ 'STEP.LIST.TOOLTIP.GOTOPROC' | translate }}" ng-click="gotoProcedure()">STEP.FORPROC</div>
                        <div class="flexitem-0 flexbottom ll-stepversion">{{ step.versname.text | translate : step.versname }}</div>
                    </div>
                </div>
                <dropbox data="uploaderinfo"></dropbox>
                <div class="flexitem-0 ll-controls flexcnt">
                    <div class="flexitem-0"><i class="fa fa-search ll-ptrhand" ng-class="{ 'text-success': filters.textfilter.length > 0 }" tooltip-popup-delay='1000' tooltip="{{ 'STEP.LIST.TOOLTIP.CLTF' | translate }}" ng-click="clearTextfilter()"></i></div>
                    <div class="flexspace-s"></div>
                    <input type="text" id="listfilter" ng-model="filters.textfilter" class="flexitem-1 form-control ll-input input-xxs" ng-blur="saveFilterSettings()">
                </div>
                <div class="flexitem-1 ll-body">
                    <div class="ll-item flexvcnt ll-ptrhand" ng-repeat="step in items | filter:filters.textfilter" ng-class="itemSelected(step)" ng-click="goto(step.id)">
                        <div class="flexitem-0 flexcnt">
                            <div class="flexitem-1 ll-title">{{ step.title | translate_model }}</div>
                        </div>
                        <div class="flexitem-0 ll-counter"><span class="ll-code">{{ step.code }}: </span> {{ 'STEP.LIST.BTMROW' | translate : { mcnt: step.mcnt, icnt: step.icnt, dcnt: step.dcnt } }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-3 ll-fullheight"><ui-view/></div>
    </div>
</div>