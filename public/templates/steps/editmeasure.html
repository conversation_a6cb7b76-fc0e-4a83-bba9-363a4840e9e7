<div class="modal-body flexvcnt ll-editpanel ll-editpanel-inmodal"
     draggabilly="{ containment: 'body', handle: '.ll-header', parent: 1 }">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{ editor.title }}" translate-values="editor.titlevals"></div>
        <div class="flexitem-0 ll-subheader flexbottom">{{editor.subtitle.text | translate}} {{ editor.subtitle.vers }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt" ng-show="stepMEditLoaded">
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MEASURE.EDIT.CODE' | translate }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-2">
                    <input type="text" id="m_code" ng-model="item.code" class="form-control ll-input flexitem-2"
                           ng-disabled="true" autofocus>
                </div>
                <div class="ll-measuresubkey"
                     ng-show="mtinfo.link == 'bycode'">{{ 'MEASURE.EDIT.COMPLCODE' | translate }}</div>
                <div class="flexitem-2" ng-show="mtinfo.link == 'bycode'">
                    <input type="text" id="m_complcode" ng-model="item.calculation.complcode"
                           class="form-control ll-input" ng-disabled="isVersion">
                </div>
                <div class="flexspace-m"></div>
                <div class="ll-measuresubkey" ng-show="item.calculation.internal == 1">{{ 'MEASURE.EDIT.INTERNAL' | translate }}</div>
                <div class="ll-measuresubkey" ng-show="item.calculation.internal != 1">{{ 'MEASURE.EDIT.INTERNALEXTERNAL' | translate }}</div>
                <button type="button" class="btn"
                        ng-class="{ 'btn-primary': item.calculation.internal == 1, 'btn-default': item.calculation.internal != 1 }"
                        ng-model="item.calculation.internal" btn-checkbox btn-checkbox-true="1" btn-checkbox-false="0"
                        ng-disabled="isVersion">
                    <i ng-class="{ 'fa-check': item.calculation.internal == 1, 'fa-remove': item.calculation.internal != 1}"
                       class="fa fa-fw"></i></button>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MEASURE.EDIT.TITLE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="m_title" ng-model="item.title[languageinfo.selected]"
                       class="form-control ll-input" ui-keyup="langToggleKeymap">
                <lang-completion-info langinfo="languageinfo" model="item.title"></lang-completion-info>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MEASURE.EDIT.HINTS' | translate }}</div>

            <div class="ll-edit flexitem-0">
                <text-angular ng-model="item.description[languageinfo.selected]" id="md_desc" name="md_desc" disabled-ui-keyup="langToggleKeymap"></text-angular>
                <lang-completion-info langinfo="languageinfo" model="item.description" focus-ta-after-change="md_desc"></lang-completion-info>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MEASURE.EDIT.MTYPE' | translate }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-1"><select id="m_mtype" class="form-control ll-input" ng-model="item.measuretype"
                                                ng-change="prepareCalcArea()"
                                                ng-options="item.id as item.name | translate for item in measuretypes"
                                                ng-disabled="isVersion"></select>
                </div>
                <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.MANDATORY' | translate }}</div>
                <div class="flexitem-0 btn-group">
                    <label class="btn btn-default" ng-model="item.calculation.optional" btn-radio="1" ng-disabled="isVersion" uncheckable>
                        {{ 'MEASURE.EDIT.NO' | translate }}
                        <i ng-show="item.calculation.optional == 1" class="fa fa-check"></i>
                    </label>
                    <label class="btn btn-default" ng-model="item.calculation.optional" btn-radio="0" ng-disabled="isVersion" uncheckable>
                        {{ 'MEASURE.EDIT.YES' | translate }}
                        <i ng-show="item.calculation.optional == 0" class="fa fa-check"></i>
                    </label>
                </div>

            </div>
        </div>

        <div ng-show="mtinfo.type == 'thold'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.TARGET' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-2"><input type="number" id="m_t1_targv" ng-model="item.calculation.t1_targetv"
                                                   class="form-control ll-inputlg ll-justright"
                                                   ng-disabled="isVersion"></div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.UNIT' | translate }}</div>
                    <div class="flexitem-1">
                        <input type="text" id="m_t1_targu" ng-model="item.calculation.t1_targetu"
                               class="form-control ll-inputlg ll-justright"
                               typeahead="preval for preval in getUnitTypeahead() | filter:$viewValue:emptyOrMatch"
                               typeahead-focus class="s-textfield form-control"
                               ng-blur="addUnitTypeahead(item.calculation.t1_targetu)"
                               ng-disabled="isVersion">
                    </div>
                </div>
            </div>
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.THOLD' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-2"><input type="number" id="m_t1_tholdv" ng-model="item.calculation.t1_tholdv"
                                                   class="form-control ll-inputlg ll-justright"
                                                   ng-disabled="isVersion"></div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.UNIT' | translate }}</div>
                    <div class="flexitem-1">
                        <input type="text" id="m_t1_tholdu" ng-model="item.calculation.t1_tholdu"
                               class="form-control ll-inputlg ll-justright"
                               typeahead="preval for preval in getUnitTypeahead() | filter:$viewValue:emptyOrMatch"
                               typeahead-focus class="s-textfield form-control"
                               ng-blur="addUnitTypeahead(item.calculation.t1_tholdu)"
                               ng-disabled="isVersion">
                    </div>
                </div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'absrng'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.MIN' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-2"><input type="number" id="m_t2_minv" ng-model="item.calculation.t2_minv"
                                                   class="form-control ll-inputlg ll-justright"
                                                   ng-disabled="isVersion"></div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.UNIT' | translate }}</div>
                    <div class="flexitem-1">
                        <input type="text" id="m_t2_unit1" ng-model="item.calculation.t2_unit"
                               class="form-control ll-inputlg ll-justright"
                               typeahead="preval for preval in getUnitTypeahead() | filter:$viewValue:emptyOrMatch"
                               typeahead-focus class="s-textfield form-control"
                               ng-blur="addUnitTypeahead(item.calculation.t2_unit)"
                               ng-disabled="isVersion">
                    </div>
                </div>
            </div>
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.MAX' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-2"><input type="number" id="m_t2_maxv" ng-model="item.calculation.t2_maxv"
                                                   class="form-control ll-inputlg ll-justright"
                                                   ng-disabled="isVersion"></div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.UNIT' | translate }}</div>
                    <div class="flexitem-1">
                        <input type="text" id="m_t2_unit2" ng-model="item.calculation.t2_unit"
                               class="form-control ll-inputlg ll-justright"
                               typeahead="preval for preval in getUnitTypeahead() | filter:$viewValue:emptyOrMatch"
                               typeahead-focus class="s-textfield form-control"
                               ng-blur="addUnitTypeahead(item.calculation.t2_unit)"
                               ng-disabled="isVersion">
                    </div>
                </div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'abs'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.TARGET' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-2"><input type="number" id="m_t8_value" ng-model="item.calculation.t8_value"
                                                   class="form-control ll-inputlg ll-justright"
                                                   ng-disabled="isVersion"></div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.UNIT' | translate }}</div>
                    <div class="flexitem-1">
                        <input type="text" id="m_t8_unit" ng-model="item.calculation.t8_unit"
                               class="form-control ll-inputlg ll-justright"
                               typeahead="preval for preval in getUnitTypeahead() | filter:$viewValue:emptyOrMatch"
                               typeahead-focus class="s-textfield form-control"
                               ng-blur="addUnitTypeahead(item.calculation.t8_unit)"
                               ng-disabled="isVersion">
                    </div>
                </div>
            </div>
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.COMPARATOR' | translate }}</div>
                <div class="ll-edit btn-group">
                    <label class="btn btn-primary" ng-model="item.calculation.t8_comparator" btn-radio="1" ng-disabled="isVersion" uncheckable>
                        &lt; <i ng-show="item.calculation.t8_comparator == 1" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t8_comparator" btn-radio="2" ng-disabled="isVersion" uncheckable>≤
                        <i ng-show="item.calculation.t8_comparator == 2" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t8_comparator" btn-radio="3" ng-disabled="isVersion" uncheckable>=
                        <i ng-show="item.calculation.t8_comparator == 3" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t8_comparator" btn-radio="4" ng-disabled="isVersion" uncheckable>≥
                        <i ng-show="item.calculation.t8_comparator == 4" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t8_comparator" btn-radio="5" ng-disabled="isVersion" uncheckable>
                        &gt; <i ng-show="item.calculation.t8_comparator == 5" class="fa fa-check"></i></label>
                </div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'reschk'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.TARGET' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-2"><input type="number" id="m_t10_value" ng-model="item.calculation.t10_value"
                                                   class="form-control ll-inputlg ll-justright"
                                                   ng-disabled="isVersion"></div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.UNIT' | translate }}</div>
                    <div class="flexitem-1">
                        <input type="text" id="m_t10_unit" ng-model="item.calculation.t10_unit"
                               class="form-control ll-inputlg ll-justright"
                               typeahead="preval for preval in getUnitTypeahead() | filter:$viewValue:emptyOrMatch"
                               typeahead-focus class="s-textfield form-control"
                               ng-blur="addUnitTypeahead(item.calculation.t10_unit)"
                               ng-disabled="isVersion">
                    </div>
                </div>
            </div>
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.COMPARATOR' | translate }}</div>
                <div class="ll-edit btn-group">
                    <label class="btn btn-primary" ng-model="item.calculation.t10_comparator" btn-radio="1" ng-disabled="isVersion" uncheckable>
                        &lt; <i ng-show="item.calculation.t10_comparator == 1" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t10_comparator" btn-radio="2" ng-disabled="isVersion" uncheckable>≤
                        <i ng-show="item.calculation.t10_comparator == 2" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t10_comparator" btn-radio="3" ng-disabled="isVersion" uncheckable>=
                        <i ng-show="item.calculation.t10_comparator == 3" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t10_comparator" btn-radio="4" ng-disabled="isVersion" uncheckable>≥
                        <i ng-show="item.calculation.t10_comparator == 4" class="fa fa-check"></i></label>
                    <label class="btn btn-primary" ng-model="item.calculation.t10_comparator" btn-radio="5" ng-disabled="isVersion" uncheckable>
                        &gt; <i ng-show="item.calculation.t10_comparator == 5" class="fa fa-check"></i></label>
                </div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'statst'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.UNIT' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-1">
                        <input type="text" id="m_t11_unit" ng-model="item.calculation.t11_unit"
                               class="form-control ll-inputlg ll-justright"
                               typeahead="preval for preval in getUnitTypeahead() | filter:$viewValue:emptyOrMatch"
                               typeahead-focus class="s-textfield form-control"
                               ng-blur="addUnitTypeahead(item.calculation.t11_unit)"
                               ng-disabled="isVersion">
                    </div>
                    <div class="flexitem-2"></div>
                </div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'frtext'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.MINLEN' | translate }}</div>
                <div class="ll-edit">
                    <div class="input-group ll-measureplusminus">
                        <span class="input-group-btn"><button class="btn btn-default" type="button"
                                                              ng-click="incrvalue('t3_minlen',20,null)"
                                                              ng-disabled="isVersion"><i
                                class="fa fa-plus"></i></button></span>
                        <input type="text" ng-model="item.calculation.t3_minlen"
                               class="form-control ll-inputlg ll-measurepmtext" disabled="true">
                        <span class="input-group-btn"><button class="btn btn-default" type="button"
                                                              ng-click="decrvalue('t3_minlen',0,null)"
                                                              ng-disabled="isVersion"><i
                                class="fa fa-minus"></i></button></span>
                    </div>
                </div>
                <div class="flexitem-2"></div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'regexp'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.REGEXP' | translate }}</div>
                <div class="ll-edit"><input type="text" id="m_t4_regexp" ng-model="item.calculation.t4_regexp"
                                            class="form-control ll-inputlg"
                                            ng-disabled="isVersion"></div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'flag'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.EXP' | translate }}</div>
                <div class="ll-edit btn-group">
                    <label class="btn btn-default" ng-model="item.calculation.t5_expected" btn-radio="1"
                           ng-disabled="isVersion" uncheckable>{{ 'MEASURE.EDIT.YES' | translate }} <i
                            ng-show="item.calculation.t5_expected == 1" class="fa fa-check"></i></label>
                    <label class="btn btn-default" ng-model="item.calculation.t5_expected" btn-radio="0"
                           ng-disabled="isVersion" uncheckable>{{ 'MEASURE.EDIT.NO' | translate }} <i
                            ng-show="item.calculation.t5_expected == 0" class="fa fa-check"></i></label>
                    <label class="btn btn-default" ng-model="item.calculation.t5_expected" btn-radio="2"
                           ng-disabled="isVersion" uncheckable>{{ 'MEASURE.EDIT.ANY' | translate }} <i
                            ng-show="item.calculation.t5_expected == 2" class="fa fa-check"></i></label>
                </div>
            </div>
        </div>

        <div ng-show="mtinfo.input == 'matrix'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.MATRIX' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="flexitem-1">
                        <button type="button" class="btn btn-primary" ng-click="setupMatrix()" ng-disabled="isVersion"><i
                                class="fa fa-table"></i> {{ 'MEASURE.EDIT.BUTTON.SETUP' | translate }}</button>
                    </div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.NUMCOL' | translate }}</div>
                    <div class="flexitem-1">
                        <div class="input-group ll-measureplusminus">
                            <span class="input-group-btn"><button class="btn btn-default" type="button"
                                                                  ng-click="incrvalue('mx_xsize',9)"
                                                                  ng-disabled="isVersion"><i
                                    class="fa fa-plus"></i></button></span>
                            <input type="text" ng-model="item.calculation.mx_xsize"
                                   class="form-control ll-inputlg ll-measurepmtext" disabled="true">
                            <span class="input-group-btn"><button class="btn btn-default" type="button"
                                                                  ng-click="decrvalue('mx_xsize',1)"
                                                                  ng-disabled="isVersion"><i
                                    class="fa fa-minus"></i></button></span>
                        </div>
                    </div>
                    <div class="ll-measuresubkey">{{ 'MEASURE.EDIT.NUMROW' | translate }}</div>
                    <div class="flexitem-1">
                        <div class="input-group ll-measureplusminus">
                            <span class="input-group-btn"><button class="btn btn-default" type="button"
                                                                  ng-click="incrvalue('mx_ysize',9)"
                                                                  ng-disabled="isVersion"><i
                                    class="fa fa-plus"></i></button></span>
                            <input type="text" ng-model="item.calculation.mx_ysize"
                                   class="form-control ll-inputlg ll-measurepmtext" disabled="true">
                            <span class="input-group-btn"><button class="btn btn-default" type="button"
                                                                  ng-click="decrvalue('mx_ysize',1)"
                                                                  ng-disabled="isVersion"><i
                                    class="fa fa-minus"></i></button></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.FORMULA' | translate }}</div>
                <div class="ll-edit"><input type="text" id="m_mx_formula" ng-model="item.calculation.mx_formula"
                                            class="form-control ll-input" ng-disabled="isVersion"></div>
            </div>
        </div>

        <div ng-show="mtinfo.type == 'timerq' || mtinfo.type == 'timerc'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.COMPARATOR' | translate }}</div>
                <div class="ll-edit flexcnt">
                    <div class="btn-group flexitem-1">
                        <label class="btn btn-primary" ng-model="item.calculation.t15_comparator" btn-radio="1"
                               ng-disabled="isVersion" uncheckable>&lt; <i ng-show="item.calculation.t15_comparator == 1"
                                                   class="fa fa-check"></i></label>
                        <label class="btn btn-primary" ng-model="item.calculation.t15_comparator" btn-radio="5"
                               ng-disabled="isVersion" uncheckable>&gt; <i ng-show="item.calculation.t15_comparator == 5"
                                                   class="fa fa-check"></i></label>
                    </div>
                    <div class="ll-measuresubkey flexitem-0">{{ 'MEASURE.EDIT.TARGET' | translate }}</div>
                    <div class="flexitem-2"><input type="number" id="m_t15_value" ng-model="item.calculation.t15_value"
                                                   class="form-control ll-inputlg ll-justright"
                                                   ng-disabled="isVersion"></div>
                    <div class="ll-measuresubkey flexitem-0">min</div>
                </div>
            </div>
        </div>


        <div ng-show="mtinfo.type == 'choice'">
            <div class="flexitem-0 ll-block">
                <div class="ll-key">{{ 'MEASURE.EDIT.CHOICE' | translate }}</div>
                <div class="ll-edit">
                    <div class="input-group ll-measureplusminus">
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button"
                                    ng-click="incrvalue('t17_len', 15, null); recalculateChoiceList()"
                                    ng-disabled="isVersion"><i
                                    class="fa fa-plus"></i></button>
                        </span>
                        <input type="text" ng-model="item.calculation.t17_len"
                               class="form-control ll-inputlg ll-measurepmtext" disabled="true">
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button"
                                    ng-click="decrvalue('t17_len', 1, null); recalculateChoiceList()"
                                    ng-disabled="isVersion">
                                <i class="fa fa-minus"></i></button>
                        </span>
                    </div>
                </div>
                <div class="flexitem-2"></div>
            </div>

            <div class="flexitem-0 ll-block" ng-repeat="field in item.calculation.t17_list track by $index">
                <div class="ll-key">{{ 'MEASURE.EDIT.CHOICEVAL' | translate : { val: $index+1 } }}</div>
                <div class="ll-edit">
                    <input type="text" id="m_t17_choice" ng-model="item.calculation.t17_list[+$index]"
                           class="form-control ll-inputlg" maxlength="50" ng-disabled="isVersion"></div>
            </div>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'MEASURE.EDIT.TTYPE' | translate }}</div>
            <div class="ll-edit flexcnt">
                <div class="flexitem-1"><select id="m_ttype" class="form-control ll-input" ng-model="item.tooltype_id"
                                                ng-options="item.id as tooltypeCboxName(item) for item in tooltypes"
                                                ng-disabled="isVersion"></select>
                </div>
                <div class="ll-measuresubkey"
                     ng-show="mtinfo.input == 'numeric' || mtinfo.input == 'matrix'">{{ 'MEASURE.EDIT.FLOATFRM.TEXT' | translate }}</div>
                <div class="flexitem-0" ng-show="mtinfo.input == 'numeric' || mtinfo.input == 'matrix'"><select
                        id="m_numform" class="form-control ll-input" ng-model="item.metadata.floatformat"
                        ng-options="format.id as format.name | translate for format in floatformats"
                        ng-disabled="isVersion"></select></div>
            </div>
        </div>

    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt" ng-show="stepMEditLoaded">
        <div class="btn-group flexitem-0">
            <label class="btn btn-primary" ng-repeat="lang in languageinfo.languages" ng-model="languageinfo.selected"
                   btn-radio="lang.code">{{ lang.name }}</label>
        </div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0">
            <button type="button" class="btn btn-primary" ng-click="check()"><i
                    class="fa fa-eye"></i> {{ 'MEASURE.EDIT.BUTTON.TEST' | translate }}</button>
        </div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0">
            <button type="button" class="btn btn-danger" ng-click="cancel()"><i
                    class="fa fa-ban"></i> {{ 'MEASURE.EDIT.BUTTON.CANCEL' | translate }}</button>
        </div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0">
            <button type="button" class="btn btn-success" ng-click="continue()"><i
                    class="fa fa-save"></i> {{ 'MEASURE.EDIT.BUTTON.CLOSE' | translate }}</button>
        </div>
    </div>
</div>
