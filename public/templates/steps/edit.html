<div class="flexvcnt ll-editpanel" ng-show="stepEditLoaded">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{editor.title}}" translate-values="editor.titlevals"></div>
        <div class="flexitem-0 ll-subheader flexbottom">{{editor.subtitle.text | translate}} {{ editor.subtitle.vers }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'STEP.EDIT.CODE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="st_code" ng-model="item.code" class="form-control ll-input" ng-disabled="true">
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'STEP.EDIT.TITLE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="st_title" ng-model="item.title[languageinfo.selected]" class="form-control ll-input " ui-keyup="langToggleKeymap">
                <lang-completion-info langinfo="languageinfo" model="item.title" focus-after-change="#st_title"></lang-completion-info>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'STEP.EDIT.INSTRUCTION' | translate }}</div>
            <div class="ll-edit">

                <!-- checkbox for type 'instruction' and tooltip -->
                <div style="width: 50px" tooltip-popup-delay='1000' tooltip="{{ 'STEP.EDIT.TOOLTIP.INSTRUCTION' | translate }}">
                    <button type="button" class="btn" ng-class="{ 'btn-primary': item.steptype == 1, 'btn-default': item.steptype != 1 }" ng-model="item.steptype"
                            btn-checkbox btn-checkbox-true="1" btn-checkbox-false="0" ng-disabled="item.steptype == 0 && item.measurecnt > 0">
                        <i ng-class="{ 'fa-check': item.steptype == 1, 'fa-remove': item.steptype != 1}" class="fa fa-fw"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'STEP.EDIT.DESC' | translate }}</div>
            <div class="ll-edit">
                <text-angular ng-model="item.description[languageinfo.selected]" id="st_desc" name="st_desc" disabled-ui-keyup="langToggleKeymap"></text-angular>
                <lang-completion-info langinfo="languageinfo" model="item.description" focus-ta-after-change="st_desc"></lang-completion-info>
            </div>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="btn-group flexitem-0">
            <label class="btn btn-primary" ng-repeat="lang in languageinfo.languages" ng-model="languageinfo.selected" btn-radio="lang.code">{{ lang.name }}</label>
        </div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancelEdit()"><i class="fa fa-ban"></i> {{ 'STEP.EDIT.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="saveAndReturn()"><i class="fa fa-save"></i> {{ 'STEP.EDIT.BUTTON.SAVE' | translate }}</button></div>
    </div>
</div>
