<div class="flexcnt ll-viewpanel">
    <div class="flexitem-1 flexvcnt ll-info" ng-show="stepViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ item.title | translate_model }}</div>
            <div class="flexitem-0 ll-code flexbottom">{{ item.code }}</div>
            <comm-report class="flexitem-0" style="margin-left:5px" type="step" obj-id="{{ item.id }}"></comm-report>
        </div>
        <div class="flexitem-0 ll-version">{{ 'STEP.VIEW.VERSION' | translate }}: {{ item.versname.text | translate : item.versname }}</div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body flexvcnt">
            <div class="flexitem-0 ll-title">{{ 'STEP.VIEW.CODE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.code }}</div>
            <div class="flexitem-0 ll-title">{{ 'STEP.VIEW.TITLE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.title | translate_model }}</div>
            <div class="flexitem-0 ll-title">{{ 'STEP.VIEW.DESC' | translate }}</div>
            <div class="flexitem-0 ll-value flexwrap" ng-bind-html="item.description | translate_model"></div>
            <div class="flexitem-0 ll-title">{{ 'STEP.VIEW.MEDIA' | translate }}</div>
            <div class="flexitem-0 ll-value flexcnt">
                <div class="flexitem-1">{{ 'STEP.VIEW.IMREG' | translate : { imgnum: item.images.length } }}<br>{{ 'STEP.VIEW.DOCREG' | translate : { docnum: item.documents.length } }}</div>
                <div class="flexitem-0"><button type="button" class="btn btn-default" ng-show="showEditButtons('EDTPSM')" ng-click="mediamanager()"><i class="fa fa-image"></i> {{ 'STEP.VIEW.BUTTON.MEDMGR' | translate }}</button></div>
            </div>
            <div class="flexitem-0 ll-title">{{ 'STEP.VIEW.RULES' | translate }}</div>
            <div class="flexitem-0 ll-value flexcnt">
                <div class="flexitem-1 flexvcnt"><div ng-repeat="mod in wflowRulesStep" class="flexitem-0" translate translate-values="mod[1]">{{ mod[0] }}</div></div>
                <div class="flexitem-0"><button type="button" class="btn btn-default" ng-show="showEditButtons('EDTPSM')" ng-click="wfRuleManagerStep()"><i class="fa fa-code-fork"></i> {{ 'STEP.VIEW.BUTTON.REDITOR' | translate }}</button></div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-buttonbar flexcnt-r">
            <div class="flexitem-0"><button type="button" class="btn btn-primary" ng-click="exportItem($event)"><i class="fa fa-download"></i> {{ 'STEP.VIEW.BUTTON.EXPORT' | translate }}</button></div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0"><button type="button" ng-show="showEditButtons('EDTPSM')"  class="btn btn-danger" ng-click="deleteItem()"><i class="fa fa-ban"></i> {{ 'STEP.VIEW.BUTTON.DELETE' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button type="button" ng-show="showEditButtons('EDTPSM')" class="btn btn-primary" ng-click="editItem()"><i class="fa fa-edit"></i> {{ 'STEP.VIEW.BUTTON.EDIT' | translate }}</button></div>
        </div>
    </div>
    <div class="flexspace-divv" ng-show="stepViewLoaded"></div>
    <div class="flexspace-s"></div>
    <div class="flexitem-1 flexvcnt ll-list" ng-show="stepViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1 ll-ptrhand" ng-click="gotoSteps()">{{ 'STEP.VIEW.MEASURES' | translate }}</div>
            <div class="flexitem-0 ll-ptrhand" ng-show="showActivateReorderIcon('EDTPSM') && item.steptype != 1" tooltip-popup-delay='1000' tooltip="{{ 'STEP.VIEW.TOOLTIP.ACTREORD' | translate }}" ng-click="activateReordering(true)"><i class="fa fa-sort"></i></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0">
                <i class="fa fa-upload ll-ptrhand" ng-show="showEditButtons('EDTPSM') && item.steptype != 1"
                   tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.UPLMEAS' | translate }}"
                   ng-click="toggleUploader()" ng-disabled="item.steptype == 1"></i>
            </div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0">
                <i class="fa fa-copy ll-ptrhand" ng-show="showEditButtons('EDTPSM') && item.steptype != 1"
                   tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.CLNMEAS' | translate }}"
                   ng-click="cloneMeasure()"></i>
            </div>
            <div class="flexspace-s" ng-show="showEditButtons('EDTPSM') && item.steptype != 1"></div>
            <div class="flexitem-0 ll-ptrhand">
                <i class="fa fa-lg fa-plus-square-o" ng-show="showEditButtons('EDTPSM') && item.steptype != 1"
                   tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.NEWMEAS' | translate }}"
                   ng-click="newMeasure()"></i>
            </div>
        </div>
        <dropbox data="uploaderinfo"></dropbox>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body" ui-sortable="sublistSortOptions" ng-model="item.measures">
            <div class="ll-item flexcnt" ng-repeat="measure in item.measures">
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-0 ll-title ll-ptrhand" ng_show="showEditButtons('EDTPSM')" ng-click="editMeasure(measure)">{{ measure.title | translate_model }}</div>
                    <div class="flexitem-0 ll-title" ng_hide="showEditButtons('EDTPSM')">{{ measure.title | translate_model }}</div>
                    <div class="flexitem-0 ll-counter"><span class="ll-code">{{ measure.code }}: </span>{{measure.typedesc | translate }} <i class="fa fa-info-circle text-danger" ng-show="measure.calculation.internal == 1"></i></div>
                </div>
                <div class="flexspace-m"></div>
                <div class="ll-dragaction text-primary" ng_show="showReorderIcon('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.REORDER' | translate }}"><i class="fa fa-sort"></i></div>
                <div class="ll-actions text-muted" ng_show="showEditButtons('EDTPSM') && $index == 0" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCETOP' | translate }}"><i class="fa fa-fw fa-circle-o"></i></div>
                <div class="ll-actions text-success" ng_show="showEditButtons('EDTPSM') && $index > 0 && measure.enforce == PSM_ENFORCE_NONE" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE0' | translate }}" ng-click="setEnforceLevel(measure,1)"><i class="fa fa-fw fa-circle-o"></i></div>
                <div class="ll-actions text-danger" ng_show="showEditButtons('EDTPSM') && $index > 0 && measure.enforce == PSM_ENFORCE_LASTONE" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE1' | translate }}" ng-click="setEnforceLevel(measure,2)"><i class="fa fa-fw fa-dot-circle-o"></i></div>
                <div class="ll-actions text-danger" ng_show="showEditButtons('EDTPSM') && $index > 0 && measure.enforce == PSM_ENFORCE_ALLPRIOR" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE2' | translate }}" ng-click="setEnforceLevel(measure,0)"><i class="fa fa-fw fa-circle"></i></div>
                <div class="ll-actions text-primary" ng_show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.EDITRULE' | translate }}" ng-click="wfRuleManagerMeasure(measure)"><i class="fa fa-code-fork"></i><span class="ll-subscript">{{ measure.flowcontrol.length }}</span></div>
                <div class="ll-actions text-primary" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.EXPMEAS' | translate }}" ng-click="exportItem($event, 'measure', measure)"><i class="fa fa-download"></i></div>
                <div class="ll-actions text-primary" ng_show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.EDITMEAS' | translate }}" ng-click="editMeasure(measure)"><i class="fa fa-edit"></i></div>
                <div class="ll-actions text-primary" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.TESTMEAS' | translate }}" ng-click="testMeasure(measure)"><i class="fa fa-eye"></i></div>
                <div class="ll-actions text-danger" ng_show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.REMMEAS' | translate }}" ng-click="deleteMeasure(measure)"><i class="fa fa-trash-o"></i></div>
            </div>
        </div>
    </div>
</div>
