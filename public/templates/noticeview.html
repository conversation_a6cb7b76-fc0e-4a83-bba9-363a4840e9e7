<div class="ll-mainview">
    <div class="ll-mainpane ll-fullwidth ll-noticeboard flexvcnt">
        <div class="ll-nbbuttons flexitem-0 flexcnt">
            <div class="ll-nbaction flexitem-0 ll-ptrhand" ng-click="editCategories()"><i class="fa fa-reorder"></i> <span translate>NOTICES.BUTTON.CATEGORIES</span></div>
            <div class="ll-nbaction flexitem-0 ll-ptrhand" ng-click="editTemplates()"><i class="fa fa-newspaper-o"></i> <span translate>NOTICES.BUTTON.TEMPLATES</span></div>
            <div class="flexitem-1"></div>
            <div class="ll-nbaction flexitem-0 ll-ptrhand" ng-click="export()"><i class="fa fa-download"></i> <span translate>NOTICES.BUTTON.EXPORT</span></div>
        </div>
        <div class="ll-nbfilter flexitem-0 flexcnt">
            <div class="flexitem-1 ll-nbfelem flexcnt" ng-repeat="filter in filterlist">
                <div class="flexitem-1 ll-nbftitle">{{ 'NOTICES.VIEW.'+filter.toUpperCase() | translate }}</div>
                <div class="flexitem-0 ll-nbficons ll-ptrhand">
                    <i ng-if="filters[filter].show == 0" class="fa fa-eye" tooltip-popup-delay='1000' tooltip="{{ 'NOTICES.TOOLTIP.SHOW' | translate }}" ng-click="toggleShow(filter)"></i>
                    <i ng-if="filters[filter].show == 1" class="text-primary fa fa-eye" tooltip-popup-delay='1000' tooltip="{{ 'NOTICES.TOOLTIP.SHOWALL' | translate }}" ng-click="toggleShow(filter)"></i>
                    <i ng-if="filters[filter].show == 2" class="fa fa-eye-slash" tooltip-popup-delay='1000' tooltip="{{ 'NOTICES.TOOLTIP.HIDE' | translate }}" ng-click="toggleShow(filter)"></i>
                    <i ng-if="!filters[filter].filter" class="fa fa-filter" tooltip-popup-delay='1000' tooltip="{{ 'NOTICES.TOOLTIP.FILTER' | translate }}" ng-click="toggleFilter(filter)"></i>
                    <i ng-if="filters[filter].filter" class="text-primary fa fa-filter" tooltip-popup-delay='1000' tooltip="{{ 'NOTICES.TOOLTIP.NOFILTER' | translate }}" ng-click="toggleFilter(filter)"></i>
                </div>
            </div>
        </div>
        <div class="ll-nblist flexitem-1 ll-scroll">
            <div class="flexvcnt">
                <div class="ll-nbitem flexitem-0 flexcnt ll-ptrhand" ng-repeat="notice in notices" ng-class="{'ll-nbodditem': $odd}" ng-click="manageNotice(notice)">
                    <div class="flexitem-fix30 ll-nbcell ll-nbid">{{ notice.id }}</div>
                    <div class="flexitem-2 ll-nbcell flexvcnt">
                        <div class="flexitem-0" ng-if="filters.category.show > 0">
                            <span class="ll-nbelhdr">{{ 'NOTICES.VIEW.CATEGORY' | translate }}:</span>
                            <span class="ll-nbcat"><b>{{ notice.category.text }}</b></span>
                        </div>
                        <div class="flexitem-0" ng-if="filters.path.show > 0">
                            <span class="ll-nbelhdr">{{ 'NOTICES.VIEW.LOCATION' | translate }}:</span>
                            <span class="ll-nbpath">
                                <span ng-repeat="segment in notice.pathstrings"><span translate-compile translate="{{segment.text}}" translate-values="segment"></span><span ng-if="!$last">, </span></span>
                            </span>
                        </div>
                        <div class="flexitem-0 ll-nbtext flexwrap" ng-if="filters.text.show">{{ notice.text }}</div>
                    </div>
                    <div class="flexitem-1 ll-nbcell flexvcnt">
                        <div class="flexitem-0" ng-if="filters.article.show > 0">
                            <span class="ll-nbelhdr">{{ 'NOTICES.VIEW.ARTICLE' | translate }}:</span>
                            <span class="ll-nbart">
                                <span ng-if="notice.articlestring !== ''" ng-bind-html="notice.articlestring"></span>
                                <b ng-if="notice.articlestring === ''" translate="NOTICES.VIEW.NOTEXT"></b>
                            </span>
                        </div>
                        <div class="flexitem-0" ng-if="filters.timeloss.show > 0">
                            <span class="ll-nbelhdr">{{ 'NOTICES.VIEW.TIMELOSS' | translate }}:</span>
                            <span class="ll-nbtloss">
                                <b>{{ notice.timelossstring | translate }}</b>
                            </span>
                        </div>
                        <div class="flexitem-0 ll-nbstatus" ng-repeat="sstring in notice.statusstrings" translate-compile translate="{{sstring.text}}" translate-values="sstring" ng-if="filters.status.show > 0 && ($last || filters.status.show == 2)"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

