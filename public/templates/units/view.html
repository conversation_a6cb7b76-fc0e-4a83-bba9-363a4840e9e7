<div class="flexcnt ll-viewpanel">
    <div class="flexitem-1 flexvcnt ll-info" ng-show="unitViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ item.code }}</div>
            <div class="flexitem-0 ll-code flexbottom">{{ item.customer }}</div>
            <comm-report class="flexitem-0" style="margin-left:5px" type="unitfull" obj-id="{{ item.id }}"></comm-report>
        </div>
        <div class="flexitem-0 ll-version">
            {{ 'UNIT.VIEW.STATUS.TTL' | translate }}:
            <i class="fa fa-exchange ll-ptrhand text-primary" ng-show="hasGrant('EDTUNT') && hasGrant('WFLMNG') && statusChangeable && item.status < 100" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.UNITSTAT' | translate }}" ng-click="setUnitStatus()"></i>
            <i class="fa fa-reply ll-ptrhand text-primary" ng-show="hasGrant('EDTUNT') && hasGrant('WFLMNG') && item.status >= 100" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.UNARCH' | translate }}" ng-click="unarchive()"></i>
            {{ getUnitStatus() | translate }}
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body flexvcnt">
            <div class="flexitem-0 ll-title">{{ 'UNIT.VIEW.MODEL' | translate }}</div>
            <div class="flexitem-0 ll-value ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.GOTOMOD' | translate }}" ng-click="gotoModel(item.model)">{{ item.model.code }}: {{ item.model.title | translate_model }} ({{item.model.versname.text | translate : item.model.versname}})</div>
            <div class="flexitem-0 ll-title">{{ 'UNIT.VIEW.CODE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.code }}</div>
            <div class="flexitem-0 ll-title">{{ 'UNIT.VIEW.CUST' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.customer }}</div>
            <div class="flexitem-0 ll-title">{{ 'UNIT.VIEW.DATES' | translate }}</div>
            <div class="flexitem-0 ll-value" ng-show="item.commissioned"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateCommissioned()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.COMM.ON' | translate }} {{ item.commissioned | date:i18n.selectedLanguage.dformat }}</div>
            <div class="flexitem-0 ll-value" ng-hide="item.commissioned"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateCommissioned()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.COMM.NOT' | translate }}</div>
            <div class="flexitem-0 ll-value" ng-show="item.finished"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateFinished()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.FIN.ON' | translate }} {{ item.finished | date:i18n.selectedLanguage.dformat }}</div>
            <div class="flexitem-0 ll-value" ng-hide="item.finished"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateFinished()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.FIN.NOT' | translate }}</div>
            <div class="flexitem-0 ll-value" ng-show="item.delivered"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateDelivered()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.DEL.ON' | translate }} {{ item.delivered | date:i18n.selectedLanguage.dformat }}</div>
            <div class="flexitem-0 ll-value" ng-hide="item.delivered"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateDelivered()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.DEL.NOT' | translate }}</div>
            <div class="flexitem-0 ll-value" ng-show="item.approved"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateApproved()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.APPR.ON' | translate }} {{ item.approved | date:i18n.selectedLanguage.dformat }}</div>
            <div class="flexitem-0 ll-value" ng-hide="item.approved"><i class="fa fa-calendar ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITDATE' | translate }}" ng-click="editDateApproved()" ng-show="hasGrant('EDTUNT')"></i>{{ 'UNIT.VIEW.APPR.NOT' | translate }}</div>
            <!-- TODO deactivated for configtable-Teilauslieferung
            <div feature-flag="configtable" class="flexitem-0 ll-title">{{ 'MODEL.VIEW.CONFIGTABLE.TTL' | translate }}</div>
            <div feature-flag="configtable" ng-show="item.configtable" class="flexitem-0 ll-value flexcnt">
                <div class="flexitem-1">{{ item.configtable.title | translate_model }}</div>
                <div class="flexitem-0"><button type="button" class="btn btn-default" ng-click="gotoConfigtable(item.configtable)"><i class="fa fa-external-link-square"></i> {{ 'MODEL.VIEW.BUTTON.CONFIGTABLE' | translate }}</button></div>
            </div>
            -->
            <div class="flexitem-0 ll-title">{{ 'UNIT.VIEW.COMMENT' | translate }}</div>
            <div class="flexitem-0 ll-value flexwrap"><i class="fa fa-edit ll-modifyicon" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.VIEW.TOOLTIP.EDITCOMM' | translate }}" ng-click="editComment()" ng-show="hasGrant('EDTUNT')"></i>{{ item.comment }}</div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-buttonbar flexcnt-r">
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('EDTUNT') && hasGrant('WFLMNG') && statusChangeable && item.status < 100" class="btn btn-primary" ng-click="setUnitStatus()"><i class="fa fa-exchange"></i> {{ 'UNIT.VIEW.BUTTON.CHSTATUS' | translate }}</button></div>
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('EDTUNT') && hasGrant('WFLMNG') && item.status >= 100" class="btn btn-primary" ng-click="unarchive()"><i class="fa fa-reply"></i> {{ 'UNIT.VIEW.BUTTON.UNARCHIVE' | translate }}</button></div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('EDTUNT') && hasGrant('DELUNT')" class="btn btn-danger" ng-click="deleteItem()"><i class="fa fa-ban"></i> {{ 'UNIT.VIEW.BUTTON.DELETE' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('EDTUNT')" class="btn btn-primary" ng-click="editItem()"><i class="fa fa-edit"></i> {{ 'UNIT.VIEW.BUTTON.EDIT' | translate }}</button></div>
        </div>
    </div>
    <div class="flexspace-divv" ng-show="unitViewLoaded"></div>
    <div class="flexspace-s"></div>
    <div class="flexitem-1 flexvcnt ll-list" ng-show="unitViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ 'UNIT.VIEW.CHECKS' | translate }}</div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0 ll-ptrhand"><i class="fa fa-lg fa-plus-square-o" ng-show="hasGrant('CRTCHK') && item.status < 10 && item.model.activeprocedures.length > 0 && item.model.activechecktypes.length > 0" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'UNIT.VIEW.TOOLTIP.ADDCHK' | translate }}" ng-click="newCheck()"></i></div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body">
            <div class="ll-item flexcnt" ng-repeat="check in item.checks">
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-0 ll-unittitle ll-ptrhand" ng-click="gotoCheck(check)">
                        {{ 'UNIT.VIEW.CHK' | translate }}
                        <span class="ll-unitchecktype">#{{ check.id }}</span>
                        ({{check.checktype.code}}:
                        <span class="ll-unitchecktype">{{ check.checktype.title | translate_model }}</span>)
                        <span class="text-info ll-unitusedversion" ng_show="check.model.version != item.model.version"> {{ 'UNIT.VIEW.CHECK.MODVERS' | translate : { mvers: check.model.version } }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="!check.status || check.status ==  0">
                        <i class="fa fa-star-o fa-fw text-muted"></i>&nbsp;
                        {{ 'UNIT.VIEW.SCHED.NOT' | translate }}
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 10">
                        <i class="fa fa-calendar fa-fw text-primary"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.SCHED.ON' | translate }} {{ check.scheduled | date:i18n.selectedLanguage.dformat }}</span>
                        <span ng-show="!skipsched && check.dueby">, {{ 'UNIT.VIEW.DUE.ON' | translate }} {{ check.dueby | date:i18n.selectedLanguage.dformat }}</span>
                        <span ng-show="!skipsched && !check.dueby">, {{ 'UNIT.VIEW.DUE.NOT' | translate }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 20">
                        <i class="fa fa-clock-o fa-fw text-primary"></i>&nbsp;
                        <span ng-show="check.started">{{ 'UNIT.VIEW.START.ON' | translate }} {{ check.started | date:i18n.selectedLanguage.dformat }}</span>
                        <span ng-hide="skipsched || check.started">, {{ 'UNIT.VIEW.SCHED.ON' | translate }} {{ check.scheduled | date:i18n.selectedLanguage.dformat }}</span>
                        <span ng-show="!skipsched && check.dueby">, {{ 'UNIT.VIEW.DUE.ON' | translate }} {{ check.dueby | date:i18n.selectedLanguage.dformat }}</span>
                        <span ng-show="!skipsched && !check.dueby">, {{ 'UNIT.VIEW.DUE.NOT' | translate }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 30">
                        <i class="fa fa-check-square-o fa-fw text-danger"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.FIN.ON' | translate }} {{ check.finished | date:i18n.selectedLanguage.dformat }}, {{ 'UNIT.VIEW.CHECK.FAIL' | translate }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 33">
                        <i class="fa fa-check-square-o fa-fw text-warning"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.FIN.ON' | translate }} {{ check.finished | date:i18n.selectedLanguage.dformat }}, {{ 'UNIT.VIEW.CHECK.WARN' | translate }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 35">
                        <i class="fa fa-check-square-o fa-fw text-success"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.FIN.ON' | translate }} {{ check.finished | date:i18n.selectedLanguage.dformat }}, {{ 'UNIT.VIEW.CHECK.PASS' | translate }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 40">
                        <i class="fa fa-lock fa-fw text-danger"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.CHECK.FAILCLOSED' | translate }} {{ check.finished | date:i18n.selectedLanguage.dformat }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 43">
                        <i class="fa fa-lock fa-fw text-warning"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.CHECK.WARNCLOSED' | translate }} {{ check.finished | date:i18n.selectedLanguage.dformat }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 45">
                        <i class="fa fa-lock fa-fw text-success"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.CHECK.PASSCLOSED' | translate }} {{ check.finished | date:i18n.selectedLanguage.dformat }}</span>
                    </div>
                    <div class="flexitem-0 ll-unitcheckdata" ng-show="check.status == 49">
                        <i class="fa fa-ban fa-fw text-danger"></i>&nbsp;
                        <span>{{ 'UNIT.VIEW.CHECK.PASSCANCEL' | translate }}</span>
                    </div>
                </div>
                <div class="flexspace-m"></div>
                <!--div class="flexitem-fix30 flexcenter ll-actions text-danger" ng_show="showEditButtons() && check.model.version != item.model.version" ng-click="updateStep(step)"><i class="fa fa-recycle"></i></div-->
                <div class="ll-actions text-danger" ng_show="hasGrant('DELCHK') && check.status < 20" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'UNIT.VIEW.TOOLTIP.DELCHK' | translate }}" ng-click="deleteCheck(check)"><i class="fa fa-trash-o"></i></div>
                <div class="ll-actions text-primary" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'UNIT.VIEW.TOOLTIP.GOTOCHK' | translate }}" ng-click="gotoCheck(check)"><i class="fa fa-external-link-square"></i></div>
            </div>
        </div>
    </div>
</div>


