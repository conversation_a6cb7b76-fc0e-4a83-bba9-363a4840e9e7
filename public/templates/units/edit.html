<div class="flexvcnt ll-editpanel" ng-show="unitEditLoaded">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{editor.title}}" translate-values="editor.titlevals"></div>
        <div class="flexitem-0 ll-subheader flexbottom">{{editor.subtitle.text | translate}} {{ editor.subtitle.vers }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'UNIT.EDIT.CODE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="un_code" ng-model="item.code" class="form-control ll-input" ng-disabled="!hasGrant('CHGCOD') && item.id">
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'UNIT.EDIT.CUST' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="un_cust" ng-model="item.customer" class="form-control ll-input">
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'UNIT.EDIT.MODEL' | translate }}</div>
            <div class="ll-edit">
                <select id="md_devtype" class="form-control" ng-model="item.model_id" ng-options="item.id as modelCboxName(item) for item in models"></select>
            </div>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancelEdit()"><i class="fa fa-ban"></i> {{ 'UNIT.EDIT.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="saveAndReturn()"><i class="fa fa-save"></i> {{ 'UNIT.EDIT.BUTTON.SAVE' | translate }}</button></div>
    </div>
</div>
