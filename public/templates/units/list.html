<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-1 ll-listpanel flexvcnt ll-fullheight">
            <div class="flexitem-1 flexvcnt" ng-show="unitListLoaded">
                <div class="flexitem-0 ll-header">
                    <div class="flexcnt">
                        <div class="flexitem-1">{{ 'UNIT.LIST.UNITS' | translate }}</div>
                        <div class="flexitem-0"><i class="fa fa-lg fa-plus-square-o ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.LIST.TOOLTIP.NEW' | translate }}" ng-click="newUnit()" ng-show="hasGrant('EDTUNT') && hasGrant('CRTUNT')"></i></div>
                    </div>
                    <div class="ll-stepsubheader flexcnt">
                        <div class="flexitem-1 ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'UNIT.LIST.TOOLTIP.GOTOMOD' | translate }}" ng-click="gotoModel()">{{ 'UNIT.FORMODEL' | translate : { mdl: model.code } }}: {{ model.title | translate_model }}</div>
                    </div>
                    <div class="ll-stepsubheader flexcnt" ng-show="archivemode">
                        <div class="flexitem-1 ll-ptrhand" tooltip-placement='right' tooltip-popup-delay='1000' tooltip="XX{{ 'UNIT.LIST.TOOLTIP.ARCHMODE' | translate }}" ng-click="toggleArchiveMode()">{{ 'UNIT.INARCHIVE' | translate : { pat: archivemode } }}</div>
                    </div>
                </div>
                <div class="flexitem-0 ll-controls flexcnt">
                    <div class="flexitem-0"><i class="fa fa-search ll-ptrhand" tooltip-placement='right' tooltip-popup-delay='1000' tooltip="{{ 'UNIT.LIST.TOOLTIP.CLTF' | translate }}" ng-class="{ 'text-success': filters.textfilter.length > 0 }" ng-click="clearTextfilter()"></i></div>
                    <div class="flexspace-s"></div>
                    <input type="text" id="listfilter" ng-model="filters.textfilter" class="flexitem-1 form-control ll-input input-xxs" ng-blur="saveFilterSettings()">
                    <div class="flexspace-m"></div>
                    <div class="flexitem-0"><i class="fa fa-lock ll-ptrhand" tooltip-append-to-body='true' tooltip-popup-delay='1000' tooltip="{{ 'UNIT.LIST.TOOLTIP.LOCKMODE' | translate }}" ng-class="{ 'text-success': filters.showLocked || archivemode }" ng-click="toggleShowLocked()"></i></div>
                    <div class="flexspace-m"></div>
                    <div class="flexitem-0"><i class="fa fa-archive ll-ptrhand" tooltip-append-to-body='true' tooltip-popup-delay='1000' tooltip="{{ 'UNIT.LIST.TOOLTIP.ARCHMODE' | translate }}" ng-class="{ 'text-success': archivemode }" ng-click="toggleArchiveMode()"></i></div>
                </div>
                <div class="flexitem-1 ll-body">
                    <div class="ll-item flexcnt ll-ptrhand" ng-repeat="unit in items | filter:filters.textfilter" ng-class="itemSelected(unit)" ng-click="goto(unit.id)" ng-hide="unit.status >= 10 && !filters.showLocked && !archivemode">
                        <div class="flexitem-1 ll-title">
                            <i class="fa fa-fw fa-lock" ng-show="unit.status == 10"></i>
                            <i class="fa fa-fw fa-trash-o" ng-show="unit.status == 20"></i>
                            <i class="fa fa-fw fa-lock text-muted" ng-show="unit.status == 110"></i>
                            <i class="fa fa-fw fa-trash-o text-muted" ng-show="unit.status == 120"></i>
                            {{ unit.code }}
                            <span class="ll-unitcustomer">{{ unit.customer }}</span>
                        </div>
                        <div class="flexitem-0 ll-title">
                            <span class="ll-unitcheckcnt">{{ unit.ckcnt }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-3 ll-fullheight"><ui-view/></div>
    </div>
</div>