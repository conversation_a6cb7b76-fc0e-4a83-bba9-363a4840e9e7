<div class="flexcnt ll-viewpanel">
    <div class="flexitem-1 flexvcnt ll-info" ng-show="procViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1">{{ item.title | translate_model }}</div>
            <div class="flexitem-0 ll-code flexbottom">{{ item.code }}</div>
            <div class="flexspace-m"></div>
            <div class="flexitem-0 flexbottom ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.STAT' | translate }}" ng-click="measureStatistics()"><i class="fa fa-bar-chart-o"></i></div>
        </div>
        <div class="flexitem-0 ll-version">
            {{ 'PROCEDURE.VIEW.VERS' | translate }}:
            {{ item.versname.text | translate : item.versname }}
            <i class="fa fa-fw fa-exchange text-primary ll-ptrhand" ng-show="item.versions.length > 1" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.CHVERS' | translate }}" ng-click="changeVersion()"></i>
            <i tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.TESTVER' | translate }}" class="fa fa-fw fa-eye text-primary ll-ptrhand" ng-show="item.versions.length > 1" ng-click="testVersion()"></i>
            <i class="fa fa-fw fa-list text-primary ll-ptrhand" ng-show="hasGrant('EDTPSM') && hasGrant('FINALZ')" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.CHANGELOG' | translate }}" ng-click="showChangeLog()"></i>
            <i class="fa fa-fw fa-undo text-danger ll-ptrhand" ng-show="item.versions.length > 1 && !isVersion() && item.dirty && hasGrant('EDTPSM') && hasGrant('FINALZ')" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.RESET' | translate }}" ng-click="resetChanges()"></i>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body flexvcnt">
            <div ng-show="item.updatablemodels.length > 0" class="flexitem0 ll-message ll-ptrhand flexwrap" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.UPDMOD' | translate }}" ng-click="updateModels()" ng-show="hasGrant('FINALZ') && hasGrant('EDTPSM')"><i class="fa fa-recycle text-primary"></i> {{ 'PROCEDURE.VIEW.UPDATEINFO' | translate : { updcnt: item.updatablemodels.length } }}</div>
            <div class="flexitem-0 ll-title">{{ 'PROCEDURE.VIEW.CODE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.code }}</div>
            <div class="flexitem-0 ll-title">{{ 'PROCEDURE.VIEW.TITLE' | translate }}</div>
            <div class="flexitem-0 ll-value">{{ item.title | translate_model }}</div>
            <div feature-flag="customTags">
                <div ng-repeat="tag in tags track by $index">
                    <div class="flexitem-0 ll-title">{{tag.value[getSelectedLanguage().code]}}</div>
                    <div class="flexitem-0 ll-value">{{getTagValue($index + 1)}}</div>
                </div>
            </div>
            <div class="flexitem-0 ll-title">{{ 'PROCEDURE.VIEW.PROCTIME' | translate }}</div>
            <div class="flexitem-0 ll-value flexwrap" ng-if="item.processing_time">
                <span ng-if="item.processing_time.hours">{{item.processing_time.hours}} {{'PROCEDURE.VIEW.HOURS' | translate}} </span>
                <span ng-if="item.processing_time.minutes">{{item.processing_time.minutes}} {{'PROCEDURE.VIEW.MINUTES' | translate}}</span>
            </div>
            <div class="flexitem-0 ll-title">{{ 'PROCEDURE.VIEW.DESC' | translate }}</div>
            <div class="flexitem-0 ll-value flexwrap" ng-bind-html="item.description | translate_model"></div>
            <div class="flexitem-0 ll-title">{{ 'PROCEDURE.VIEW.RULES' | translate }}</div>
            <div class="flexitem-0 ll-value flexcnt">
                <div class="flexitem-1 flexvcnt"><div ng-repeat="mod in wflowRulesProc" class="flexitem-0" translate translate-values="mod[1]">{{ mod[0] }}</div></div>
                <div class="flexitem-0" ng-show="showEditButtons('EDTPSM')"><button type="button" class="btn btn-default" ng-click="wfRuleManager()"><i class="fa fa-code-fork"></i> {{ 'PROCEDURE.VIEW.REDITOR' | translate }}</button></div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-0 ll-buttonbar flexcnt-r">
            <div class="flexitem-0"><button type="button" class="btn btn-primary" ng-click="exportItem($event)"><i class="fa fa-download"></i> {{ 'PROCEDURE.VIEW.BUTTON.EXPORT' | translate }}</button></div>
            <div class="flexitem-1"></div>
            <div class="flexitem-0"><button type="button" ng-show="hasGrant('DELPRC') && showEditButtons('EDTPSM') && !isVersion()" class="btn btn-danger" ng-click="deleteItem()"><i class="fa fa-ban"></i> {{ 'PROCEDURE.VIEW.BUTTON.DELETE' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button type="button" ng-show="!isVersion() && hasGrant('EDTPSM') && hasGrant('FINALZ')" ng-disabled="!item.dirty" class="btn btn-primary" ng-click="finalizeItem()"><i class="fa fa-check-circle"></i> {{ 'PROCEDURE.VIEW.BUTTON.FIN' | translate }}</button></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><button type="button" ng-show="showEditButtons('EDTPSM')" class="btn btn-primary" ng-click="editItem()"><i class="fa fa-edit"></i> {{ 'PROCEDURE.VIEW.BUTTON.EDIT' | translate }}</button></div>
        </div>
    </div>
    <div class="flexspace-divv" ng-show="procViewLoaded"></div>
    <div class="flexspace-s"></div>
    <div class="flexitem-1 flexvcnt ll-list" ng-show="procViewLoaded">
        <div class="flexitem-0 ll-header flexcnt">
            <div class="flexitem-1 ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.GOTOSTEP' | translate }}" ng-click="gotoSteps()">{{ 'PROCEDURE.VIEW.STEPS' | translate }}</div>
            <div class="flexitem-0 ll-ptrhand" ng-show="showActivateReorderIcon('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ACTREORD' | translate }}" ng-click="activateReordering(true)"><i class="fa fa-sort"></i></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><i class="fa fa-upload ll-ptrhand" ng-show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.UPLSTEP' | translate }}" ng-click="toggleUploader()"></i></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0"><i class="fa fa-copy ll-ptrhand" ng-show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.CLNSTEP' | translate }}" ng-click="cloneStep()"></i></div>
            <div class="flexspace-s"></div>
            <div class="flexitem-0 ll-ptrhand"><i class="fa fa-lg fa-plus-square-o" ng-show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.NEWSTEP' | translate }}" ng-click="newStep()"></i></div>
        </div>
        <dropbox data="uploaderinfo"></dropbox>
        <div class="flexspace-m"></div>
        <div class="flexitem-1 ll-body" ui-sortable="sublistSortOptions" ng-model="item.steps">
            <div class="ll-item flexcnt" ng-repeat="step in item.steps">
                <div class="flexitem-1 flexvcnt">
                    <div class="flexitem-0 ll-title ll-ptrhand" ng-click="gotoStep(step)">{{ step.title | translate_model }}</div>
                    <div class="flexitem-0 ll-counter">
                        <span class="ll-code">{{ step.code }}: </span>
                        {{ 'PROCEDURE.VIEW.MEASURES' | translate : { msrcnt: step.mcnt } }}
                        <i ng-show="step.steptype === 1" class="fa fa-book"></i>
                    </div>
                </div>
                <div class="flexspace-m"></div>
                <div class="ll-dragaction text-primary" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.REORD' | translate }}" ng-show="showReorderIcon('EDTPSM')"><i class="fa fa-sort"></i></div>
                <div class="ll-actions text-muted" ng-show="showEditButtons('EDTPSM') && $index == 0" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCETOP' | translate }}"><i class="fa fa-fw fa-circle-o"></i></div>
                <div class="ll-actions text-muted" ng-show="showEditButtons('EDTPSM') && $index > 0 && step.steptype === 1" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCEINSTR' | translate }}"><i class="fa fa-fw fa-circle-o"></i></div>
                <div class="ll-actions text-success" ng-show="showEditButtons('EDTPSM') && $index > 0 && step.steptype !== 1 && step.enforce == PSM_ENFORCE_NONE" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE0' | translate }}" ng-click="setEnforceLevel(step,1)"><i class="fa fa-fw fa-circle-o"></i></div>
                <div class="ll-actions text-danger" ng-show="showEditButtons('EDTPSM') && $index > 0 && step.enforce == PSM_ENFORCE_LASTONE" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE1' | translate }}" ng-click="setEnforceLevel(step,2)"><i class="fa fa-fw fa-dot-circle-o"></i></div>
                <div class="ll-actions text-danger" ng-show="showEditButtons('EDTPSM') && $index > 0 && step.enforce == PSM_ENFORCE_ALLPRIOR" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.ENFORCE2' | translate }}" ng-click="setEnforceLevel(step,0)"><i class="fa fa-fw fa-circle"></i></div>
                <div class="ll-actions text-primary" ng-show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'STEP.VIEW.TOOLTIP.EDITRULE' | translate }}" ng-click="wfRuleManagerStep(step)"><i class="fa fa-code-fork"></i><span class="ll-subscript">{{ step.flowcontrol.length }}</span></div>
                <div class="ll-actions text-primary" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.EXPSTEP' | translate }}" ng-click="exportItem($event, 'step', step)"><i class="fa fa-download"></i></div>
                <div class="ll-actions text-primary" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.GOTOSTEP' | translate }}" ng-click="gotoStep(step)"><i class="fa fa-external-link-square"></i></div>
                <div class="ll-actions text-danger" ng-show="showEditButtons('EDTPSM')" tooltip-popup-delay='1000' tooltip-placement='left' tooltip="{{ 'PROCEDURE.VIEW.TOOLTIP.REMSTEP' | translate }}" ng-click="deleteStep(step)"><i class="fa fa-trash-o"></i></div>
            </div>
        </div>
    </div>
</div>
