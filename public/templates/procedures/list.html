<div class="ll-mainview">
    <div class="ll-mainpane flexcnt ll-fullwidth">
        <div class="flexitem-1 ll-listpanel flexvcnt ll-fullheight">
            <div class="flexitem-1 flexvcnt" ng-show="procListLoaded">
                <div class="flexitem-0 ll-header flexcnt">
                    <div class="flexitem-1">{{ 'PROCEDURE.LIST.PROCEDURES' | translate }}</div>
                    <div class="flexitem-0"><i class="fa fa-upload ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.IMPORT' | translate }}" ng-click="toggleUploader()" ng-show="hasGrant('EDTPSM') && hasGrant('CRTPSM')"></i></div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-copy ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.CLONE' | translate }}" ng-click="cloneProcedure()" ng-show="hasGrant('EDTPSM') && hasGrant('CRTPSM')"></i></div>
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-lg fa-plus-square-o ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.ADD' | translate }}" ng-click="newProcedure()" ng-show="hasGrant('EDTPSM') && hasGrant('CRTPSM')"></i></div>
                </div>
                <dropbox data="uploaderinfo"></dropbox>
                <div class="flexitem-0 ll-controls flexcnt">
                    <div class="flexitem-0"><i class="fa fa-search ll-ptrhand" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.CLTF' | translate }}" ng-class="{ 'text-success': filters.textfilter.length > 0 }" ng-click="clearTextfilter()"></i></div>
                    <div class="flexspace-s"></div>
                    <input type="text" id="listfilter" ng-model="filters.textfilter" class="flexitem-1 form-control ll-input input-xxs" ng-blur="saveFilterSettings()">
                    <div class="flexspace-s"></div>
                    <div class="flexitem-0"><i class="fa fa-eye ll-ptrhand" ng-class="{'text-success': showDeleted}" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.SHOWDEL' | translate }}" ng-click="toggleShowDeleted()"></i></div>
                    <div feature-flag="customTags" class="flexitem-0">
                        <i ng-if="isTagfilterActive()" class="text-primary fa fa-filter ll-ptrhand" ng-class="" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.PROCFILTER' | translate }}" ng-click="openFilterDialog()"></i>
                        <i ng-if="!isTagfilterActive()" class="fa fa-filter ll-ptrhand" ng-class="" tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.PROCFILTER' | translate }}" ng-click="openFilterDialog()"></i>
                    </div>
                </div>
                <div class="flexitem-1 ll-body">
                    <div class="ll-item flexvcnt ll-ptrhand" ng-repeat="procedure in items | filter:filters.tagfilter:containsComparator | attributefilter : ['code', 'title'] : filters.textfilter : lang | orderBy:'1*code ? 1*code : code'" ng-class="itemSelected(procedure)" ng-mousedown="goto(procedure.id)" ng-show="showDeleted || !itemDeleted(procedure)">
                        <div class="flexitem-0 flexcnt">
                            <div class="flexitem-1 ll-title" ng-class="{ 'll-disabled': itemDeleted(procedure), 'll-dirty': procedure.dirty }">{{ procedure.title | translate_model }}</div>
                            <div class="flexitem-0 flexbottom ll-version">{{ procedure.versname.text | translate : procedure.versname }}</div>
                        </div>
                        <div class="flexitem-0 ll-counter flexcnt">
                            <div class="flexitem-1"><span class="ll-code">{{ procedure.code }}: </span> {{ 'PROCEDURE.LIST.BTMROW' | translate : { stpcnt: procedure.stepcnt, modcnt: procedure.usage[0].length } }}<span ng-show="procedure.usage[1].length>0">{{ 'PROCEDURE.LIST.BTMROWUPD' | translate : { updcnt: procedure.usage[1].length } }}</span></div>
                            <div class="flexspace-s"></div>
                            <div class="flexitem-0"><i tooltip-append-to-body='true' tooltip-popup-delay='1000' tooltip="{{ 'PROCEDURE.LIST.TOOLTIP.GOTOMODEL' | translate }}" class="fa fa-external-link-square ll-ptrhand text-primary" ng-show="procedure.usage[0].length > 0" ng-click="gotoModels(procedure,$event)"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flexspace-m"></div>
        <div class="flexitem-3 ll-fullheight"><ui-view/></div>
    </div>
</div>