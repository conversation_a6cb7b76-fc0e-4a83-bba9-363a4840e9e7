<div class="flexvcnt ll-editpanel" ng-show="procEditLoaded">
    <div class="flexitem-0 ll-header flexcnt">
        <div class="flexitem-1" translate="{{editor.title}}" translate-values="editor.titlevals"></div>
        <div class="flexitem-0 ll-subheader flexbottom">{{editor.subtitle.text | translate}} {{ editor.subtitle.vers }}</div>
    </div>
    <form role="form" class="flexitem-1 ll-body flexvcnt">
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'PROCEDURE.EDIT.CODE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="pr_code" ng-model="item.code" class="form-control ll-input" ng-disabled="true">
            </div>
        </div>
        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'PROCEDURE.EDIT.TITLE' | translate }}</div>
            <div class="ll-edit">
                <input type="text" id="pr_title" ng-model="item.title[languageinfo.selected]" class="form-control ll-input" ui-keyup="langToggleKeymap">
                <lang-completion-info langinfo="languageinfo" model="item.title" focus-after-change="#pr_title"></lang-completion-info>
            </div>
        </div>

        <div feature-flag="customTags">
            <div ng-repeat="tag in tags track by $index">
                <div class="flexitem-0 ll-block">
                    <div class="ll-key">{{tag.description[getSelectedLanguage().code]}}</div>
                    <select class="form-control" ng-model="tagValues[$index]" ng-options="item as item.value for item in tag.availableTagValues track by item.value" ng-change="tagChanged()">
                        <option style="display:none" value=""></option>
                    </select>
                    <div class="flexitem-0 text-danger ll-icon ll-ptrhand"><i class="fa fa-fw fa-trash-o" ng-click="removeTagValue($index)" tooltip-popup-delay='1000' tooltip="{{ 'SETTINGS.PROC.TAGVALUE.DELETE' | translate }}"></i></div>
                </div>
            </div>

        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'PROCEDURE.EDIT.PROCTIME' | translate }}</div>
            <div class="flexitem-0">
                <input type="text" id="pr_processing_hours" class="form-control ll-input ll-processing-time-input" ng-model="item.processing_time.hours">
            </div>
            <span class="ll-time-edit-text">{{'PROCEDURE.EDIT.HOURS' | translate}}</span>
            <div class="flexitem-0">
                <input type="text" id="pr_processing_minutes" class="form-control ll-input ll-processing-time-input"  ng-model="item.processing_time.minutes">
            </div>
            <span class="ll-time-edit-text">{{'PROCEDURE.EDIT.MINUTES' | translate}}</span>
        </div>

        <div class="flexitem-0 ll-block">
            <div class="ll-key">{{ 'PROCEDURE.EDIT.DESC' | translate }}</div>
            <div class="ll-edit">
                <text-angular ng-model="item.description[languageinfo.selected]" id="pr_desc" name="pr_desc" disabled-ui-keyup="langToggleKeymap"></text-angular>
                <lang-completion-info langinfo="languageinfo" model="item.description" focus-ta-after-change="pr_desc"></lang-completion-info>
            </div>
        </div>
    </form>
    <div class="flexspace-l"></div>
    <div class="flexitem-0 ll-buttonbar flexcnt">
        <div class="btn-group flexitem-0">
            <label class="btn btn-primary" ng-repeat="lang in languageinfo.languages" ng-model="languageinfo.selected" btn-radio="lang.code">{{ lang.name }}</label>
        </div>
        <div class="flexitem-1"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-danger" ng-click="cancelEdit()"><i class="fa fa-ban"></i> {{ 'PROCEDURE.EDIT.BUTTON.CANCEL' | translate }}</button></div>
        <div class="flexspace-s"></div>
        <div class="flexitem-0"><button type="button" class="btn btn-success" ng-click="saveProcedure()"><i class="fa fa-save"></i> {{ 'PROCEDURE.EDIT.BUTTON.SAVE' | translate }}</button></div>
    </div>
</div>
