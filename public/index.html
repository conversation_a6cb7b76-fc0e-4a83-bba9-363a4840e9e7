<!DOCTYPE html>

<!-- lang set to 'en' to make sure the decimal separator is always a period '.' -->
<html lang="en" ng-app="leanLogic">
<head>
    <base href="/">

    <meta charset="utf8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, target-densitydpi=device-dpi" />
    <META HTTP-EQUIV="CACHE-CONTROL" CONTENT="NO-CACHE">
    <META HTTP-EQUIV="PRAGMA" CONTENT="NO-CACHE">

    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <link rel="apple-touch-icon" href="/img/diso_logo.jpg"/>
    <link rel="stylesheet" href="/css/app.css">
    <link rel="stylesheet" href="/css/fontawesome/css/font-awesome.min.css">
    <link rel='stylesheet' href='/textAngular/src/textAngular.css'>
    <link rel="stylesheet" href="/css/select.css">
    <title>LeanQA</title>
</head>
<body>

  <!-- AUTOINSERT_EXTLIB -->

  <script src="js/app.js"></script>

  <!-- AUTOINSERT_WEBAPP -->

    <nav class="navbar navbar-inverse navbar-fixed-top" style="min-width: 1000px;" ng-controller="AppCtrl" ng-if="appLoaded">
        <div class="navbar-inner" ng-cloak><!--div(class="container")-->
            <span class="navbar-brand" style="font-size:x-large;">
                <i class="fa fa-mobile fa-lg" ng-if="isMobileApp" ng-click="switchToDesktop()"></i>
                <i class="fa fa-desktop" ng-if="!isMobileApp" ng-click="switchToMobile()"></i>
                <span translate>FRAME.TITLE</span>
                <span class="version">{{ appversion }}</span>
            </span>
            <ul class="nav navbar-nav navbar-right" ng-show="isLoggedIn()">
                <li ng-show="noticeButtonPath">
                    <a href ng-click="showNoticeEditor()"><i class="fa fa-bullhorn fa-lg"></i></a>
                </li>
                <span class="navbar-brand">{{ currUserRealName() }}</span>
                <li ng-controller="LanguageSelectionCtrl" class="dropdown" dropdown>
                    <a href class="dropdown-toggle" ng-if="languages.length > 1" dropdown-toggle><i class="fa fa-globe fa-lg"></i> {{selectedLanguage().name}}<b class="caret"></b></a>
                    <ul class="dropdown-menu">
                        <li ng-repeat="language in languages"><a href ng-click="selectLanguage()">{{language.name}}</a></li>
                    </ul>
                </li>
                <li ng-if="showtest">
                    <a href ng-click="test()"><i class="fa fa-bug fa-lg"></i> <span>TEST</span></a>
                </li>
                <li ng-show="userinfo.dashboardinfo.length < 2">
                    <a href="/app/dashboard/0"><i class="fa fa-dashboard fa-lg"></i> <span translate>FRAME.DASHBOARD</span></a>
                </li>
                <li class="dropdown" dropdown ng-show="userinfo.dashboardinfo.length > 1">
                    <a href class="dropdown-toggle" dropdown-toggle><i class="fa fa-dashboard fa-lg"></i> <span translate>FRAME.DASHBOARDS</span></a>
                    <ul class="dropdown-menu">
                        <li ng-repeat="dashboard in userinfo.dashboardinfo"><a href="/app/dashboard/{{$index}}"><i class="fa fa-dashboard fa-fw fa-lg"></i>{{$index+1}}: {{ dashboard.name }}</a></li>
                    </ul>
                </li>
                <!--li class="dropdown"><a href="#" data-toggle="debugdd1" class="dropdown-toggle"><i class="fa fa-bug fa-fw fa-lg"></i>Grants</a>
                    <ul class="dropdown-menu">
                        <li style="font-size:12px;" ng-repeat="grant in listOfGrants()"><a ng-click="toggleGrant(grant.code)"><i class="fa fa-fw fa-lg" ng-class="hasThisGrant(grant.code) ? 'fa-check-square-o' : 'fa-square-o'"></i> {{ grant.code }} <span style="font-weight:200;font-style:italic;">{{ grant.name | translate }}</span></a></li>
                    </ul>
                </li-->
                <!--li class="dropdown"><a href="#" data-toggle="debugdd2" class="dropdown-toggle"><i class="fa fa-bug fa-fw fa-lg"></i>Acclevel</a>
                    <ul class="dropdown-menu">
                        <li><a ng-click="changeEffectiveLevel(1)"><i class="fa fa-fw fa-lg" ng-class="hasEffectiveLevel(1) ? 'fa-check-square-o' : 'fa-square-o'"></i> Level 1</a></li>
                        <li><a ng-click="changeEffectiveLevel(2)"><i class="fa fa-fw fa-lg" ng-class="hasEffectiveLevel(2) ? 'fa-check-square-o' : 'fa-square-o'"></i> Level 2</a></li>
                        <li><a ng-click="changeEffectiveLevel(3)"><i class="fa fa-fw fa-lg" ng-class="hasEffectiveLevel(3) ? 'fa-check-square-o' : 'fa-square-o'"></i> Level 3</a></li>
                        <li><a ng-click="changeEffectiveLevel(4)"><i class="fa fa-fw fa-lg" ng-class="hasEffectiveLevel(4) ? 'fa-check-square-o' : 'fa-square-o'"></i> Level 4</a></li>
                        <li><a ng-click="changeEffectiveLevel(5)"><i class="fa fa-fw fa-lg" ng-class="hasEffectiveLevel(5) ? 'fa-check-square-o' : 'fa-square-o'"></i> Level 5</a></li>
                        <li><a ng-click="changeEffectiveLevel(6)"><i class="fa fa-fw fa-lg" ng-class="hasEffectiveLevel(6) ? 'fa-check-square-o' : 'fa-square-o'"></i> Level 6</a></li>
                    </ul>
                </li-->
                <li class="dropdown" dropdown ng-if="!isMobileApp">
                    <a href class="dropdown-toggle" dropdown-toggle><i class="fa fa-edit fa-lg"></i> <span translate>FRAME.MANAGE.TITLE</span><b class="caret"></b></a>
                    <ul class="dropdown-menu">
                        <li ng-show="hasGrant('MNGMUC')"><a href="/app/models"><i class="fa fa-car fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.MODEL</span></a></li>
                        <li ng-show="hasGrant('MNGPSM')"><a href="/app/procedures"><i class="fa fa-sitemap fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.PROCEDURE</span></a></li>
                        <li ng-show="hasGrant('MNGTOL')"><a href="/app/tools"><i class="fa fa-wrench fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.TOOL</span></a></li>
                        <li ng-show="hasGrant('MNGPAR')"><a ng-click="deviceTypeEditor()"><i class="fa fa-rocket fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.DEVICETYPE</span></a></li>
                        <li feature-flag="configtable" ng-show="hasGrant('MNGCFG')"><a href="/app/configmgt"><i class="fa fa-list-alt fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.CONFIGTABLE</span></a></li>
                        <li ng-show="hasGrant('MNGPAR')"><a ng-click="checkTypeEditor()"><i class="fa fa-list-ol fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.CHECKTYPE</span></a></li>
                        <li feature-flag="customTags" ng-show="hasGrant('MNGPAR')"><a ng-click="settingsEditor()"><i class="fa fa-cogs fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.SETTINGS</span></a></li>
                        <li feature-flag="measurementErrorReport" ng-show="hasGrant('MNGNTC')"><a ng-click="measurementErrorCategoriesEditor()"><i class="fa fa-exclamation-triangle fa-fw fa-lg" aria-hidden="true"></i> <span translate>FRAME.MANAGE.MEASUREMENTERRORCATEGORIES</span></a></li>
                        <li feature-flag="measurementErrorReport" ng-show="hasGrant('MNGNTC')"><a ng-click="measurementErrorList()"><i class="fa fa fa-chain-broken fa-fw fa-lg" aria-hidden="true"></i> <span translate>FRAME.MANAGE.MEASUREMENTERRORCATEGORIESREPORT</span></a></li>
                        <li ng-show="hasGrant('MNGUSR')"><a href="/app/usermgt/me/1"><i class="fa fa-users fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.USERS</span></a></li>
                        <li ng-show="hasGrant('MNGNTC')"><a href="/app/noticemgt"><i class="fa fa-bullhorn fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.NOTICES</span></a></li>
                        <li><a ng-click="changePassword()"><i class="fa fa-key fa-fw fa-lg"></i> <span translate>FRAME.MANAGE.CHANGEPW</span></a></li>
                    </ul>
                </li>
                <li ng-if="hasGrant('GLSRCH') && !isMobileApp">
                    <a ng-click="globalSearchView()"><i class="fa fa-search fa-fw fa-lg"></i> <span translate>FRAME.GSEARCH</span></a>
                </li>
                <li>
                    <a ng-click="logout()"><i class="fa fa-power-off fa-lg"></i> <span translate>FRAME.LOGOUT</span></a>
                </li>
                <li><div style="width: 20px;"/></li>
            </ul>
        </div>
    </nav>
    <div class="ll-loadingshade" ng-show="showLoadingShade"><div style="position: absolute; top:50%;left:50%;"><span us-spinner="{radius:100, width:20, length: 32}"></span></div></div>
    <div class="ll-topview" ng-if="appLoaded" ui-view></div>

</body>
</html>

<!-- LIB_LIST
In diese Liste die von der Webapp verwendeten Libraries eintragen:
1. Zeile: <Name Library> (<URL>), <Lizenz>:
n. Zeile:  - <Pfad zu .js nicht minified> : <Pfad zu .js minified>    (für Prod. wird minified, für Dev. nicht minified verwendet)
    oder:  - <Pfad zu .js>                                            (für Prod. und Dev. wird ein .js verwendet)
    oder:  - : <Pfad zu .js minified>                                 (das .js wird nur bei Prod. verwendet)
    oder:  - <Pfad zu .js nicht minified>                             (das .js wird nur bei Dev. verwendet)

FastClick (https://github.com/ftlabs/fastclick), MIT:
 - fastclick/lib/fastclick.js
LoDash (https://lodash.com), unrestricted:
 - lodash/lodash.js : lodash/lodash.min.js
JQuery (https://jquery.com), MIT:
 - jquery/dist/jquery.js : jquery/dist/jquery.min.js
Elevate Zoom (http://www.elevateweb.co.uk/image-zoom), MIT/GPL:
 - lib/elevatezoom-master-3.0.8/jquery.elevateZoom-3.0.8.min.js
AngularJS (https://angularjs.org), MIT:
 - angular/angular.js : angular/angular.min.js
 - angular-cookies/angular-cookies.js
JQuery UI (https://jqueryui.com), MIT:
 - jquery-ui/ui/core.js : jquery-ui/ui/minified/core.min.js
 - jquery-ui/ui/widget.js : jquery-ui/ui/minified/widget.min.js
 - jquery-ui/ui/mouse.js : jquery-ui/ui/minified/mouse.min.js
 - jquery-ui/ui/sortable.js : jquery-ui/ui/minified/sortable.min.js
Angular Translate (https://github.com/angular-translate/angular-translate), MIT:
 - angular-translate/angular-translate.js : angular-translate/angular-translate.min.js
 - angular-translate-loader-static-files/angular-translate-loader-static-files.js : angular-translate-loader-static-files/angular-translate-loader-static-files.min.js
Restangular (https://github.com/mgonto/restangular), MIT:
 - restangular/src/restangular.js : restangular/dist/restangular.min.js
Bootstrap (http://getbootstrap.com), MIT:
 - bootstrap/dist/js/bootstrap.js : bootstrap/dist/js/bootstrap.min.js
Angular UI Router (https://github.com/angular-ui/ui-router), MIT:
 - angular-ui-router/release/angular-ui-router.js : angular-ui-router/release/angular-ui-router.min.js
Angular UI Utils (http://angular-ui.github.io/ui-utils/), MIT:
 - angular-ui-utils/ui-utils.js : angular-ui-utils/ui-utils.min.js
Spin.JS (http://fgnass.github.io/spin.js/), MIT:
 - spin.js/spin.js : spin.js/spin.min.js
Angular Spinner (https://github.com/urish/angular-spinner), MIT:
 - angular-spinner/angular-spinner.js : angular-spinner/angular-spinner.min.js
Angular-Timer, MIT:
 - angular-timer/dist/angular-timer.js : angular-timer/dist/angular-timer.js
Humanize-Duration, MIT:
 - humanize-duration/humanize-duration.js : humanize-duration/humanize-duration.js
Momentjs, MIT:
 - moment/moment.js : moment/moment.js
Angular Bootstrap UI (https://angular-ui.github.io/bootstrap/), MIT:
 - angular-bootstrap/ui-bootstrap-tpls.js : angular-bootstrap/ui-bootstrap-tpls.min.js
Angular File Upload (https://github.com/nervgh/angular-file-upload), MIT:
 - angular-file-upload/angular-file-upload.js : angular-file-upload/angular-file-upload.min.js
CryptoJS (https://code.google.com/p/crypto-js/), BSD 3-Clause:
 - CryptoJS/build/rollups/sha1.js
Angular UI Sortable (https://github.com/angular-ui/ui-sortable), MIT:
 - angular-ui-sortable/sortable.js : angular-ui-sortable/sortable.min.js
SprintF (https://www.npmjs.com/package/sprintf-js), BSD 3-Clause:
 - sprintf/dist/sprintf.min.js
Draggabilly (http://draggabilly.desandro.com), MIT:
 - draggabilly/dist/draggabilly.pkgd.js : draggabilly/dist/draggabilly.pkgd.min.js
Rangy (https://github.com/timdown/rangy), MIT:
 - rangy/rangy-core.js : rangy/rangy-core.min.js
 - rangy/rangy-selectionsaverestore.js : rangy/rangy-selectionsaverestore.min.js
TextAngular (https://github.com/fraywing/textAngular), MIT:
 - textAngular/dist/textAngular-rangy.min.js : textAngular/dist/textAngular-rangy.min.js
 - textAngular/dist/textAngular-sanitize.js : textAngular/dist/textAngular-sanitize.min.js
 - textAngular/dist/textAngularSetup.js : -
 - textAngular/dist/textAngular.js : -
Videogular (http://www.videogular.com), MIT:
 - videogular/videogular.js : videogular/videogular.min.js
 - videogular-buffering/buffering.js : videogular-buffering/buffering.min.js
 - videogular-controls/controls.js : videogular-controls/controls.min.js
AngularFeatureFlags (https://github.com/mjt01/angular-feature-flags), MIT:
 - angular-feature-flags/dist/featureFlags.js : angular-feature-flags/dist/featureFlags.min.js
ui-select (https://github.com/angular-ui/ui-select), MIT:
 - angular-ui-select/dist/select.js : angular-ui-select/dist/select.min.js
ng-touch (https://angularjs.org), MIT:
 - angular-touch/angular-touch.js : angular-touch.min.js
-->
