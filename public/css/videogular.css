@font-face {
	font-family: 'icomoon';
	src:url('../css/fonts/icomoon.eot');
	src:url('../css/fonts/icomoon.eot?#iefix') format('embedded-opentype'),
	url('../css/fonts/icomoon.woff') format('woff'),
	url('../css/fonts/icomoon.ttf') format('truetype'),
	url('../css/fonts/icomoon.svg#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

videogular {
	position: relative;
	background-color: #000000;
	overflow: hidden;
    display: block;

	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: moz-none;
	-ms-user-select: none;
	user-select: none;
}

videogular.fullscreen {
	position: absolute;
	left: 0;
	top: 0;
}

videogular video {
	position: absolute;
    width: 100%;
    height: auto;
	z-index: 1;
}

.iconButton {
	color: #FFFFFF;
	font-family: 'icomoon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
}

/**********************/
/* OverlayPlay plugin */
/**********************/
vg-overlay-play {
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 3;
}

vg-overlay-play div.play:before {
	content: "\e000";
}

.overlayPlayContainer {
	font-size: 100px;
	width: 100%;
	height: 100%;
	position: absolute;
	display: table;
	cursor: pointer;

	zoom: 1;
	filter: alpha(opacity=60);
	opacity: 0.6;
}

.overlayPlayContainer div {
	vertical-align: middle;
	text-align: center;
	display: table-cell;
}

/********************/
/* Buffering plugin */
/********************/
vg-buffering {
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 4;
}

.bufferingContainer {
	width: 100%;
	position: absolute;
	cursor: pointer;
	top: 50%;
	margin-top: -50px;

	zoom: 1;
	filter: alpha(opacity=60);
	opacity: 0.6;
}

/* Loading Spinner
 * http://www.alessioatzeni.com/blog/css3-loading-animation-loop/
 */
.loadingSpinner {
	background-color: rgba(0,0,0,0);
	border:5px solid rgba(255,255,255,1);
	opacity:.9;
	border-top:5px solid rgba(0,0,0,0);
	border-left:5px solid rgba(0,0,0,0);
	border-radius:50px;
	box-shadow: 0 0 35px #FFFFFF;
	width:50px;
	height:50px;
	margin: 0 auto;
	-moz-animation:spin .5s infinite linear;
	-webkit-animation:spin .5s infinite linear;
}

.stop {
	-webkit-animation-play-state:paused;
	-moz-animation-play-state:paused;
}

@-moz-keyframes spin {
	0% { -moz-transform:rotate(0deg); }
	100% { -moz-transform:rotate(360deg); }
}
@-moz-keyframes spinoff {
	0% { -moz-transform:rotate(0deg); }
	100% { -moz-transform:rotate(-360deg); }
}
@-webkit-keyframes spin {
	0% { -webkit-transform:rotate(0deg); }
	100% { -webkit-transform:rotate(360deg); }
}
@-webkit-keyframes spinoff {
	0% { -webkit-transform:rotate(0deg); }
	100% { -webkit-transform:rotate(-360deg); }
}

/*********************/
/* Controlbar plugin */
/*********************/
vg-controls {
	width: 100%;
	height: 50px;
	position: absolute;
	z-index: 5;
}
vg-controls #controls-container {
	width: 100%;
	height: 50px;
	background-color: #000000;
	position: absolute;
	display: table;

	zoom: 1;
	filter: alpha(opacity=50);
	opacity: 0.5;
}

vg-play-pause-button {
	display: table-cell;
	width: 50px;
	vertical-align: middle;
	text-align: center;
	cursor: pointer;
}

vg-timedisplay {
	color: #FFFFFF;
	display: table-cell;
	font-family: Arial;
	font-size: 18px;
	width: 75px;
	vertical-align: middle;
	text-align: center;
	cursor: default;
}

.vgTimeDisplay {
	display: table-cell;
	font-family: Arial;
	font-size: 18px;
	width: auto;
}

vg-scrubbar {
	width: auto;
	display: table-cell;
	cursor: pointer;
}

vg-scrubBarCurrentTime {
	background-color: #FFFFFF;
	width: 100%;
	height: 50px;
	display: block;
	cursor: pointer;
}

vg-volume {
	display: table-cell;
	width: 50px;
	vertical-align: middle;
	text-align: center;
	cursor: pointer;
}

vg-volumebar {
	width: 50px;
	height: 100px;
	top: -100px;
	margin-left: -25px;
	vertical-align: middle;
	text-align: center;
	position: absolute;
	cursor: pointer;
}

/* IE10 hack */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
	vg-volumebar {
		zoom: 1;
		filter: alpha(opacity=50);
		opacity: 0.5;
	}
}

vg-fullscreenButton {
	display: table-cell;
	width: 50px;
	vertical-align: middle;
	text-align: center;
	cursor: pointer;
}

vg-volumebar .verticalVolumeBar {
	width: 50px;
	height: 100px;
	background-color: #000000;
	position: absolute;
}

vg-volumebar .volumeBackground {
	width: 20px;
	height: 70px;
	left: 15px;
	top: 15px;
	background-color: #222222;
	position: absolute;
}

vg-volumebar .volumeValue {
	width: 20px;
	height: 100%;
	background-color: #FFFFFF;
	position: absolute;
}

vg-volumebar .volumeClickArea {
	width: 20px;
	height: 100%;
	position: absolute;
}

vg-controls .hide-animation {
	animation: hideControlsAnimationFrames ease-out 0.5s;
	animation-iteration-count: 1;
	animation-fill-mode:forwards; /*when the spec is finished*/
	-webkit-animation: hideControlsAnimationFrames ease-out 0.5s;
	-webkit-animation-iteration-count: 1;
	-webkit-animation-fill-mode:forwards; /*Chrome 16+, Safari 4+*/
	-moz-animation: hideControlsAnimationFrames ease-out 0.5s;
	-moz-animation-iteration-count: 1;
	-moz-animation-fill-mode:forwards; /*FF 5+*/
	-o-animation: hideControlsAnimationFrames ease-out 0.5s;
	-o-animation-iteration-count: 1;
	-o-animation-fill-mode:forwards; /*Not implemented yet*/
	-ms-animation: hideControlsAnimationFrames ease-out 0.5s;
	-ms-animation-iteration-count: 1;
	-ms-animation-fill-mode:forwards; /*IE 10+*/
}

@keyframes hideControlsAnimationFrames{
	0% {
		left:0px;
		top:0px;
		opacity: 0.5;
		transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:50px;
		opacity: 0.5;
		transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-moz-keyframes hideControlsAnimationFrames{
	0% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-moz-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-moz-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-webkit-keyframes hideControlsAnimationFrames {
	0% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-webkit-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-webkit-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-o-keyframes hideControlsAnimationFrames {
	0% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-o-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-o-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-ms-keyframes hideControlsAnimationFrames {
	0% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-ms-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-ms-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

vg-controls .show-animation {
	animation: showControlsAnimationFrames ease-out 0.5s;
	animation-iteration-count: 1;
	animation-fill-mode:forwards; /*when the spec is finished*/
	-webkit-animation: showControlsAnimationFrames ease-out 0.5s;
	-webkit-animation-iteration-count: 1;
	-webkit-animation-fill-mode:forwards; /*Chrome 16+, Safari 4+*/
	-moz-animation: showControlsAnimationFrames ease-out 0.5s;
	-moz-animation-iteration-count: 1;
	-moz-animation-fill-mode:forwards; /*FF 5+*/
	-o-animation: showControlsAnimationFrames ease-out 0.5s;
	-o-animation-iteration-count: 1;
	-o-animation-fill-mode:forwards; /*Not implemented yet*/
	-ms-animation: showControlsAnimationFrames ease-out 0.5s;
	-ms-animation-iteration-count: 1;
	-ms-animation-fill-mode:forwards; /*IE 10+*/
}

@keyframes showControlsAnimationFrames{
	0% {
		left:0px;
		top:50px;
		opacity: 0.5;
		transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:0px;
		opacity: 0.5;
		transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-moz-keyframes showControlsAnimationFrames{
	0% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-moz-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-moz-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-webkit-keyframes showControlsAnimationFrames {
	0% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-webkit-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-webkit-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-o-keyframes showControlsAnimationFrames {
	0% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-o-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-o-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

@-ms-keyframes showControlsAnimationFrames {
	0% {
		left:0px;
		top:50px;
		opacity: 0.5;
		-ms-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
	100% {
		left:0px;
		top:0px;
		opacity: 0.5;
		-ms-transform:  rotate(0deg) scaleX(1) scaleY(1) ;
	}
}

/* Controlbar icons */
vg-play-pause-button div.play:before {
	content: "\e000";
}

vg-play-pause-button div.pause:before {
	content: "\e001";
}

vg-mutebutton div.level3:before {
	content: "\e002";
}

vg-mutebutton div.level2:before {
	content: "\e003";
}

vg-mutebutton div.level1:before {
	content: "\e004";
}

vg-mutebutton div.level0:before {
	content: "\e005";
}

vg-mutebutton div.mute:before {
	content: "\e006";
}

vg-fullscreenButton div.enter:before {
	content: "\e007";
}

vg-fullscreenButton div.exit:before {
	content: "\e008";
}

/*****************/
/* Poster plugin */
/*****************/
vg-poster-image {
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 2;
}

vg-poster-image img, vg-poster-image img.none {
	top: 0;
	bottom: 0;
	right: 0;
	left: 0;
	margin: auto;
	position: absolute;
}

vg-poster-image img.fit {
	width: 100%;
}

vg-poster-image img.fill {
	max-width: none;
	height: 100%;
}

/**********************/
/* IMA ads plugin */
/**********************/
vg-ima-ads {
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 6;
	display: none;
}
