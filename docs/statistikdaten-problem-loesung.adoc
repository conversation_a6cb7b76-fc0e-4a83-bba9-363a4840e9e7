= LLQA v3.0: Lösung für fehlende Statistikdaten
Daniel <PERSON> <<EMAIL>>
v1.0, 2025-08-13
:toc: left
:toclevels: 3
:sectnums:
:source-highlighter: highlight.js
:icons: font

== Problemübersicht

=== Beschreibung

Nach der Migration der Datenbank von LLQA v2.x auf v3.0 sind in der produktiven Umgebung keine Statistikdaten in der Prüfungsübersicht verfügbar. Die Analyse hat gezeigt, dass die Daten korrekt von der *Datenbank* → *Backend* → *Frontend* gelesen und dargestellt werden, jedoch sind die entsprechenden Status-Felder leer.

=== Betroffenes Ticket

* *Linear Ticket:* https://linear.app/talent-factory/issue/LLQA-2/keine-statistikdaten-vorhanden[LLQA-2]
* *GitLab Issue:* https://gitlab.com/talent-factory/customer-projects/llqa/llqa-database/-/issues/7

=== Symptome

* Leere Statistikdiagramme in der Prüfungsübersicht
* Keine Anzeige von TODO/PASS/FAIL/SKIP Verteilungen
* Frontend erhält Status-Werte von `0` für alle Procedures, Steps und Measures

== Technische Analyse

=== Ursache des Problems

In LLQA v3.0 wurde die Architektur grundlegend geändert:

. **Alte Architektur (v2.x):** Status-Werte wurden direkt in den Tabellen `procedures`, `steps` und `measurements` gespeichert
. **Neue Architektur (v3.0):** Status-Logik wurde in die `workflow_element_checks` Tabelle und die `checkdata`-Struktur verlagert

=== Frontend-Statistikberechnung

Die Statistikberechnung erfolgt clientseitig im Frontend:

[source,javascript]
----
// WorkflowIntroCtrl.js - Zeilen 111, 132, 149
statistics.meas[stat_statistic_map[meas.status]] += 1;
statistics.step[stat_statistic_map[step.status]] += 1; 
statistics.proc[stat_statistic_map[proc.status]] += 1;
----

Diese Status-Werte kommen aus der `/api/workflow/:cid` Route und basieren auf der `checkdata`-Struktur in der `checks` Tabelle.

=== Status-Konstanten

[source,ruby]
----
CHK_CDSTATUS_TODO = 0           # Noch zu erledigen
CHK_CDSTATUS_PASSED = 10        # Erfolgreich abgeschlossen  
CHK_CDSTATUS_WARNINGS = 13      # Mit Warnungen abgeschlossen
CHK_CDSTATUS_FAILED = 15        # Fehlgeschlagen
CHK_CDSTATUS_OMITTED_HARD = 20  # Hart übersprungen
CHK_CDSTATUS_OMITTED_SOFT = 21  # Weich übersprungen  
CHK_CDSTATUS_SKIPPED = 30       # Übersprungen
----

== Lösungsansätze

=== Option 1: SQL-basierte Reparatur (Empfohlen)

Verwendung des bereitgestellten SQL-Skripts für eine schnelle und effiziente Reparatur:

[source,bash]
----
# Skript ausführen
psql -h <hostname> -U <username> -d <database> -f utils/fix_statistics_data.sql
----

**Vorteile:**
* Schnelle Ausführung
* Atomare Transaktion
* Umfassende Diagnose und Verifikation

=== Option 2: Ruby-basierte Reparatur

Verwendung des Ruby-Skripts für eine programmatische Lösung:

[source,bash]
----
# Umgebungsvariablen setzen
export DB_HOST=localhost
export DB_NAME=llqa
export DB_USER=postgres  
export DB_PASSWORD=admin

# Skript ausführen
cd server
ruby ../utils/fix_statistics_data.rb
----

**Vorteile:**
* Bessere Fehlerbehandlung
* Detailliertes Logging
* Schrittweise Verarbeitung

== Implementierung

=== Vorbereitung

. **Backup erstellen**
+
[source,sql]
----
-- Backup der aktuellen checkdata
CREATE TABLE checks_checkdata_backup AS 
SELECT id, checkdata FROM checks WHERE checkdata IS NOT NULL;
----

. **Diagnose durchführen**
+
[source,sql]
----
-- Aktuelle Status-Verteilung prüfen
SELECT 
    COUNT(*) as total_checks,
    SUM(CASE WHEN checkdata->'procedures' IS NOT NULL THEN 1 ELSE 0 END) as with_procedures
FROM checks;
----

=== Ausführung

==== SQL-Skript

[source,bash]
----
# 1. In das Projektverzeichnis wechseln
cd /path/to/llqa2-monolith

# 2. Skript ausführen
psql -h <hostname> -U <username> -d <database> -f utils/fix_statistics_data.sql

# 3. Logs prüfen
tail -f /var/log/postgresql/postgresql.log
----

==== Ruby-Skript

[source,bash]
----
# 1. Abhängigkeiten installieren
cd server
bundle install

# 2. Umgebung konfigurieren
export DB_HOST=your-db-host
export DB_NAME=llqa
export DB_USER=your-username
export DB_PASSWORD=your-password

# 3. Skript ausführen
ruby ../utils/fix_statistics_data.rb
----

=== Verifikation

Nach der Ausführung sollten folgende Prüfungen durchgeführt werden:

. **Datenbank-Verifikation**
+
[source,sql]
----
-- Status-Verteilung nach Reparatur
SELECT 
    'Status nach Reparatur' as info,
    COUNT(*) as total_checks,
    SUM(proc_count) as total_procedures,
    SUM(proc_with_status) as procedures_with_status
FROM (
    SELECT 
        id,
        jsonb_array_length(checkdata->'procedures') as proc_count,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(checkdata->'procedures') as proc
            WHERE (proc->>'status')::int > 0
        ) as proc_with_status
    FROM checks 
    WHERE checkdata->'procedures' IS NOT NULL
) status_check;
----

. **Frontend-Test**
+
* LLQA-Anwendung neu starten
* Prüfungsübersicht aufrufen
* Statistikdiagramme prüfen

== Monitoring und Wartung

=== Performance-Optimierung

Nach der Reparatur sollte ein Index für bessere Performance erstellt werden:

[source,sql]
----
-- GIN-Index für checkdata-Abfragen
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checks_checkdata_gin 
ON checks USING gin (checkdata);

-- Tabellenstatistiken aktualisieren
ANALYZE checks;
----

=== Überwachung

Regelmässige Prüfung der Status-Verteilung:

[source,sql]
----
-- Wöchentliche Status-Übersicht
SELECT 
    DATE_TRUNC('week', created_at) as week,
    COUNT(*) as checks_created,
    AVG(
        CASE 
            WHEN checkdata->'procedures' IS NOT NULL 
            THEN jsonb_array_length(checkdata->'procedures')
            ELSE 0 
        END
    ) as avg_procedures_per_check
FROM checks 
WHERE created_at >= NOW() - INTERVAL '4 weeks'
GROUP BY DATE_TRUNC('week', created_at)
ORDER BY week DESC;
----

== Troubleshooting

=== Häufige Probleme

==== Problem: Skript schlägt mit Berechtigungsfehlern fehl

**Lösung:**
[source,bash]
----
# Berechtigungen prüfen
GRANT SELECT, UPDATE ON checks TO your_user;
GRANT SELECT ON measurements TO your_user;
----

==== Problem: Statistiken sind immer noch leer

**Lösung:**
. Browser-Cache leeren
. LLQA-Backend neu starten
. Datenbankverbindung prüfen

==== Problem: Performance-Probleme nach der Reparatur

**Lösung:**
[source,sql]
----
-- Vacuum und Analyze ausführen
VACUUM ANALYZE checks;

-- Index-Nutzung prüfen
EXPLAIN ANALYZE SELECT * FROM checks WHERE checkdata->'procedures' IS NOT NULL;
----

=== Logs und Debugging

**Wichtige Log-Dateien:**
* `/var/log/postgresql/postgresql.log` - Datenbank-Logs
* `/var/log/llqa/backend.log` - Backend-Logs
* Browser Developer Tools - Frontend-Logs

== Commit-Nachricht

Gemäss den Projektrichtlinien sollte die Implementierung mit folgender Commit-Message dokumentiert werden:

[source,text]
----
fix: Statistikdaten nach Migration reparieren

Behebt das Problem fehlender Statistikdaten in der produktiven Umgebung
- SQL-Skript zur Reparatur der checkdata-Status-Werte
- Ruby-Alternative für programmatische Reparatur  
- Umfassende Diagnose und Verifikation
- Performance-Optimierungen durch GIN-Index
- Dokumentation in AsciiDoc-Format

Fixes: LLQA-2
----

== Weiterführende Informationen

=== Relevante Dateien

* `server/models/checks_check.rb` - Check-Model mit checkdata-Serialisierung
* `server/routes/02_rest_check.rb` - Workflow-API-Routen
* `public/js/controllers/WorkflowIntroCtrl.js` - Frontend-Statistikberechnung
* `server/lib/constants.rb` - Status-Konstanten

=== Migration-Skripte

* `LlqaMigration_3_0/src/main/resources/sql/preMigrateScriptsOsterwalder.json`
* `LlqaMigration_3_0/src/main/resources/sql/postMigrateScriptsOsterwalder.json`

=== Kontakt

Bei Fragen oder Problemen:

* **Entwickler:** Daniel Senften <<EMAIL>>
* **Projekt:** LLQA v3.0 Osterwalder
* **Repository:** https://gitlab.com/talent-factory/customer-projects/llqa/llqa2-monolith
