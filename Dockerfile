FROM ruby:2.3.0

ENV LANG=C.UTF-8

RUN mkdir /usr/src/app && mkdir /usr/src/app/server && mkdir /usr/src/app/public && mkdir /usr/src/app/uploads

VOLUME /usr/src/app/uploads

COPY package.json /usr/src/app/package.json
COPY docker-entrypoint.sh /usr/src/app/docker-entrypoint.sh
COPY ./server /usr/src/app/server
COPY ./public /usr/src/app/public

WORKDIR /usr/src/app

# steps required for installing FFmpeg on Debian 8 (Jessie)
RUN echo "deb http://ftp.uk.debian.org/debian jessie-backports main" >> /etc/apt/sources.list
RUN apt-get update -y
RUN apt-get install -y ffmpeg

RUN apt-get -y install nodejs npm

# need to update nodejs to the latest stable version, easiest with n
RUN npm install -g n
RUN n stable
RUN ln -s /usr/bin/nodejs /usr/bin/node

RUN npm install -g yarn
RUN yarn

WORKDIR /usr/src/app/server

RUN bundle install

WORKDIR /usr/src/app

RUN chmod +x /usr/src/app/docker-entrypoint.sh

EXPOSE 4567

CMD ["/usr/src/app/docker-entrypoint.sh"]