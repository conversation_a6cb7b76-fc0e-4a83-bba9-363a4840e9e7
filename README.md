# Umgebungen:

* LLQA Demo: http://************
* Osterwalder Lyss: http://osterwalder-lyss.llqa.ch/app/login
* gfms: http://gfms-milling.llqa.ch/app/login

# Releaseerstellung und installation:

* Version bump in public/js/app.js
* Alle Änderungen einchecken und in den “preview”-Branch mergen (auf den ist der Testserver eingestellt)
* Auf dem Testserver einloggen
* mit “screen -x” in das server-TTY wechseln, das Serverscript dort mit ctrl-C beenden.
* Ein Verzeichnis zurück gehen (-> /home/<USER>/leanqa2)
* Mit “git pull” die Änderungen holen
* OPTIONAL: mit `yarn` die JS-Libs erneuern
* Wieder in das Server-Vz. wechseln (-> /home/<USER>/leanqa2/server)
* OPTIONAL: mit “bundle” die Ruby-Libs erneuern (wenn sich in server/Gemfile was geändert hat)
* OPTIONAL: mit “rake db:migrate” die DB updaten, wenn es neue Migrationen gibt, i.e. sich an dem DB-Schema was geändert hat
* Den Server mit “ruby server.rb” starten
* Mit ctrl-A,D das server-TTY verlassen

# GIT Zugriff von Cloud-Testumgebung
Der Bitbucket-Benutzer LeanLogic wurde per 7.11.2016 gelöscht, der Zugriff auf GIT muss wie folgt über HTTP konfiguriert werden:

git remote add myremote https://<EMAIL>/diso/leanlogic-qa-prototype.git

# Migration erstellen und durchführen
rake db:create_migration NAME=create_dogs

rake db:migrate